# Spring State Machine 集成说明

## 问题解决：StateMachineFactory 找不到

### 问题描述
在集成Spring State Machine时，遇到 `StateMachineFactory` 类找不到的问题。

### 原因分析
1. **版本兼容性问题**：Spring Boot 2.7.18 需要使用兼容的Spring State Machine版本
2. **依赖配置问题**：需要正确配置Spring State Machine的依赖
3. **API变化**：不同版本的Spring State Machine API可能有所不同

### 解决方案

#### 1. 依赖配置
在父pom.xml中添加版本管理：
```xml
<properties>
    <spring-statemachine.version>3.2.1</spring-statemachine.version>
</properties>

<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
            <version>${spring-statemachine.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-starter</artifactId>
            <version>${spring-statemachine.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

在模块pom.xml中添加依赖：
```xml
<dependency>
    <groupId>org.springframework.statemachine</groupId>
    <artifactId>spring-statemachine-core</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.statemachine</groupId>
    <artifactId>spring-statemachine-starter</artifactId>
</dependency>
```

#### 2. 简化配置方案
由于版本兼容性问题，我们采用了简化的配置方案：

**状态机配置类：**
```java
@Configuration
@EnableStateMachine(name = "workPermitStateMachine")
public class WorkPermitStateMachineSimpleConfig extends EnumStateMachineConfigurerAdapter<WorkPermitState, WorkPermitEvent> {
    // 配置状态和转换
}
```

**状态机服务类：**
```java
@Service
public class WorkPermitStateMachineSimpleService {
    
    @Autowired
    @Qualifier("workPermitStateMachine")
    private StateMachine<WorkPermitState, WorkPermitEvent> stateMachine;
    
    // 业务逻辑
}
```

### 实现的功能

#### 1. 状态定义
```java
public enum WorkPermitState {
    DRAFT(0, "草稿"),
    PENDING_ISSUE(10, "待签发"),
    REJECTED(11, "已打回"),
    IN_PROGRESS(20, "作业中"),
    CLOSING(90, "关闭中"),
    CLOSED(100, "已结束");
}
```

#### 2. 事件定义
```java
public enum WorkPermitEvent {
    SUBMIT,      // 提交事件
    ISSUE,       // 签发事件
    REJECT,      // 打回事件
    RESUBMIT,    // 重新提交事件
    END_WORK,    // 结束作业事件
    CLOSE        // 关闭事件
}
```

#### 3. 状态转换规则
- 草稿 → 待签发 (SUBMIT)
- 待签发 → 作业中 (ISSUE)
- 待签发 → 已打回 (REJECT)
- 已打回 → 待签发 (RESUBMIT)
- 作业中 → 关闭中 (END_WORK)
- 关闭中 → 已结束 (CLOSE)

### 使用方式

#### 1. 状态转换
```java
@Service
public class WorkPermitServiceImpl implements WorkPermitService {
    
    @Autowired
    private WorkPermitStateMachineSimpleService stateMachineService;
    
    public void submitWorkPermit(WorkPermitStateTransition param) {
        // 使用状态机进行状态转换
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.SUBMIT, param);
    }
}
```

#### 2. 状态验证
```java
// 检查状态转换是否有效
boolean isValid = stateMachineService.isValidTransition(currentState, event);

// 获取目标状态
WorkPermitState targetState = stateMachineService.getTargetState(currentState, event);
```

### 测试验证
运行测试类验证状态机配置：
```bash
mvn test -Dtest=WorkPermitStateMachineSimpleServiceTest
```

### 注意事项
1. **版本兼容性**：确保Spring State Machine版本与Spring Boot版本兼容
2. **Bean命名**：使用@Qualifier注解指定具体的状态机Bean
3. **状态同步**：确保数据库状态与状态机状态保持一致
4. **异常处理**：妥善处理状态转换异常

### 后续优化
1. **持久化支持**：可以添加状态机状态的持久化
2. **工厂模式**：在版本兼容性解决后，可以升级到工厂模式
3. **监听器增强**：添加更多的状态变更监听器
4. **性能优化**：优化状态机的性能和内存使用

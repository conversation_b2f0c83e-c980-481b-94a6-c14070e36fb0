package com.whfc.quality.service;

import com.whfc.common.exception.BizException;
import com.whfc.quality.dto.QualityAcceptanceDTO;
import com.whfc.quality.param.QualityAcceptanceAddParam;
import com.whfc.quality.param.QualityAcceptanceEditParam;

import java.util.Date;

/**
 * @author: hw
 * @date: 2021-10-29 17:28
 * @description: 验收情况
 */
public interface QualityAcceptanceService {

    /**
     * 新增验收情况
     *
     * @param param
     * @throws BizException
     */
    void add(QualityAcceptanceAddParam param) throws BizException;

    /**
     * 修改验收情况
     *
     * @param param
     * @throws BizException
     */
    void edit(QualityAcceptanceEditParam param) throws BizException;

    /**
     * 验收情况详情
     *
     * @param deptId
     * @param time
     * @return
     * @throws BizException
     */
    QualityAcceptanceDTO details(Integer deptId, Date time) throws BizException;
}

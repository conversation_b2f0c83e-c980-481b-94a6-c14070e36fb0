package com.whfc.fcm.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fcm.dto.WorkPermitDTO;
import com.whfc.fcm.dto.WorkPermitDetailDTO;
import com.whfc.fcm.param.WorkPermitAdd;
import com.whfc.fcm.param.WorkPermitEdit;
import com.whfc.fcm.param.WorkPermitQuery;
import com.whfc.fcm.param.WorkPermitStateTransition;

import java.util.List;

/**
 * 作业票服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
public interface WorkPermitService {

    /**
     * 分页查询作业票列表
     *
     * @param query    查询参数
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 分页数据
     * @throws BizException 业务异常
     */
    PageData<WorkPermitDTO> getWorkPermitList(WorkPermitQuery query, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 查询作业票列表
     *
     * @param query 查询参数
     * @return 作业票列表
     * @throws BizException 业务异常
     */
    List<WorkPermitDTO> getWorkPermitList(WorkPermitQuery query) throws BizException;

    /**
     * 根据GUID获取作业票详情
     *
     * @param guid GUID
     * @return 作业票详情
     * @throws BizException 业务异常
     */
    WorkPermitDetailDTO getWorkPermitDetail(String guid) throws BizException;

    /**
     * 新增作业票
     *
     * @param param 新增参数
     * @return 作业票GUID
     * @throws BizException 业务异常
     */
    String addWorkPermit(WorkPermitAdd param) throws BizException;

    /**
     * 编辑作业票
     *
     * @param param 编辑参数
     * @throws BizException 业务异常
     */
    void editWorkPermit(WorkPermitEdit param) throws BizException;

    /**
     * 删除作业票
     *
     * @param guid GUID
     * @throws BizException 业务异常
     */
    void deleteWorkPermit(String guid) throws BizException;

    /**
     * 提交作业票（草稿 -> 待签发）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void submitWorkPermit(WorkPermitStateTransition param) throws BizException;

    /**
     * 签发作业票（待签发 -> 作业中）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void issueWorkPermit(WorkPermitStateTransition param) throws BizException;

    /**
     * 打回作业票（待签发 -> 已打回）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void rejectWorkPermit(WorkPermitStateTransition param) throws BizException;

    /**
     * 重新提交作业票（已打回 -> 待签发）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void resubmitWorkPermit(WorkPermitStateTransition param) throws BizException;

    /**
     * 开始作业（作业中状态下的操作记录）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void startWork(WorkPermitStateTransition param) throws BizException;

    /**
     * 结束作业（作业中 -> 关闭中）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void endWork(WorkPermitStateTransition param) throws BizException;

    /**
     * 关闭作业票（关闭中 -> 已结束）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void closeWorkPermit(WorkPermitStateTransition param) throws BizException;

    /**
     * 状态转换
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void transitionState(WorkPermitStateTransition param) throws BizException;

    /**
     * 检查状态转换是否合法
     *
     * @param guid    作业票GUID
     * @param toState 目标状态
     * @return 是否合法
     * @throws BizException 业务异常
     */
    boolean checkStateTransition(String guid, Integer toState) throws BizException;
}

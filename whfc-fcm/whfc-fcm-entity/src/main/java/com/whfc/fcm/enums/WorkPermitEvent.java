package com.whfc.fcm.enums;

/**
 * 作业票状态机事件枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
public enum WorkPermitEvent {

    /**
     * 提交事件（草稿 -> 待签发）
     */
    SUBMIT,

    /**
     * 签发事件（待签发 -> 作业中）
     */
    ISSUE,

    /**
     * 打回事件（待签发 -> 已打回）
     */
    REJECT,

    /**
     * 重新提交事件（已打回 -> 待签发）
     */
    RESUBMIT,

    /**
     * 结束作业事件（作业中 -> 关闭中）
     */
    END_WORK,

    /**
     * 关闭事件（关闭中 -> 已结束）
     */
    CLOSE
}

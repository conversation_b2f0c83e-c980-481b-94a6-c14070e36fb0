package com.whfc.fcm.enums;

/**
 * 作业票状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
public enum WorkPermitState {

    DRAFT(0, "草稿"),
    PENDING_ISSUE(10, "待签发"),
    REJECTED(11, "已打回"),
    IN_PROGRESS(20, "作业中"),
    CLOSING(90, "关闭中"),
    CLOSED(100, "已结束");

    private final Integer value;
    private final String desc;

    WorkPermitState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值获取状态
     *
     * @param value 状态值
     * @return 状态枚举
     */
    public static WorkPermitState parseByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (WorkPermitState state : WorkPermitState.values()) {
            if (state.getValue().equals(value)) {
                return state;
            }
        }
        return null;
    }

    /**
     * 根据值获取状态描述
     *
     * @param value 状态值
     * @return 状态描述
     */
    public static String getDescByValue(Integer value) {
        WorkPermitState state = parseByValue(value);
        return state != null ? state.getDesc() : null;
    }

    /**
     * 检查状态转换是否合法（已废弃，使用Spring State Machine管理）
     *
     * @param fromState 原状态
     * @param toState   目标状态
     * @return 是否合法
     * @deprecated 使用 WorkPermitStateMachineService.isValidTransition 替代
     */
    @Deprecated
    public static boolean isValidTransition(WorkPermitState fromState, WorkPermitState toState) {
        if (fromState == null || toState == null) {
            return false;
        }

        switch (fromState) {
            case DRAFT:
                return toState == PENDING_ISSUE;
            case PENDING_ISSUE:
                return toState == IN_PROGRESS || toState == REJECTED;
            case REJECTED:
                return toState == PENDING_ISSUE;
            case IN_PROGRESS:
                return toState == CLOSING;
            case CLOSING:
                return toState == CLOSED;
            case CLOSED:
                return false; // 已结束状态不能转换到其他状态
            default:
                return false;
        }
    }
}

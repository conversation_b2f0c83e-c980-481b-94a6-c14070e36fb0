package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClasssName SafetyCheckDescDTO
 * @Description 安全问题上报详情返回类
 * <AUTHOR>
 * @Date 2020/8/5 10:16
 * @Version 1.0
 */
@Data
public class SafetyCheckDescDTO implements Serializable {

    /**
     * 操作日志表主键
     */
    @JsonIgnore
    private Integer logId;

    @JsonIgnore
    private Integer deptId;

    /**
     * 检查人
     */
    private String userName;


    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 检查说明
     */
    private String checkDesc;

    /**
     * 检查地点
     */
    private String address;

    /**
     * 检查部位
     */
    private String partName;

    /**
     * 责任区域
     */
    private String areaName;

    /**
     * 问题类别
     */
    private String issueTypeName;

    /**
     * 问题明细
     */
    private String issueContent;

    /**
     * 整改要求
     */
    private String rectifyRequire;

    /**
     * 整改期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rectifyTime;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 紧急程度 1-一般  2-紧急
     */
    private Integer urgency;

    /**
     * 紧急程度
     */
    private String urgencyName;

    /**
     * 是否超期
     */
    private Integer overdue;

    /**
     * 现场图片
     */
    private List<String> imgUrlList;

    /**
     * 问题安全处理流程
     */
    private List<SafetyCheckLogListDTO> logList;

    /**
     * 人员信息
     */
    private List<SafetyCheckUserDTO> userList;
}

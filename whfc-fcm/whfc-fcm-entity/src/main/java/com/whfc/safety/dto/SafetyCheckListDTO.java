package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SafetyCheckListDTO
 * @Description 问题列表返回类
 * <AUTHOR>
 * @Date 2020/8/5 10:11
 * @Version 1.0
 */
@Data
public class SafetyCheckListDTO implements Serializable {

    /**
     * 全部问题数量
     */
    private Integer allNum;

    /**
     * 我上报的问题数量
     */
    private Integer reportNum;

    /**
     * 我处理的问题数量
     */
    private Integer disposeNum;

    /**
     * 问题列表
     */
    private List<SafetyCheckDTO> checkList;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 总数
     */
    private Integer total;
}

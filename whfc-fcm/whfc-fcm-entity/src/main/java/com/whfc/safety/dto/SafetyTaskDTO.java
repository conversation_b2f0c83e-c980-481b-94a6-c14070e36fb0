package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 检查任务列表
 * <AUTHOR>
 * @Date 2021/3/4 10:41
 * @Version 1.0
 */
@Data
public class SafetyTaskDTO implements Serializable {

    /**
     * 任务主键
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 任务说明
     */
    private String content;

    /**
     * 发布人id
     */
    private Integer userId;

    /**
     * 发布人姓名
     */
    private String userName;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 状态；0-未发布，1-未开始，2-进行中，3-已完成
     */
    private Integer state;

    /**
     * 检查进度
     */
    private String progress;
}

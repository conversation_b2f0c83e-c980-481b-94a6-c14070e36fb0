package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏 安全检查信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/17 16:45
 */
@Data
public class MdSafetyCheckDTO implements Serializable {

    /**
     * 是否超期  0-未超期  1-超期
     */
    private Integer overdue;

    /**
     * 上报人姓名
     */
    private String checkUserName;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 问题明细
     */
    private String issueContent;

    /**
     * 检查部位名称
     */
    private String partName;

    /**
     * 完成状态   10-待整改 11-待整改(复查不合格) 12-待整改(核验不合格) 20-待复查 30-待核验 40-已完成
     */
    private Integer state;

    /**
     * 紧急程度 1-一般  2-紧急
     */
    private Integer urgency;

    /**
     * 紧急程度
     */
    private String urgencyName;

    /**
     * 问题ID
     */
    private Integer checkId;

    /**
     * 上报
     */
    private SafetyCheckLogListDTO report;

    /**
     * 整改
     */
    private SafetyCheckLogListDTO rectify;

    /**
     * 复查
     */
    private SafetyCheckLogListDTO review;

    /**
     * 核验
     */
    private SafetyCheckLogListDTO verify;
}

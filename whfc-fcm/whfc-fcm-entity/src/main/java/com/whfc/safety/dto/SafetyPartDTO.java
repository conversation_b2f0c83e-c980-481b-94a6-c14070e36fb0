package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SafetyPartDTO
 * @Description 安全问题部位列表返回类
 * <AUTHOR>
 * @Date 2020/8/6 9:07
 * @Version 1.0
 */
@Data
public class SafetyPartDTO implements Serializable {


    /**
     * 部位ID
     */
    private Integer partId;
    /**
     * 父节点ID
     */
    private Integer pid;
    /**
     * 部位编码
     */
    private String code;
    /**
     * 部位名称
     */
    private String name;
    /**
     * 子节点
     */
    private List<SafetyPartDTO> children;
}

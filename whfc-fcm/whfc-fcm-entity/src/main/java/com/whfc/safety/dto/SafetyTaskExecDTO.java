package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 检查记录列表
 * <AUTHOR>
 * @Date 2021/3/8 17:19
 * @Version 1.0
 */
@Data
public class SafetyTaskExecDTO implements Serializable {

    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 检查信息Id
     */
    private Integer execId;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 检查人姓名
     */
    private String checkName;

    /**
     * 检查类型
     */
    private Integer taskType;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 检查部位
     */
    private String partName;

    /**
     * 检查部位id
     */
    private Integer partId;

    /**
     * 检查说明
     */
    private String content;

    /**
     * 检查结果
     */
    private String checkResult;

    /**
     * 是否超期；0-未超期，1-超期
     */
    private Integer overdue;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 地址
     */
    private String location;

    /**
     * 图片列表
     */
    private List<String> imgUrls;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date startTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date endTime;
}

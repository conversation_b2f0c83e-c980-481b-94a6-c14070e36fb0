package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: hw
 * @date: 2021-10-18 17:16
 * @description: 整改通知单列表
 */
@Data
public class SafetyRectifyDTO implements Serializable {

    /**
     * 整改通知单id
     */
    private Integer rectifyId;

    /**
     * 状态 1-已整改 2-未整改
     */
    private Integer state;

    /**
     * 单位Id
     */
    private Integer corpId;

    /**
     * 单位名称
     */
    private String corpName;

    /**
     * 问题数量
     */
    private Integer questionNum;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtil.DATE_TIME_FORMAT)
    private Date createTime;
}

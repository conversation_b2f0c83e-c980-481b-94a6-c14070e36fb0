package com.whfc.safety.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClasssName SafetyIssueDTO
 * @Description 获取安全问题列表返回类
 * <AUTHOR>
 * @Date 2020/8/5 14:29
 * @Version 1.0
 */
@Data
public class SafetyIssueDTO implements Serializable {


    /**
     * 问题ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long issueId;
    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 编码
     */
    private String code;
    /**
     * 检查内容
     */
    private String content;
    /**
     * 整改要求
     */
    private String require;
    /**
     * 整改期限 （天）
     */
    private Integer duration;
    /**
     * 状态   0-已禁用  1-启用
     */
    private Integer enableFlag;
    /**
     * 父ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    private Long issueTypeId;

    /**
     * 父问题类别名称
     */
    private String issueTypeName;
}

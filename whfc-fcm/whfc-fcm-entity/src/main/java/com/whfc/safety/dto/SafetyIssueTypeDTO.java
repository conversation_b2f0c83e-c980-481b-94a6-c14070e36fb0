package com.whfc.safety.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SafetyIssueTypeDTO
 * @Description 获取安全问题类型返回类
 * <AUTHOR>
 * @Date 2020/8/5 14:22
 * @Version 1.0
 */
@Data
public class SafetyIssueTypeDTO implements Serializable {

    /**
     * 问题类型ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long issueTypeId;
    /**
     * 父节点ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    private Long pid;
    /**
     * 部位编码
     */
    private String code;
    /**
     * 问题类型名称
     */
    private String name;
    /**
     * 子节点
     */
    private List<SafetyIssueTypeDTO> children;
    /**
     * 问题明细
     */
    private List<SafetyIssueDTO> issues;
}

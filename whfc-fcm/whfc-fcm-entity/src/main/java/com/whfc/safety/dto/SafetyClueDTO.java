package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SafetyClueDTO implements Serializable {

    /**
     * 线索ID
     */
    private Integer clueId;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 时间
     */
    private Date time;

    /**
     * 姓名
     */
    private String user;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 文件地址
     */
    private String fileGuid;

    /**
     * 扩展信息
     */
    @JsonIgnore
    private String ext;

    /**
     * 图片
     */
    private List<String> images;
}
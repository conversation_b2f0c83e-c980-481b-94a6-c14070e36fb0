package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 检查信息数据
 * <AUTHOR>
 * @Date 2021/3/4 10:57
 * @Version 1.0
 */
@Data
public class SafetyExecNumDTO implements Serializable {

    /**
     * 检查总数
     */
    private Integer checkNum;

    /**
     * 问题总数
     */
    private Integer issueNum;

    /**
     * 排查总数
     */
    private Integer screenNum;

    /**
     * 进行中
     */
    private Integer wayNum;

    /**
     * 未开始
     */
    private Integer notStartedNum;

    /**
     * 进行中
     */
    private Integer underwayNum;

    /**
     * 已完成
     */
    private Integer doneNum;

    /**
     * 未发布
     */
    private Integer unpublished;

    /**
     * 我上报的
     */
    private Integer meNum;

}

package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SafetyInspectDTO implements Serializable {

    private String guid;

    private Integer type;

    private Integer around;

    private String location;

    private String rainstorm;

    private String typhoon;

    private Date checkTime;

    private Integer checkUserId;

    private String checkUserName;

    private String position;

    private String sign;

    private Integer state;

    private String exportUrl;

    /**
     * 检查项列表
     */
    private List<SafetyInspectItemDTO> itemList;

    /**
     * 检查图片列表
     */
    private List<SafetyInspectImageDTO> imageList;
}
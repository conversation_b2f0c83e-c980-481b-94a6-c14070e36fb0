package com.whfc.safety.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 安全大屏检查统计
 * <AUTHOR>
 * @Date 2021-09-02 14:56
 * @Version 1.0
 */
@Data
public class SafetyCheckScreenDTO implements Serializable {

    /**
     * 问题总数
     */
    private Integer questionNum;

    /**
     * 排查总数
     */
    private Integer checkNum;

    /**
     * 待整改
     */
    private Integer rectifiedNum;

    /**
     * 待复查
     */
    private Integer reviewedNum;

    /**
     * 待核验
     */
    private Integer verifiedNum;

    /**
     * 不合格
     */
    private Integer unqualifiedNum;

    /**
     * 合格
     */
    private Integer successNum;

    /**
     * 上报问题类别
     */
    List<SafetyIssueNumDTO> list;
}

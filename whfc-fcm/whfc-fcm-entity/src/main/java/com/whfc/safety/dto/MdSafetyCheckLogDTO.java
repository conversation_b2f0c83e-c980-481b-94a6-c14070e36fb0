package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏 安全检查动态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/17 16:58
 */
@Data
public class MdSafetyCheckLogDTO implements Serializable {

    /**
     * 记录ID
     */
    @JsonIgnore
    private Integer logId;

    /**
     * 检查记录ID
     */
    @JsonIgnore
    private Integer checkId;

    /**
     * 检查说明
     */
    private String checkDesc;

    /**
     * 问题时间
     */
    private Date opTime;

    /**
     * 操作类型 1-上报 2-整改 3-复查 4-核验
     */
    private Integer opType;

    /**
     * 操作说明
     */
    private String opDesc;

    /**
     * 操作状态
     */
    private Integer opResult;

    /**
     * 问题图片
     */
    private String imgUrl;

}

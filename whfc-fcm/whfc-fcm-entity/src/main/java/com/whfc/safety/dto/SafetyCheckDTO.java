package com.whfc.safety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Objects;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClasssName SafetyCheckDTO
 * @Description 问题列表
 * <AUTHOR>
 * @Date 2020/8/5 10:11
 * @Version 1.0
 */
@Data
public class SafetyCheckDTO implements Serializable, Comparable<SafetyCheckDTO> {

    /**
     * 问题上报主键
     */
    private Integer checkId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 部位名称
     */
    private String partName;

    /**
     * 问题明细
     */
    private String issueContent;

    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 检查说明
     */
    private String checkDesc;

    /**
     * 上报人ID
     */
    private Integer checkUserId;

    /**
     * 上报人姓名
     */
    private String checkUserName;

    /**
     * 整改要求
     */
    private String rectifyRequire;

    /**
     * 整改期限 （天）
     */
    private Integer rectifyDuration;

    /**
     * 整改开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rectifyStartTime;

    /**
     * 整改结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rectifyEndTime;

    /**
     * 整改结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rectifyTime;

    /**
     * 整改人呢姓名
     */
    private String rectifyUserName;

    /**
     * 整改结果
     */
    private String rectifyResults;


    /**
     * 成状态   10-待整改 11-待整改(复查不合格) 12-待整改(核验不合格) 20-待复查 30-待核验 40-已完
     */
    private Integer state;

    /**
     * 紧急程度 1-一般  2-紧急
     */
    private Integer urgency;

    /**
     * 紧急程度
     */
    private String urgencyName;

    /**
     * 是否超期  0-未超期  1-超期
     */
    private Integer overdue;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 地址
     */
    private String address;

    /**
     * 操作类型 1-上报 2-整改 3-复查 4-核验
     */
    @JsonIgnore
    private Integer Type;

    /**
     * 问题图片
     */
    private List<String> imgUrlList;

    /**
     * 整改图片
     */
    private List<String> rectifyImgUrlList;

    /**
     * 问题安全处理流程
     */
    private List<SafetyCheckLogListDTO> logList;

    /**
     * 人员信息
     */
    private List<SafetyCheckUserDTO> userList;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SafetyCheckDTO that = (SafetyCheckDTO) o;
        return Objects.equal(checkId, that.checkId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(checkId);
    }

    @Override
    public int compareTo(@NotNull SafetyCheckDTO o) {
        return this.checkId.compareTo(o.getCheckId());
    }
}

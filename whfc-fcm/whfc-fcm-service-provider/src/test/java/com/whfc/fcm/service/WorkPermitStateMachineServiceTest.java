package com.whfc.fcm.service;

import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import com.whfc.fcm.param.WorkPermitStateTransition;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 作业票状态机服务测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@SpringBootTest
@ActiveProfiles("test")
public class WorkPermitStateMachineServiceTest {

    @Autowired
    private WorkPermitStateMachineSimpleService stateMachineService;

    @Test
    public void testValidTransitions() {
        // 测试有效的状态转换
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.SUBMIT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.ISSUE));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.REJECT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.REJECTED, WorkPermitEvent.RESUBMIT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.IN_PROGRESS, WorkPermitEvent.END_WORK));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.CLOSING, WorkPermitEvent.CLOSE));
    }

    @Test
    public void testInvalidTransitions() {
        // 测试无效的状态转换
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.ISSUE));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.REJECT));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.IN_PROGRESS, WorkPermitEvent.SUBMIT));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.CLOSED, WorkPermitEvent.SUBMIT));
    }

    @Test
    public void testEventByTransition() {
        // 测试根据状态转换获取事件
        assertEquals(WorkPermitEvent.SUBMIT, 
            stateMachineService.getEventByTransition(WorkPermitState.DRAFT, WorkPermitState.PENDING_ISSUE));
        assertEquals(WorkPermitEvent.ISSUE, 
            stateMachineService.getEventByTransition(WorkPermitState.PENDING_ISSUE, WorkPermitState.IN_PROGRESS));
        assertEquals(WorkPermitEvent.REJECT, 
            stateMachineService.getEventByTransition(WorkPermitState.PENDING_ISSUE, WorkPermitState.REJECTED));
        assertEquals(WorkPermitEvent.RESUBMIT, 
            stateMachineService.getEventByTransition(WorkPermitState.REJECTED, WorkPermitState.PENDING_ISSUE));
        assertEquals(WorkPermitEvent.END_WORK, 
            stateMachineService.getEventByTransition(WorkPermitState.IN_PROGRESS, WorkPermitState.CLOSING));
        assertEquals(WorkPermitEvent.CLOSE, 
            stateMachineService.getEventByTransition(WorkPermitState.CLOSING, WorkPermitState.CLOSED));
    }

    @Test
    public void testInvalidEventByTransition() {
        // 测试无效的状态转换应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getEventByTransition(WorkPermitState.DRAFT, WorkPermitState.IN_PROGRESS);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getEventByTransition(WorkPermitState.CLOSED, WorkPermitState.DRAFT);
        });
    }

    @Test
    public void testStateMachineWorkflow() {
        String testGuid = "test-work-permit-" + System.currentTimeMillis();
        
        try {
            // 测试完整的工作流程
            WorkPermitStateTransition param = new WorkPermitStateTransition();
            param.setGuid(testGuid);
            param.setOpUserId(1);
            param.setOpUserName("测试用户");
            param.setRemark("测试状态转换");

            // 1. 提交（草稿 -> 待签发）
            boolean result1 = stateMachineService.sendEvent(testGuid, WorkPermitState.DRAFT, WorkPermitEvent.SUBMIT, param);
            assertTrue(result1);
            assertEquals(WorkPermitState.PENDING_ISSUE, stateMachineService.getCurrentState(testGuid));

            // 2. 签发（待签发 -> 作业中）
            boolean result2 = stateMachineService.sendEvent(testGuid, WorkPermitState.PENDING_ISSUE, WorkPermitEvent.ISSUE, param);
            assertTrue(result2);
            assertEquals(WorkPermitState.IN_PROGRESS, stateMachineService.getCurrentState(testGuid));

            // 3. 结束作业（作业中 -> 关闭中）
            boolean result3 = stateMachineService.sendEvent(testGuid, WorkPermitState.IN_PROGRESS, WorkPermitEvent.END_WORK, param);
            assertTrue(result3);
            assertEquals(WorkPermitState.CLOSING, stateMachineService.getCurrentState(testGuid));

            // 4. 关闭（关闭中 -> 已结束）
            boolean result4 = stateMachineService.sendEvent(testGuid, WorkPermitState.CLOSING, WorkPermitEvent.CLOSE, param);
            assertTrue(result4);
            assertEquals(WorkPermitState.CLOSED, stateMachineService.getCurrentState(testGuid));

        } catch (Exception e) {
            fail("状态机工作流程测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testRejectAndResubmitWorkflow() {
        String testGuid = "test-reject-resubmit-" + System.currentTimeMillis();
        
        try {
            WorkPermitStateTransition param = new WorkPermitStateTransition();
            param.setGuid(testGuid);
            param.setOpUserId(1);
            param.setOpUserName("测试用户");
            param.setRemark("测试打回重提");

            // 1. 提交（草稿 -> 待签发）
            boolean result1 = stateMachineService.sendEvent(testGuid, WorkPermitState.DRAFT, WorkPermitEvent.SUBMIT, param);
            assertTrue(result1);

            // 2. 打回（待签发 -> 已打回）
            boolean result2 = stateMachineService.sendEvent(testGuid, WorkPermitState.PENDING_ISSUE, WorkPermitEvent.REJECT, param);
            assertTrue(result2);
            assertEquals(WorkPermitState.REJECTED, stateMachineService.getCurrentState(testGuid));

            // 3. 重新提交（已打回 -> 待签发）
            boolean result3 = stateMachineService.sendEvent(testGuid, WorkPermitState.REJECTED, WorkPermitEvent.RESUBMIT, param);
            assertTrue(result3);
            assertEquals(WorkPermitState.PENDING_ISSUE, stateMachineService.getCurrentState(testGuid));

        } catch (Exception e) {
            fail("打回重提工作流程测试失败: " + e.getMessage());
        }
    }
}

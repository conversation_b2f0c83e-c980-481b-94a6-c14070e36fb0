package com.whfc.fcm.service;

import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 作业票状态机简单服务测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@SpringBootTest
@ActiveProfiles("test")
public class WorkPermitStateMachineSimpleServiceTest {

    @Autowired
    private WorkPermitStateMachineSimpleService stateMachineService;

    @Test
    public void testValidTransitions() {
        // 测试有效的状态转换
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.SUBMIT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.ISSUE));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.REJECT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.REJECTED, WorkPermitEvent.RESUBMIT));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.IN_PROGRESS, WorkPermitEvent.END_WORK));
        assertTrue(stateMachineService.isValidTransition(WorkPermitState.CLOSING, WorkPermitEvent.CLOSE));
    }

    @Test
    public void testInvalidTransitions() {
        // 测试无效的状态转换
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.ISSUE));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.DRAFT, WorkPermitEvent.REJECT));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.IN_PROGRESS, WorkPermitEvent.SUBMIT));
        assertFalse(stateMachineService.isValidTransition(WorkPermitState.CLOSED, WorkPermitEvent.SUBMIT));
    }

    @Test
    public void testEventByTransition() {
        // 测试根据状态转换获取事件
        assertEquals(WorkPermitEvent.SUBMIT, 
            stateMachineService.getEventByTransition(WorkPermitState.DRAFT, WorkPermitState.PENDING_ISSUE));
        assertEquals(WorkPermitEvent.ISSUE, 
            stateMachineService.getEventByTransition(WorkPermitState.PENDING_ISSUE, WorkPermitState.IN_PROGRESS));
        assertEquals(WorkPermitEvent.REJECT, 
            stateMachineService.getEventByTransition(WorkPermitState.PENDING_ISSUE, WorkPermitState.REJECTED));
        assertEquals(WorkPermitEvent.RESUBMIT, 
            stateMachineService.getEventByTransition(WorkPermitState.REJECTED, WorkPermitState.PENDING_ISSUE));
        assertEquals(WorkPermitEvent.END_WORK, 
            stateMachineService.getEventByTransition(WorkPermitState.IN_PROGRESS, WorkPermitState.CLOSING));
        assertEquals(WorkPermitEvent.CLOSE, 
            stateMachineService.getEventByTransition(WorkPermitState.CLOSING, WorkPermitState.CLOSED));
    }

    @Test
    public void testTargetState() {
        // 测试根据当前状态和事件获取目标状态
        assertEquals(WorkPermitState.PENDING_ISSUE, 
            stateMachineService.getTargetState(WorkPermitState.DRAFT, WorkPermitEvent.SUBMIT));
        assertEquals(WorkPermitState.IN_PROGRESS, 
            stateMachineService.getTargetState(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.ISSUE));
        assertEquals(WorkPermitState.REJECTED, 
            stateMachineService.getTargetState(WorkPermitState.PENDING_ISSUE, WorkPermitEvent.REJECT));
        assertEquals(WorkPermitState.PENDING_ISSUE, 
            stateMachineService.getTargetState(WorkPermitState.REJECTED, WorkPermitEvent.RESUBMIT));
        assertEquals(WorkPermitState.CLOSING, 
            stateMachineService.getTargetState(WorkPermitState.IN_PROGRESS, WorkPermitEvent.END_WORK));
        assertEquals(WorkPermitState.CLOSED, 
            stateMachineService.getTargetState(WorkPermitState.CLOSING, WorkPermitEvent.CLOSE));
    }

    @Test
    public void testInvalidEventByTransition() {
        // 测试无效的状态转换应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getEventByTransition(WorkPermitState.DRAFT, WorkPermitState.IN_PROGRESS);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getEventByTransition(WorkPermitState.CLOSED, WorkPermitState.DRAFT);
        });
    }

    @Test
    public void testInvalidTargetState() {
        // 测试无效的状态和事件组合应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getTargetState(WorkPermitState.DRAFT, WorkPermitEvent.ISSUE);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            stateMachineService.getTargetState(WorkPermitState.CLOSED, WorkPermitEvent.SUBMIT);
        });
    }
}

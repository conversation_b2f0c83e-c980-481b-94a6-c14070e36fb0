package com.whfc.quality.dao;

import com.whfc.quality.entity.QualityTaskItemImg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QualityTaskItemImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTaskItemImg record);

    int insertSelective(QualityTaskItemImg record);

    QualityTaskItemImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTaskItemImg record);

    int updateByPrimaryKey(QualityTaskItemImg record);

    /**
     * 查询图片地址
     * @param execId
     * @return
     */
    List<String> selectImgUrlByExecId(Integer execId);

    /**
     * 批量插入图片
     * @param execId
     * @param imgUrls
     */
    void insertAll(@Param("execId") Integer execId, @Param("imgUrls") List<String> imgUrls);
}
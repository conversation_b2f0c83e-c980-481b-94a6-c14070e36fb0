package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityExecNumDTO;
import com.whfc.quality.dto.QualityTaskDTO;
import com.whfc.quality.entity.QualityTask;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface QualityTaskMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTask record);

    int insertSelective(QualityTask record);

    QualityTask selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTask record);

    int updateByPrimaryKey(QualityTask record);

    /**
     * 检查记录统计
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    QualityExecNumDTO selectNumByDeptId(@Param("deptId") Integer deptId,
                                        @Param("title") String title,
                                        @Param("overdue") Integer overdue,
                                        @Param("startTime") Date startTime,
                                        @Param("endTime") Date endTime);

    /**
     * 检查任务列表
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityTaskDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("title") String title,
                                        @Param("overdue") Integer overdue, @Param("state") Integer state,
                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 更新任务状态
     *
     * @param taskIds
     */
    void updateStateByTaskIds(List<Integer> taskIds);


    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    int logicDeleteByPrimaryKey(Integer id);
}
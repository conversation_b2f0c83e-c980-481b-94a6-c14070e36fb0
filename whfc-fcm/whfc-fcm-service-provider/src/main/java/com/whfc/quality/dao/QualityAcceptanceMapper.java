package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityAcceptanceDTO;
import com.whfc.quality.entity.QualityAcceptance;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * @author: hw
 * @date: 2021-10-29 17:20
 * @description: //todo
 */
public interface QualityAcceptanceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityAcceptance record);

    int insertSelective(QualityAcceptance record);

    QualityAcceptance selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityAcceptance record);

    int updateByPrimaryKey(QualityAcceptance record);

    /**
     * 查询验收情况
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    QualityAcceptanceDTO selectByDeptId(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityTaskItemPartDTO;
import com.whfc.quality.entity.QualityTaskItemPart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QualityTaskItemPartMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTaskItemPart record);

    int insertSelective(QualityTaskItemPart record);

    QualityTaskItemPart selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTaskItemPart record);

    int updateByPrimaryKey(QualityTaskItemPart record);

    /**
     * 批量添加部位
     *
     * @param taskId
     * @param taskItemId
     * @param partIdList
     */
    void insertAll(@Param("taskId") Integer taskId, @Param("taskItemId") Integer taskItemId, @Param("partIdList") List<Integer> partIdList);

    /**
     * 查询检查任务-部位
     *
     * @param taskItemId
     * @return
     */
    List<QualityTaskItemPartDTO> selectByTaskItemId(@Param("taskItemId") Integer taskItemId);

    /**
     * 软删除
     *
     * @param taskId
     */
    void del(Integer taskId);
}
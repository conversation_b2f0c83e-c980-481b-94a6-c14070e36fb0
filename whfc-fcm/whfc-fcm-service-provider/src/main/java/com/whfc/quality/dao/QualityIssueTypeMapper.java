package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityIssueTypeDTO;
import com.whfc.quality.entity.QualityIssueType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-29 15:24
 */

public interface QualityIssueTypeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityIssueType record);

    int insertSelective(QualityIssueType record);

    QualityIssueType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QualityIssueType record);

    int updateByPrimaryKey(QualityIssueType record);

    /**
     * 查询问题类型列表
     *
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return 问题类型
     */
    List<QualityIssueTypeDTO> selectIssueTypeList(@Param("deptId") Integer deptId,
                                                  @Param("keyword") String keyword);

    /**
     * 使用编码查询问题类型
     *
     * @param code 编码
     * @return QualityIssueTypeDTO
     * <AUTHOR>
     * @date 2020/8/4 14:49
     **/
    QualityIssueType selectIssueTypeByCode(@Param("deptId") Integer deptId,
                                           @Param("code") String code);

    /**
     * @param issueTypeId 问题类别主键
     * @param code        问题类别编码
     * @param name        问题内别名称
     * @return
     * <AUTHOR>
     * @date 2020/8/4 15:06
     **/
    void updateById(@Param("issueTypeId") Long issueTypeId, @Param("code") String code, @Param("name") String name);

    /**
     * 逻辑删除部位
     *
     * @param issueTypeId 问题内别主键
     * @return
     * <AUTHOR>
     * @date 2020/8/4 15:15
     **/
    void logicDel(@Param("issueTypeId") Long issueTypeId);

    /**
     * 使用父类id查询对应信息
     *
     * @param pid 父类id
     * @return
     * <AUTHOR>
     * @date 2020/8/6 18:42
     **/
    List<QualityIssueType> selectByPid(@Param("pid") Long pid);

    /**
     * 使用code 查询全部下级
     * @param code 编码
     * @return
     */
    List<QualityIssueType> selectLikeCode(@Param("code") String code);


}
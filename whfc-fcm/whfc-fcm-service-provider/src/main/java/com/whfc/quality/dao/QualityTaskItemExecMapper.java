package com.whfc.quality.dao;

import com.whfc.quality.dto.*;
import com.whfc.quality.entity.QualityTaskItem;
import com.whfc.quality.entity.QualityTaskItemExec;
import com.whfc.quality.entity.QualityTaskItemTime;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface QualityTaskItemExecMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTaskItemExec record);

    int insertSelective(QualityTaskItemExec record);

    QualityTaskItemExec selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTaskItemExec record);

    int updateByPrimaryKey(QualityTaskItemExec record);

    /**
     * 检查日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<QualityUserNumDTO> selectUserNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 批量添加
     *
     * @param deptId
     * @param taskId
     * @param taskItem
     * @param itemParts
     * @param itemTimes
     */
    void insertAll(@Param("deptId") Integer deptId, @Param("taskId") Integer taskId, @Param("taskItem") QualityTaskItem taskItem,
                   @Param("itemParts") List<QualityTaskItemPartDTO> itemParts, @Param("itemTimes") List<QualityTaskItemTime> itemTimes);

    /**
     * 检查记录列表
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityTaskExecDTO> selectByDeptId(@Param("userId") Integer userId, @Param("deptId") Integer deptId,
                                            @Param("keyword") String keyword, @Param("overdue") Integer overdue,
                                            @Param("partId") Integer partId, @Param("state") Integer state,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询到期时间为昨天的任务
     *
     * @return
     */
    List<QualityTaskItemExec> selectYesterday();

    /**
     * 更新任务超期状态为超期
     *
     * @param execIds
     */
    void updateOverdue(List<Integer> execIds);

    /**
     * 查询开始时间为昨天的任务
     *
     * @return
     */
    List<QualityTaskItemExec> selectToday();

    /**
     * 查询任务
     *
     * @param taskId
     * @return
     */
    List<QualityTaskItemExec> selectByTaskId(Integer taskId);

    /**
     * 更新任务状态
     *
     * @param execIds
     */
    void updateStateByExecIds(List<Integer> execIds);

    /**
     * 查询是否存在未完成数据
     *
     * @param taskItemId
     * @return
     */
    List<QualityTaskItemExec> selectUnfinishedByTaskItemId(Integer taskItemId);

    /**
     * 查询是否存在未完成数据
     *
     * @param taskId
     * @return
     */
    List<QualityTaskItemExec> selectUnfinishedByTaskId(Integer taskId);

    /**
     * 检查信息数据
     *
     * @param taskItemId
     * @return
     */
    QualityExecNumDTO selectExecNumByTaskItemId(Integer taskItemId);

    /**
     * 任务统计
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    QualityExecNumDTO selectExecNumByDeptId(@Param("userId") Integer userId, @Param("deptId") Integer deptId,
                                            @Param("keyword") String keyword, @Param("overdue") Integer overdue,
                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询我上报的数量
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     */
    QualityExecNumDTO selectMeNum(@Param("userId") Integer userId, @Param("deptId") Integer deptId,
                                  @Param("partId") Integer partId, @Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime);

    /**
     * 检查信息详情
     *
     * @param taskItemId
     * @return
     */
    List<QualityExecDetailsDTO> selectDetailsByTaskItemId(Integer taskItemId);

    /**
     * 查询检查任务信息
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityTaskExecDTO> selectByDeptIdAndUserId(@Param("userId") Integer userId, @Param("deptId") Integer deptId,
                                                     @Param("partId") Integer partId, @Param("state") Integer state,
                                                     @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询排查任务数量
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectNumByDeptId(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 删除检查任务
     * @param execId 检查任务ID
     */
    void logicDel(@Param("execId") Integer execId);
}
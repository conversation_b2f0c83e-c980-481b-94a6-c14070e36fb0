package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityRectifyDTO;
import com.whfc.quality.entity.QualityRectifyInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-19 9:56
 * @description: //todo
 */
public interface QualityRectifyInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityRectifyInfo record);

    int insertSelective(QualityRectifyInfo record);

    QualityRectifyInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityRectifyInfo record);

    int updateByPrimaryKey(QualityRectifyInfo record);

    /**
     * 整改通知单列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityRectifyDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 删除
     *
     * @param rectifyId
     */
    void del(@Param("rectifyId") Integer rectifyId);
}
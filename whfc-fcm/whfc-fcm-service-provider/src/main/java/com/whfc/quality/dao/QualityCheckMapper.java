package com.whfc.quality.dao;

import com.whfc.common.exception.BizException;
import com.whfc.quality.dto.*;
import com.whfc.quality.entity.QualityCheck;
import com.whfc.quality.param.QualityCheckListParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-29 15:24
 */

public interface QualityCheckMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(QualityCheck record);

    QualityCheck selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityCheck record);

    /**
     * 查询问题上报列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param states
     * @return
     */
    List<QualityCheckDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime,
                                         @Param("states") List<Integer> states);

    /**
     * 查询问题上报列表
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @param states
     * @param partId
     * @param urgency
     * @param overdue
     * @param idList
     * @return
     */
    List<QualityCheckDTO> selectCheckList(@Param("deptIdList") List<Integer> deptIdList,
                                          @Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime,
                                          @Param("state") Integer[] states,
                                          @Param("partId") Integer partId,
                                          @Param("urgency") Integer[] urgency,
                                          @Param("overdue") Integer[] overdue,
                                          @Param("idList") List<Integer> idList);

    /**
     * 查询我提交的
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectMyCommitIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询我上报的总条数
     *
     * @param qualityCheckListParam 问题上报请求类
     * @return
     * <AUTHOR>
     * @date 2020/8/4 10:20
     **/
    Integer countReport(QualityCheckListParam qualityCheckListParam);

    /**
     * 使用主键查询问题表详情
     *
     * @param id 问题表主键id
     * @return QualityCheckDescDTO
     * <AUTHOR>
     * @date 2020/8/1 9:19
     **/
    QualityCheckDescDTO selectById(@Param("id") Integer id);

    /**
     * 使用问题提交时间查询问题数量
     *
     * @param deptId    组织机构ID
     * @param userId    用户id
     * @param checkTime 问题提交时间
     * @param channel   1- 日，2-年
     * @return Integer
     * <AUTHOR>
     * @date 2020/8/3 17:17
     **/
    Integer selectByCheckTime(@Param("deptId") Integer deptId, @Param("userId") Integer userId, @Param("checkTime") Date checkTime, @Param("channel") Integer channel);

    /**
     * 按天统计
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectByDate(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按月统计
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectByMonth(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 使用提交时间和状态查询问题数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     * @date 2020/8/3 18:54
     **/
    List<QualityCheckStatDTO> selectByStateAndCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 使用提交时间和紧急程度查询问题数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     * @date 2020/8/3 19:05
     **/
    List<QualityCheckStatDTO> selectByUrgencyAndCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题上报类别数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return List<QualityCheckIssueStatisticalDTO>
     * <AUTHOR>
     * @date 2020/8/3 18:51
     **/
    List<QualityCheckStatDTO> selectIssueByCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题上报部位数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     * @date 3 19:26
     **/
    List<QualityCheckStatDTO> selectPartByCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题上报紧急程度
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectLevelByCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询全部上报信息
     *
     * @return
     * <AUTHOR>
     * @date 2020/8/12 9:21
     **/
    List<Integer> selectCheckIdAll();

    /**
     * 更新超期状态为超期
     *
     * @param checkIds 问题id
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/12 11:28
     **/
    void updateOverdue(@Param("checkIds") List<Integer> checkIds);

    /**
     * 问题日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<QualityUserNumDTO> selectUserNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 问题日志
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityIssueNumDTO> selectIssueNum(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 问题统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    QualityCheckScreenDTO selectStatisticalByDeptId(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 未销问题分析
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    QualityCheckUnfinishedDTO selectUnfinishedByDeptId(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题类别分析
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityIssueNumDTO> selectIssueTypeNum(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题部位统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectPartNum(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题紧急程度统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectLevelNum(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题-按项目统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityCheckStatDTO> selectProjectNum(@Param("deptIdList") Collection<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 大屏 检查数据
     *
     * @param deptId 组织架构ID
     * @return 检查数据
     */
    List<MdQualityCheckDTO> selectMdBoardCheckList(@Param("deptId") Integer deptId);

    /**
     * 获取新增问题数量
     *
     * @param deptId    组织架构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 新增问题数量
     */
    Integer countCheckNewIssue(@Param("deptId") Integer deptId,
                               @Param("startTime") Date startTime,
                               @Param("endTime") Date endTime);

    /**
     * 统计待整改数量
     *
     * @param deptId 组织架构ID
     * @return 待整改数量
     */
    Integer countCheckToBeRectified(@Param("deptId") Integer deptId);

    /**
     * 统计检查数量
     *
     * @param deptId 组织架构ID
     * @param state  状态
     * @return 检查数量
     */
    Integer countCheckNum(@Param("deptId") Integer deptId, @Param("state") Integer state);

    /**
     * 获取质量的组织机构ID列表
     *
     * @return 组织机构ID列表
     */
    List<Integer> selectQualityDeptIds();


    /**
     * 统计当天完成
     *
     * @param deptId 组织机构ID
     * @param date   日期
     * @return 当天完成数量
     */
    Integer countCheckComplete(@Param("deptId") Integer deptId, @Param("date") Date date);

    Integer countCheckCompleteMonth(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 逻辑删除检查记录
     *
     * @param checkId 检查记录ID
     */
    void logicDel(@Param("checkId") Integer checkId);
}
package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityCheckDTO;
import com.whfc.quality.entity.QualityRectifyDetails;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-19 9:56
 * @description: //todo
 */
public interface QualityRectifyDetailsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityRectifyDetails record);

    int insertSelective(QualityRectifyDetails record);

    QualityRectifyDetails selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityRectifyDetails record);

    int updateByPrimaryKey(QualityRectifyDetails record);

    /**
     * 整改通知单详情
     *
     * @param rectifyId
     * @return
     */
    List<QualityCheckDTO> selectByRectifyId(Integer rectifyId);

    /**
     * 添加整改通知单详情
     *
     * @param rectifyId
     * @param checkIds
     */
    void insertAll(@Param("rectifyId") Integer rectifyId, @Param("checkIds") List<Integer> checkIds);

    /**
     * 软删除
     * @param rectifyId
     */
    void delByRectifyId(@Param("rectifyId") Integer rectifyId);
}
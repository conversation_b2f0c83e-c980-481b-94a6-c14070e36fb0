package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityInsLotDetailDTO;
import com.whfc.quality.entity.QualityInspectionLotDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QualityInspectionLotDetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityInspectionLotDetail record);

    int insertSelective(QualityInspectionLotDetail record);

    QualityInspectionLotDetail selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityInspectionLotDetail record);

    int updateByPrimaryKey(QualityInspectionLotDetail record);

    /**
     * 批量插入
     *
     * @param itemList
     * @return
     */
    int batchInsert(@Param("itemList") List<QualityInsLotDetailDTO> itemList);

    /**
     * 查询检验批详情
     *
     * @param lotId
     * @return
     */
    List<QualityInsLotDetailDTO> selectByLotId(@Param("lotId") Integer lotId);
}
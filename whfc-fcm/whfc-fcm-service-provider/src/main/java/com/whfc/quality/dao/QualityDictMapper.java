package com.whfc.quality.dao;

import com.whfc.quality.entity.QualityDict;
import org.apache.ibatis.annotations.Param;

public interface QualityDictMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityDict record);

    int insertSelective(QualityDict record);

    QualityDict selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityDict record);

    /**
     * 查询字段
     *
     * @param deptId
     * @param code
     * @return
     */
    QualityDict selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);
}
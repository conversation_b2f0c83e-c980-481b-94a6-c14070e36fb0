package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityPartDTO;
import com.whfc.quality.entity.QualityPart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-29 15:24
 */

public interface QualityPartMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityPart record);

    int insertSelective(QualityPart record);

    QualityPart selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityPart record);

    int updateByPrimaryKey(QualityPart record);

    /**
     * 获取部位列表
     *
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return 部位列表
     */
    List<QualityPartDTO> selectQualityPartDTOList(@Param("deptId") Integer deptId, @Param("keyword") String keyword);

    /**
     * 根据部位ID 更新部位名称
     *
     * @param partId 部位ID
     * @param code   部位编码
     * @param name   部位名称
     */
    void updateNameById(@Param("partId") Integer partId, @Param("code") String code, @Param("name") String name);

    /**
     * 逻辑删除部位
     *
     * @param partId 部位ID
     */
    void logicDel(@Param("partId") Integer partId);

    /**
     * 根据编码查询部位
     *
     * @param code 编码
     * @param deptId
     * @return 部位
     */
    QualityPart selectPartByCode(@Param("code") String code,@Param("deptId") Integer deptId);

    /**
     * 获取所有的编码
     *
     * @param deptId 组织机构ID
     * @return 编码
     */
    List<String> selectCodeList(Integer deptId);

    /**
     * 使用父id查询子
     *
     * @param pid 父级ID
     * @return
     * <AUTHOR>
     * @date 2020/8/6 19:03
     **/
    List<QualityPart> selectByPid(@Param("pid") Integer pid);

    /**
     * 使用编码查询全部子节点
     *
     * @param code 编码
     * @return
     * <AUTHOR>
     * @date 2020/8/6 19:48
     **/
    List<QualityPart> selectLikeCode(@Param("code") String code);


}
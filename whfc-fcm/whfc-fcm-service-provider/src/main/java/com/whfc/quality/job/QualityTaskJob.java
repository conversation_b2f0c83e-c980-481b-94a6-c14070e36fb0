package com.whfc.quality.job;

import com.whfc.XxlJobConfig;
import com.whfc.common.util.DateUtil;
import com.whfc.quality.dao.*;
import com.whfc.quality.entity.QualityCheckStatisticsDay;
import com.whfc.quality.entity.QualityTaskItemExec;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 检查任务定时任务
 * <AUTHOR>
 * @Date 2021-08-30 16:25
 * @Version 1.0
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class QualityTaskJob {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QualityTaskItemExecMapper qualityTaskItemExecMapper;

    @Autowired
    private QualityTaskItemMapper qualityTaskItemMapper;

    @Autowired
    private QualityTaskMapper qualityTaskMapper;

    @Autowired
    private QualityCheckMapper qualityCheckMapper;

    @Autowired
    private QualityCheckStatisticsDayMapper qualityCheckStatisticsDayMapper;

    @XxlJob("qualityCheckUpdateOverdue")
    public void safetyCheckUpdateOverdue() {
        try {
            List<Integer> checkIds = qualityCheckMapper.selectCheckIdAll();
            XxlJobHelper.log("开始执行更新质量问题超期任务,超期任务ID{}", checkIds);
            if (checkIds != null && !checkIds.isEmpty()) {
                qualityCheckMapper.updateOverdue(checkIds);
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail("执行更新质量问题超期任务失败{}" + e.getMessage());
        }
    }

    @XxlJob("qualityJudgeOverdue")
    public void executeJudgeOverdue() {
        try {
            XxlJobHelper.log("执行昨日已过期的检查任务");
            judgeOverdue();
        } catch (Exception e) {
            XxlJobHelper.handleFail("执行昨日已过期的检查任务失败" + e.getMessage());
        }
    }

    @XxlJob("qualityJudgeState")
    public void executeJudgeState() {
        try {
            XxlJobHelper.log("执行今日开始的任务");
            judgeState();
        } catch (Exception e) {
            XxlJobHelper.handleFail("执行今日开始的任务失败" + e.getMessage());
        }
    }

    @XxlJob("qualityCheckStatisticsDay")
    protected void qualityCheckStatisticsDay() {
        try {
            XxlJobHelper.log("执行质量每天统计的任务");
            //获取时间 统计昨天的质量检查
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();

            List<QualityCheckStatisticsDay> statisticsDayList = new ArrayList<>();

            //取出需要定时统计的组织机构
            List<Integer> list = qualityCheckMapper.selectQualityDeptIds();
            for (Integer deptId : list) {
                QualityCheckStatisticsDay statisticsDay = new QualityCheckStatisticsDay();
                statisticsDay.setDeptId(deptId);
                statisticsDay.setDate(yesterday);
                // 昨天新增
                Integer newIssueNum = qualityCheckMapper.countCheckNewIssue(deptId, DateUtil.getDateBegin(yesterday), DateUtil.getDateEnd(yesterday));
                statisticsDay.setNewIssueNum(newIssueNum);
                // 昨天待整改
                Integer todoIssueNum = qualityCheckMapper.countCheckToBeRectified(deptId);
                statisticsDay.setTodoIssueNum(todoIssueNum);
                // 昨天完成
                Integer completeIssueNum = qualityCheckMapper.countCheckComplete(deptId, yesterday);
                statisticsDay.setCompleteIssueNum(completeIssueNum);
                statisticsDayList.add(statisticsDay);
            }
            //保存记录
            if(!statisticsDayList.isEmpty()){
                qualityCheckStatisticsDayMapper.batchInsert(statisticsDayList);
            }
        } catch (Exception e) {
            XxlJobHelper.handleFail("执行质量每天统计的任务失败.error:" + e);
        }
    }

    protected void judgeOverdue() {
        List<QualityTaskItemExec> execs = qualityTaskItemExecMapper.selectYesterday();
        if (execs == null || execs.isEmpty()) {
            XxlJobHelper.handleSuccess("没有需要执行的过去任务");
            return;
        }
        XxlJobHelper.log("昨日已过期的检查任务数量：" + execs.size());
        List<Integer> execIds = execs.stream().map(QualityTaskItemExec::getId).distinct().collect(Collectors.toList());
        List<Integer> taskItemIds = execs.stream().map(QualityTaskItemExec::getTaskItemId).distinct().collect(Collectors.toList());
        qualityTaskItemExecMapper.updateOverdue(execIds);
        qualityTaskItemMapper.updateOverdue(taskItemIds);
        XxlJobHelper.handleSuccess();
    }

    protected void judgeState() {
        List<QualityTaskItemExec> execs = qualityTaskItemExecMapper.selectToday();
        if (execs == null || execs.isEmpty()) {
            XxlJobHelper.handleSuccess("今天没有需要开始的任务");
            return;
        }
        XxlJobHelper.log("执行今日开始的任务数量：" + execs.size());
        List<Integer> execIds = execs.stream().map(QualityTaskItemExec::getId).distinct().collect(Collectors.toList());
        List<Integer> taskIds = execs.stream().map(QualityTaskItemExec::getTaskId).distinct().collect(Collectors.toList());
        List<Integer> taskItemIds = execs.stream().map(QualityTaskItemExec::getTaskItemId).distinct().collect(Collectors.toList());
        qualityTaskItemExecMapper.updateStateByExecIds(execIds);
        qualityTaskMapper.updateStateByTaskIds(taskIds);
        qualityTaskItemMapper.updateStateByTaskItem(taskItemIds);
    }


}

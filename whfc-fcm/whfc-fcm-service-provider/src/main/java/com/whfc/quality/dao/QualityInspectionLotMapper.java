package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityInsLotDTO;
import com.whfc.quality.entity.QualityInspectionLot;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface QualityInspectionLotMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityInspectionLot record);

    int insertSelective(QualityInspectionLot record);

    QualityInspectionLot selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityInspectionLot record);

    int updateByPrimaryKey(QualityInspectionLot record);

    /**
     * 查询检验批
     *
     * @param deptId
     * @param lotType
     * @param startTime
     * @param endTime
     * @return
     */
    List<QualityInsLotDTO> selectInsLotList(@Param("deptId") Integer deptId,
                                            @Param("lotType") Integer lotType,
                                            @Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    QualityInsLotDTO selectInsLot(@Param("lotId") Integer lotId);

    /**
     * 逻辑删除
     *
     * @param locId
     * @return
     */
    int logicDeleteById(@Param("locId") Integer locId);
}
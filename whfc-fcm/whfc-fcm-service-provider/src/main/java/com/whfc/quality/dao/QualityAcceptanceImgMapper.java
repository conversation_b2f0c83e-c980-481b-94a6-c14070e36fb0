package com.whfc.quality.dao;

import com.whfc.quality.entity.QualityAcceptanceImg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-29 17:20
 * @description: //todo
 */
public interface QualityAcceptanceImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityAcceptanceImg record);

    int insertSelective(QualityAcceptanceImg record);

    QualityAcceptanceImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityAcceptanceImg record);

    int updateByPrimaryKey(QualityAcceptanceImg record);

    /**
     * 批量添加
     *
     * @param acceptanceId
     * @param deptId
     * @param imgUrls
     */
    void insertAll(@Param("acceptanceId") Integer acceptanceId, @Param("deptId") Integer deptId, @Param("imgUrls") List<String> imgUrls);

    /**
     * 软删除
     *
     * @param acceptanceId
     */
    void deleteByAcceptanceId(Integer acceptanceId);

    /**
     * 查询图片信息
     *
     * @param acceptanceId
     * @return
     */
    List<String> selectByAcceptanceId(Integer acceptanceId);
}
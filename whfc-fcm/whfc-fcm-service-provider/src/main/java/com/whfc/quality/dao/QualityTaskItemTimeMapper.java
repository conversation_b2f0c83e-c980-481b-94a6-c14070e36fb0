package com.whfc.quality.dao;

import com.whfc.quality.entity.QualityTaskItemTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QualityTaskItemTimeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTaskItemTime record);

    int insertSelective(QualityTaskItemTime record);

    QualityTaskItemTime selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTaskItemTime record);

    int updateByPrimaryKey(QualityTaskItemTime record);

    /**
     * 批量新增
     *
     * @param taskId
     * @param taskItemId
     * @param timeList
     */
    void insertAll(@Param("taskId") Integer taskId, @Param("taskItemId") Integer taskItemId, @Param("timeList") List<QualityTaskItemTime> timeList);

    /**
     * 查询
     *
     * @param taskItemId
     * @return
     */
    List<QualityTaskItemTime> selectByTaskItemId(@Param("taskItemId") Integer taskItemId);

    /**
     * 软删除
     *
     * @param taskId
     */
    void del(Integer taskId);
}
package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityCheckUserDTO;
import com.whfc.quality.entity.QualityCheckUser;
import com.whfc.quality.param.QualityCheckUserParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-29 15:24
 */

public interface QualityCheckUserMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityCheckUser record);

    int insertSelective(QualityCheckUser record);

    QualityCheckUser selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityCheckUser record);

    int updateByPrimaryKey(QualityCheckUser record);

    /**
     * 查询质量问题id，用户id，人员类型查询质量问题干系人
     *
     * @param checkId 质量问题上报主表id
     * @param userId  用户主键
     * @param type    人员类型
     * @return
     * <AUTHOR>
     * @date 2020/7/31 9:19
     **/
    QualityCheckUser selectByCheckIdAndUserIdAndType(@Param("checkId") Integer checkId,
                                                     @Param("userId") Integer userId,
                                                     @Param("type") Integer type);


    /**
     * 根据问题ID 和 人员类型 查询 干系人员
     *
     * @param checkId 质量问题上报主表id
     * @param type    人员类型
     * @return 干系人员
     **/
    List<QualityCheckUserDTO> selectByCheckIdAndType(@Param("checkId") Integer checkId, @Param("type") Integer type);


    /**
     * 使用质量问题上报主表id查询人员信息
     *
     * @param checkId 质量问题上报主表id
     * @return
     * <AUTHOR>
     * @date 2020/8/1 10:18
     **/
    List<QualityCheckUserDTO> selectByCheckId(@Param("checkId") Integer checkId);

    /**
     * 根据问题ID列表查询干系人员
     *
     * @param checkIdList 问题ID列表
     * @return 干系人员
     **/
    List<QualityCheckUserDTO> selectByCheckIdList(@Param("checkIdList") List<Integer> checkIdList);

    /**
     * 查找用户名
     *
     * @param checkId 检查记录ID
     * @param type    类型
     * @return 用户名
     */
    String selectUserNamesByType(@Param("checkId") Integer checkId, @Param("type") Integer type);

    /**
     * 批量插入
     *
     * @param checkId
     * @param checkUsers
     * @return
     */
    int batchInsert(@Param("checkId") Integer checkId, @Param("checkUsers") List<QualityCheckUserParam> checkUsers);

    /**
     * 查询需要我处理的问题
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectAuditCheckIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询需要我整改的问题
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectRectifyCheckIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询需要我处理的问题(不合格)
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectUnqualifiedCheckIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询需要我复查的问题
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectReviewCheckIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询需要我核验的问题
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectVerifyCheckIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);
}
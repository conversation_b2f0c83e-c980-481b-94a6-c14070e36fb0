package com.whfc.quality.dao;

import com.whfc.quality.entity.QualityCheckStatisticsDay;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/18 11:25
 */
@Repository
public interface QualityCheckStatisticsDayMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityCheckStatisticsDay record);

    int insertSelective(QualityCheckStatisticsDay record);

    QualityCheckStatisticsDay selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityCheckStatisticsDay record);

    int updateByPrimaryKey(QualityCheckStatisticsDay record);

    /**
     * 根据组织机构
     *
     * @param deptId
     * @param date
     * @return
     */
    QualityCheckStatisticsDay selectByDeptIdAndDate(@Param("deptId") Integer deptId, @Param("date") Date date);

    QualityCheckStatisticsDay selectByDeptIdAndTime(@Param("deptId") Integer deptId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 批量保存统计数据
     *
     * @param statisticsDayList 检查情况天统计
     */
    void batchInsert(@Param("statisticsDayList") List<QualityCheckStatisticsDay> statisticsDayList);


}
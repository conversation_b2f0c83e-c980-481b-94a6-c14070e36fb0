package com.whfc.quality.dao;

import com.whfc.quality.dto.QualityTaskDetailsDTO;
import com.whfc.quality.entity.QualityTaskItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QualityTaskItemMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(QualityTaskItem record);

    int insertSelective(QualityTaskItem record);

    QualityTaskItem selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(QualityTaskItem record);

    int updateByPrimaryKey(QualityTaskItem record);

    /**
     * 查询检查任务-时间
     *
     * @param taskId
     * @return
     */
    List<QualityTaskItem> selectByTaskId(Integer taskId);

    /**
     * 软删除
     *
     * @param task_id
     */
    void del(Integer task_id);

    /**
     * 检查任务详情
     *
     * @param taskId
     * @return
     */
    List<QualityTaskDetailsDTO> selectDetailsByTaskId(Integer taskId);

    /**
     * 检查任务详情
     *
     * @param taskIds
     * @return
     */
    List<QualityTaskItem> selectByTaskIds(@Param("taskIds") List<Integer> taskIds);

    /**
     * 更新任务状态
     *
     * @param taskItemIds
     */
    void updateStateByTaskItem(List<Integer> taskItemIds);

    /**
     * 更新超期状态
     * @param taskItemIds
     */
    void updateOverdue(List<Integer> taskItemIds);
}
package com.whfc.fcm.service.impl;

import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.common.util.RandomUtil;
import com.whfc.fcm.mapper.WorkPermitImgMapper;
import com.whfc.fcm.mapper.WorkPermitMapper;
import com.whfc.fcm.mapper.WorkPermitOpLogMapper;
import com.whfc.fcm.mapper.WorkPermitUserMapper;
import com.whfc.fcm.dto.WorkPermitDTO;
import com.whfc.fcm.dto.WorkPermitDetailDTO;
import com.whfc.fcm.dto.WorkPermitOpLogDTO;
import com.whfc.fcm.dto.WorkPermitUserDTO;
import com.whfc.fcm.entity.WorkPermit;
import com.whfc.fcm.entity.WorkPermitImg;
import com.whfc.fcm.entity.WorkPermitOpLog;
import com.whfc.fcm.entity.WorkPermitUser;
import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import com.whfc.fcm.param.*;
import com.whfc.fcm.service.WorkPermitService;
import com.whfc.fcm.service.WorkPermitStateMachineService;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 作业票服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@DubboService
public class WorkPermitServiceImpl implements WorkPermitService {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitServiceImpl.class);

    @Autowired
    private WorkPermitMapper workPermitMapper;

    @Autowired
    private WorkPermitUserMapper workPermitUserMapper;

    @Autowired
    private WorkPermitOpLogMapper workPermitOpLogMapper;

    @Autowired
    private WorkPermitImgMapper workPermitImgMapper;

    @Autowired
    private WorkPermitStateMachineService stateMachineService;

    @Override
    public PageData<WorkPermitDTO> getWorkPermitList(WorkPermitQuery query, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("分页查询作业票列表, query: {}, pageNum: {}, pageSize: {}", query, pageNum, pageSize);
        
        PageHelper.startPage(pageNum, pageSize);
        List<WorkPermitDTO> list = workPermitMapper.selectWorkPermitList(query);
        PageHelper.clearPage();
        
        // 设置状态描述
        for (WorkPermitDTO dto : list) {
            dto.setStateDesc(WorkPermitState.getDescByValue(dto.getState()));
        }
        
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<WorkPermitDTO> getWorkPermitList(WorkPermitQuery query) throws BizException {
        logger.info("查询作业票列表, query: {}", query);
        
        List<WorkPermitDTO> list = workPermitMapper.selectWorkPermitList(query);
        
        // 设置状态描述
        for (WorkPermitDTO dto : list) {
            dto.setStateDesc(WorkPermitState.getDescByValue(dto.getState()));
        }
        
        return list;
    }

    @Override
    public WorkPermitDetailDTO getWorkPermitDetail(String guid) throws BizException {
        logger.info("获取作业票详情, guid: {}", guid);
        
        if (!StringUtils.hasText(guid)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "GUID不能为空");
        }
        
        WorkPermitDetailDTO detail = workPermitMapper.selectWorkPermitDetail(guid);
        if (detail == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        
        // 设置状态描述
        detail.setStateDesc(WorkPermitState.getDescByValue(detail.getState()));
        
        // 查询人员列表
        List<WorkPermitUserDTO> userList = workPermitUserMapper.selectByWorkPermitId(detail.getId());
        detail.setUserList(userList);
        
        // 查询操作日志
        List<WorkPermitOpLogDTO> opLogList = workPermitOpLogMapper.selectByWorkPermitId(detail.getId());
        detail.setOpLogList(opLogList);
        
        // 查询图片列表
        List<String> imageUrls = workPermitImgMapper.selectUrlsByWorkPermitId(detail.getId());
        detail.setImageUrls(imageUrls);
        
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addWorkPermit(WorkPermitAdd param) throws BizException {
        logger.info("新增作业票, param: {}", param);
        
        // 生成GUID和编号
        String guid = IdUtil.simpleUUID();
        String code = generateWorkPermitCode(param.getDeptId());
        
        // 创建作业票实体
        WorkPermit workPermit = new WorkPermit();
        BeanUtils.copyProperties(param, workPermit);
        workPermit.setGuid(guid);
        workPermit.setCode(code);
        workPermit.setState(WorkPermitState.DRAFT.getValue());
        workPermit.setDelFlag(0);
        workPermit.setCreateTime(new Date());
        workPermit.setUpdateTime(new Date());
        
        // 插入作业票
        workPermitMapper.insertSelective(workPermit);
        
        // 保存人员信息
        saveWorkPermitUsers(workPermit.getId(), param.getUserList());
        
        // 保存图片信息
        saveWorkPermitImages(workPermit.getId(), param.getImageUrls());
        
        // 记录操作日志
        saveOpLog(workPermit.getId(), 1, null, WorkPermitState.DRAFT.getValue(), 
                 "创建作业票", param.getApplicantUserId(), param.getApplicantUserName());
        
        return guid;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editWorkPermit(WorkPermitEdit param) throws BizException {
        logger.info("编辑作业票, param: {}", param);
        
        WorkPermit existingWorkPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (existingWorkPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        
        // 只有草稿状态才能编辑
        if (!WorkPermitState.DRAFT.getValue().equals(existingWorkPermit.getState())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有草稿状态的作业票才能编辑");
        }
        
        // 更新作业票信息
        WorkPermit workPermit = new WorkPermit();
        BeanUtils.copyProperties(param, workPermit);
        workPermit.setId(existingWorkPermit.getId());
        workPermit.setUpdateTime(new Date());
        
        workPermitMapper.updateByPrimaryKeySelective(workPermit);
        
        // 更新人员信息
        workPermitUserMapper.deleteByWorkPermitId(existingWorkPermit.getId());
        saveWorkPermitUsers(existingWorkPermit.getId(), param.getUserList());
        
        // 更新图片信息
        workPermitImgMapper.deleteByWorkPermitId(existingWorkPermit.getId());
        saveWorkPermitImages(existingWorkPermit.getId(), param.getImageUrls());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkPermit(String guid) throws BizException {
        logger.info("删除作业票, guid: {}", guid);
        
        WorkPermit workPermit = workPermitMapper.selectByGuid(guid);
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        
        // 只有草稿状态才能删除
        if (!WorkPermitState.DRAFT.getValue().equals(workPermit.getState())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有草稿状态的作业票才能删除");
        }
        
        // 逻辑删除
        workPermitMapper.logicDeleteById(workPermit.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitWorkPermit(WorkPermitStateTransition param) throws BizException {
        logger.info("提交作业票, param: {}", param);
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.SUBMIT, param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issueWorkPermit(WorkPermitStateTransition param) throws BizException {
        logger.info("签发作业票, param: {}", param);

        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }

        // 使用状态机进行状态转换
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.ISSUE, param);

        // 更新签发信息
        workPermitMapper.updateIssueInfo(workPermit.getId(), param.getOpUserId(),
                                       param.getOpUserName(), new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectWorkPermit(WorkPermitStateTransition param) throws BizException {
        logger.info("打回作业票, param: {}", param);
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.REJECT, param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resubmitWorkPermit(WorkPermitStateTransition param) throws BizException {
        logger.info("重新提交作业票, param: {}", param);
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.RESUBMIT, param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startWork(WorkPermitStateTransition param) throws BizException {
        logger.info("开始作业, param: {}", param);
        
        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        
        if (!WorkPermitState.IN_PROGRESS.getValue().equals(workPermit.getState())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有作业中状态才能记录开始作业");
        }
        
        // 记录操作日志（不改变状态）
        saveOpLog(workPermit.getId(), 5, WorkPermitState.IN_PROGRESS.getValue(), 
                 WorkPermitState.IN_PROGRESS.getValue(), param.getRemark(), 
                 param.getOpUserId(), param.getOpUserName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endWork(WorkPermitStateTransition param) throws BizException {
        logger.info("结束作业, param: {}", param);
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.END_WORK, param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeWorkPermit(WorkPermitStateTransition param) throws BizException {
        logger.info("关闭作业票, param: {}", param);
        transitionStateWithStateMachine(param.getGuid(), WorkPermitEvent.CLOSE, param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transitionState(WorkPermitStateTransition param) throws BizException {
        logger.info("状态转换, param: {}", param);

        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }

        WorkPermitState fromState = WorkPermitState.parseByValue(workPermit.getState());
        WorkPermitState toState = WorkPermitState.parseByValue(param.getToState());

        // 获取对应的事件
        WorkPermitEvent event = stateMachineService.getEventByTransition(fromState, toState);

        // 使用状态机进行状态转换
        transitionStateWithStateMachine(param.getGuid(), event, param);
    }

    /**
     * 使用状态机进行状态转换
     *
     * @param workPermitGuid 作业票GUID
     * @param event          状态机事件
     * @param param          转换参数
     * @throws BizException 业务异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void transitionStateWithStateMachine(String workPermitGuid, WorkPermitEvent event,
                                               WorkPermitStateTransition param) throws BizException {
        logger.info("使用状态机进行状态转换: workPermitGuid={}, event={}", workPermitGuid, event);

        WorkPermit workPermit = workPermitMapper.selectByGuid(workPermitGuid);
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }

        WorkPermitState currentState = WorkPermitState.parseByValue(workPermit.getState());

        // 使用状态机发送事件
        boolean success = stateMachineService.sendEvent(workPermitGuid, currentState, event, param);

        if (success) {
            // 获取状态机转换后的新状态
            WorkPermitState newState = stateMachineService.getCurrentState(workPermitGuid);

            // 更新数据库状态
            workPermitMapper.updateState(workPermit.getId(), newState.getValue());

            // 记录操作日志
            Integer opType = getOpTypeByEvent(event);
            saveOpLog(workPermit.getId(), opType, currentState.getValue(), newState.getValue(),
                     param.getRemark(), param.getOpUserId(), param.getOpUserName());

            logger.info("状态转换成功: workPermitGuid={}, {} -> {}",
                workPermitGuid, currentState.getDesc(), newState.getDesc());
        } else {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(),
                String.format("状态转换失败: 当前状态[%s]不支持事件[%s]",
                    currentState.getDesc(), event.name()));
        }
    }

    @Override
    public boolean checkStateTransition(String guid, Integer toState) throws BizException {
        WorkPermit workPermit = workPermitMapper.selectByGuid(guid);
        if (workPermit == null) {
            return false;
        }

        WorkPermitState fromState = WorkPermitState.parseByValue(workPermit.getState());
        WorkPermitState toStateEnum = WorkPermitState.parseByValue(toState);

        try {
            // 获取对应的事件
            WorkPermitEvent event = stateMachineService.getEventByTransition(fromState, toStateEnum);

            // 使用状态机检查转换是否有效
            return stateMachineService.isValidTransition(fromState, event);
        } catch (IllegalArgumentException e) {
            // 不支持的状态转换
            return false;
        }
    }

    /**
     * 生成作业票编号
     */
    private String generateWorkPermitCode(Integer deptId) {
        String prefix = "WP" + String.format("%04d", deptId);
        return workPermitMapper.generateCode(deptId, prefix);
    }

    /**
     * 保存作业票人员
     */
    private void saveWorkPermitUsers(Integer workPermitId, List<WorkPermitUserAdd> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        List<WorkPermitUser> users = new ArrayList<>();
        for (WorkPermitUserAdd userAdd : userList) {
            WorkPermitUser user = new WorkPermitUser();
            BeanUtils.copyProperties(userAdd, user);
            user.setPermitId(workPermitId);
            user.setCreateTime(new Date());
            users.add(user);
        }

        workPermitUserMapper.batchInsert(users);
    }

    /**
     * 保存作业票图片
     */
    private void saveWorkPermitImages(Integer workPermitId, List<String> imageUrls) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return;
        }

        List<WorkPermitImg> images = new ArrayList<>();
        for (String imageUrl : imageUrls) {
            WorkPermitImg img = new WorkPermitImg();
            img.setPermitId(workPermitId);
            img.setImgUrl(imageUrl);
            img.setCreateTime(new Date());
            images.add(img);
        }

        workPermitImgMapper.batchInsert(images);
    }

    /**
     * 保存操作日志
     */
    private void saveOpLog(Integer workPermitId, Integer opType, Integer fromState,
                          Integer toState, String remark, Integer opUserId, String opUserName) {
        WorkPermitOpLog opLog = new WorkPermitOpLog();
        opLog.setObjId(workPermitId);
        opLog.setObjType(opType);
        opLog.setOpState(toState);
        opLog.setRemark(remark);
        opLog.setOpUserId(opUserId);
        opLog.setOpUserName(opUserName);
        opLog.setOpTime(new Date());
        opLog.setCreateTime(new Date());

        workPermitOpLogMapper.insertSelective(opLog);
    }

    /**
     * 根据状态转换获取操作类型（已废弃）
     * @deprecated 使用 getOpTypeByEvent 替代
     */
    @Deprecated
    private Integer getOpTypeByTransition(Integer fromState, Integer toState) {
        if (fromState == null && WorkPermitState.DRAFT.getValue().equals(toState)) {
            return 1; // 创建
        }
        if (WorkPermitState.DRAFT.getValue().equals(fromState) &&
            WorkPermitState.PENDING_ISSUE.getValue().equals(toState)) {
            return 2; // 提交
        }
        if (WorkPermitState.PENDING_ISSUE.getValue().equals(fromState) &&
            WorkPermitState.IN_PROGRESS.getValue().equals(toState)) {
            return 3; // 签发
        }
        if (WorkPermitState.PENDING_ISSUE.getValue().equals(fromState) &&
            WorkPermitState.REJECTED.getValue().equals(toState)) {
            return 4; // 打回
        }
        if (WorkPermitState.IN_PROGRESS.getValue().equals(fromState) &&
            WorkPermitState.CLOSING.getValue().equals(toState)) {
            return 6; // 结束作业
        }
        if (WorkPermitState.CLOSING.getValue().equals(fromState) &&
            WorkPermitState.CLOSED.getValue().equals(toState)) {
            return 7; // 关闭
        }
        return 0; // 其他
    }

    /**
     * 根据状态机事件获取操作类型
     */
    private Integer getOpTypeByEvent(WorkPermitEvent event) {
        switch (event) {
            case SUBMIT:
                return 2; // 提交
            case ISSUE:
                return 3; // 签发
            case REJECT:
                return 4; // 打回
            case RESUBMIT:
                return 2; // 重新提交（同提交）
            case END_WORK:
                return 6; // 结束作业
            case CLOSE:
                return 7; // 关闭
            default:
                return 0; // 其他
        }
    }
}

package com.whfc.fcm.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ResultEnum;
import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Service;

/**
 * 作业票状态机简单服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Service
public class WorkPermitStateMachineSimpleService {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitStateMachineSimpleService.class);

    @Autowired
    @Qualifier("workPermitStateMachine")
    private StateMachine<WorkPermitState, WorkPermitEvent> stateMachine;

    /**
     * 发送事件到状态机
     *
     * @param workPermitGuid 作业票GUID
     * @param currentState   当前状态
     * @param event          事件
     * @param context        上下文数据
     * @return 是否成功
     * @throws BizException 业务异常
     */
    public boolean sendEvent(String workPermitGuid, WorkPermitState currentState, WorkPermitEvent event, Object context) throws BizException {
        logger.info("发送状态机事件: workPermitGuid={}, currentState={}, event={}", workPermitGuid, currentState, event);
        
        try {
            // 检查当前状态是否匹配
            if (!currentState.equals(stateMachine.getState().getId())) {
                logger.warn("状态机状态不一致，重置状态机: 数据库状态={}, 状态机状态={}", 
                    currentState, stateMachine.getState().getId());
                // 对于简单实现，我们直接检查转换是否有效
            }
            
            // 检查转换是否有效
            if (!isValidTransition(currentState, event)) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), 
                    String.format("状态转换失败，当前状态[%s]不支持事件[%s]", currentState.getDesc(), event.name()));
            }
            
            // 构建消息
            Message<WorkPermitEvent> message = MessageBuilder
                .withPayload(event)
                .setHeader("workPermitGuid", workPermitGuid)
                .setHeader("context", context)
                .build();
            
            // 发送事件
            boolean result = stateMachine.sendEvent(message);
            
            if (result) {
                logger.info("状态机事件处理成功: workPermitGuid={}, newState={}", 
                    workPermitGuid, stateMachine.getState().getId());
            } else {
                logger.error("状态机事件处理失败: workPermitGuid={}, event={}", workPermitGuid, event);
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), 
                    String.format("状态转换失败，当前状态[%s]不支持事件[%s]", currentState.getDesc(), event.name()));
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("状态机事件处理异常: workPermitGuid={}, event={}", workPermitGuid, event, e);
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException(ResultEnum.SYSTEM_ERROR.getCode(), "状态机处理异常: " + e.getMessage());
        }
    }

    /**
     * 获取状态机当前状态
     *
     * @param workPermitGuid 作业票GUID
     * @return 当前状态
     */
    public WorkPermitState getCurrentState(String workPermitGuid) {
        try {
            return stateMachine.getState().getId();
        } catch (Exception e) {
            logger.error("获取状态机状态失败: workPermitGuid={}", workPermitGuid, e);
            return WorkPermitState.DRAFT; // 默认返回草稿状态
        }
    }

    /**
     * 检查状态转换是否有效
     *
     * @param currentState 当前状态
     * @param event        事件
     * @return 是否有效
     */
    public boolean isValidTransition(WorkPermitState currentState, WorkPermitEvent event) {
        try {
            // 检查是否可以发送事件
            return stateMachine.getTransitions().stream()
                .anyMatch(transition -> 
                    transition.getSource().getId().equals(currentState) && 
                    transition.getTrigger() != null &&
                    transition.getTrigger().getEvent().equals(event));
                    
        } catch (Exception e) {
            logger.error("检查状态转换失败: currentState={}, event={}", currentState, event, e);
            return false;
        }
    }

    /**
     * 根据状态转换获取对应的事件
     *
     * @param fromState 源状态
     * @param toState   目标状态
     * @return 对应的事件
     */
    public WorkPermitEvent getEventByTransition(WorkPermitState fromState, WorkPermitState toState) {
        if (fromState == WorkPermitState.DRAFT && toState == WorkPermitState.PENDING_ISSUE) {
            return WorkPermitEvent.SUBMIT;
        }
        if (fromState == WorkPermitState.PENDING_ISSUE && toState == WorkPermitState.IN_PROGRESS) {
            return WorkPermitEvent.ISSUE;
        }
        if (fromState == WorkPermitState.PENDING_ISSUE && toState == WorkPermitState.REJECTED) {
            return WorkPermitEvent.REJECT;
        }
        if (fromState == WorkPermitState.REJECTED && toState == WorkPermitState.PENDING_ISSUE) {
            return WorkPermitEvent.RESUBMIT;
        }
        if (fromState == WorkPermitState.IN_PROGRESS && toState == WorkPermitState.CLOSING) {
            return WorkPermitEvent.END_WORK;
        }
        if (fromState == WorkPermitState.CLOSING && toState == WorkPermitState.CLOSED) {
            return WorkPermitEvent.CLOSE;
        }
        
        throw new IllegalArgumentException(
            String.format("不支持的状态转换: %s -> %s", fromState.getDesc(), toState.getDesc()));
    }

    /**
     * 获取目标状态（根据当前状态和事件）
     *
     * @param currentState 当前状态
     * @param event        事件
     * @return 目标状态
     */
    public WorkPermitState getTargetState(WorkPermitState currentState, WorkPermitEvent event) {
        switch (currentState) {
            case DRAFT:
                if (event == WorkPermitEvent.SUBMIT) return WorkPermitState.PENDING_ISSUE;
                break;
            case PENDING_ISSUE:
                if (event == WorkPermitEvent.ISSUE) return WorkPermitState.IN_PROGRESS;
                if (event == WorkPermitEvent.REJECT) return WorkPermitState.REJECTED;
                break;
            case REJECTED:
                if (event == WorkPermitEvent.RESUBMIT) return WorkPermitState.PENDING_ISSUE;
                break;
            case IN_PROGRESS:
                if (event == WorkPermitEvent.END_WORK) return WorkPermitState.CLOSING;
                break;
            case CLOSING:
                if (event == WorkPermitEvent.CLOSE) return WorkPermitState.CLOSED;
                break;
            case CLOSED:
                // 已结束状态不能转换
                break;
        }
        
        throw new IllegalArgumentException(
            String.format("不支持的状态转换: %s + %s", currentState.getDesc(), event.name()));
    }
}

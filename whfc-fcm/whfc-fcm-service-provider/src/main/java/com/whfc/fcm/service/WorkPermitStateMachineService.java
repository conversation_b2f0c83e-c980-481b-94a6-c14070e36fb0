package com.whfc.fcm.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ResultEnum;
import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 作业票状态机服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Service
public class WorkPermitStateMachineService {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitStateMachineService.class);

    @Autowired
    @Qualifier("workPermitStateMachineFactory")
    private StateMachineFactory<WorkPermitState, WorkPermitEvent> stateMachineFactory;

    @Autowired
    @Qualifier("workPermitStateMachinePersister")
    private StateMachinePersister<WorkPermitState, WorkPermitEvent, String> workPermitStateMachinePersister;

    /**
     * 发送事件到状态机
     *
     * @param workPermitGuid 作业票GUID
     * @param currentState   当前状态
     * @param event          事件
     * @param context        上下文数据
     * @return 是否成功
     * @throws BizException 业务异常
     */
    public boolean sendEvent(String workPermitGuid, WorkPermitState currentState, WorkPermitEvent event, Object context) throws BizException {
        logger.info("发送状态机事件: workPermitGuid={}, currentState={}, event={}", workPermitGuid, currentState, event);

        StateMachine<WorkPermitState, WorkPermitEvent> stateMachine = null;
        try {
            // 获取状态机实例
            stateMachine = stateMachineFactory.getStateMachine(workPermitGuid);

            // 恢复状态机状态
            workPermitStateMachinePersister.restore(stateMachine, workPermitGuid);

            // 如果状态机当前状态与数据库状态不一致，需要重置状态机
            if (!currentState.equals(stateMachine.getState().getId())) {
                logger.warn("状态机状态不一致，重置状态机: 数据库状态={}, 状态机状态={}",
                    currentState, stateMachine.getState().getId());
                resetStateMachine(stateMachine, workPermitGuid, currentState);
            }

            // 构建消息
            Message<WorkPermitEvent> message = MessageBuilder
                .withPayload(event)
                .setHeader("workPermitGuid", workPermitGuid)
                .setHeader("context", context)
                .build();

            // 发送事件
            boolean result = stateMachine.sendEvent(message);

            if (result) {
                // 保存状态机状态
                workPermitStateMachinePersister.persist(stateMachine, workPermitGuid);
                logger.info("状态机事件处理成功: workPermitGuid={}, newState={}",
                    workPermitGuid, stateMachine.getState().getId());
            } else {
                logger.error("状态机事件处理失败: workPermitGuid={}, event={}", workPermitGuid, event);
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(),
                    String.format("状态转换失败，当前状态[%s]不支持事件[%s]", currentState.getDesc(), event.name()));
            }

            return result;

        } catch (Exception e) {
            logger.error("状态机事件处理异常: workPermitGuid={}, event={}", workPermitGuid, event, e);
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException(ResultEnum.SYSTEM_ERROR.getCode(), "状态机处理异常: " + e.getMessage());
        } finally {
            // 停止状态机实例以释放资源
            if (stateMachine != null) {
                stateMachine.stop();
            }
        }
    }

    /**
     * 获取状态机当前状态
     *
     * @param workPermitGuid 作业票GUID
     * @return 当前状态
     */
    public WorkPermitState getCurrentState(String workPermitGuid) {
        StateMachine<WorkPermitState, WorkPermitEvent> stateMachine = null;
        try {
            stateMachine = stateMachineFactory.getStateMachine(workPermitGuid);
            workPermitStateMachinePersister.restore(stateMachine, workPermitGuid);
            return stateMachine.getState().getId();
        } catch (Exception e) {
            logger.error("获取状态机状态失败: workPermitGuid={}", workPermitGuid, e);
            return WorkPermitState.DRAFT; // 默认返回草稿状态
        } finally {
            if (stateMachine != null) {
                stateMachine.stop();
            }
        }
    }

    /**
     * 检查状态转换是否有效
     *
     * @param currentState 当前状态
     * @param event        事件
     * @return 是否有效
     */
    public boolean isValidTransition(WorkPermitState currentState, WorkPermitEvent event) {
        StateMachine<WorkPermitState, WorkPermitEvent> tempStateMachine = null;
        try {
            // 创建临时状态机实例进行验证
            tempStateMachine = stateMachineFactory.getStateMachine("temp_" + System.currentTimeMillis());

            // 检查是否可以发送事件
            return tempStateMachine.getTransitions().stream()
                .anyMatch(transition ->
                    transition.getSource().getId().equals(currentState) &&
                    transition.getTrigger() != null &&
                    transition.getTrigger().getEvent().equals(event));

        } catch (Exception e) {
            logger.error("检查状态转换失败: currentState={}, event={}", currentState, event, e);
            return false;
        } finally {
            if (tempStateMachine != null) {
                tempStateMachine.stop();
            }
        }
    }

    /**
     * 重置状态机到指定状态
     *
     * @param stateMachine   状态机实例
     * @param workPermitGuid 作业票GUID
     * @param targetState    目标状态
     */
    private void resetStateMachine(StateMachine<WorkPermitState, WorkPermitEvent> stateMachine,
                                  String workPermitGuid, WorkPermitState targetState) {
        try {
            // 停止状态机
            stateMachine.stop();

            // 重新启动状态机
            stateMachine.start();

            // 如果目标状态不是初始状态，需要手动设置
            if (!WorkPermitState.DRAFT.equals(targetState)) {
                // 这里可能需要根据实际情况调整状态设置逻辑
                logger.warn("需要手动设置状态机状态到: {}", targetState);
            }

        } catch (Exception e) {
            logger.error("重置状态机失败: workPermitGuid={}, targetState={}", workPermitGuid, targetState, e);
        }
    }

    /**
     * 根据状态转换获取对应的事件
     *
     * @param fromState 源状态
     * @param toState   目标状态
     * @return 对应的事件
     */
    public WorkPermitEvent getEventByTransition(WorkPermitState fromState, WorkPermitState toState) {
        if (fromState == WorkPermitState.DRAFT && toState == WorkPermitState.PENDING_ISSUE) {
            return WorkPermitEvent.SUBMIT;
        }
        if (fromState == WorkPermitState.PENDING_ISSUE && toState == WorkPermitState.IN_PROGRESS) {
            return WorkPermitEvent.ISSUE;
        }
        if (fromState == WorkPermitState.PENDING_ISSUE && toState == WorkPermitState.REJECTED) {
            return WorkPermitEvent.REJECT;
        }
        if (fromState == WorkPermitState.REJECTED && toState == WorkPermitState.PENDING_ISSUE) {
            return WorkPermitEvent.RESUBMIT;
        }
        if (fromState == WorkPermitState.IN_PROGRESS && toState == WorkPermitState.CLOSING) {
            return WorkPermitEvent.END_WORK;
        }
        if (fromState == WorkPermitState.CLOSING && toState == WorkPermitState.CLOSED) {
            return WorkPermitEvent.CLOSE;
        }
        
        throw new IllegalArgumentException(
            String.format("不支持的状态转换: %s -> %s", fromState.getDesc(), toState.getDesc()));
    }
}

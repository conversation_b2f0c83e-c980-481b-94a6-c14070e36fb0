package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyClueDTO;
import com.whfc.safety.entity.SafetyClue;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SafetyClueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyClue record);

    int insertSelective(SafetyClue record);

    SafetyClue selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyClue record);

    /**
     * 查询线索列表
     *
     * @param deptId
     * @param keyword
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyClueDTO> selectClueList(@Param("deptId") Integer deptId,
                                       @Param("keyword") String keyword,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    int logicDeleteById(@Param("id") Integer id);
}
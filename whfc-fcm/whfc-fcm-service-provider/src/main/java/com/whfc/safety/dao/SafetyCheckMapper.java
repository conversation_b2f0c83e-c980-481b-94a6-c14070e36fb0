package com.whfc.safety.dao;

import com.whfc.common.exception.BizException;
import com.whfc.safety.dto.*;
import com.whfc.safety.entity.SafetyCheck;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-04 11:05
 */

public interface SafetyCheckMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(SafetyCheck record);

    SafetyCheck selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyCheck record);

    /**
     * 查询问题上报列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param states
     * @return
     */
    List<SafetyCheckDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                        @Param("startTime") Date startTime,
                                        @Param("endTime") Date endTime,
                                        @Param("states") List<Integer> states);

    /**
     * 查询问题上报列表
     */
    List<SafetyCheckDTO> selectCheckList(@Param("deptIdList") List<Integer> deptIdList,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime,
                                         @Param("state") Integer[] states,
                                         @Param("partId") Integer partId,
                                         @Param("urgency") Integer urgency,
                                         @Param("overdue") Integer overdue,
                                         @Param("idList") List<Integer> idList);

    /**
     * 查询我提交的
     *
     * @param deptIdList
     * @param userId
     * @return
     */
    List<Integer> selectMyCommitIds(@Param("deptIdList") List<Integer> deptIdList, @Param("userId") Integer userId);

    /**
     * 查询指定用户提交
     *
     * @param deptIdList
     * @param checkUserName
     * @return
     */
    List<Integer> selectCommitIdsByCheckUserName(@Param("deptIdList") List<Integer> deptIdList, @Param("checkUserName") String checkUserName);

    /**
     * 使用主键查询问题表详情
     *
     * @param id 问题表主键id
     * @return SafetyCheckDescDTO
     * <AUTHOR>
     * @date 2020/8/5 11:17
     **/
    SafetyCheckDescDTO selectById(@Param("id") Integer id);

    /**
     * 查询项目问题上报
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyCheckDTO> selectByDeptIdAndTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询项目问题上报数量
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    int countByDeptIdAndTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按天统计
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyCheckStatDTO> selectByDate(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 按月统计
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyCheckStatDTO> selectByMonth(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 使用提交时间和状态查询问题数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     * @date 2020/8/3 18:54
     **/
    List<SafetyCheckStatDTO> selectByStateAndCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 使用提交时间和紧急程度查询问题数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     * @date 2020/8/3 19:05
     **/
    List<SafetyCheckStatDTO> selectByUrgencyAndCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题上报类别数量
     *
     * @param deptId    组织机构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return List<SafetyCheckIssueStatisticalDTO>
     * <AUTHOR>
     * @date 2020/8/3 18:51
     **/
    List<SafetyCheckStatDTO> selectIssueByCheckTime(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 问题统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyCheckScreenDTO selectStatisticalByDeptId(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 未销问题分析
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyCheckUnfinishedDTO selectUnfinishedByDeptId(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题类别分析
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyIssueNumDTO> selectIssueTypeNum(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题上报部位数量
     *
     * @param deptIdList 组织机构ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return
     * <AUTHOR>
     * @date 3 19:26
     **/
    List<SafetyCheckStatDTO> selectPartByCheckTime(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询传入时间区间问题紧急程度数量
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyCheckStatDTO> selectLevelByCheckTime(@Param("deptIdList") List<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 上报问题-按项目统计
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyCheckStatDTO> selectProjectNum(@Param("deptIdList") Collection<Integer> deptIdList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询全部上报信息
     *
     * @return
     * <AUTHOR>
     * @date 2020/8/12 9:21
     **/
    List<Integer> selectCheckIdAll();

    /**
     * 更新超期状态
     *
     * @param checkIds 问题id
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/12 11:28
     **/
    void updateOverdue(@Param("checkIds") List<Integer> checkIds);

    /**
     * 问题日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<SafetyUserNumDTO> selectUserNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 问题日志
     *
     * @param deptId
     * @return
     */
    List<SafetyIssueNumDTO> selectIssueNum(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 软删除
     *
     * @param checkId
     */
    void del(Integer checkId);

    /**
     * 获取安全检查记录
     *
     * @param deptId 组织机构ID
     * @return 安全检查记录
     */
    List<MdSafetyCheckDTO> selectMdBoardCheckList(@Param("deptId") Integer deptId);

    /**
     * 获取新增问题数量
     *
     * @param deptId    组织架构ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 新增问题数量
     */
    Integer countCheckNewIssue(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计待整改数量
     *
     * @param deptId 组织架构ID
     * @return 待整改数量
     */
    Integer countCheckToBeRectified(@Param("deptId") Integer deptId);

    /**
     * 统计检查数量
     *
     * @param deptId 组织架构ID
     * @param state  状态
     * @return 检查数量
     */
    Integer countCheckNum(@Param("deptId") Integer deptId, @Param("state") Integer state);

    /**
     * 统计当天完成
     *
     * @param deptId 组织机构ID
     * @param date   日期
     * @return 当天完成数量
     */
    Integer countCheckComplete(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 获取安全的组织机构ID列表
     *
     * @return 组织机构ID列表
     */
    List<Integer> selectSafetyDeptIds();
}
package com.whfc.safety.dao;

import com.whfc.safety.entity.SafetyFineUser;

import java.util.List;

public interface SafetyFineUserMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyFineUser record);

    int insertSelective(SafetyFineUser record);

    SafetyFineUser selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyFineUser record);

    int updateByPrimaryKey(SafetyFineUser record);

    /**
     * 查询人员干系
     *
     * @param fineId
     * @return
     */
    List<SafetyFineUser> selectByFineId(Integer fineId);
}
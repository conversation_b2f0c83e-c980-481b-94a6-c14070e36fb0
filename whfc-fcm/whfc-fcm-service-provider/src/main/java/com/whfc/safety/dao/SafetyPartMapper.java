package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyPartDTO;
import com.whfc.safety.entity.SafetyPart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR> qzexing
* @version : 1.0
* @date : 2020-08-04 11:05
*/

public interface SafetyPartMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyPart record);

    int insertSelective(SafetyPart record);

    SafetyPart selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyPart record);

    int updateByPrimaryKey(SafetyPart record);

    /**
     * 获取部位列表
     *
     * @param deptId 组织机构ID
     * @return 部位列表
     */
    List<SafetyPartDTO> selectSafetyPartDTOList(@Param("deptId") Integer deptId,@Param("keyword")String keyword);

    /**
     * 根据部位ID 更新部位名称
     *
     * @param partId 部位ID
     * @param code   部位编码
     * @param name   部位名称
     */
    void updateNameById(@Param("partId") Integer partId, @Param("code") String code, @Param("name") String name);

    /**
     * 逻辑删除部位
     *
     * @param partId 部位ID
     */
    void logicDel(@Param("partId") Integer partId);

    /**
     * 根据编码查询部位
     *
     * @param code 编码
     * @return 部位
     */
    SafetyPart selectPartByCode(@Param("code") String code,@Param("deptId")Integer deptId);

    /**
     * 获取所有的编码
     *
     * @param deptId 组织机构ID
     * @return 编码
     */
    List<String> selectCodeList(Integer deptId);

    /**
     * 使用父id查询子
     *
     * @param pid 父级ID
     * @return
     * <AUTHOR>
     * @date 2020/8/6 19:03
     **/
    List<SafetyPart> selectByPid(@Param("pid") Integer pid);


    /**
     * 使用编码查询全部子节点
     *
     * @param code 编码
     * @return
     * <AUTHOR>
     * @date 2020/8/6 19:48
     **/
    List<SafetyPart> selectLikeCode(@Param("code") String code);


}
package com.whfc.safety.service.impl;

import com.deepoove.poi.XWPFTemplate;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.base.param.AppFileExportParam;
import com.whfc.base.service.AppExportService;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileUploadUtil;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.common.util.RandomUtil;
import com.whfc.fuum.service.SysDeptService;
import com.whfc.safety.dao.SafetyDictMapper;
import com.whfc.safety.dao.SafetyRectifyDetailsMapper;
import com.whfc.safety.dao.SafetyRectifyInfoMapper;
import com.whfc.safety.dto.SafetyCheckDTO;
import com.whfc.safety.dto.SafetyCheckExportParam;
import com.whfc.safety.dto.SafetyRectifyDTO;
import com.whfc.safety.entity.SafetyDict;
import com.whfc.safety.entity.SafetyRectifyInfo;
import com.whfc.safety.enums.SafetyDictConst;
import com.whfc.safety.param.SafetyRectifyAddParam;
import com.whfc.safety.param.SafetyRectifyEditParam;
import com.whfc.safety.service.SafetyRectifyService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.UrlResource;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: hw
 * @date: 2021-10-18 17:41
 * @description: 质量整改单
 */
@DubboService(interfaceClass = SafetyRectifyService.class, version = "1.0.0")
public class SafetyRectifyServiceImpl implements SafetyRectifyService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SafetyDictMapper safetyDictMapper;

    @Autowired
    private SafetyRectifyInfoMapper safetyRectifyInfoMapper;

    @Autowired
    private SafetyRectifyDetailsMapper safetyRectifyDetailsMapper;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @DubboReference(interfaceClass = AppExportService.class, version = "1.0.0")
    private AppExportService appExportService;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Override
    public PageData<SafetyRectifyDTO> list(Integer deptId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("整改通知单列表,deptId：{},startTime：{},endTime：{},pageNum：{},pageSize：{}", deptId, startTime, endTime, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyRectifyDTO> list = safetyRectifyInfoMapper.selectByDeptId(deptId, startTime, endTime);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<SafetyCheckDTO> details(Integer rectifyId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("整改通知单详情,rectifyId：{},pageNum：{},pageSize：{}", rectifyId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyCheckDTO> list = safetyRectifyDetailsMapper.selectByRectifyId(rectifyId);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void add(SafetyRectifyAddParam param) throws BizException {
        logger.info("添加整改通知单,param:{}", param);
        SafetyRectifyInfo record = new SafetyRectifyInfo();
        record.setQuestionNum(param.getCheckIds().size());
        BeanUtils.copyProperties(param, record);
        safetyRectifyInfoMapper.insertSelective(record);
        safetyRectifyDetailsMapper.insertAll(record.getId(), param.getCheckIds());
    }

    @Override
    public void edit(SafetyRectifyEditParam param) throws BizException {
        SafetyRectifyInfo record = safetyRectifyInfoMapper.selectByPrimaryKey(param.getRectifyId());
        if (record == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "未找到该整改单信息");
        }
        safetyRectifyDetailsMapper.delByRectifyId(record.getId());
        BeanUtils.copyProperties(param, record);
        safetyRectifyInfoMapper.updateByPrimaryKeySelective(record);
        safetyRectifyDetailsMapper.insertAll(record.getId(), param.getCheckIds());
    }

    @Override
    public void del(Integer rectifyId) throws BizException {
        logger.info("删除整改通知单,rectifyId:{}", rectifyId);
        safetyRectifyDetailsMapper.delByRectifyId(rectifyId);
        safetyRectifyInfoMapper.del(rectifyId);
    }

    @Override
    public void export(Integer rectifyId) throws BizException {
        logger.info("导出整改通知单,rectifyId:{}", rectifyId);

        SafetyRectifyInfo rectifyInfo = safetyRectifyInfoMapper.selectByPrimaryKey(rectifyId);
        Integer deptId = rectifyInfo.getDeptId();

        //导出参数
        SafetyDict dict = safetyDictMapper.selectByDeptIdAndCode(deptId, SafetyDictConst.safety_check_export);
        if (dict == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "缺少导出参数配置");
        }
        SafetyCheckExportParam param = JSONUtil.parseObject(dict.getParam(), SafetyCheckExportParam.class);
        if (param == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "缺少导出参数配置");
        }
        String template = param.getTemplateRectify();
        String companyName = param.getCompanyName();

        //导出记录
        String fileDesc = "安全整改通知单导出";
        String fileName = RandomUtil.getRandomFileName();
        String suffix = "docx";
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(rectifyInfo.getDeptId());
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        //异步执行导出
        CompletableFuture.runAsync(() -> {
            try {
                //导出参数
                String deptName = sysDeptService.getDeptName(deptId);

                logger.info("整改通知单导出,生成文件开始.....");

                Map<String, Object> checkMap = this.getExportMap(rectifyId);
                checkMap.put("companyName", companyName);
                checkMap.put("deptName", deptName);

                //模版文件
                InputStream is = new UrlResource(template).getInputStream();

                //目标文件
                File tempFile = File.createTempFile("safety", ".docx");
                String tempFilePath = tempFile.getAbsolutePath();

                //生成文件
                XWPFTemplate.compile(is).render(checkMap).writeToFile(tempFilePath);
                logger.info("整改通知单导出,{}", tempFilePath);

                logger.info("整改通知单导出,生成文件结束.....");

                //上传oss
                String path = "safety/rectify/" + fileName + "." + suffix;
                String fileUrl = fileUploadUtil.upload(path, new FileInputStream(tempFilePath), FileExpirationRules.DAYS_7);

                //导出成功
                logger.info("整改通知单导出,文件路径:{}", fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);
            } catch (Exception ex) {
                appExportService.fileExportFailure(exportId);
                logger.error("整改通知单导出,失败", ex);
            }
        });
    }

    private Map<String, Object> getExportMap(Integer rectifyId) {
        SafetyRectifyInfo rectifyInfo = safetyRectifyInfoMapper.selectByPrimaryKey(rectifyId);
        List<SafetyCheckDTO> list = safetyRectifyDetailsMapper.selectByRectifyId(rectifyId);
        List<Map<String, Object>> checkList = this.getExportList(list);
        String timeStr = DateUtil.formatDate(rectifyInfo.getCreateTime());
        String[] times = timeStr.split("-");

        Map<String, Object> map = new HashMap<>();
        map.put("corpName", rectifyInfo.getCorpName());
        map.put("years", times[0]);
        map.put("month", times[1]);
        map.put("day", times[2]);
        map.put("checkList", checkList);
        return map;
    }

    private List<Map<String, Object>> getExportList(List<SafetyCheckDTO> list) {
        List<Map<String, Object>> mapList = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            SafetyCheckDTO check = list.get(i);
            Map<String, Object> map = new HashMap<>();
            map.put("idx", i + 1);
            map.put("issueContent", check.getIssueContent());
            map.put("rectifyUserName", check.getRectifyUserName());
            map.put("rectifyTime", DateUtil.formatDate(check.getRectifyTime()));
            mapList.add(map);
        }
        return mapList;
    }
}

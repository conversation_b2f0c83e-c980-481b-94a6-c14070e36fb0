package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyAreaDTO;
import com.whfc.safety.entity.SafetyArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SafetyAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyArea record);

    int insertSelective(SafetyArea record);

    SafetyArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyArea record);

    int updateByPrimaryKey(SafetyArea record);

    /**
     * 查询责任区域列表
     *
     * @param deptId
     * @param name
     * @param keyword
     * @return
     */
    List<SafetyAreaDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("name") String name, @Param("keyword") String keyword);

    /**
     * 软删除
     *
     * @param areaId
     */
    void del(Integer areaId);

    /**
     * 查询下级Id数量
     *
     * @param areaId
     * @return
     */
    Integer selectNumByPid(Integer areaId);

    /**
     * 使用名称查询责任区域
     *
     * @param deptId
     * @param name
     * @return
     */
    SafetyArea selectByDeptIdAndName(@Param("deptId") Integer deptId,@Param("name") String name);
}
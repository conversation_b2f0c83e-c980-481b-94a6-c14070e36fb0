package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyCheckImgDTO;
import com.whfc.safety.entity.SafetyCheckImg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-04 11:05
 */

public interface SafetyCheckImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyCheckImg record);

    int insertSelective(SafetyCheckImg record);

    SafetyCheckImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyCheckImg record);

    int updateByPrimaryKey(SafetyCheckImg record);

    /**
     * 批量查询图片
     *
     * @param checkIdList
     * @return
     */
    List<SafetyCheckImgDTO> selectByCheckIdList(@Param("checkIdList") List<Integer> checkIdList);

    /**
     * 查询图片
     *
     * @param checkId 安全问题表主键
     * @param opType  流程状态码
     * @param logId   流程状态码
     * @return
     * <AUTHOR>
     * @date 2020/8/1 9:30
     **/
    List<String> selectByCheckId(@Param("checkId") Integer checkId, @Param("opType") Integer opType, @Param("logId") Integer logId);

    /**
     * 查询图片
     *
     * @param checkId
     * @param opType
     * @return
     */
    List<String> selectByCheckIdAndOpType(@Param("checkId") Integer checkId, @Param("opType") Integer opType);

    /**
     * 查询图片
     *
     * @param logId
     * @return
     */
    List<String> selectByLogId(@Param("logId") Integer logId);

    /**
     * 软删除
     *
     * @param checkId
     */
    void del(Integer checkId);

    /**
     * 批量插入
     *
     * @param checkId
     * @param logId
     * @param imgUrls
     * @return
     */
    int batchInsert(@Param("checkId") Integer checkId, @Param("logId") Integer logId, @Param("imgUrls") List<String> imgUrls);
}
package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyExecNumDTO;
import com.whfc.safety.dto.SafetyTaskDTO;
import com.whfc.safety.entity.SafetyTask;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SafetyTaskMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyTask record);

    int insertSelective(SafetyTask record);

    SafetyTask selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyTask record);

    int updateByPrimaryKey(SafetyTask record);

    /**
     * 检查任务列表
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyTaskDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("title") String title,
                                       @Param("overdue") Integer overdue, @Param("state") Integer state,
                                       @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 更新任务状态
     *
     * @param taskIds
     */
    void updateStateByTaskIds(List<Integer> taskIds);

    /**
     * 检查记录统计
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO selectNumByDeptId(@Param("deptId") Integer deptId,
                                       @Param("title") String title,
                                       @Param("overdue") Integer overdue,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);
}
package com.whfc.safety.dao;

import com.whfc.safety.entity.SafetyFineImg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SafetyFineImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyFineImg record);

    int insertSelective(SafetyFineImg record);

    SafetyFineImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyFineImg record);

    int updateByPrimaryKey(SafetyFineImg record);

    /**
     * 批量添加
     *
     * @param findId
     * @param urls
     */
    void insertAll(@Param("findId") Integer findId, @Param("urls") List<String> urls);

    /**
     * 使用安全罚单id查询图片
     *
     * @param fineId
     * @return
     */
    List<String> selectByFineId(Integer fineId);
}
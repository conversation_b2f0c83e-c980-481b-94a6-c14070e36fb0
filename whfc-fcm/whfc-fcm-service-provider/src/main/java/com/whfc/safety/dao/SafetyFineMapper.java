package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyFineDTO;
import com.whfc.safety.dto.SafetyFineNumDTO;
import com.whfc.safety.entity.SafetyFine;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SafetyFineMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyFine record);

    int insertSelective(SafetyFine record);

    SafetyFine selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyFine record);

    int updateByPrimaryKey(SafetyFine record);


    /**
     * 查询质量罚单列表
     *
     * @param deptId
     * @param corpName
     * @param userName
     * @param states
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyFineDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                       @Param("corpName") String corpName,
                                       @Param("userName") String userName,
                                       @Param("states") List<Integer> states,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);

    /**
     * 质量罚款统计
     *
     * @param deptId
     * @param corpName
     * @param userName
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyFineNumDTO selectNumByDeptId(@Param("deptId") Integer deptId,
                                       @Param("corpName") String corpName,
                                       @Param("userName") String userName,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);
}
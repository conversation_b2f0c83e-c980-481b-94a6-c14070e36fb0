package com.whfc.safety.dao;

import com.whfc.safety.dto.MdSafetyCheckLogDTO;
import com.whfc.safety.dto.SafetyCheckLogListDTO;
import com.whfc.safety.entity.SafetyCheckLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-04 11:05
 */

public interface SafetyCheckLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyCheckLog record);

    int insertSelective(SafetyCheckLog record);

    SafetyCheckLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyCheckLog record);

    int updateByPrimaryKey(SafetyCheckLog record);

    List<SafetyCheckLogListDTO> selectByCheckIdList(@Param("checkIdList") List<Integer> checkIdList);

    /**
     * 使用安全问题记录表主键查询安全问题流程
     *
     * @param checkId 问题记录表主键
     * @return List<SafetyCheckLogListDTO>
     * <AUTHOR>
     * @date 2020/8/5 9:49
     **/
    List<SafetyCheckLogListDTO> selectByCheckId(@Param("checkId") Integer checkId);

    /**
     * 根据问题ID和操作类型查找日志
     *
     * @param checkId 问题ID
     * @param opType  操作类型
     * @return 日志
     */
    SafetyCheckLog selectByCheckIdAndOpType(@Param("checkId") Integer checkId, @Param("opType") Integer opType);

    /**
     * 软删除
     *
     * @param checkId
     */
    void del(Integer checkId);

    /**
     * 根据组织机构ID查找操作记录
     *
     * @param deptId 组织机构ID
     * @return 操作记录
     */
    List<MdSafetyCheckLogDTO> selectLogByDeptId(@Param("deptId") Integer deptId);
}
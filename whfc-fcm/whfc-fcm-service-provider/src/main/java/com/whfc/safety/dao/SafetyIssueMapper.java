package com.whfc.safety.dao;

import com.whfc.safety.dto.SafetyIssueDTO;
import com.whfc.safety.entity.SafetyIssue;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-04 11:05
 */

public interface SafetyIssueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyIssue record);

    int insertSelective(SafetyIssue record);

    SafetyIssue selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SafetyIssue record);

    int updateByPrimaryKey(SafetyIssue record);

    /**
     * 使用问题类别主键查询问题明细列表
     *
     * @param issueTypeId 问题类别主键
     * @return
     * <AUTHOR>
     * @date 2020/8/6 15:46
     **/
    List<SafetyIssueDTO> selectByIssueTypeId(@Param("issueTypeId") Long issueTypeId);

    /**
     * 使用组织机构ID查询问题明细
     *
     * @param deptId 组织机构ID
     * @return
     * <AUTHOR>
     * @date 2020/8/8 17:01
     **/
    List<SafetyIssueDTO> selectByDeptId(@Param("deptId") Integer deptId);

    /**
     * 修改问题明细
     *
     * @param issueId  问题明细表主键
     * @param content  检查内容
     * @param require  整改要求
     * @param duration 整改期限 （天）
     * @return
     * <AUTHOR>
     * @date 2020/8/6 16:06
     **/
    void updateByIssueId(@Param("issueId") Long issueId, @Param("content") String content, @Param("require") String require, @Param("duration") Integer duration);

    /**
     * @param issueId 问题明细表主键
     * @return
     * <AUTHOR>
     * @date 2020/8/6 16:14
     **/
    void logicDel(@Param("issueId") Long issueId);

    /**
     * 禁用/启用安全问题明细
     *
     * @param issueId 问题明细表主键
     * @param enable  是否启用0-禁用  1-启用
     * @return
     * <AUTHOR>
     * @date 2020/8/6 16:38
     **/
    void disableOrEnabledIssue(@Param("issueId") Long issueId, @Param("enable") Integer enable);

    /**
     * 获取安全问题历史列表
     *
     * @param deptId
     * @param pageSize
     * @return
     */
    List<SafetyIssueDTO> selectHistoryByDeptIdAndPageSize(@Param("deptId")Integer deptId, @Param("pageSize")Integer pageSize);
}
package com.whfc.safety.dao;

import com.whfc.safety.entity.SafetyDict;
import org.apache.ibatis.annotations.Param;

public interface SafetyDictMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyDict record);

    int insertSelective(SafetyDict record);

    SafetyDict selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyDict record);

    /**
     * 查询配置
     *
     * @param deptId
     * @param code
     * @return
     */
    SafetyDict selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);
}
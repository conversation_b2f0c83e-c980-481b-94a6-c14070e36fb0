package com.whfc.config;

import com.whfc.common.pdf.OfficeProperties;
import com.whfc.common.pdf.OfficeUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OfficeConfig {

    /**
     * office工具类参数
     */
    @Bean(name = "officeProps")
    @ConfigurationProperties(prefix = "libre.office")
    public OfficeProperties officeProperties() {
        return new OfficeProperties();
    }

    /**
     * office工具类
     *
     * @param properties
     * @return
     */
    @Bean(name = "officeUtil")
    public OfficeUtil officeUtil(@Qualifier("officeProps") OfficeProperties properties) {
        return new OfficeUtil(properties.getHome(), properties.getPorts());
    }
}

package com.whfc.config;

import com.whfc.common.entity.DefaultProperties;
import com.whfc.common.spring.AppContextUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2021-05-19 15:24
 * @Version 1.0
 */
@Configuration
public class FcmConfig {

    @Bean
    public AppContextUtil appContextUtil() {
        return new AppContextUtil();
    }

    @Bean
    @ConfigurationProperties("whfc.default")
    public DefaultProperties defaultProperties() {
        return new DefaultProperties();
    }
}

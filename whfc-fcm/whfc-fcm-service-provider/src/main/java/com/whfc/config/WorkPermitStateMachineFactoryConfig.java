package com.whfc.config;

import com.whfc.fcm.enums.WorkPermitEvent;
import com.whfc.fcm.enums.WorkPermitState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.statemachine.transition.Transition;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 作业票状态机工厂配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Configuration
@EnableStateMachineFactory(name = "workPermitStateMachineFactory")
public class WorkPermitStateMachineFactoryConfig extends EnumStateMachineConfigurerAdapter<WorkPermitState, WorkPermitEvent> {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitStateMachineFactoryConfig.class);

    @Override
    public void configure(StateMachineConfigurationConfigurer<WorkPermitState, WorkPermitEvent> config) throws Exception {
        config
            .withConfiguration()
            .autoStartup(true)
            .listener(stateMachineListener());
    }

    @Override
    public void configure(StateMachineStateConfigurer<WorkPermitState, WorkPermitEvent> states) throws Exception {
        states
            .withStates()
            .initial(WorkPermitState.DRAFT)
            .states(java.util.EnumSet.allOf(WorkPermitState.class))
            .end(WorkPermitState.CLOSED);
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<WorkPermitState, WorkPermitEvent> transitions) throws Exception {
        transitions
            // 草稿 -> 待签发
            .withExternal()
                .source(WorkPermitState.DRAFT)
                .target(WorkPermitState.PENDING_ISSUE)
                .event(WorkPermitEvent.SUBMIT)
                .and()
            // 待签发 -> 作业中
            .withExternal()
                .source(WorkPermitState.PENDING_ISSUE)
                .target(WorkPermitState.IN_PROGRESS)
                .event(WorkPermitEvent.ISSUE)
                .and()
            // 待签发 -> 已打回
            .withExternal()
                .source(WorkPermitState.PENDING_ISSUE)
                .target(WorkPermitState.REJECTED)
                .event(WorkPermitEvent.REJECT)
                .and()
            // 已打回 -> 待签发
            .withExternal()
                .source(WorkPermitState.REJECTED)
                .target(WorkPermitState.PENDING_ISSUE)
                .event(WorkPermitEvent.RESUBMIT)
                .and()
            // 作业中 -> 关闭中
            .withExternal()
                .source(WorkPermitState.IN_PROGRESS)
                .target(WorkPermitState.CLOSING)
                .event(WorkPermitEvent.END_WORK)
                .and()
            // 关闭中 -> 已结束
            .withExternal()
                .source(WorkPermitState.CLOSING)
                .target(WorkPermitState.CLOSED)
                .event(WorkPermitEvent.CLOSE);
    }

    /**
     * 状态机监听器
     */
    @Bean
    public StateMachineListener<WorkPermitState, WorkPermitEvent> stateMachineListener() {
        return new StateMachineListenerAdapter<WorkPermitState, WorkPermitEvent>() {
            @Override
            public void stateChanged(State<WorkPermitState, WorkPermitEvent> from, State<WorkPermitState, WorkPermitEvent> to) {
                logger.info("状态机状态变更: {} -> {}", 
                    from != null ? from.getId() : "null", 
                    to != null ? to.getId() : "null");
            }

            @Override
            public void transition(Transition<WorkPermitState, WorkPermitEvent> transition) {
                logger.info("状态机转换: {} -> {} on {}", 
                    transition.getSource().getId(), 
                    transition.getTarget().getId(), 
                    transition.getTrigger().getEvent());
            }

            @Override
            public void transitionStarted(Transition<WorkPermitState, WorkPermitEvent> transition) {
                logger.debug("状态机转换开始: {}", transition);
            }

            @Override
            public void transitionEnded(Transition<WorkPermitState, WorkPermitEvent> transition) {
                logger.debug("状态机转换结束: {}", transition);
            }
        };
    }

    /**
     * 状态机持久化器
     */
    @Bean
    public StateMachinePersister<WorkPermitState, WorkPermitEvent, String> workPermitStateMachinePersister() {
        return new DefaultStateMachinePersister<>(new InMemoryStateMachinePersist());
    }

    /**
     * 内存状态机持久化实现
     */
    private static class InMemoryStateMachinePersist implements StateMachinePersist<WorkPermitState, WorkPermitEvent, String> {
        
        private final ConcurrentHashMap<String, StateMachineContext<WorkPermitState, WorkPermitEvent>> contexts = new ConcurrentHashMap<>();

        @Override
        public void write(StateMachineContext<WorkPermitState, WorkPermitEvent> context, String contextObj) throws Exception {
            logger.debug("保存状态机上下文: {} -> {}", contextObj, context.getState());
            contexts.put(contextObj, context);
        }

        @Override
        public StateMachineContext<WorkPermitState, WorkPermitEvent> read(String contextObj) throws Exception {
            StateMachineContext<WorkPermitState, WorkPermitEvent> context = contexts.get(contextObj);
            if (context == null) {
                // 如果没有找到上下文，返回初始状态
                context = new DefaultStateMachineContext<>(WorkPermitState.DRAFT, null, null, null);
                logger.debug("未找到状态机上下文: {}，返回初始状态", contextObj);
            } else {
                logger.debug("读取状态机上下文: {} -> {}", contextObj, context.getState());
            }
            return context;
        }
    }
}

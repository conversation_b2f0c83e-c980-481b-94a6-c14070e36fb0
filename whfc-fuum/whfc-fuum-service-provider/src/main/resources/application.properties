#server
server.tomcat.basedir=/data/tmp
#spring
spring.application.name=fuum
spring.profiles.active=dev
#xxl
xxl.job.executor.appname=fuum-xxl-job
xxl.job.executor.port=21001
#mybatis
mybatis.mapper-locations=classpath:com/whfc/fuum/mapper/*Mapper.xml
mybatis.config-location=classpath:mybatis/mybatis-config.xml
#liquibase
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:liquibase/master.xml
#dubbo
nacos.config.server-addr=${NACOS_SERVER}
nacos.config.username=${NACOS_USER}
nacos.config.password=${NACOS_PASS}
dubbo.application.name=whfc-fuum-service-provider
dubbo.scan.base-packages=com.whfc.fuum.service
dubbo.protocol.name=dubbo
dubbo.protocol.port=20001
dubbo.registry.address=nacos://${NACOS_SERVER}
dubbo.registry.username=${NACOS_USER}
dubbo.registry.password=${NACOS_PASS}
dubbo.registry.use-as-config-center=true
dubbo.registry.use-as-metadata-center=false
dubbo.provider.timeout=30000
#logger
logging.config=classpath:logback-${spring.profiles.active}.xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.fuum.dao.SysRoleMapper">
  <resultMap id="BaseResultMap" type="com.whfc.fuum.entity.SysRole">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="create_by" jdbcType="TINYINT" property="createBy"/>
    <result column="description" jdbcType="VARCHAR" property="description"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    name,
    status,
    create_by,
    create_time,
    update_time,
    description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_role
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete
    from sys_role
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.fuum.entity.SysRole">
    insert into sys_role (id, dept_id, name, status, create_By, create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
    #{status,jdbcType=TINYINT}, #{createBy,jdbcType=TINYINT},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.fuum.entity.SysRole" useGeneratedKeys="true" keyProperty="id">
    insert into sys_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.fuum.entity.SysRole">
    update sys_role
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.fuum.entity.SysRole">
    update sys_role
    set dept_id     = #{deptId,jdbcType=INTEGER},
    name        = #{name,jdbcType=VARCHAR},
    status      = #{status,jdbcType=TINYINT},
    create_by   = #{createBy,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectRoleList" parameterType="java.lang.Integer" resultType="com.whfc.fuum.dto.SysRoleDTO">
    select id,
    dept_id,
    `name`,
    description
    from sys_role
    <where>
      <if test="deptIds != null and deptIds.size() != 0">
        dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
          #{deptId}
        </foreach>
      </if>
      <if test="keyword != null and keyword != ''">
        AND `name` like concat('%', #{keyword}, '%')
      </if>
    </where>
    order by dept_id ,create_time DESC
  </select>

  <select id="selectByNameAndDept" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM sys_role
    WHERE dept_id = #{deptId}
    AND name = #{roleName}
    LIMIT 1
  </select>
</mapper>
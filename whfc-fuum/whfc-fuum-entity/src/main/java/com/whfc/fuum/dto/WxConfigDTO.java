package com.whfc.fuum.dto;

import java.io.Serializable;

/**
 * @Description: 微信配置
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-15 10:44
 */
public class WxConfigDTO implements Serializable {

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用秘钥
     */
    private String appSecret;

    /**
     * 应用标识
     */
    private String token;

    /**
     * aes秘钥
     */
    private String aesKey;

    /**
     * 格式:json/xml
     */
    private String format;

    /**
     * 接口调用凭证
     */
    private String accessToken;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}

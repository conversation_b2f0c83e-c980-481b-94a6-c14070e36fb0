package com.whfc.wxmp.validator;

import com.whfc.common.enums.SysUserType;
import com.whfc.common.util.RequestAttr;
import com.whfc.common.util.SessionAttr;
import com.whfc.common.validator.FieldScopeType;
import com.whfc.common.validator.ValidatorExec;
import com.whfc.fuum.entity.SysUser;
import com.whfc.mach.service.AppMachService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

import static org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-06-21 19:01
 */
@Component
public class ValidatorExecImpl implements ValidatorExec {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = AppMachService.class, version = "1.0.0")
    private AppMachService appMachService;

    @Override
    public boolean valid(FieldScopeType scopeType, Object value) {
        logger.info("参数鉴权, scopeType:{},value:{}", scopeType, value);
        boolean result;
        //获取用户
        Session session = SecurityUtils.getSubject().getSession();
        SysUser sysUser = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        //验证用户
        if (sysUser == null) {
            return false;
        }
        //内部账号跳过越权验证
        if (SysUserType.INNER.getValue().equals(sysUser.getType())) {
            return true;
        }
        //获取组织机构ID
        Integer deptId = (Integer) RequestContextHolder.currentRequestAttributes().getAttribute(RequestAttr.DEPT_ID, SCOPE_REQUEST);
        if (deptId == null) {
            return false;
        }
        //鉴权
        switch (scopeType) {
            case MACH_ID:
                result = validMachId(value, deptId);
                break;
            case EMP_ID:
                result = validEmpId(value, deptId);
                break;
            default:
                result = false;
                break;
        }
        return result;
    }

    /**
     * 人员ID鉴权
     *
     * @param value  参数值
     * @param deptId 组织机构ID
     * @return 鉴权结果
     */
    private boolean validEmpId(Object value, Integer deptId) {
        // TODO:人员ID鉴权

        return false;
    }

    /**
     * 设备ID鉴权
     *
     * @param value  参数值
     * @param deptId 组织机构ID
     * @return 鉴权结果
     */
    private boolean validMachId(Object value, Integer deptId) {
        logger.info("验证设备ID，value:{},deptId:{}", value, deptId);
        int machId;
        try {
            machId = Integer.parseInt(value.toString());
        } catch (Exception e) {
            logger.warn("value:{} 转换为machId失败，error:{}", value, e.getMessage());
            return false;
        }
        //验证用户是否有该设备的权限
        return appMachService.validDeptMachId(deptId, machId);
    }
}

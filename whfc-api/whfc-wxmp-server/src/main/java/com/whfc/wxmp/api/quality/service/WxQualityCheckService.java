package com.whfc.wxmp.api.quality.service;

import com.whfc.fuum.dto.SysUserDTO;
import com.whfc.fuum.entity.SysUser;
import com.whfc.quality.dto.QualityCheckDescDTO;
import com.whfc.quality.dto.QualityCheckListDTO;
import com.whfc.quality.dto.QualityCheckStatisticalDTO;
import com.whfc.quality.dto.QualityDictItemDTO;
import com.whfc.quality.param.QualityCheckListParam;
import com.whfc.quality.param.QualityCheckLogParam;
import com.whfc.quality.param.QualityCheckParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * @Description 质量问题接口
 * <AUTHOR>
 * @Date 2020/7/30 10:54
 * @Version 1.0
 */
public interface WxQualityCheckService {

    /**
     * 获取质量问题上报列表
     *
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    QualityCheckListDTO getCheckList(QualityCheckListParam param, Integer pageNum, Integer pageSize);

    /**
     * 获取质量问题上报列表
     *
     * @param param
     * @return
     */
    QualityCheckListDTO getCheckList(QualityCheckListParam param);

    /**
     * 使用问题上报id查询问题详情
     *
     * @param checkId 问题上报ID
     * @return
     * <AUTHOR>
     * @date 2020/8/3 14:21
     **/
    QualityCheckDescDTO getCheck(Integer checkId);

    /**
     * 问题上报接口
     *
     * @return: null
     * <AUTHOR>
     * @Date 2020/7/30
     **/
    void add(QualityCheckParam checkParam, SysUser sysUser);

    /**
     * 质量问题处理流程
     *
     * @Param:
     * @Return:
     * <AUTHOR>
     * @Date 2020/7/31 9:53
     **/
    void addCheckLog(QualityCheckLogParam qualityCheckLogParam);

    /**
     * 查询统计信息
     *
     * @param deptId 组织机构ID
     * @param time   时间
     * @return
     * <AUTHOR>
     * @date 2020/8/3 19:34
     **/
    QualityCheckStatisticalDTO statistical(Integer deptId, Date time);

    /**
     * 使用组织机构id查询人员信息
     *
     * @Date 2020/7/31 10:27
     * @Param:
     * @Return:
     * <AUTHOR>
     **/
    List<SysUserDTO> sysUserList(Integer deptId);

    /**
     * 质量问题图片上传接口
     *
     * @Param:
     * @Return:
     * <AUTHOR>
     * @Date 2020/7/31 10:40
     **/
    String uploadImg(MultipartFile file);

    /**
     * 问题紧急程度
     *
     * @param deptId
     * @return
     */
    List<QualityDictItemDTO> getUrgencyList(Integer deptId);
}

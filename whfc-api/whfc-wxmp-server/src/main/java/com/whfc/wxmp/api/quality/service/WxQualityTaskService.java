package com.whfc.wxmp.api.quality.service;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.quality.dto.QualityTaskExecDTO;
import com.whfc.quality.param.QuailTaskCompleteParam;
import com.whfc.quality.param.QualityTaskReportParam;

import java.util.Date;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021-05-18 10:00
 * @Version 1.0
 */
public interface WxQualityTaskService {


    /**
     * 检查记录列表（分页）
     *
     * @param userId
     * @param deptId
     * @param report
     * @param startTime
     * @param endTime
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<QualityTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime, Integer pageSize, Integer pageNum);

    /**
     * 检查记录列表
     *
     * @param userId
     * @param deptId
     * @param report
     * @param startTime
     * @param endTime
     * @return
     */
    ListData<QualityTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime);

    /**
     * 需要处理的任务
     *
     * @param userId
     * @param deptId
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<QualityTaskExecDTO> meList(Integer userId, Integer deptId, Integer pageSize, Integer pageNum);

    /**
     * 排查上报
     *
     * @param param
     */
    void execAdd(QualityTaskReportParam param);

    /**
     * 完成任务
     *
     * @param param
     */
    void execComplete(QuailTaskCompleteParam param);
}

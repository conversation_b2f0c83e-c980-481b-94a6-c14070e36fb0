package com.whfc.wxmp.api.fmam.service;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fmam.entity.dto.*;
import com.whfc.fmam.entity.qo.WeighNoteAddQO;
import com.whfc.fuum.entity.SysUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * @Description:
 * @author: qzexing
 * @version: 1.0
 * @date: 2020/4/8 22:03
 */
public interface WxFmamWeighNoteService {

    /**
     * 获取移动收发料磅单列表
     *
     * @param weightNoteType 磅单类型
     * @param deptId         组织机构ID
     * @param date           日期
     * @param code           磅单编码
     * @param pageNum        页码
     * @param pageSize       每页数量
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> list(Integer weightNoteType, Integer deptId, Date date, String code, Integer pageNum, Integer pageSize);

    /**
     * 查询磅单详情
     *
     * @param weighNoteId 磅单ID
     * @return 磅单详情
     */
    FmamWeighNoteDetailDTO detail(Long weighNoteId);

    /**
     * 获取磅单数量
     *
     * @param deptId 组织机构ID
     * @param date   日期
     * @param code   磅单编码
     * @return 磅单数量
     */
    FmamWeighNoteTotalDTO getWeighNoteTotal(Integer deptId, Date date, String code);

    /**
     * 新增磅单
     *
     * @param sysUser        用户
     * @param weighNoteAddQO 新增磅单参数
     */
    void add(SysUser sysUser, WeighNoteAddQO weighNoteAddQO);

    /**
     * 上传物资图片
     *
     * @param imgType 图片类型
     * @param file    图片文件
     * @return 图片信息
     */
    FmamWeighNoteImgDTO uploadImg(Integer imgType, MultipartFile file);

    /**
     * 获取磅单统计数量
     *
     * @param deptId 组织机构ID
     * @return 磅单统计数量
     */
    FmamWeighNoteCountDTO weighNoteCount(Integer deptId);

    /**
     * 磅单根据收发料类型统计 --日统计
     *
     * @param deptId 组织机构ID
     * @param date   日期
     * @return 磅单统计数量DTO
     */
    FmamWeighNoteTotalDTO recvSendTypeTotalByDay(Integer deptId, Date date);

    /**
     * 磅单根据收发料类型统计 --月统计
     *
     * @param deptId 组织机构ID
     * @param month  月份
     * @return 磅单统计数量DTO
     */
    FmamWeighNoteTotalDTO recvSendTypeTotalByMonth(Integer deptId, Date month);

    /**
     * 磅单根据收发料类型统计 --累计统计
     *
     * @param deptId 组织机构ID
     * @return 磅单统计数量DTO
     */
    FmamWeighNoteTotalDTO recvSendTypeTotal(Integer deptId);

    /**
     * 根据收发料类型和日期获取磅单列表
     *
     * @param deptId        组织机构ID
     * @param date          日期
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> listByRecvSendTypeAndDay(Integer deptId, Date date, Integer weighNoteType, Integer recvSendType, Integer pageSize, Integer pageNum);

    /**
     * 根据收发料类型和月份获取磅单列表
     *
     * @param deptId        组织机构ID
     * @param month         月份
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> listByRecvSendTypeAndMonth(Integer deptId, Date month, Integer weighNoteType, Integer recvSendType, Integer pageSize, Integer pageNum);

    /**
     * 根据收发料类型获取磅单列表
     *
     * @param deptId        组织机构ID
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamDateWeighNoteDTO> listByRecvSendType(Integer deptId, Integer weighNoteType, Integer recvSendType, Integer pageSize, Integer pageNum);

    /**
     * 磅单材料分析列表 - 每日
     *
     * @param deptId        组织机构ID
     * @param date          日期
     * @param weighNoteType 磅单类型
     * @param source        来源
     * @param recvSendType  收发料类型
     * @param matTypeId     材料类型ID
     * @return 磅单材料分析
     */
    ListData<FmamWeighNoteAnalysisDTO> analysisByDate(Integer deptId, Date date, Integer weighNoteType, Integer source, Integer recvSendType, Long matTypeId);

    /**
     * 磅单材料分析列表 - 每月
     *
     * @param deptId        组织机构ID
     * @param month         月份
     * @param weighNoteType 磅单类型
     * @param source        来源
     * @param recvSendType  收发料类型
     * @param matTypeId     材料类型ID
     * @return 磅单材料分析
     */
    ListData<FmamWeighNoteAnalysisDTO> analysisByMonth(Integer deptId, Date month, Integer weighNoteType, Integer source, Integer recvSendType, Long matTypeId);

    /**
     * 磅单材料分析列表 - 累计
     *
     * @param deptId        组织机构ID
     * @param weighNoteType 磅单类型
     * @param source        来源
     * @param recvSendType  收发料类型
     * @param matTypeId     材料类型ID
     * @return 磅单材料分析
     */
    ListData<FmamWeighNoteAnalysisDTO> analysis(Integer deptId, Integer weighNoteType, Integer source, Integer recvSendType, Long matTypeId);

    /**
     * 磅单材料分析-磅单列表-每日
     *
     * @param deptId        组织机构ID
     * @param matId         材料ID
     * @param date          日期
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param code          磅单编号
     * @param source        来源
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> analysisListByDate(Integer deptId, Long matId, Date date, Integer weighNoteType, Integer recvSendType, String code, Integer source, Integer pageSize, Integer pageNum);

    /**
     * 磅单材料分析-磅单列表-每月
     *
     * @param deptId        组织机构ID
     * @param matId         材料ID
     * @param month         月份
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param code          磅单编号
     * @param source        来源
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> analysisListByMonth(Integer deptId, Long matId, Date month, Integer weighNoteType, Integer recvSendType, String code, Integer source, Integer pageSize, Integer pageNum);

    /**
     * 磅单材料分析-磅单列表-累计
     *
     * @param deptId        组织机构ID
     * @param matId         材料ID
     * @param weighNoteType 磅单类型
     * @param recvSendType  收发料类型
     * @param code          磅单编号
     * @param source        来源
     * @param pageSize      每页数量
     * @param pageNum       页码
     * @return 磅单列表
     */
    PageData<FmamWeighNoteDTO> analysisList(Integer deptId, Long matId, Integer weighNoteType, Integer recvSendType, String code, Integer source, Integer pageSize, Integer pageNum);

    /**
     * 材料进出场日报
     *
     * @param deptId
     * @param time
     * @param weighNoteType
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<FmamWeighNoteDailyDTO> daily(Integer deptId, Date time, Integer weighNoteType, Integer pageSize, Integer pageNum);
}

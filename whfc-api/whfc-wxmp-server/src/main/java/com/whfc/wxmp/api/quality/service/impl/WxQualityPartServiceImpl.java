package com.whfc.wxmp.api.quality.service.impl;

import com.whfc.quality.dto.QualityPartDTO;
import com.whfc.quality.service.QualityPartService;
import com.whfc.wxmp.api.quality.service.WxQualityPartService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClasssName WxQualityPartServiceImpl
 * @Description 质量问题部位 实现
 * <AUTHOR>
 * @Date 2020/8/4 18:41
 * @Version 1.0
 */
@Service
public class WxQualityPartServiceImpl implements WxQualityPartService {

    @DubboReference(interfaceClass = QualityPartService.class, version = "1.0.0")
    private QualityPartService qualityPartService;

    @Override
    public List<QualityPartDTO> partList(Integer deptId, String keyword) {
        return qualityPartService.list(deptId, keyword);
    }
}

package com.whfc.iot.fse.crane.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 塔吊数据-工作循环
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-08 9:24
 */
@Data
public class TwMsgWorkCycle extends TwMsg implements Serializable {
    /**
     * 运行时长(s)
     */
    private Integer timeLong;
    /**
     * 最大重量(t)
     */
    private Double maxWeight;
    /**
     * 最大力矩(t·m)
     */
    private Double maxMoment;
    /**
     * 最大力矩百分比 %
     */
    private Double maxMomentPercent;
    /**
     * 最小幅度(m）
     */
    private Double minMargin;
    /**
     * 最大幅度(m)
     */
    private Double maxMargin;
    /**
     * 最大高度(m)
     */
    private Double maxHeight;
    /**
     * 最小高度(m)
     */
    private Double minHeight;
    /**
     * 风速
     */
    private Double windSpeed;
    /**
     * 风级
     */
    private Integer wind;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}

package com.whfc.openapi.api.face.controller;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.enums.RecMode;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.enums.FaceGateType;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.openapi.api.face.service.FaceGateSyncService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @Description 沃土闸机回调
 * <AUTHOR>
 * @Date 2021/1/20 16:57
 * @Version 1.0
 */
@RestController
@RequestMapping("/open/api/faceGate/wo")
public class FaceGateWoController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FaceGateSyncService faceGateSyncService;

    /**
     * 人员注册回调
     */
    @PostMapping("regist/callback")
    public String photoAuthCallback(@RequestParam(value = "eventGuid", required = false) String eventGuid,
                                    @RequestParam(value = "eventCode", required = false) String eventCode,
                                    @RequestParam(value = "eventMsg", required = false) String eventMsg) {
        logger.info("wo,人员注册回调,eventGuid:{},eventCode:{},eventMsg:{}", eventGuid, eventCode, eventMsg);

        return "success";
    }

    /**
     * 人员识别回调
     */
    @PostMapping("rec/callback")
    public String recCallback(@RequestParam(value = "personGuid", required = false) String personGuid,
                              @RequestParam(value = "deviceKey", required = false) String deviceKey,
                              @RequestParam(value = "photoUrl", required = false) String photoUrl,
                              @RequestParam(value = "showTime", required = false) String showTime,
                              @RequestParam(value = "data", required = false) String data,
                              @RequestParam(value = "type", required = false) Integer type,
                              @RequestParam(value = "temperature", required = false) Double temperature,
                              @RequestParam(value = "idCardInfo", required = false) String idCardInfo,
                              @RequestParam(value = "eventGuid", required = false) String eventGuid,
                              @RequestParam(value = "eventCode", required = false) String eventCode,
                              @RequestParam(value = "eventMsg", required = false) String eventMsg
    ) {
        logger.info("wo,人员识别回调,personGuid:{},deviceKey:{},photoUrl:{},showTime:{},data:{},type:{},temperature:{},idCardInfo:{},eventGuid:{},eventCode:{},eventMsg:{}",
                personGuid, deviceKey, photoUrl, showTime, data, type, temperature, idCardInfo, eventGuid, eventCode, eventMsg);

        Date time = new Date();
        if (showTime != null) {
            time = DateUtil.parseDate(showTime, "yyyy-MM-dd'T'HH:mm:ssZ");
        }
        if (StringUtils.isEmpty(deviceKey) && StringUtils.isNotEmpty(eventMsg)) {
            JSONObject msg = JSONObject.parseObject(eventMsg);
            deviceKey = msg.getString("deviceNo");
            personGuid = msg.getString("admitGuid");
            photoUrl = msg.getString("filePath");
            time = new Date(msg.getLongValue("showTime"));
        }
        EmpAttendSyncDataParam dataParam = new EmpAttendSyncDataParam();
        dataParam.setPersonGuid(personGuid);
        dataParam.setDeviceKey(deviceKey);
        dataParam.setPicture(photoUrl);
        dataParam.setShowTime(time);
        dataParam.setPlatform(FaceGateType.WO.getCode());
        dataParam.setRecMode(RecMode.FACE.getValue());
        faceGateSyncService.syncAttend(dataParam);
        return "success";
    }
}

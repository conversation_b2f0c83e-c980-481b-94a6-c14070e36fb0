package com.whfc.openapi.api.fsdm;

import com.whfc.common.result.ListData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.fuum.dto.open.OpenApiClientDTO;
import com.whfc.openapi.constant.OpenApiConst;
import com.whfc.uni.dto.fsdm.FsdmStageTemplateDTO;
import com.whfc.uni.dto.fsdm.FsdmTaskDTO;
import com.whfc.uni.service.FsdmTaskService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * @Description: 任务管理接口
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-02-23 15:25
 */
@RestController
@RequestMapping("/open/api/fsdm")
public class OpenTaskController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = FsdmTaskService.class, version = "1.0.0")
    private FsdmTaskService taskService;


    @GetMapping("task/latest")
    public Result taskLatest(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        logger.info("分层分块最新数据,deptId:{}", client.getDeptId());
        List<FsdmTaskDTO> list = taskService.getTaskLatest(client.getDeptId());
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("task/history")
    public Result taskHistory(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client,
                              @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        logger.info("分层分块历史数据,deptId:{},date:{}", client.getDeptId(), date);
        List<FsdmTaskDTO> list = taskService.getTaskHistory(client.getDeptId(), date);
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("act/task/latest")
    public Result actTaskLatest(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        logger.info("分层分块-实际-最新数据,deptId:{}", client.getDeptId());
        List<FsdmTaskDTO> list = taskService.getActTaskLatest(client.getDeptId());
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("act/task/history")
    public Result actTaskHistory(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client,
                                 @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        logger.info("分层分块-实际-历史数据,deptId:{},date:{}", client.getDeptId(), date);
        List<FsdmTaskDTO> list = taskService.getActTaskHistory(client.getDeptId(), date);
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("plan/task/latest")
    public Result planTaskLatest(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        logger.info("分层分块-计划-最新数据,deptId:{}", client.getDeptId());
        List<FsdmTaskDTO> list = taskService.getPlanTaskLatest(client.getDeptId());
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("plan/task/history")
    public Result planTaskHistory(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client,
                                  @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        logger.info("分层分块-计划-历史数据,deptId:{},date:{}", client.getDeptId(), date);
        List<FsdmTaskDTO> list = taskService.getPlanTaskHistory(client.getDeptId(), date);
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("plan/task/list")
    public Result planTaskList(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        logger.info("分层分块-计划-任务列表,deptId:{}", client.getDeptId());
        List<FsdmTaskDTO> list = taskService.getPlanTaskList(client.getDeptId());
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("milestone/list")
    public Result milestoneList(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        Integer deptId = client.getDeptId();
        logger.info("里程碑列表,deptId:{}", deptId);
        List<FsdmTaskDTO> list = taskService.milestoneList(deptId);
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("stage/tpl/list")
    public Result stageTplList(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        logger.info("分层分块-工序模版,deptId:{}", client.getDeptId());
        List<FsdmStageTemplateDTO> list = taskService.stageTemplateList(client.getDeptId());
        return ResultUtil.success(new ListData<>(list));
    }
}

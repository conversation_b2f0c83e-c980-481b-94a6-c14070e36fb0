package com.whfc.openapi.api.fmam.service.impl;

import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.result.ResultEnum;
import com.whfc.fmam.entity.dto.FmamClientDTO;
import com.whfc.fmam.entity.enums.WeighNoteImgType;
import com.whfc.fmam.service.FmamSyncDataService;
import com.whfc.openapi.api.fmam.service.FmamSyncDataLocalService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/19 11:41
 */
@Service
public class FmamSyncDataLocalServiceImpl implements FmamSyncDataLocalService {

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private FilePathConfig filePathConfig;

    @DubboReference(interfaceClass = FmamSyncDataService.class, version = "1.0.0")
    private FmamSyncDataService fmamSyncDataService;

    @Override
    public void uploadWeighNoteImage(FmamClientDTO fmamClientDTO, String guid, Integer imgType, MultipartFile[] images, Integer genType, Date genTime) {
        Integer type = Integer.valueOf(imgType.toString().substring(0, 1));
        WeighNoteImgType noteImgType = WeighNoteImgType.parseValue(type);
        if (noteImgType == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "图片类型错误.");
        }
        List<String> imgUrls = new ArrayList<>(images.length);
        //图片上传到oss
        for (MultipartFile image : images) {
            String imgUrl = null;
            String ossKey = filePathConfig.getFilePath("fmam/note/" + guid, imgType + ".jpg");
            try {
                imgUrl = fileHandler.upload(ossKey, image.getInputStream());
                imgUrls.add(imgUrl);
            } catch (Exception ex) {
                throw new BizException(ResultEnum.FAILURE.getCode(), "上传文件到oss出现异常");
            }
        }
        //保存磅单图片
        fmamSyncDataService.uploadWeighNoteImage(fmamClientDTO, guid, type, imgType, imgUrls, genType, genTime);
    }
}

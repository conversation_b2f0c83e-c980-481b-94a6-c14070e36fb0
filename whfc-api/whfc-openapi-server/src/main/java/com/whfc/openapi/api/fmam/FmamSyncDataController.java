package com.whfc.openapi.api.fmam;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.fmam.entity.constant.FmamConstant;
import com.whfc.fmam.entity.dto.*;
import com.whfc.fmam.service.FmamSyncDataService;
import com.whfc.openapi.api.fmam.service.FmamSyncDataLocalService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/14 19:54
 */
@RestController
@RequestMapping("/open/api/fmam")
@Validated
public class FmamSyncDataController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = FmamSyncDataService.class, version = "1.0.0")
    private FmamSyncDataService fmamSyncDataService;

    @Autowired
    private FmamSyncDataLocalService fmamSyncDataLocalService;

    @GetMapping(value = "serverTime")
    public Result syncServerTime(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO) {
        logger.info("获取服务器时间,fmamClientDTO:[{}]", fmamClientDTO);
        ServerTimeDTO serverTimeDTO = new ServerTimeDTO();
        serverTimeDTO.setServerTime(new Date());
        return ResultUtil.success(serverTimeDTO);
    }

    @GetMapping(value = "sync/supplier")
    public Result syncSupplier(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "lastSyncTime", required = false) Date lastSyncTime) {
        logger.info("同步供应商,fmamClientDTO:[{}],lastSyncTime:[{}]", fmamClientDTO, lastSyncTime);
        ListData<SyncFmamSupplierDTO> listData = fmamSyncDataService.getSyncSupplierDTO(fmamClientDTO, lastSyncTime);
        return ResultUtil.success(listData);
    }

    @GetMapping(value = "sync/warehouse")
    public Result syncWarehouse(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                                @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "lastSyncTime", required = false) Date lastSyncTime) {
        logger.info("同步仓库,fmamClientDTO:[{}],lastSyncTime:[{}]", fmamClientDTO, lastSyncTime);
        ListData<SyncFmamWarehouseDTO> listData = fmamSyncDataService.getSyncWarehoureDTO(fmamClientDTO, lastSyncTime);
        return ResultUtil.success(listData);
    }

    @GetMapping(value = "sync/matType")
    public Result syncMatType(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                              @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "lastSyncTime", required = false) Date lastSyncTime,
                              @RequestParam("pageNum") Integer pageNum,
                              @RequestParam("pageSize") Integer pageSize) {
        logger.info("同步材料类别,fmamClientDTO:[{}],lastSyncTime:[{}],pageNum:[{}],pageSize:[{}]", fmamClientDTO, lastSyncTime, pageNum, pageSize);
        PageData<SyncFmamMatTypeDTO> pageData = fmamSyncDataService.getSyncMatTypeDTO(fmamClientDTO, lastSyncTime, pageNum, pageSize);
        return ResultUtil.success(pageData);
    }

    @GetMapping(value = "sync/mat")
    public Result syncMat(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                          @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "lastSyncTime", required = false) Date lastSyncTime,
                          @RequestParam("pageNum") Integer pageNum,
                          @RequestParam("pageSize") Integer pageSize) {
        logger.info("同步材料,fmamClientDTO:[{}],lastSyncTime:[{}],pageNum:[{}],pageSize:[{}]", fmamClientDTO, lastSyncTime, pageNum, pageSize);
        PageData<SyncFmamMatDTO> pageData = fmamSyncDataService.getSyncMatDTO(fmamClientDTO, lastSyncTime, pageNum, pageSize);
        return ResultUtil.success(pageData);
    }

    @PostMapping(value = "sync/weighNote")
    public Result syncWeighNote(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                                @RequestBody @Validated UploadWeighNoteDTO uploadWeighNoteDTO) {
        logger.info("上传磅单,fmamClientDTO:[{}],UploadWeighNoteDTO:[{}]", fmamClientDTO, JSONUtil.toString(uploadWeighNoteDTO));
        fmamSyncDataService.uploadWeighNote(fmamClientDTO, uploadWeighNoteDTO);
        return ResultUtil.success();
    }

    @PostMapping(value = "sync/weighNote/images")
    public Result syncWeighNoteImages(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                                      @RequestParam("guid") String guid,
                                      @RequestParam("type") Integer type,
                                      @NotEmpty MultipartFile[] images,
                                      @RequestParam("genType") Integer genType,
                                      @RequestParam("genTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date genTime) {
        logger.info("上传磅单照片,fmamClientDTO:[{}],guid:[{}],type:[{}],images:{},genType:{},genTime:{}", fmamClientDTO, guid, type, images.length, genType, genTime);
        fmamSyncDataLocalService.uploadWeighNoteImage(fmamClientDTO, guid, type, images, genType, genTime);
        return ResultUtil.success();
    }

    @GetMapping(value = "sync/weighNote")
    public Result syncWeighNote(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO,
                                @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(value = "lastSyncTime", required = false) Date lastSyncTime,
                                @RequestParam("pageNum") Integer pageNum,
                                @RequestParam("pageSize") Integer pageSize) {
        logger.info("下传磅单,fmamClientDTO:[{}],lastSyncTime:[{}],pageNum:[{}],pageSize:[{}]", fmamClientDTO, lastSyncTime, pageNum, pageSize);
        PageData<SyncFmamWeighNoteDTO> pageData = fmamSyncDataService.getSyncWeighNoteDTO(fmamClientDTO, lastSyncTime, pageNum, pageSize);
        return ResultUtil.success(pageData);
    }

    @GetMapping(value = "sync/unit")
    public Result syncUnit(@RequestAttribute(FmamConstant.FMAM_CLIENT) FmamClientDTO fmamClientDTO) {
        logger.info("同步材料计量单位,fmamClientDTO:[{}]", fmamClientDTO);
        ListData<SyncFmamUnitDTO> listData = fmamSyncDataService.getSyncUnitDTO();
        return ResultUtil.success(listData);
    }
}

package com.whfc.openapi.api.fmam.service;

import com.whfc.fmam.entity.dto.FmamClientDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/19 11:40
 */
public interface FmamSyncDataLocalService {

    /**
     * 上传磅单图片
     *
     * @param fmamClientDTO
     * @param guid
     * @param imgType
     * @param images
     * @param genType
     * @param genTime
     */
    void uploadWeighNoteImage(FmamClientDTO fmamClientDTO, String guid, Integer imgType, MultipartFile[] images, Integer genType, Date genTime);
}

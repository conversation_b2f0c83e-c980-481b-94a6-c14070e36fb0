package com.whfc.openapi.api.fmam;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.third.yjbh.station.YhbhProduce;
import com.whfc.common.third.yjbh.station.YjbhDosage;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.DateUtil;
import com.whfc.fmam.entity.dto.FmamConcreteStationDosageDTO;
import com.whfc.fmam.entity.dto.FmamConcreteStationPieceDTO;
import com.whfc.fmam.entity.dto.FmamConcreteStationProduceDTO;
import com.whfc.fmam.service.FmamStationService;
import com.whfc.fuum.dto.open.OpenApiClientDTO;
import com.whfc.openapi.constant.OpenApiConst;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 引江补汉-拌合站-数据推送接口
 */
@RestController
@RequestMapping("/open/api/bhz")
public class YjbhFmamBhzController {

    private Logger logger = LoggerFactory.getLogger(YjbhFmamBhzController.class);

    @DubboReference(interfaceClass = FmamStationService.class, version = "1.0.0")
    private FmamStationService fmamStationService;

    @PostMapping("yjbh/data")
    public Result bhzData(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client, @RequestBody String body) {
        logger.info("引江补汉-拌合站-数据推送接口,deptId:{},body:{}", client.getDeptId(), body);

        try {
            List<YhbhProduce> produceList = JSONObject.parseArray(body, YhbhProduce.class);
            if (produceList != null && produceList.size() > 0) {
                List<FmamConcreteStationProduceDTO> produceDTOList = new ArrayList<>(produceList.size());
                for (YhbhProduce produce : produceList) {
                    String produceGuid = produce.getProdNo();
                    if (StringUtils.isBlank(produceGuid)) {
                        produceGuid = DateUtil.formatDate(produce.getStartTime(), "yyyyMMddHHmmss");
                        produce.setProdNo(produceGuid);
                    }
                    //消耗按批次分组
                    produce.getDetail().stream().forEach(dosage -> {
                        if (StringUtils.isEmpty(dosage.getBatchId())) {
                            dosage.setBatchId("1");
                        }
                    });
                    if (produce.getPlanAmount() == null) {
                        produce.setPlanAmount(produce.getAmount());
                    }
                    Map<String, List<YjbhDosage>> dosagesMap = CollectionUtil.groupBy(produce.getDetail(), YjbhDosage::getBatchId);
                    List<FmamConcreteStationPieceDTO> pieceList = new ArrayList<>(dosagesMap.size());
                    for (Map.Entry<String, List<YjbhDosage>> entry : dosagesMap.entrySet()) {
                        String batchId = entry.getKey();
                        String pieceGuid = produce.getProdNo() + entry.getKey();
                        List<FmamConcreteStationDosageDTO> dosageDTOList = new ArrayList<>(dosagesMap.size());
                        for (YjbhDosage dosage : entry.getValue()) {
                            FmamConcreteStationDosageDTO dosageDTO = new FmamConcreteStationDosageDTO();
                            dosageDTO.setPieceGuid(pieceGuid);
                            dosageDTO.setMatType(dosage.getMaterialType());
                            dosageDTO.setMatName(dosage.getMaterialName());
                            dosageDTO.setPlanamnt(new BigDecimal(String.valueOf(dosage.getRatedMaterial())));
                            dosageDTO.setFactamnt(new BigDecimal(String.valueOf(dosage.getActMaterial())));
                            dosageDTO.setTime(produce.getCompleteTime());
                            dosageDTOList.add(dosageDTO);
                        }
                        FmamConcreteStationPieceDTO pieceDTO = new FmamConcreteStationPieceDTO();
                        pieceDTO.setProduceGuid(produceGuid);
                        pieceDTO.setGuid(pieceGuid);
                        pieceDTO.setBatchId(batchId);
                        pieceDTO.setDosageList(dosageDTOList);
                        pieceDTO.setDatetime(produce.getCompleteTime());
                        pieceDTO.setPieamnt(new BigDecimal(String.valueOf(produce.getAmount())));
                        pieceList.add(pieceDTO);
                    }
                    FmamConcreteStationProduceDTO produceDTO = new FmamConcreteStationProduceDTO();
                    produceDTO.setDeviceName(produce.getDeviceName());
                    produceDTO.setGuid(produceGuid);
                    produceDTO.setCode(produce.getProdNo());
                    produceDTO.setTime(produce.getStartTime());
                    produceDTO.setStartTime(produce.getStartTime());
                    produceDTO.setEndTime(produce.getCompleteTime());
                    produceDTO.setPlanmete(new BigDecimal(String.valueOf(produce.getPlanAmount())));
                    produceDTO.setProdmete(new BigDecimal(String.valueOf(produce.getAmount())));
                    produceDTO.setGrade(produce.getGrdOfConcrete());
                    produceDTO.setVehicle(produce.getPlateNumber());
                    produceDTO.setConsposCode(produce.getEngineering());
                    produceDTO.setConspos(produce.getPouringObject());
                    produceDTO.setPieceList(pieceList);

                    produceDTOList.add(produceDTO);
                }

                //同步拌合站数据
                fmamStationService.addProduce(client.getDeptId(), produceDTOList);
            }
        } catch (Exception ex) {
            logger.error("引江补汉-拌合站-数据推送接口异常", ex);
        }
        return ResultUtil.success();
    }

    @GetMapping("yjbh/time")
    public Result getBhTime(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        Integer deptId = client.getDeptId();
        logger.info("查询拌合同步时间,deptId:{}", deptId);
        Date syncTime = fmamStationService.getSyncTime(deptId);
        JSONObject data = new JSONObject();
        data.put("time", syncTime);
        return ResultUtil.success(data);
    }
}

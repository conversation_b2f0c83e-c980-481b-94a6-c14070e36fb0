package com.whfc.openapi.api.bim;

import com.whfc.uni.service.BimService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Optional;

/**
 * @Description:bimface回调接口
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-10 17:35
 */
@RestController
@RequestMapping("/open/api/bim/callback")
public class BimFaceCallbackController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = BimService.class, version = "1.0.0")
    private BimService bimService;

    @GetMapping("anon")
    public String bimfaceCallback(HttpServletRequest request,
                                  @RequestParam(value = "fileId", required = false) String fileId,
                                  @RequestParam(value = "integrateId", required = false) String integrateId,
                                  @RequestParam(value = "compareId", required = false) String compareId,
                                  @RequestParam(value = "sceneId", required = false) String sceneId,
                                  @RequestParam("status") String status,
                                  @RequestParam("thumbnail") String thumbnail,
                                  @RequestParam("reason") String reason,
                                  @RequestParam("signature") String signature,
                                  @RequestParam("nonce") String nonce) {
        logger.info("bimface回调,{}|{}|{}|{}", fileId, status, thumbnail, reason);

        if (StringUtils.isNotBlank(fileId)) {

            //取第一张缩略图
            Optional<String> cover = Arrays.stream(thumbnail.split(","))
                    .map(str -> str.startsWith("https") ? str : "https://m.bimface.com/" + str)
                    .findFirst();
            bimService.updateTranslateStatus(fileId, status, reason, cover.get());
        }

        return "success";
    }
}

package com.whfc.openapi.api.fmam;

import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.fmam.entity.dto.FmamAccessTokenDTO;
import com.whfc.fmam.entity.qo.AccessTokenQO;
import com.whfc.fmam.entity.qo.LicenseActiveQO;
import com.whfc.fmam.service.FmamLicenseService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/14 19:33
 */
@RestController
@RequestMapping("/open/api/fmam")
public class FmamSyncAuthController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = FmamLicenseService.class, version = "1.0.0")
    private FmamLicenseService fmamLicenseService;

    @PostMapping("license/active")
    public Result licenseActive(@RequestBody @Validated LicenseActiveQO licenseActiveQO) {
        logger.info("授权文件激活,licenseActiveQO:[{}]", licenseActiveQO);
        fmamLicenseService.licenseActive(licenseActiveQO);
        return ResultUtil.success();
    }

    @PostMapping("accessToken")
    public Result accessToken(@RequestBody @Validated AccessTokenQO accessTokenQO) {
        logger.info("获取accessToken,accessTokenQO:[{}]", accessTokenQO);
        FmamAccessTokenDTO fmamAccessTokenDTO = fmamLicenseService.getAccessTokenDTO(accessTokenQO);
        return ResultUtil.success(fmamAccessTokenDTO);
    }

}

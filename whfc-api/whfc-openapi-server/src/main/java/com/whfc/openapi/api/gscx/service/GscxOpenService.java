package com.whfc.openapi.api.gscx.service;

import com.whfc.common.exception.BizException;
import com.whfc.mach.dto.OpenApiMachDTO;
import com.whfc.mach.dto.obd.ObdFrameItemDTO;
import com.whfc.mach.dto.obd.ObdParamDTO;
import com.whfc.mach.dto.obd.ObdQyjData;
import com.whfc.mach.service.AppMachService;
import com.whfc.mach.service.ObdDataService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/30 11:36
 */
@Service
public class GscxOpenService {

    @DubboReference(interfaceClass = AppMachService.class, version = "1.0.0")
    private AppMachService appMachService;

    @DubboReference(interfaceClass = ObdDataService.class, version = "1.0.0")
    private ObdDataService obdDataService;

    /**
     * 查询设备列表
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    public List<OpenApiMachDTO> qyjList(Integer deptId) throws BizException {
        List<OpenApiMachDTO> list = appMachService.openApiMachList(deptId);
        return list.stream().filter(dto -> "牵引机".equals(dto.getMachTypeName())).collect(Collectors.toList());
    }

    /**
     * 查询设备实时数据
     *
     * @param deptId
     * @param machId
     * @throws BizException
     */
    public ObdQyjData qyjDetail(Integer deptId, Integer machId) throws BizException {
        boolean check = appMachService.validDeptMachId(deptId, machId);
        if (check) {
            ObdParamDTO paramDTO = obdDataService.getParam(machId);
            if (paramDTO != null) {
                ObdFrameItemDTO frameItemDTO = paramDTO.getFrameItemDTO();
                ObdQyjData data = new ObdQyjData();
                data.setMachId(machId);
                data.setTime(paramDTO.getTime());
                data.setWorkState(paramDTO.getWorkState());
                data.setForce1(frameItemDTO.getForce1());
                data.setSpeed1(frameItemDTO.getSpeed1());
                data.setLength1(frameItemDTO.getLength1());
                data.setFanWork(frameItemDTO.getDisplay_fan());
                data.setEngineOilTemperature(frameItemDTO.getEngine_oil_temperature());
                data.setMainPressue1(frameItemDTO.getMain_pressue1());
                return data;
            }
        }
        return null;
    }
}

package com.whfc.openapi.api.mon.controller;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.fuum.dto.open.OpenApiClientDTO;
import com.whfc.openapi.constant.OpenApiConst;
import com.whfc.uni.dto.settlement.SettlementPointLogDTO;
import com.whfc.uni.dto.settlement.SettlementPointLogSyncDataDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 深基坑-沉降位移
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/9 17:16
 */
@RestController
@RequestMapping("/open/api/v1/settlement")
public class SettlementSyncController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AmqpTemplate amqpTemplate;

    @PostMapping("/sync/data")
    public Result dataAdd(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO openApiClientDTO,
                          @RequestBody String data) {
        logger.info("开放平台同步沉降位移数据");
        List<SettlementPointLogDTO> list = JSON.parseArray(data, SettlementPointLogDTO.class);
        logger.info("沉降位移数据同步数量:{}", list.size());
        if (!list.isEmpty()) {
            SettlementPointLogSyncDataDTO syncDataDTO = new SettlementPointLogSyncDataDTO();
            syncDataDTO.setDeptId(openApiClientDTO.getDeptId());
            syncDataDTO.setList(list);
            amqpTemplate.convertAndSend(QueueConst.MON_SETTLEMENT_DATA, JSON.toJSONString(syncDataDTO));
        }
        return ResultUtil.success();
    }

}

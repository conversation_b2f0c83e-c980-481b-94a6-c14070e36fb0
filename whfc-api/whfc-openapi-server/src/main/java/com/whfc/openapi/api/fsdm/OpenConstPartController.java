package com.whfc.openapi.api.fsdm;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.whfc.common.base.BaseController;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.fuum.dto.open.OpenApiClientDTO;
import com.whfc.openapi.constant.OpenApiConst;
import com.whfc.uni.dto.constlog.ConstPartDTO;
import com.whfc.uni.service.ConstPartService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-29 11:45
 * @description: 施工日报
 */
@RestController
@RequestMapping("/open/api/const/part")
public class OpenConstPartController extends BaseController {

    @DubboReference(interfaceClass = ConstPartService.class, version = "1.0.0")
    private ConstPartService constPartService;

    @GetMapping(value = "tree")
    public Result tree(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        Integer deptId = client.getDeptId();
        logger.info("获取施工部位树，deptId:{}", deptId);
        List<ConstPartDTO> list = constPartService.list(deptId, null, null);
        List<Tree<String>> treeList = buildPartTree(list);
        return ResultUtil.success(treeList);
    }

    @GetMapping(value = "list")
    public Result list(@RequestAttribute(OpenApiConst.OPENAPI_CLIENT) OpenApiClientDTO client) {
        Integer deptId = client.getDeptId();
        logger.info("获取施工部位列表，deptId:{},", deptId);
        List<ConstPartDTO> list = constPartService.list(deptId, null, null);
        return ResultUtil.success(list);
    }

    public List<Tree<String>> buildPartTree(List<ConstPartDTO> constPartList) {
        // 生成bim构件树
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("pid");
        treeNodeConfig.setNameKey("name");

        //转换器
        return TreeUtil.build(constPartList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId().toString());
                    tree.setParentId(treeNode.getPid().toString());
                    tree.setName(treeNode.getName());
                    tree.putExtra("guid", treeNode.getGuid());
                    tree.putExtra("code", treeNode.getCode());
                    tree.putExtra("type", treeNode.getType());
                    tree.putExtra("quantity", treeNode.getQuantity());
                    tree.putExtra("unit", treeNode.getUnit());
                    tree.putExtra("status", treeNode.getStatus());
                    tree.putExtra("isSelect", treeNode.getIsSelected());
                });
    }
}

package com.whfc.ms.api.fcm;

import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.SessionAttr;
import com.whfc.fcm.dto.WorkPermitDTO;
import com.whfc.fcm.dto.WorkPermitDetailDTO;
import com.whfc.fcm.param.WorkPermitAdd;
import com.whfc.fcm.param.WorkPermitEdit;
import com.whfc.fcm.param.WorkPermitQuery;
import com.whfc.fcm.param.WorkPermitStateTransition;
import com.whfc.fcm.service.WorkPermitService;
import com.whfc.fuum.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;

/**
 * 作业票控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Tag(name = "作业票管理", description = "作业票相关接口")
@RestController
@RequestMapping("/ms/api/fcm/work-permit")
public class WorkPermitController {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitController.class);

    @DubboReference
    private WorkPermitService workPermitService;

    @Operation(summary = "分页查询作业票列表")
    @PostMapping("/list")
    public Result<PageData<WorkPermitDTO>> getWorkPermitList(
            @RequestBody @Valid WorkPermitQuery query,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        logger.info("分页查询作业票列表, query: {}, pageNum: {}, pageSize: {}", query, pageNum, pageSize);

        PageData<WorkPermitDTO> result = workPermitService.getWorkPermitList(query, pageNum, pageSize);
        return ResultUtil.success(result);
    }

    @Operation(summary = "查询作业票列表")
    @PostMapping("/list/all")
    public Result<List<WorkPermitDTO>> getAllWorkPermitList(@RequestBody @Valid WorkPermitQuery query) {
        logger.info("查询作业票列表, query: {}", query);

        List<WorkPermitDTO> result = workPermitService.getWorkPermitList(query);
        return ResultUtil.success(result);
    }

    @Operation(summary = "获取作业票详情")
    @GetMapping("/detail")
    public Result<WorkPermitDetailDTO> getWorkPermitDetail(
            @Parameter(description = "作业票GUID") @RequestParam String guid) {
        logger.info("获取作业票详情, guid: {}", guid);

        WorkPermitDetailDTO result = workPermitService.getWorkPermitDetail(guid);
        return ResultUtil.success(result);
    }

    @Operation(summary = "新增作业票")
    @PostMapping("/add")
    public Result<String> addWorkPermit(@RequestBody @Valid WorkPermitAdd param, HttpSession session) {
        logger.info("新增作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setApplicantUserId(user.getId());
            param.setApplicantUserName(user.getNickname());
        }

        String guid = workPermitService.addWorkPermit(param);
        return ResultUtil.success(guid);
    }

    @Operation(summary = "编辑作业票")
    @PostMapping("/edit")
    public Result<Void> editWorkPermit(@RequestBody @Valid WorkPermitEdit param) {
        logger.info("编辑作业票, param: {}", param);

        workPermitService.editWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "删除作业票")
    @DeleteMapping("/delete")
    public Result<Void> deleteWorkPermit(@Parameter(description = "作业票GUID") @RequestParam String guid) {
        logger.info("删除作业票, guid: {}", guid);

        workPermitService.deleteWorkPermit(guid);
        return ResultUtil.success();
    }

    @Operation(summary = "提交作业票")
    @PostMapping("/submit")
    public Result<Void> submitWorkPermit(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("提交作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.submitWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "签发作业票")
    @PostMapping("/issue")
    public Result<Void> issueWorkPermit(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("签发作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.issueWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "打回作业票")
    @PostMapping("/reject")
    public Result<Void> rejectWorkPermit(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("打回作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.rejectWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "重新提交作业票")
    @PostMapping("/resubmit")
    public Result<Void> resubmitWorkPermit(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("重新提交作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.resubmitWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "开始作业")
    @PostMapping("/start-work")
    public Result<Void> startWork(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("开始作业, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.startWork(param);
        return ResultUtil.success();
    }

    @Operation(summary = "结束作业")
    @PostMapping("/end-work")
    public Result<Void> endWork(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("结束作业, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.endWork(param);
        return ResultUtil.success();
    }

    @Operation(summary = "关闭作业票")
    @PostMapping("/close")
    public Result<Void> closeWorkPermit(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("关闭作业票, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.closeWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "状态转换")
    @PostMapping("/transition")
    public Result<Void> transitionState(@RequestBody @Valid WorkPermitStateTransition param, HttpSession session) {
        logger.info("状态转换, param: {}", param);

        SysUser user = (SysUser) session.getAttribute(SessionAttr.MS_USER);
        if (user != null) {
            param.setOpUserId(user.getId());
            param.setOpUserName(user.getNickname());
        }

        workPermitService.transitionState(param);
        return ResultUtil.success();
    }

    @Operation(summary = "检查状态转换是否合法")
    @GetMapping("/check-transition")
    public Result<Boolean> checkStateTransition(
            @Parameter(description = "作业票GUID") @RequestParam String guid,
            @Parameter(description = "目标状态") @RequestParam Integer toState) {
        logger.info("检查状态转换是否合法, guid: {}, toState: {}", guid, toState);

        boolean result = workPermitService.checkStateTransition(guid, toState);
        return ResultUtil.success(result);
    }
}

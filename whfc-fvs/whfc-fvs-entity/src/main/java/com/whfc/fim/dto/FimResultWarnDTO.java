package com.whfc.fim.dto;

import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-09 17:32
 */
@Data
public class FimResultWarnDTO implements Serializable {

    /**
     * 报警记录ID
     */
    private Integer resultId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 监控设备ID
     */
    private Integer deviceId;

    /**
     * 报警类型
     */
    private Integer algCheckType;

    /**
     * 报警类型名称
     */
    private String algCheckTypeName;

    /**
     * 报警时间
     */
    private Date time;

    /**
     * 查找消息接收人信息
     */
    List<AppMsgToUserDTO> userDtoList;

    /**
     * 查找消息接收方式
     */
    List<Integer> msgChannelList;

}

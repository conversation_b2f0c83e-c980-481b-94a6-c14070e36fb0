package com.whfc.fim.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 报警记录分析DTO
 * @author: qzexing
 * @version: 1.0
 * @date: 2020/3/19 11:55
 */
@Data
public class FimResultAnalysisDTO implements Serializable {

    /**
     * 报警总数
     */
    private Integer total;

    /**
     * 报警类型统计
     */
    private List<FimAlgCheckTypeCountDTO> algCheckTypeCountList;

    /**
     * 报警频次
     */
    private List<FimResultRateContDTO> resultRateCountList;

}

package com.whfc.fim.entity;

import java.util.Date;

/**
 * 智能检测结果接收方式
* <AUTHOR> qzexing
* @version : 1.0
* @date : 2020-09-08 18:38
*/
public class FimWarnRuleChannel {
    private Integer id;

    /**
    * 智能检测规则id（表fim_warn_rule中id
    */
    private Integer warnRuleId;

    /**
    * 接收方式 1-小程序 2-公众号 3-后台 4-短信
    */
    private Integer msgchannel;

    /**
    * 删除标记 0-未删除 1-已删除
    */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getWarnRuleId() {
        return warnRuleId;
    }

    public void setWarnRuleId(Integer warnRuleId) {
        this.warnRuleId = warnRuleId;
    }

    public Integer getMsgchannel() {
        return msgchannel;
    }

    public void setMsgchannel(Integer msgchannel) {
        this.msgchannel = msgchannel;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.whfc.fim.dto;

import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 消息接收人 和接收方式实体
 * @date 2020-07-18
 */
@Data
public class FimWarnMsgRecDTO implements Serializable {
    /**
     * 接收方式 1-小程序 2-公众号 3-后台 4-短信
     */
    private List<Integer> msgChannelList;

    /**
     * 接收消息的人
     */
    private List<AppMsgToUserDTO> userList;
}

package com.whfc.fim.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: qzexing
 * @version: 1.0
 * @date: 2020/3/17 20:48
 */
@Data
public class FimResultDTO implements Serializable {

    /**
     * 报警记录ID
     */
    private Integer resultId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 报警类型
     */
    private Integer algCheckType;

    /**
     * 报警类型名称
     */
    private String algCheckTypeName;

    /**
     * 报警时间
     */
    private Date time;

    /**
     * 处理状态
     */
    private Integer handleStatus;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 处理备注
     */
    private String handleRemark;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理人员ID
     */
    private Integer handleUserId;

    /**
     * 处理人员姓名
     */
    private String handleUser;

    /**
     * 报警记录图片地址
     */
    private String snapshotUrl;

    /**
     * 报警记录视频地址
     */
    private String videoUrl;

    /**
     * 报警记录图片地址列表
     */
    private List<String> resultImgUrlList;

    /**
     * 人员姓名列表
     */
    @Deprecated
    private List<String> personNameList;
}

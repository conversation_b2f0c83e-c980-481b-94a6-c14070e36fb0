package com.whfc.fim.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-04
 */
@Data
public class FimWarnCacheDTO implements Serializable {
    /**
     * 最近报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 一个小时的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 一天报警次数累计
     */
    private Integer dayWarnNo;

    /**
     * 最近一小时报警次数累计
     */
    private Integer hourWarnNo;


}

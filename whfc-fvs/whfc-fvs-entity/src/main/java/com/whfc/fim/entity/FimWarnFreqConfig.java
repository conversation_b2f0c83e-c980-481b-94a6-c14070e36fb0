package com.whfc.fim.entity;

import java.util.Date;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-10-09 15:16
 */

/**
 * 报警频率配置
 */
public class FimWarnFreqConfig {
    private Integer id;

    /**
     * 项目id
     */
    private Integer deptId;

    /**
     * 每天最大次数
     */
    private Integer frequencyLimitDay;

    /**
     * 每小时最大次数
     */
    private Integer frequencyLimitHour;

    /**
     * 报警时间间隔（分钟）
     */
    private Integer frequencyLimitInterval;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getFrequencyLimitDay() {
        return frequencyLimitDay;
    }

    public void setFrequencyLimitDay(Integer frequencyLimitDay) {
        this.frequencyLimitDay = frequencyLimitDay;
    }

    public Integer getFrequencyLimitHour() {
        return frequencyLimitHour;
    }

    public void setFrequencyLimitHour(Integer frequencyLimitHour) {
        this.frequencyLimitHour = frequencyLimitHour;
    }

    public Integer getFrequencyLimitInterval() {
        return frequencyLimitInterval;
    }

    public void setFrequencyLimitInterval(Integer frequencyLimitInterval) {
        this.frequencyLimitInterval = frequencyLimitInterval;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.whfc.fvs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-25 18:08
 */
@Data
@Schema(description = "抓拍时间配置")
public class FvsSnapshotConfigDTO implements Serializable {

    @Schema(description = "延时摄影开关  0-关 1-开")
    private Integer tlpState;

    @Schema(description = "抓拍时间列表")
    private List<String> timeList;

}

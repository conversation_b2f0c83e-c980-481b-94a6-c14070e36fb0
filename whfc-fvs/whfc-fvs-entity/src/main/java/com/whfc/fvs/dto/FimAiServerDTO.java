package com.whfc.fvs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/18
 */
@Schema(description = "AI服务器")
@Data
public class FimAiServerDTO implements Serializable {

    @Schema(description = "主键ID")
    private Integer id;

    /**
     * GUID
     */
    @Schema(description = "GUID")
    private String guid;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String name;

    /**
     * 平台
     */
    @Schema(description = "平台")
    private String platform;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String sn;

    /**
     * 安装位置
     */
    @Schema(description = "安装位置")
    private String address;

    /**
     * 算法数量
     */
    @Schema(description = "算法数量")
    private Integer algNum;

    /**
     * 网络状态 0-离线 1-在线
     */
    @Schema(description = "网络状态 0-离线 1-在线")
    private Integer netState;
}

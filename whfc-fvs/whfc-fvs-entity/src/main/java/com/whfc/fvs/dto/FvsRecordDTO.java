package com.whfc.fvs.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@ToString
public class FvsRecordDTO implements Serializable {
    /**
     * 设备记录ID
     */
    private Integer id;
    /**
     * 设备ID
     */
    private Integer deviceId;
    /**
     * 记录开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 记录结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 记录地址
     */
    private String recordUrl;
    /**
     * 记录类型
     */
    private String fileFormat;
}

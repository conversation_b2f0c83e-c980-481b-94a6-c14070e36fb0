package com.whfc.fvs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/8 10:08
 */
@Schema(description = "视频监控Token")
@Data
public class FvsTokenDTO implements Serializable {

    /**
     * 平台
     */
    @Schema(description = "平台")
    private Integer platform;

    /**
     * token
     */
    @Schema(description = "token")
    private String token;

    /**
     * appKey
     */
    @Schema(description = "appKey")
    private String appKey;

    /**
     * appSecret
     */
    @Schema(description = "appSecret")
    private String appSecret;

    /**
     * ip
     */
    @Schema(description = "ip")
    private String ip;

    /**
     * port
     */
    @Schema(description = "端口")
    private String port;

    /**
     * key
     */
    @Schema(description = "key")
    private String key;

}

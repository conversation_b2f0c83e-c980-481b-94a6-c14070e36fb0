package com.whfc.fvs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 视频监控-抓拍
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-24 12:00
 */
@Schema(description = "视频监控-抓拍")
@Data
public class FvsSnapshotDTO implements Serializable {

    @Schema(description = "抓拍设备ID")
    private String deviceId;
    /**
     * 抓拍设备名称
     */
    @Schema(description = "抓拍设备名称")
    private String deviceName;
    /**
     * 抓拍图片地址
     */
    @Schema(description = "抓拍图片地址")
    private String snapshotUrl;
    /**
     * 抓拍时间
     */
    @Schema(description = "抓拍时间")
    private Date time;

    @Schema(description = "抓拍列表")
    private List<FvsSnapshotDTO> snapshotList;

    @Schema(description = "视频地址")
    private String videoUrl;

    @Schema(description = "日期")
    private Date date;
}
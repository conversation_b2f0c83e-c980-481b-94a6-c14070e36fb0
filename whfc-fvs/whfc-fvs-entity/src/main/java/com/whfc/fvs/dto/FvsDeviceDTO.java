package com.whfc.fvs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ToString
@Schema(description = "视频监控设备信息")
public class FvsDeviceDTO implements Serializable {

    /**
     * ID
     */
    @Schema(description =  "ID")
    private Integer id;

    /**
     * PID
     */
    @Schema(description =  "PID")
    private Integer pid;

    /**
     * 组织机构ID
     */
    @Schema(description =  "组织机构ID")
    private Integer deptId;

    /**
     * 组织机构名称
     */
    @Schema(description =  "组织机构名称")
    private String deptName;

    /**
     * 分组id
     */
    @Schema(description =  "分组id")
    private Integer groupId;


    /**
     * 分组名称
     */
    @Schema(description =  "分组名称")
    private String groupName;

    /**
     * 唯一编码
     */
    @Schema(description =  "唯一编码")
    private String guid;

    /**
     * 设备名称
     */
    @Schema(description =  "设备名称")
    private String name;

    /**
     * 设备厂商
     */
    @Schema(description =  "设备厂商")
    private String vendor;

    /**
     * 通道号
     */
    @Schema(description =  "通道号")
    private Integer channelNo;

    /**
     * 设备平台:0-海康萤石，1-大华乐橙，2-阿里云RTMP，3-阿里云GB28181，4-自定义视频监控
     */
    @Schema(description =  "设备平台:0-海康萤石，1-大华乐橙，2-阿里云RTMP，3-阿里云GB28181，4-自定义视频监控")
    private String platform;

    /**
     * 设备状态:0-离线 1-在线
     */
    @Schema(description =  "设备状态:0-离线 1-在线")
    private Integer deviceStatus;

    /**
     * 流状态:0-离线 1-在线
     */
    @Schema(description =  "流状态:0-离线 1-在线")
    private Integer streamStatus;

    /**
     * 截图地址
     */
    @Schema(description =  "截图地址")
    private String snapshotUrl;

    /**
     * 阿里云设备ID
     */
    @Schema(description =  "阿里云设备ID")
    private String aliDeviceId;

    /**
     * 阿里云流ID
     */
    @Schema(description =  "阿里云流ID")
    private String aliStreamId;

    /**
     * 国标ID
     */
    @Schema(description =  "国标ID")
    private String gbId;

    /**
     * 用户名
     */
    @Schema(description =  "用户名")
    private String username;

    /**
     * 密码
     */
    @Schema(description =  "密码")
    private String password;

    /**
     * 设备类型：0-网络摄像头，1-硬盘录像机
     */
    @Schema(description =  "设备类型：0-网络摄像头，1-硬盘录像机")
    private Integer deviceType;

    /**
     * 是否支持云台  0-不支持  1-支持
     */
    @Schema(description =  "是否支持云台  0-不支持  1-支持")
    private Integer ptz;

    /**
     * 播放地址
     */
    @Schema(description =  "播放地址")
    private String playUrl;

    /**
     * 视频录像机通道设备
     */
    @Schema(description =  "视频录像机通道设备")
    private List<FvsDeviceDTO> children;

    /**
     * 播放模式:1-直播模式 2-监控模式
     */
    @Schema(description =  "播放模式:1-直播模式 2-监控模式")
    private Integer playMode;

    /**
     * 播放流模式 1-标清  2-高清
     *
     * @see com.whfc.fvs.enums.FvsDeviceStreamMode
     */
    @Schema(description =  "播放流模式 1-标清  2-高清")
    private Integer streamMode;

    /**
     * 品牌型号
     */
    @Schema(description =  "品牌型号")
    private String model;

    /**
     * 尺寸大小
     */
    @Schema(description =  "尺寸大小")
    private String size;

    /**
     * 摄像头类型 1-球机,2-枪机,3-鹰眼摄像机,4-其他
     */
    @Schema(description =  "摄像头类型 1-球机,2-枪机,3-鹰眼摄像机,4-其他 5-执法记录仪")
    private Integer webcamType;

    /**
     * 经度
     */
    @Schema(description =  "经度")
    private Double lng;


    /**
     * 纬度
     */
    @Schema(description =  "纬度")
    private Double lat;

    /**
     * 地址
     */
    @Schema(description =  "地址")
    private String location;

    private String localIp;

    private String localPort;

    private String localUser;

    private String localPwd;

    /**
     * 排序字段
     */
    @Schema(description =  "排序字段")
    private Integer idx;

    @Schema(description =  "是否隐藏 0-否 1-是")
    private Integer hiddenFlag;

    /**
     * 更新时间
     */
    @Schema(description =  "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description =  "创建时间")
    private Date createTime;


}

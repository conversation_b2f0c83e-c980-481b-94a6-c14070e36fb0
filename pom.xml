<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <groupId>com.whfc</groupId>
    <artifactId>whfc-server-dubbo</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>whfc-api</module>
        <module>whfc-common</module>
        <module>whfc-base</module>
        <module>whfc-fmam</module>
        <module>whfc-fuum</module>
        <module>whfc-mach</module>
        <module>whfc-emp</module>
        <module>whfc-env</module>
        <module>whfc-fvs</module>
        <module>whfc-fse</module>
        <module>whfc-fcm</module>
        <module>whfc-doc</module>
        <module>whfc-conf</module>
        <module>whfc-uni</module>
    </modules>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <shiro.version>1.4.0</shiro.version>
        <auth0.jwt.version>3.4.0</auth0.jwt.version>
        <jsonwebtoken.jjwt.version>0.9.1</jsonwebtoken.jjwt.version>
        <page.helper.starter.version>2.1.0</page.helper.starter.version>
        <page.helper.version>5.1.4</page.helper.version>
        <poi.version>4.1.2</poi.version>
        <aliyun.sdk.core>4.1.0</aliyun.sdk.core>
        <aliyun.sdk.sms>1.1.0</aliyun.sdk.sms>
        <aliyun.sdk.oss>3.10.2</aliyun.sdk.oss>
        <aliyun.loghub-client>0.6.16</aliyun.loghub-client>
        <mysql.connector.version>5.1.46</mysql.connector.version>
        <alibaba.druid.version>1.0.9</alibaba.druid.version>
        <alibaba.fastjson.version>1.2.83</alibaba.fastjson.version>
        <alibaba.dingtalk.version>1.0.1</alibaba.dingtalk.version>
        <mybatis.spring-boot.version>2.3.2</mybatis.spring-boot.version>
        <liquibase.version>3.10.3</liquibase.version>
        <jedis.version>2.9.0</jedis.version>
        <commons.pool2.version>2.11.1</commons.pool2.version>
        <commons.lang3.version>3.5</commons.lang3.version>
        <servlet.api.version>2.5</servlet.api.version>
        <hibernate.validator.version>6.0.18.Final</hibernate.validator.version>
        <google.guava.version>25.1-jre</google.guava.version>
        <google.protobuf.version>3.11.4</google.protobuf.version>
        <google.zxing.version>3.2.0</google.zxing.version>
        <slf4j.version>1.7.25</slf4j.version>
        <httpclient.version>4.5.5</httpclient.version>
        <dom4j.version>2.1.1</dom4j.version>
        <xstream.version>1.4.10</xstream.version>
        <weixin-java.version>3.4.0</weixin-java.version>
        <excel-util.version>1.2.1</excel-util.version>
        <redisson.version>3.8.2</redisson.version>
        <netty.version>4.1.112.Final</netty.version>
        <jackson.version>2.17.2</jackson.version>
        <ahocorasick.version>0.4.0</ahocorasick.version>
        <nacos-spring-boot.version>0.2.12</nacos-spring-boot.version>
        <dubbo-spring-boot.version>3.3.0</dubbo-spring-boot.version>
        <dubbo-registry-nacos.version>3.3.0</dubbo-registry-nacos.version>
        <dubbo.version>3.3.0</dubbo.version>
        <nacos.version>2.5.1</nacos.version>
        <alibaba-spring-context-support.version>1.0.10</alibaba-spring-context-support.version>
        <org.mapstruct.version>1.3.0.Final</org.mapstruct.version>
        <lombok.version>1.18.20</lombok.version>
        <junit.version>4.12</junit.version>
        <guava.version>20.0</guava.version>
        <influxdb-java.version>2.24</influxdb-java.version>
        <kotlin.stdlib.version>1.3.71</kotlin.stdlib.version>
        <ognl.version>3.1.12</ognl.version>
        <jsoup>1.13.1</jsoup>
        <xxl-job.version>2.3.0</xxl-job.version>
        <thumbnailator.version>0.4.14</thumbnailator.version>
        <aopalliance.version>1.0</aopalliance.version>
        <bcprov.version>1.60</bcprov.version>
        <google.aviator.version>2.0</google.aviator.version>
        <vividsolutions.jts.version>1.13</vividsolutions.jts.version>
        <minio.version>8.5.9</minio.version>
        <itextpdf-html2pdf>3.0.4</itextpdf-html2pdf>
        <itextpdf.version>5.5.11</itextpdf.version>
        <itextpdf-font-asian>7.1.16</itextpdf-font-asian>
        <pdfbox.version>2.0.24</pdfbox.version>
        <apache-ant>1.10.10</apache-ant>
        <freemarker.version>2.3.31</freemarker.version>
        <saucer-pdf.version>9.1.5</saucer-pdf.version>
        <asian.version>5.2.0</asian.version>
        <saucer.varsion>9.1.5</saucer.varsion>
        <easyexcel.version>3.0.5</easyexcel.version>
        <cglib.version>2.2</cglib.version>
        <mpxj.version>13.10.0</mpxj.version>
        <jaxb.verson>2.1</jaxb.verson>
        <jave.version>3.3.1</jave.version>
        <poi-tl.version>1.10.0</poi-tl.version>
        <paho-mqtt.version>1.2.5</paho-mqtt.version>
        <jod.version>4.4.7</jod.version>
        <hutool.version>5.8.25</hutool.version>
        <knife4j.version>4.5.0</knife4j.version>
        <tencentcloud.version>3.1.1281</tencentcloud.version>
        <spring-statemachine.version>3.2.1</spring-statemachine.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- knife4j -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--jwt-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${auth0.jwt.version}</version>
            </dependency>
            <!--shiro-spring -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <!--shiro-ehcache-->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-ehcache</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>${shiro.version}</version>
            </dependency>
            <!--pageHelper-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.helper.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${page.helper.version}</version>
            </dependency>
            <!-- poi-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!--validator-->
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <!--aliyun-sdk-core-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.sdk.core}</version>
            </dependency>
            <!--aliyun-sdk-sms-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun.sdk.sms}</version>
            </dependency>
            <!--aliyun-sdk-oss-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${alibaba.fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons.pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <!--mybatis-->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${google.zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${google.zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${google.protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>${servlet.api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.sargeraswang.util</groupId>
                <artifactId>excel-util</artifactId>
                <version>${excel-util.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.ahocorasick</groupId>
                <artifactId>ahocorasick</artifactId>
                <version>${ahocorasick.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${alibaba-spring-context-support.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.influxdb</groupId>
                <artifactId>influxdb-java</artifactId>
                <version>${influxdb-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.stdlib.version}</version>
            </dependency>
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>${ognl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup}</version>
            </dependency>
            <!-- 谷歌图片处理工具 -->
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>
            <dependency>
                <groupId>aopalliance</groupId>
                <artifactId>aopalliance</artifactId>
                <version>${aopalliance.version}</version>
            </dependency>
            <!-- java加密组件 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>
            <!-- java公式引擎 -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${google.aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vividsolutions</groupId>
                <artifactId>jts</artifactId>
                <version>${vividsolutions.jts.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <!--itext-兼容性-->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>${itextpdf-html2pdf}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>font-asian</artifactId>
                <version>${itextpdf-font-asian}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf.tool</groupId>
                <artifactId>xmlworker</artifactId>
                <version>${itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${apache-ant}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>${asian.version}</version>
            </dependency>
            <!-- Apache PDFBox -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version> <!-- 请使用最新的版本 -->
            </dependency>
            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>flying-saucer-pdf</artifactId>
                <version>${saucer.varsion}</version>
            </dependency>
            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>flying-saucer-pdf-itext5</artifactId>
                <version>${saucer.varsion}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.sf.mpxj</groupId>
                <artifactId>mpxj</artifactId>
                <version>${mpxj.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.verson}</version>
            </dependency>
            <dependency>
                <groupId>ws.schild</groupId>
                <artifactId>jave-all-deps</artifactId>
                <version>${jave.version}</version>
            </dependency>
            <dependency>
                <groupId>ws.schild</groupId>
                <artifactId>jave-core</artifactId>
                <version>${jave.version}</version>
            </dependency>
            <dependency>
                <groupId>ws.schild</groupId>
                <artifactId>jave-nativebin-win64</artifactId>
                <version>${jave.version}</version>
            </dependency>
            <dependency>
                <groupId>ws.schild</groupId>
                <artifactId>jave-nativebin-linux64</artifactId>
                <version>${jave.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${paho-mqtt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-local-lo</artifactId>
                <version>${jod.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencentcloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>4.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>
            <!-- Spring State Machine -->
            <dependency>
                <groupId>org.springframework.statemachine</groupId>
                <artifactId>spring-statemachine-core</artifactId>
                <version>${spring-statemachine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.statemachine</groupId>
                <artifactId>spring-statemachine-starter</artifactId>
                <version>${spring-statemachine.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!--slf4j-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <!--junit-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>${cglib.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- skipTests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.16</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
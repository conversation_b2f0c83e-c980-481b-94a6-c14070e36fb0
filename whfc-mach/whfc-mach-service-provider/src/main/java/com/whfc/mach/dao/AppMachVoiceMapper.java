package com.whfc.mach.dao;

import com.whfc.mach.dto.AppMachVoiceDTO;
import com.whfc.mach.entity.AppMachVoice;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppMachVoiceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachVoice record);

    int insertSelective(AppMachVoice record);

    AppMachVoice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachVoice record);

    int updateByPrimaryKey(AppMachVoice record);

    /**
     * 查询设备录音列表
     *
     * @param machId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppMachVoiceDTO> selectVoiceListByMachId(@Param("machId") Integer machId,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);

    /**
     * 删除设备录音
     *
     * @param id
     * @return
     */
    int logicDeleteById(@Param("id") Integer id);
}
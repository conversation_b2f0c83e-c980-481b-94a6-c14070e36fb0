package com.whfc.mach.dao;

import com.whfc.mach.dto.MachFileDTO;
import com.whfc.mach.entity.AppMachFile;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppMachFileMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachFile record);

    int insertSelective(AppMachFile record);

    AppMachFile selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachFile record);

    int updateByPrimaryKey(AppMachFile record);

    /**
     * 根据设备id查找
     *
     * @param machId
     * @return
     */
    List<MachFileDTO> selectByMachId(@Param("machId") Integer machId);

    /**
     * 修改名称
     *
     * @param fileId
     * @param name
     */
    void updateName(@Param("fileId") Integer fileId,
                    @Param("name") String name);

    /**
     * 逻辑删除
     *
     * @param fileId
     */
    void deleteLogic(@Param("fileId") Integer fileId);
}
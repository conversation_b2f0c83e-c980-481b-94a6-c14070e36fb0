package com.whfc.mach.dao;

import com.whfc.mach.dto.maintain.MaintainDTO;
import com.whfc.mach.entity.AppMachMaintain;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AppMachMaintainMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachMaintain record);

    int insertSelective(AppMachMaintain record);

    AppMachMaintain selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachMaintain record);

    int updateByPrimaryKey(AppMachMaintain record);

    /**
     * 根据组织机构查找
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @param keyword
     * @return
     */
    List<MaintainDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                     @Param("startDate") Date startDate,
                                     @Param("endDate") Date endDate,
                                     @Param("keyword") String keyword);

    /**
     * 根据保养id查找
     *
     * @param maintainId
     * @return
     */
    MaintainDTO selectById(@Param("maintainId") Integer maintainId);

    /**
     * 逻辑删除
     *
     * @param maintainId
     */
    void deleteLogic(@Param("maintainId") Integer maintainId);

    /**
     * 根据设备查找
     *
     * @param machId
     * @return
     */
    List<MaintainDTO> selectByMachId(@Param("machId") Integer machId);
}
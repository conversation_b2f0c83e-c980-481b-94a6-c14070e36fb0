package com.whfc.mach.dao;

import com.whfc.mach.entity.AppMachDaySeg;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface AppMachDaySegMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachDaySeg record);

    int insertSelective(AppMachDaySeg record);

    AppMachDaySeg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachDaySeg record);

    int updateByPrimaryKey(AppMachDaySeg record);

    /**
     * 查询设备每日的分段数据
     *
     * @param machId
     * @param date
     * @return
     */
    AppMachDaySeg selectByMachIdAndDate(@Param("machId") Integer machId, @Param("date") Date date);

    /**
     * 插入 or 更新
     *
     * @param record
     * @return
     */
    int insertOrUpdate(AppMachDaySeg record);

}
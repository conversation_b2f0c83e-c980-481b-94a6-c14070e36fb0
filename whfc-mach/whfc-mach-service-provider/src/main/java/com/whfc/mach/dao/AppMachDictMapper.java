package com.whfc.mach.dao;

import com.whfc.mach.dto.MachDictDataDTO;
import com.whfc.mach.entity.AppMachDict;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppMachDictMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachDict record);

    int insertSelective(AppMachDict record);

    AppMachDict selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachDict record);

    int updateByPrimaryKey(AppMachDict record);

    /**
     * 根据组织机构和code查找
     *
     * @param code
     * @return
     */
    Integer selectByCode(@Param("code") String code);

    /**
     * 根据组织机构和pid查找
     *
     * @param deptId
     * @param pid
     * @return
     */
    List<MachDictDataDTO> selectByDeptIdAndPid(@Param("deptId") Integer deptId, @Param("pid") Integer pid);

    /**
     * 批量插入
     *
     * @param deptId
     * @param pid
     * @param list
     */
    void batchInsert(@Param("deptId") Integer deptId,
                     @Param("pid") Integer pid,
                     @Param("list") List<MachDictDataDTO> list);

    /**
     * 根据组织机构和数据名称查找
     *
     * @param deptId
     * @param pid
     * @param name
     * @return
     */
    Integer selectByDeptIdAndName(@Param("deptId") Integer deptId,
                                  @Param("pid") Integer pid,
                                  @Param("name") String name);

    /**
     * 逻辑删除
     *
     * @param id
     */
    void deleteLogic(@Param("id") Integer id);
}
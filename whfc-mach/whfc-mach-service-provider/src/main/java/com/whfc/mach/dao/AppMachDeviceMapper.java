package com.whfc.mach.dao;

import com.whfc.mach.dto.AppMachInfo;
import com.whfc.mach.entity.AppMachDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppMachDeviceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachDevice record);

    int insertSelective(AppMachDevice record);

    AppMachDevice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachDevice record);

    int updateByPrimaryKey(AppMachDevice record);

    /**
     * 查找设备的绑定状态
     *
     * @param machId
     * @return
     */
    AppMachDevice selectByMachId(@Param("machId") Integer machId);

    /**
     * 查询与硬件绑定的设备
     *
     * @param deviceId
     * @return
     */
    AppMachDevice selectByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 删除绑定关系
     *
     * @param machId
     */
    void deleteByMachId(@Param("machId") Integer machId);

    /**
     * 统计所有设备数量
     *
     * @param deptIdList
     * @return
     */
    Integer countTotalMachNum(@Param("deptIdList") List<Integer> deptIdList);


    /**
     * 查询硬件绑定设备
     *
     * @param deviceType
     * @return
     */
    List<AppMachInfo> selectAppMachInfoList(@Param("deviceType") Integer deviceType);

    /**
     * 根据硬件ID查询绑定的设备信息
     *
     * @param deviceId
     * @return
     */
    AppMachInfo selectAppMachInfoByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 根据设备ID查询绑定的设备信息
     *
     * @param machId
     * @return
     */
    AppMachInfo selectAppMachInfoByMachId(@Param("machId") Integer machId);
}
package com.whfc.mach.dao;

import com.whfc.mach.entity.AppMachConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppMachConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachConfig record);

    int insertSelective(AppMachConfig record);

    AppMachConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachConfig record);

    int updateByPrimaryKey(AppMachConfig record);

    /**
     * 根据code查找
     *
     * @param deptId
     * @param code
     * @return
     */
    AppMachConfig selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);


    /**
     * 根据code查找
     *
     * @param deptId   组织机构ID
     * @param codeList 电子围栏类型
     * @return 有效电子围栏配置
     */
    List<AppMachConfig> selectByDeptIdAndCodeList(@Param("deptId") Integer deptId, @Param("codeList") List<String> codeList);

    /**
     * 逻辑删除
     *
     * @param deptId   组织机构ID
     * @param codeList 电子围栏类型
     */
    void delByDeptIdAndCodeList(@Param("deptId") Integer deptId, @Param("codeList") List<String> codeList);
}
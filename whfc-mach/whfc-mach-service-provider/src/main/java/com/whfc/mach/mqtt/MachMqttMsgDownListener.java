package com.whfc.mach.mqtt;

import com.whfc.common.constant.QueueConst;
import com.whfc.common.mqtt.Qos;
import com.whfc.common.util.JSONUtil;
import com.whfc.mach.dto.mqtt.MachMqttConst;
import com.whfc.mach.dto.mqtt.MachMqttMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.whfc.mach.dto.mqtt.MachMqttConst.CMD_UPGRADE;
import static com.whfc.mach.dto.mqtt.MachMqttConst.PRODUCT_KEY;

/**
 * @Description: 下行指令监听转发
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/14 9:52
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.MACH_MQTT_DOWN)}, concurrency = "1-5")
public class MachMqttMsgDownListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired(required = false)
    private MachMqttConfig.MqttMessageSender mqttMessageSender;

    @RabbitHandler
    public void process(String msg) {
        try {
            MachMqttMsg mqttMsg = JSONUtil.parseObject(msg, MachMqttMsg.class);
            String cmd = mqttMsg.getCmd();
            String deviceId = mqttMsg.getDeviceId();
            String topic = MachMqttConst.getTopicDown(PRODUCT_KEY, deviceId);
            int qos = CMD_UPGRADE.equals(cmd) ? Qos.Qos1 : Qos.Qos0;
            mqttMessageSender.sendToMqtt(topic, qos, msg);
            logger.info("下发mach_mqtt,topic:{},msg:{}", topic, msg);
        } catch (Exception ex) {
            logger.error("mach_mqtt_down,消息处理失败", ex);
        }
    }
}

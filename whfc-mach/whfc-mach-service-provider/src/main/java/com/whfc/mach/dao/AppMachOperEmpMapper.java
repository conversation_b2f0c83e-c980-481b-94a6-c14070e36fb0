package com.whfc.mach.dao;

import com.whfc.mach.dto.MachOperEmpDTO;
import com.whfc.mach.entity.AppMachOperEmp;
import com.whfc.mach.param.MachOperEmpParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppMachOperEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachOperEmp record);

    int insertSelective(AppMachOperEmp record);

    AppMachOperEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachOperEmp record);

    int updateByPrimaryKey(AppMachOperEmp record);

    /**
     * 批量插入
     *
     * @param machOperEmpParams 操作手信息
     * @return
     */
    int batchInsert(@Param("machId") Integer machId, @Param("machOperEmpParams") List<MachOperEmpParam> machOperEmpParams);

    /**
     * 删除操作手
     *
     * @param machId
     */
    int deleteLogicByMachId(@Param("machId") Integer machId);

    /**
     * 根据设备id查找操作手信息
     *
     * @param machId
     * @return
     */
    List<MachOperEmpDTO> selectByMachId(@Param("machId") Integer machId);

    /**
     * 根本设备ID查找
     *
     * @param machIdList
     * @return
     */
    List<MachOperEmpDTO> selectByMachIdList(@Param("machIdList") List<Integer> machIdList);
}
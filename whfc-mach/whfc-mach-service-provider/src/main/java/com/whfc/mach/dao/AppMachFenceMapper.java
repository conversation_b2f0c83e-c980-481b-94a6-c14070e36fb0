package com.whfc.mach.dao;

import com.whfc.mach.entity.AppMachFence;
import org.apache.ibatis.annotations.Param;

public interface AppMachFenceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachFence record);

    int insertSelective(AppMachFence record);

    AppMachFence selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachFence record);

    int updateByPrimaryKey(AppMachFence record);

    /**
     * 根据报警规则id查找
     *
     * @param ruleId
     * @return
     */
    AppMachFence selectByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 逻辑删除
     *
     * @param ruleId
     * @return
     */
    int deleteLogicByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 插入
     *
     * @param fence
     * @return
     */
    int insertSelectiveByParam(AppMachFence fence);

    /**
     * 检测设备是否出电子围栏
     *
     * @param ruleId
     * @param lng
     * @param lat
     * @return
     */
    Integer checkByRuleIdAndGps(@Param("ruleId") Integer ruleId, @Param("lng") Double lng, @Param("lat") Double lat);

}
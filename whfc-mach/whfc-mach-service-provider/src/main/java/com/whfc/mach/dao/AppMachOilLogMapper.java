package com.whfc.mach.dao;


import com.whfc.mach.dto.AppMachOilLogDTO;
import com.whfc.mach.entity.AppMachOilLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AppMachOilLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppMachOilLog record);

    AppMachOilLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachOilLog record);

    /**
     * 查询硬件的油位标定信息
     *
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppMachOilLogDTO> selectMachOilLogDTOList(@Param("deviceId") Integer deviceId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<AppMachOilLog> selectMachOilLogDTOList1(@Param("deviceId") Integer deviceId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
package com.whfc.mach.dao;

import com.whfc.mach.dto.AppMachDataStatDTO;
import com.whfc.mach.dto.MachDayDataDTO;
import com.whfc.mach.dto.MachMonthReportDTO;
import com.whfc.mach.entity.AppMachMonth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppMachMonthMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMachMonth record);

    int insertSelective(AppMachMonth record);

    AppMachMonth selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMachMonth record);

    int updateByPrimaryKey(AppMachMonth record);

    /**
     * 查找设备年报的设备
     *
     * @param deptIds
     * @param year
     * @return
     */
    List<MachMonthReportDTO> selectYearReportMach(@Param("deptIds") List<Integer> deptIds,
                                                  @Param("year") String year);

    /**
     * 查找每个月的设备数据
     *
     * @param machIds
     * @param year
     * @return
     */
    List<MachDayDataDTO> selectMonthReportByMachIds(@Param("machIds") List<Integer> machIds,
                                                    @Param("year") String year);

    /**
     * 查找一年平均数据
     *
     * @param deptIds
     * @param year
     * @return
     */
    AppMachDataStatDTO selectAvgData(@Param("deptIds") List<Integer> deptIds,
                                     @Param("year") String year);

    /**
     * 批量插入
     *
     * @param month
     * @param list
     */
    void batchInsert(@Param("month") String month, @Param("list") List<AppMachDataStatDTO> list);
}
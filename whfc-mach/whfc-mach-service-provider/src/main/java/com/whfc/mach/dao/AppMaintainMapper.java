package com.whfc.mach.dao;

import com.whfc.mach.dto.obd.ObdMaintainDTO;
import com.whfc.mach.entity.AppMaintain;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface AppMaintainMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppMaintain record);

    int insertSelective(AppMaintain record);

    AppMaintain selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppMaintain record);

    int updateByPrimaryKey(AppMaintain record);

    /**
     * 查找保养列表
     *
     * @param deptIds
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<ObdMaintainDTO> selectMaintainList(@Param("deptIds") Collection<Integer> deptIds,
                                            @Param("state") Integer state,
                                            @Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    /**
     * 更新设备保养状态
     *
     * @param maintainId
     * @param remark
     * @param maintainTime
     * @param maintainUserId
     * @param maintainUserName
     */
    void updateStateByMaintainId(@Param("maintainId") Integer maintainId,
                                 @Param("remark") String remark,
                                 @Param("maintainTime") Date maintainTime,
                                 @Param("maintainUserId") Integer maintainUserId,
                                 @Param("maintainUserName") String maintainUserName);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<AppMaintain> list);

    /**
     * 查找设备最近一次保养
     *
     * @param machId
     * @param time
     * @return
     */
    Date selectLatestMaintainTime(@Param("machId") Integer machId, @Param("time") Date time);

    /**
     * 根据设备id查找保养记录
     *
     * @param machId
     * @return
     */
    List<ObdMaintainDTO> selectByMachId(@Param("machId") Integer machId);

    /**
     * 根据设备id查找是否需要保养
     *
     * @param machId
     * @return
     */
    int countByMachId(@Param("machId") Integer machId);

    /**
     * 根据项目id查找需要保养的设备数
     *
     * @param deptId
     * @return
     */
    Integer countByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据设备id查找需要保养的信息
     *
     * @param machId
     * @return
     */
    Integer selectNeedMaintainByMachId(@Param("machId") Integer machId);
}
package com.whfc.mach.dto.obd;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * obd设备参数
 */
@Data
public class ObdFrameItemDTO implements Serializable {

    /**************甘肃OBD*************/

    /**
     * 1轮制动
     */
    @JSONField(name = "brake1")
    private Integer brake1;

    /**
     * 2轮制动
     */
    @JSONField(name = "brake2")
    private Integer brake2;

    /**
     * 3轮制动
     */
    @JSONField(name = "brake3")
    private Integer brake3;

    /**
     * 4轮制动
     */
    @JSONField(name = "brake4")
    private Integer brake4;

    /**
     * 1轮工况
     */
    @JSONField(name = "bstrain1_select")
    private Integer bstrain1_select;

    /**
     * 2轮工况
     */
    @JSONField(name = "bstrain2_select")
    private Integer bstrain2_select;

    /**
     * 3轮工况
     */
    @JSONField(name = "bstrain3_select")
    private Integer bstrain3_select;

    /**
     * 4轮工况
     */
    @JSONField(name = "bstrain4_select")
    private Integer bstrain4_select;

    /**
     * 工作模式
     */
    @JSONField(name = "work_mode")
    private Integer work_mode;

    /**
     * 控制模式
     */
    @JSONField(name = "remote_control")
    private Integer remote_control;

    /**
     * 并轮状态
     */
    @JSONField(name = "bParallel_sel_status")
    private Integer bParallel_sel_status;

    /**
     * 手柄状态
     */
    @JSONField(name = "dir_pos")
    private Integer dir_pos;

    /**
     * 手柄状态
     */
    @JSONField(name = "dir_neu")
    private Integer dir_neu;

    /**
     * 手柄状态
     */
    @JSONField(name = "dir_status")
    private Integer dir_status;

    /**
     * 风扇开关
     */
    @JSONField(name = "display_fan")
    private Integer display_fan;

//    /**
//     * 风扇状态
//     */
//    @JSONField(name = "Fan_WORK")
//    private Integer Fan_WORK;

    /**
     * 夹紧状态
     */
    @JSONField(name = "display_clamp")
    private Integer display_clamp;

    /**
     * 计程状态1
     */
    @JSONField(name = "count_set1")
    private Integer count_set1;

    /**
     * 计程状态2
     */
    @JSONField(name = "count_set2")
    private Integer count_set2;

    /**
     * 计程状态3
     */
    @JSONField(name = "count_set3")
    private Integer count_set3;

    /**
     * 计程状态4
     */
    @JSONField(name = "count_set4")
    private Integer count_set4;

    /**
     * 夹紧状态1
     */
    @JSONField(name = "display_clamp1")
    private Integer display_clamp1;

    /**
     * 夹紧状态2
     */
    @JSONField(name = "display_clamp2")
    private Integer display_clamp2;

    /**
     * 夹紧状态3
     */
    @JSONField(name = "display_clamp3")
    private Integer display_clamp3;

    /**
     * 夹紧状态4
     */
    @JSONField(name = "display_clamp4")
    private Integer display_clamp4;

    /**
     * 远程CAN联结
     */
    @JSONField(name = "sErro.CAN1")
    private Integer sErroCAN1;

    /**
     * 液压油温报警
     */
    @JSONField(name = "alarm_oiltemp")
    private Integer alarm_oiltemp;

    /**
     * 柴油油位报警
     */
    @JSONField(name = "alarm_fuel")
    private Integer alarm_fuel;

    /**
     * 尾架压力报警
     */
    @JSONField(name = "alarm_auxpress")
    private Integer alarm_auxpress;

//    /**
//     * 主泵压力传感器1
//     */
//    @JSONField(name = "sErro.press_highsensor1")
//    private Integer sErroPress_highsensor1;
//
//    /**
//     * 主泵压力传感器2
//     */
//    @JSONField(name = "sErro.press_highsensor2")
//    private Integer sErroPress_highsensor2;
//
//    /**
//     * 主压力传感器1
//     */
//    @JSONField(name = "sErro.press_mainsensor1")
//    private Integer sErroPress_mainsensor1;
//
//    /**
//     * 主压力传感器2
//     */
//    @JSONField(name = "sErro.press_mainsensor2")
//    private Integer sErroPress_mainsensor2;
//
//    /**
//     * 主压力传感器3
//     */
//    @JSONField(name = "sErro.press_mainsensor3")
//    private Integer sErroPress_mainsensor3;
//
//    /**
//     * 主压力传感器4
//     */
//    @JSONField(name = "sErro.press_mainsensor4")
//    private Integer sErroPress_mainsensor4;
//
//    /**
//     * 补油压力传感器
//     */
//    @JSONField(name = "sErro.boostpresssensor")
//    private Integer sErroBoostpresssensor;
//
//    /**
//     * 尾架压力传感器
//     */
//    @JSONField(name = "sErro.auxpresssensor")
//    private Integer sErroAuxpresssensor;
//
//    /**
//     * 1轮制动电磁铁
//     */
//    @JSONField(name = "sErro.brake1")
//    private Integer sErroBrake1;
//
//    /**
//     * 2轮制动电磁铁
//     */
//    @JSONField(name = "sErro.brake2")
//    private Integer sErroBrake2;
//
//    /**
//     * 3轮制动电磁铁
//     */
//    @JSONField(name = "sErro.brake3")
//    private Integer sErroBrake3;
//
//    /**
//     * 4轮制动电磁铁
//     */
//    @JSONField(name = "sErro.brake4")
//    private Integer sErroBrake4;
//
//    /**
//     * 控制手柄
//     */
//    @JSONField(name = "sErro.joyst")
//    private Integer sErroJoyst;
//
//    /**
//     * 补油压力报警
//     */
//    @JSONField(name = "sErro.boostpress")
//    private Integer sErroBoostpress;
//
//    /**
//     * 尾架压力报警
//     */
//    @JSONField(name = "sErro.auxpress")
//    private Integer sErroAuxpress;
//
//    /**
//     * 系统电压报警
//     */
//    @JSONField(name = "sErro.vp1")
//    private Integer sErroVp1;
//
//    /**
//     * 机油压力报警
//     */
//    @JSONField(name = "sErro.Oil_press")
//    private Integer sErroOilPress;
//
//    /**
//     * 液压油温度报警
//     */
//    @JSONField(name = "sErro.oiltemp")
//    private Integer sErroOiltemp;
//
//    /**
//     * 吸油滤油器报警
//     */
//    @JSONField(name = "sErro.Suck_alarm")
//    private Integer sErroSuckAlarm;
//
//    /**
//     * 回油滤油器报警
//     */
//    @JSONField(name = "sErro.Buck_alarm")
//    private Integer sErroBuckAlarm;
//
//    /**
//     * 燃油不足
//     */
//    @JSONField(name = "sErro_fuellevel")
//    private Integer sErroFuellevel;
//
//    /**
//     * 燃油不足
//     */
//    @JSONField(name = "sErro.watertemp")
//    private Integer sErroWatertemp;
//
//
//    /**
//     * 主泵牵引电磁铁
//     */
//    @JSONField(name = "sErro.pos_cur1")
//    private Integer sErroPos_cur1;
//
//    /**
//     * 主泵送线电磁铁
//     */
//    @JSONField(name = "sErro.neu_cur1")
//    private Integer sErroNeu_cur1;
//
//    /**
//     * 主电磁铁1故障
//     */
//    @JSONField(name = "sErro.main_cur1")
//    private Integer sErroMain_cur1;
//
//    /**
//     * 主电磁铁2故障
//     */
//    @JSONField(name = "sErro.main_cur2")
//    private Integer sErroMain_cur2;
//
//    /**
//     * 尾架比例电磁铁
//     */
//    @JSONField(name = "sErro.struc_cur1")
//    private Integer sErroStruc_cur1;
//
//    /**
//     * 风扇比例电磁铁
//     */
//    @JSONField(name = "sErro.fan_cur1")
//    private Integer sErroFan_cur1;
//
//    /**
//     * 主电磁铁3故障
//     */
//    @JSONField(name = "sErro.main_cur3")
//    private Integer sErroMain_cur3;
//
//    /**
//     * 主电磁铁4故障
//     */
//    @JSONField(name = "sErro.main_cur4")
//    private Integer sErroMain_cur4;
//
//    /**
//     * 牵引选择电磁铁1
//     */
//    @JSONField(name = "sErro.select1")
//    private Integer sErroSelect1;
//
//    /**
//     * 牵引选择电磁铁2
//     */
//    @JSONField(name = "sErro.select2")
//    private Integer sErroSelect2;
//
//    /**
//     * 牵引选择电磁铁3
//     */
//    @JSONField(name = "sErro.select3")
//    private Integer sErroSelect3;
//
//    /**
//     * 牵引选择电磁铁4
//     */
//    @JSONField(name = "sErro.select4")
//    private Integer sErroSelect4;
//
//    /**
//     * 风扇选择电磁铁
//     */
//    @JSONField(name = "sErro.fan_select")
//    private Integer sErroFan_select;
//
//    /**
//     * 并轮选择电磁铁
//     */
//    @JSONField(name = "sErro.bParallel")
//    private Integer sErroBParallel;
//
//    /**
//     * 摆线电磁铁1
//     */
//    @JSONField(name = "sErro.bsw1")
//    private Integer sErroBsw1;
//
//    /**
//     * 摆线电磁铁2
//     */
//    @JSONField(name = "sErro.bsw2")
//    private Integer sErroBsw2;


    /**
     * 故障
     */
    private Integer fault00;
    private Integer fault01;
    private Integer fault02;
    private Integer fault03;
    private Integer fault04;
    private Integer fault05;
    private Integer fault06;
    private Integer fault07;

    private Integer fault10;
    private Integer fault11;
    private Integer fault12;
    private Integer fault13;
    private Integer fault14;
    private Integer fault15;
    private Integer fault16;
    private Integer fault17;

    private Integer fault20;
    private Integer fault21;
    private Integer fault22;
    private Integer fault23;
    private Integer fault24;
    private Integer fault25;
    private Integer fault26;
    private Integer fault27;

    private Integer fault30;
    private Integer fault31;
    private Integer fault32;
    private Integer fault33;
    private Integer fault34;
    private Integer fault35;
    private Integer fault36;
    private Integer fault37;

    private Integer fault40;
    private Integer fault41;
    private Integer fault42;
    private Integer fault43;
    private Integer fault44;
    private Integer fault45;
    private Integer fault46;
    private Integer fault47;

    private Integer fault50;
    private Integer fault51;
    private Integer fault52;
    private Integer fault53;
    private Integer fault54;
    private Integer fault55;
    private Integer fault56;
    private Integer fault57;

    private Integer fault60;
    private Integer fault61;
    private Integer fault62;
    private Integer fault63;
    private Integer fault64;
    private Integer fault65;
    private Integer fault66;
    private Integer fault67;

    private Integer fault70;
    private Integer fault71;
    private Integer fault72;
    private Integer fault73;
    private Integer fault74;
    private Integer fault75;
    private Integer fault76;
    private Integer fault77;


    /**
     * 1轮预置张力
     */
    private Double tension1_set;

    /**
     * 1轮张力
     */
    private Double tension1;

    /**
     * 2轮预置张力
     */
    private Double tension2_set;

    /**
     * 2轮张力
     */
    private Double tension2;

    /**
     * 3轮预置张力
     */
    private Double tension3_set;

    /**
     * 3轮张力
     */
    private Double tension3;

    /**
     * 4轮预置张力
     */
    private Double tension4_set;

    /**
     * 4轮张力
     */
    private Double tension4;

    /**
     * 牵引力1
     */
    private Double force1;

    /**
     * 预置牵引力1
     */
    private Double force_set1;

    /**
     * 牵引力2
     */
    private Double force2;

    /**
     * 预置牵引力2
     */
    private Double force_set2;

    /**
     * 1轮速度
     */
    private Double speed1;

    /**
     * 2轮速度
     */
    private Double speed2;

    /**
     * 3轮速度
     */
    private Double speed3;

    /**
     * 4轮速度
     */
    private Double speed4;

    /**
     * 1轮放线里程
     */
    private Long length1;

    /**
     * 2轮放线里程
     */
    private Long length2;

    /**
     * 3轮放线里程
     */
    private Long length3;

    /**
     * 4轮放线里程
     */
    private Long length4;

    /**
     * 控制手柄位置
     */
    private Double joyst;

    /**
     * 控制手柄位置2
     */
    private Double joyst2;

    /**
     * 油门踏板位置
     */
    private Double pedal;

    /**
     * 预置牵引速度1
     */
    private Double speed_set;

    /**
     * 预置牵引速度2
     */
    private Double speed_set2;

    /**
     * 放线总里程1
     */
    private Long total_length1;

    /**
     * 放线总里程2
     */
    private Long total_length2;

    /**
     * 系统电压
     */
    private Double ubat_supply;

    /**
     * 液压油温度
     */
    private Double oil_tem;

    /**
     * 柴油油位
     */
    private Double fuel_level;

    /**
     * 主泵压力1
     */
    private Double pump1_pressure;

    /**
     * 主泵压力2
     */
    private Double pump2_pressure;

    /**
     * 主压力1
     */
    private Double main_pressue1;

    /**
     * 主压力2
     */
    private Double main_pressue2;

    /**
     * 主压力3
     */
    private Double main_pressue3;

    /**
     * 主压力4
     */
    private Double main_pressue4;

    /**
     * 尾架压力
     */
    private Double aux_pressue;

    /**
     * 补油压力
     */
    private Double boost_pressure;

    /**
     * 牵引电流1
     */
    private Double pos_cur1;

    /**
     * 送线电流1
     */
    private Double neu_cur1;

    /**
     * 牵引电流2
     */
    private Double pos_cur2;

    /**
     * 送线电流2
     */
    private Double neu_cur2;

    /**
     * 控制电流1
     */
    private Double main_cur1;

    /**
     * 控制电流2
     */
    private Double main_cur2;

    /**
     * 控制电流3
     */
    private Double main_cur3;

    /**
     * 控制电流4
     */
    private Double main_cur4;

    /**
     * 尾架电流
     */
    private Double aux_cur;

    /**
     * 风扇电流
     */
    private Double fan_cur;

    /**
     * 摆线电流1
     */
    private Double bx_cur1;

    /**
     * 摆线电流2
     */
    private Double bx_cur2;

    /**
     * 尾架电流2
     */
    private Double aux_cur2;

    /**
     * 辅助压力2
     */
    private Double aux_pressue2;

    /**
     * 补油压力2
     */
    private Double boost_pressure2;

    /**
     * 工作时间
     */
    private Double total_engine_hours;

    /**
     * 柴油机转速
     */
    private Double engine_speed;

    /**
     * 机油压力
     */
    private Double engine_oil_pressure;

    /**
     * 柴油机水温
     */
    private Double engine_coolant_temperature;

    /**
     * 机油温度
     */
    private Double engine_oil_temperature;

    /**
     * 当前扭矩
     */
    private Double cur_torque;

    /**
     * 当前负载
     */
    private Double cur_load;

    /**
     * 燃油总消耗
     */
    private Double total_fuel_consumption;

    /**************湖北OBD*************/

    /**
     * 柴油机转速
     */
    //private String engine_speed;

    /**
     * 转速百分比
     */
    private String speed_percent;

    /**
     * 油位百分比
     */
    private String oil_percent;

    /**
     * 油温值
     */
    private String oil_temp;

    /**
     * 水温值
     */
    private String water_temp;

    /**
     * 机压值
     */
    private String mach_pressure;

    /**
     * 电压值
     */
    private String battery_pressure;

    /**
     * 轮1预置张力
     */
    //private String tension1_set;

    /**
     * 轮1实际张力
     */
    private String tension1_act;

    /**
     * 轮2预置张力
     */
    //private String tension2_set;

    /**
     * 轮1实际张力
     */
    private String tension2_act;

    /**
     * 系统补油压力
     */
    private String pressure_0;

    /**
     * 轮1主泵补油压力
     */
    private String pressure_1_1;

    /**
     * 轮1尾车压力
     */
    private String pressure_1_2;

    /**
     * 轮1张力压力
     */
    private String pressure_1_3;

    /**
     * 轮1刹车压力
     */
    private String pressure_1_4;

    /**
     * 轮1排线压力
     */
    private String pressure_1_5;

    /**
     * 轮2主泵补油压力
     */
    private String pressure_2_1;

    /**
     * 轮2尾车压力
     */
    private String pressure_2_2;

    /**
     * 轮2张力压力
     */
    private String pressure_2_3;

    /**
     * 轮2刹车压力
     */
    private String pressure_2_4;

    /**
     * 轮2排线压力
     */
    private String pressure_2_5;

    /**
     * 轮1速度
     */
    //private String speed1;

    /**
     * 轮2速度
     */
    //private String speed2;

    /**
     * 轮1里程
     */
    //private String length1;

    /**
     * 轮1总里程
     */
    //private String total_length1;

    /**
     * 轮2里程
     */
    //private String length2;

    /**
     * 轮2总里程
     */
    //private String total_length2;

    /**
     * 启用状态
     */
    private String status_00;
    private String status_01;
    private String status_02;
    private String status_03;
    private String status_04;
    private String status_05;
    private String status_06;
    private String status_07;
    private String status_10;
    private String status_11;
    private String status_12;
    private String status_13;

    /**
     * 报警状态
     */
    private String warn_50;
    private String warn_51;
    private String warn_52;
    private String warn_53;
    private String warn_54;
    private String warn_55;
    private String warn_56;
    private String warn_57;
    private String warn_60;
    private String warn_61;
    private String warn_62;
    private String warn_63;
    private String warn_64;
    private String warn_65;
    private String warn_66;

    /**
     * 输入映射
     */
    private String in_00;
    private String in_01;
    private String in_02;
    private String in_03;
    private String in_04;
    private String in_05;
    private String in_06;
    private String in_07;
    private String in_20;
    private String in_21;
    private String in_22;
    private String in_23;
    private String in_24;
    private String in_25;
    private String in_26;
    private String in_30;
    private String in_31;

    /**
     * 输出映射
     */
    private String out_00;
    private String out_01;
    private String out_02;
    private String out_03;
    private String out_04;
    private String out_05;
    private String out_06;
    private String out_07;
    private String out_10;
    private String out_11;
    private String out_12;
    private String out_13;
    private String out_14;
    private String out_16;
    private String out_17;
    private String out_20;
    private String out_21;

    private String sig_1_1;
    private String sig_1_2;
    private String sig_1_3;
    private String sig_1_4;
    private String sig_1_5;
    private String sig_1_6;
    private String sig_1_7;
    private String sig_1_8;
    private String sig_1_9;
    private String sig_1_10;
    private String sig_1_11;
    private String sig_1_12;
    private String sig_1_13;
    private String sig_1_14;
    private String sig_1_15;

    private String sig_2_1;
    private String sig_2_2;
    private String sig_2_3;
    private String sig_2_4;
    private String sig_2_5;
    private String sig_2_6;
    private String sig_2_7;
    private String sig_2_8;
    private String sig_2_9;
    private String sig_2_10;
    private String sig_2_11;
    private String sig_2_12;
    private String sig_2_13;
    private String sig_2_14;
    private String sig_2_15;

    private String sig_3_1;
    private String sig_3_2;

}

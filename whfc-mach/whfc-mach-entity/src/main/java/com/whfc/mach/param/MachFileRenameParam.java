package com.whfc.mach.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 设备档案重命名参数
 * @date 2021-04-07
 */
@Data
public class MachFileRenameParam implements Serializable {
    /**
     * 档案id
     */
    @NotNull
    private Integer fileId;
    /**
     * 档案名称
     */
    @NotEmpty
    private String name;
}

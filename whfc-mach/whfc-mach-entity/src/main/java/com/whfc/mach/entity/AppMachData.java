package com.whfc.mach.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机械设备-硬件-最新数据
 */
@Data
public class AppMachData {
    private Integer id;

    /**
     * 机械设备ID
     */
    private Integer machId;

    /**
     * 硬件ID
     */
    private Integer deviceId;

    /**
     * 硬件时间
     */
    private Date time;

    /**
     * 工作状态:1-静止 2-怠速 3-运动 4-休眠
     */
    private Integer workState;

    /**
     * 当前工作状态-开始时间
     */
    private Date workStateStartTime;

    /**
     * 网络状态(0-离线 1-在线)
     */
    private Integer netState;

    /**
     * 当前网络状态-开始时间
     */
    private Date netStateStartTime;

    /**
     * E-东经/W-西经
     */
    private String lngFlag;

    /**
     * N-北纬/S-南纬
     */
    private String latFlag;

    private Double lng;

    private Double lat;

    private Date gpsTime;

    private Double lngWgs84;

    private Double latWgs84;

    private String location;

    private String province;

    private String city;

    private Double totalWorkHours;

    private Double totalOilWear;

    private Long totalLength1;

    private Long totalLength2;

    /**
     * 电池状态(1-在充电 2-未充电)
     */
    private Integer batteryState;

    /**
     * 电池电量(百分比)
     */
    private Integer batteryPower;

    /**
     * 硬件温度(摄氏度)
     */
    private Double deviceTemp;

    /**
     * 压力值
     */
    private Double pressure;

    /**
     * 油位
     */
    private Integer oilPos;

    /**
     * 油箱温度(摄氏度)
     */
    private Double oilTemp;

    /**
     * 油箱状态(1-正常,2-打开,3-异常)
     */
    private Integer oilState;

    /**
     * 油箱电量(百分比)
     */
    private Integer oilPower;

    /**
     * 主机版本号
     */
    private String masterVer;

    /**
     * 从机版本号
     */
    private String slaveVer;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
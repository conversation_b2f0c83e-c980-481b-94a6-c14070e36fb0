package com.whfc.mach.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClasssName DeviceMachBindParam
 * @Description 设备绑定硬件请求参数
 * <AUTHOR>
 * @Date 2020/8/18 9:47
 * @Version 1.0
 */
@Schema(description = "设备绑定硬件请求参数")
@Data
public class DeviceMachBindParam implements Serializable {


    @Schema(description =  "硬件guid")
    private String guid;

    @Schema(description =  "硬件sn")
    @NotEmpty
    private String sn;

    @Schema(description =  "设备ID")
    @NotNull
    private Integer machId;

    @Schema(description =  "人员ID")
    private Integer userId;

    @Schema(description =  "人员名称")
    private String nickName;

    @Schema(description =  "设备ID")
    private Integer deviceId;

    @Schema(description =  "设备类型")
    private Integer deviceType;

    @Schema(description =  "设备平台")
    private String platform;
}

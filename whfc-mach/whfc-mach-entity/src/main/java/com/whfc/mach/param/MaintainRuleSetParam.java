package com.whfc.mach.param;

import com.whfc.mach.dto.obd.MaintainRuleDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 设备保养触发条件设置参数
 * @date 2021-03-05
 */
@Data
public class MaintainRuleSetParam implements Serializable {
    /**
     * 保养id
     */
    private Integer deptId;
    /**
     * 触发规则
     */
    private List<MaintainRuleDTO> ruleList;
}

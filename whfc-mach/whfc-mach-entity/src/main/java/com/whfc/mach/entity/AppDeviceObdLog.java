package com.whfc.mach.entity;

import java.io.Serializable;
import java.util.Date;

public class AppDeviceObdLog implements Serializable {
    private Integer id;

    private Integer machId;

    private Integer deviceId;

    private Date time;

    /**
     * 电源状态(1-正常 1-断电)
     */
    private Integer status;

    /**
     * 工作状态(1-停机 2-怠机 3-工作)
     */
    private Integer workState;

    /**
     * 网络状态(0-离线 1-在线)
     */
    private Integer netState;

    /**
     * 发动机转速
     */
    private Double engineSpeed;

    /**
     * 1轮速速 or 牵引速度
     */
    private Double speed1;

    /**
     * 2轮速速
     */
    private Double speed2;

    /**
     * 3轮速速
     */
    private Double speed3;

    /**
     * 4轮速速
     */
    private Double speed4;

    /**
     * 总油耗
     */
    private Double totalOilWear;

    /**
     * 总里程1
     */
    private Long totalLength1;

    /**
     * 总里程2
     */
    private Long totalLength2;

    /**
     * 总工时
     */
    private Double totalWorkHours;

    /**
     * 牵引力
     */
    private Double force;

    /**
     * 1轮张力
     */
    private Double tension1;

    /**
     * 2轮张力
     */
    private Double tension2;

    /**
     * 3轮张力
     */
    private Double tension3;

    /**
     * 4轮张力
     */
    private Double tension4;

    /**
     * 预警时的
     */
    private String errFrameData;

    /**
     * 电池状态
     */
    private Integer batteryState;

    /**
     * 电量百分比
     */
    private Integer batteryPower;

    private Integer frameType;

    private Long frameId;

    private String frameIdHex;

    private Integer frameRtr;

    private Integer frameDlc;

    private String frameData;

    private String frameDataDetail;

    private Double lng;

    private Double lat;

    private Double lngWgs84;

    private Double latWgs84;

    private String location;

    private String province;

    private String city;

    private String masterVer;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMachId() {
        return machId;
    }

    public void setMachId(Integer machId) {
        this.machId = machId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWorkState() {
        return workState;
    }

    public void setWorkState(Integer workState) {
        this.workState = workState;
    }

    public Integer getNetState() {
        return netState;
    }

    public void setNetState(Integer netState) {
        this.netState = netState;
    }

    public Double getEngineSpeed() {
        return engineSpeed;
    }

    public void setEngineSpeed(Double engineSpeed) {
        this.engineSpeed = engineSpeed;
    }

    public Double getSpeed1() {
        return speed1;
    }

    public void setSpeed1(Double speed1) {
        this.speed1 = speed1;
    }

    public Double getSpeed2() {
        return speed2;
    }

    public void setSpeed2(Double speed2) {
        this.speed2 = speed2;
    }

    public Double getSpeed3() {
        return speed3;
    }

    public void setSpeed3(Double speed3) {
        this.speed3 = speed3;
    }

    public Double getSpeed4() {
        return speed4;
    }

    public void setSpeed4(Double speed4) {
        this.speed4 = speed4;
    }

    public Double getTotalOilWear() {
        return totalOilWear;
    }

    public void setTotalOilWear(Double totalOilWear) {
        this.totalOilWear = totalOilWear;
    }

    public Long getTotalLength1() {
        return totalLength1;
    }

    public void setTotalLength1(Long totalLength1) {
        this.totalLength1 = totalLength1;
    }

    public Long getTotalLength2() {
        return totalLength2;
    }

    public void setTotalLength2(Long totalLength2) {
        this.totalLength2 = totalLength2;
    }

    public Double getTotalWorkHours() {
        return totalWorkHours;
    }

    public void setTotalWorkHours(Double totalWorkHours) {
        this.totalWorkHours = totalWorkHours;
    }

    public Double getForce() {
        return force;
    }

    public void setForce(Double force) {
        this.force = force;
    }

    public Double getTension1() {
        return tension1;
    }

    public void setTension1(Double tension1) {
        this.tension1 = tension1;
    }

    public Double getTension2() {
        return tension2;
    }

    public void setTension2(Double tension2) {
        this.tension2 = tension2;
    }

    public Double getTension3() {
        return tension3;
    }

    public void setTension3(Double tension3) {
        this.tension3 = tension3;
    }

    public Double getTension4() {
        return tension4;
    }

    public void setTension4(Double tension4) {
        this.tension4 = tension4;
    }

    public String getErrFrameData() {
        return errFrameData;
    }

    public void setErrFrameData(String errFrameData) {
        this.errFrameData = errFrameData;
    }

    public Integer getBatteryState() {
        return batteryState;
    }

    public void setBatteryState(Integer batteryState) {
        this.batteryState = batteryState;
    }

    public Integer getBatteryPower() {
        return batteryPower;
    }

    public void setBatteryPower(Integer batteryPower) {
        this.batteryPower = batteryPower;
    }

    public Integer getFrameType() {
        return frameType;
    }

    public void setFrameType(Integer frameType) {
        this.frameType = frameType;
    }

    public Long getFrameId() {
        return frameId;
    }

    public void setFrameId(Long frameId) {
        this.frameId = frameId;
    }

    public String getFrameIdHex() {
        return frameIdHex;
    }

    public void setFrameIdHex(String frameIdHex) {
        this.frameIdHex = frameIdHex;
    }

    public Integer getFrameRtr() {
        return frameRtr;
    }

    public void setFrameRtr(Integer frameRtr) {
        this.frameRtr = frameRtr;
    }

    public Integer getFrameDlc() {
        return frameDlc;
    }

    public void setFrameDlc(Integer frameDlc) {
        this.frameDlc = frameDlc;
    }

    public String getFrameData() {
        return frameData;
    }

    public void setFrameData(String frameData) {
        this.frameData = frameData;
    }

    public String getFrameDataDetail() {
        return frameDataDetail;
    }

    public void setFrameDataDetail(String frameDataDetail) {
        this.frameDataDetail = frameDataDetail;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngWgs84() {
        return lngWgs84;
    }

    public void setLngWgs84(Double lngWgs84) {
        this.lngWgs84 = lngWgs84;
    }

    public Double getLatWgs84() {
        return latWgs84;
    }

    public void setLatWgs84(Double latWgs84) {
        this.latWgs84 = latWgs84;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getMasterVer() {
        return masterVer;
    }

    public void setMasterVer(String masterVer) {
        this.masterVer = masterVer;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
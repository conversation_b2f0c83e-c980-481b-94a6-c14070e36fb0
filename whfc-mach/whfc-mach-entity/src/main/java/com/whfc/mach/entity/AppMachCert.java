package com.whfc.mach.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 设备证书表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/3
 */
@Schema(description = "设备证书表")
@Data
public class AppMachCert {
    /**
     * 主键ID
     */
    @Schema(description =  "主键ID")
    private Integer id;

    /**
     * 组织机构ID
     */
    @Schema(description =  "组织机构ID")
    private Integer deptId;

    /**
     * 设备ID
     */
    @Schema(description =  "设备ID")
    private Integer machId;

    /**
     * 证书名称
     */
    @Schema(description =  "证书名称")
    private String certName;

    /**
     * 证书编码
     */
    @Schema(description =  "证书编码")
    private String certCode;

    /**
     * 技能等级
     */
    @Schema(description =  "技能等级")
    private String level;

    /**
     * 发证日期
     */
    @Schema(description =  "发证日期")
    private Date certStartDate;

    /**
     * 截止日期
     */
    @Schema(description =  "截止日期")
    private Date certEndDate;

    /**
     * 发证单位
     */
    @Schema(description =  "发证单位")
    private String certGrantOrg;

    /**
     * 证书状态 0-失效  1-正常
     */
    @Schema(description =  "证书状态 0-失效  1-正常")
    private Integer state;

    /**
     * 删除标记
     */
    @Schema(description =  "删除标记")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description =  "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description =  "创建时间")
    private Date createTime;
}
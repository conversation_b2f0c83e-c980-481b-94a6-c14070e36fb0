package com.whfc.mach.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机械设备表
 */
@Data
public class AppMach {

    private Integer id;

    private String guid;

    /**
     * 项目ID
     */
    private Integer holdDeptId;

    /**
     * 设备编号
     */
    private String machCode;

    /**
     * 设备名称
     */
    private String machName;

    /**
     * 设备类型(字典)
     */
    private Integer machType;

    /**
     * 设备类型名称
     */
    private String machTypeName;

    /**
     * 设备品牌型号
     */
    private String machModelName;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 设备在场状态 0-不在场 1-在场
     */
    private Integer state;

    /**
     * 进场时间
     */
    private Date enterTime;

    /**
     * 退场时间
     */
    private Date outerTime;

    /**
     * 累计工作时长(H)
     */
    private Double totalWorkHours;

    /**
     * 出厂时间
     */
    private Date deliveryTime;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 出厂编码
     */
    private String deliveryCode;

    /**
     * 使用部位
     */
    private String partName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 是否计算里程
     */
    private Integer mileFlag;

    /**
     * 是否计算油耗
     */
    private Integer oilFlag;

    /**
     * 油箱容积(升)
     */
    private Double oilTankVolume;

    /**
     * 油箱高度(cm)
     */
    private Double oilTankHeight;

    /**
     * 绑定状态（0-未绑定 1-绑定）
     */
    private Integer bindFlag;

    /**
     * 绑定平台
     */
    private String platform;

    /**
     * 绑定的硬件id
     */
    private Integer deviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 绑定的硬件sn码
     */
    private String sn;

    /**
     * 二维码地址
     */
    private String qr;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
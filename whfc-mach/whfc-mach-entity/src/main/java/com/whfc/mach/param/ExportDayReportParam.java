package com.whfc.mach.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 导出设备日报参数
 * @date 2020-11-26
 */
@Data
public class ExportDayReportParam implements Serializable {
    @NotNull
    private Integer deptId;

    private List<Integer> deptIds;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
}

package com.whfc.mach.param;

import com.whfc.mach.dto.obd.MaintainRuleDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 维修保养规则-编辑
 */
@Data
public class MaintainRuleEditParam implements Serializable {


    /**
     * 保养规则ID
     */
    @NotNull
    private Integer ruleId;

    /**
     * 保养规则名称
     */
    @NotEmpty
    @Length(max = 32)
    private String ruleName;

    /**
     * 触发规则
     */
    @NotEmpty
    private List<MaintainRuleDTO> ruleList;

}

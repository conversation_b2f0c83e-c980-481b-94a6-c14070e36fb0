package com.whfc.mach.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 编辑设备字典数据参数
 * @date 2020-10-29
 */
@Data
public class MachDictDataEditParam implements Serializable {
    @NotNull
    private Integer id;
    @NotEmpty
    private String name;

    private String remark;
}

package com.whfc.mach.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 增加设备字典数据参数
 * @date 2020-10-29
 */
@Data
public class MachDictDataAddParam implements Serializable {
    @NotNull
    private Integer deptId;
    @NotEmpty
    private String dictCode;
    @NotEmpty
    private String name;
    /**
     * 备注
     */
    private String remark;
}

package com.whfc.mach.param;

import com.whfc.common.validator.FieldScope;
import com.whfc.common.validator.FieldScopeType;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 设备保养处理参数
 * @date 2021-03-05
 */
@Data
public class MaintainHandleParam implements Serializable {
    /**
     * 设备id
     */
    private Integer machId;
    /**
     * 保养id
     */
    private Integer maintainId;
    /**
     * 保养说明
     */
    private String remark;
    /**
     * 保养人id
     */
    private Integer maintainUserId;
    /**
     * 保养人姓名
     */
    private String maintainUserName;

    /**
     * 保养项目
     */
    @NotEmpty
    private List<String> itemList;
}

package com.whfc.mach.entity;

import java.util.Date;

/**
 * 机械设备-硬件-每日统计数据
 */
public class AppMachDay {
    private Integer id;

    /**
     * 持有方机构ID
     */
    private Integer holdDeptId;

    /**
     * 机械设备ID
     */
    private Integer machId;

    /**
     * 硬件ID
     */
    private Integer deviceId;

    /**
     * 日期
     */
    private Date date;

    /**
     * 工作时长(秒)
     */
    private Integer workTimes;

    /**
     * 怠机时长(秒)
     */
    private Integer idleTimes;

    /**
     * 油耗(升)
     */
    private Double oilWear;

    /**
     * 加油量(升)
     */
    private Double oilAdd;

    /**
     * 里程
     */
    private Long mileage;

    /**
     * 有效工效
     */
    private Double mileEfficExp;

    /**
     * 1轮里程
     */
    private Long mileage1;

    /**
     * 2轮里程
     */
    private Long mileage2;

    /**
     * 设备生产指数
     */
    private Double yeildExp;

    /**
     * 设备稳定指数
     */
    private Double stabExp;

    /**
     * 效率指数
     */
    private Double efficExp;

    /**
     * 强度指数
     */
    private Double strengthExp;

    /**
     * 油耗指数
     */
    private Double oilExp;

    /**
     * 安全指数
     */
    private Double safeExp;

    /**
     * 综合效率指数
     */
    private Double generalExp;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHoldDeptId() {
        return holdDeptId;
    }

    public void setHoldDeptId(Integer holdDeptId) {
        this.holdDeptId = holdDeptId;
    }

    public Integer getMachId() {
        return machId;
    }

    public void setMachId(Integer machId) {
        this.machId = machId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getWorkTimes() {
        return workTimes;
    }

    public void setWorkTimes(Integer workTimes) {
        this.workTimes = workTimes;
    }

    public Integer getIdleTimes() {
        return idleTimes;
    }

    public void setIdleTimes(Integer idleTimes) {
        this.idleTimes = idleTimes;
    }

    public Double getOilWear() {
        return oilWear;
    }

    public void setOilWear(Double oilWear) {
        this.oilWear = oilWear;
    }

    public Double getOilAdd() {
        return oilAdd;
    }

    public void setOilAdd(Double oilAdd) {
        this.oilAdd = oilAdd;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public Double getMileEfficExp() {
        return mileEfficExp;
    }

    public void setMileEfficExp(Double mileEfficExp) {
        this.mileEfficExp = mileEfficExp;
    }

    public Long getMileage1() {
        return mileage1;
    }

    public void setMileage1(Long mileage1) {
        this.mileage1 = mileage1;
    }

    public Long getMileage2() {
        return mileage2;
    }

    public void setMileage2(Long mileage2) {
        this.mileage2 = mileage2;
    }

    public Double getYeildExp() {
        return yeildExp;
    }

    public void setYeildExp(Double yeildExp) {
        this.yeildExp = yeildExp;
    }

    public Double getStabExp() {
        return stabExp;
    }

    public void setStabExp(Double stabExp) {
        this.stabExp = stabExp;
    }

    public Double getEfficExp() {
        return efficExp;
    }

    public void setEfficExp(Double efficExp) {
        this.efficExp = efficExp;
    }

    public Double getStrengthExp() {
        return strengthExp;
    }

    public void setStrengthExp(Double strengthExp) {
        this.strengthExp = strengthExp;
    }

    public Double getOilExp() {
        return oilExp;
    }

    public void setOilExp(Double oilExp) {
        this.oilExp = oilExp;
    }

    public Double getSafeExp() {
        return safeExp;
    }

    public void setSafeExp(Double safeExp) {
        this.safeExp = safeExp;
    }

    public Double getGeneralExp() {
        return generalExp;
    }

    public void setGeneralExp(Double generalExp) {
        this.generalExp = generalExp;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
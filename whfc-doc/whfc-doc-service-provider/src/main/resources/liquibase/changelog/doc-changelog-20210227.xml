<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>

    <changeSet id="1" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `doc_dir` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
            `pids` varchar(255) NOT NULL COMMENT '祖先ID,逗号分隔',
            `name` varchar(32) NOT NULL COMMENT '目录名称',
            `level` int(11) NOT NULL COMMENT '层级',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除  1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '文档管理目录表';


            CREATE TABLE `doc_file` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构id',
            `dir_id` int(11) NOT NULL COMMENT '目录ID',
            `file_name` varchar(64) NOT NULL COMMENT '文件名称',
            `file_size` bigint(20) NOT NULL COMMENT '文件大小',
            `inner_name` varchar(128) NOT NULL COMMENT '存储名称',
            `file_url` varchar(255) NOT NULL COMMENT '文件存储地址',
            `user_id` int(11) NOT NULL COMMENT '上传用户ID',
            `user_name` varchar(32) NOT NULL COMMENT '上传用户名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除  1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '文档管理文件表';
        </sql>
    </changeSet>

    <changeSet id="2" author="qinzexing">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `doc_file`
            MODIFY COLUMN `file_url` varchar(255) NULL COMMENT '文件存储地址' AFTER `inner_name`;
        </sql>
    </changeSet>

    <changeSet id="3" author="qinzexing">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `doc_file`
            MODIFY COLUMN `file_url` varchar(255) NOT NULL COMMENT '文件存储地址' AFTER `inner_name`,
            ADD COLUMN `upload_state` int(11) NOT NULL DEFAULT 0 COMMENT '上传状态（0-上传中 1-上传完成）' AFTER `user_name`;
        </sql>
    </changeSet>

    <changeSet id="4" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `doc_dir_rule`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dir_id` int(11) NOT NULL COMMENT '目录ID',
            `user_id` int(11) NOT NULL COMMENT '用户ID',
            `dir_rule` varchar(20) NULL COMMENT '目录权限（上传-UPLOAD，预览-VIEW，下载-DOWNLOAD，重命名-RENAME，移动-MOVE，删除-DELETE，授权-AUTH）',
            `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '文档目录权限表';
        </sql>
    </changeSet>

    <changeSet id="5" author="qinzexing">
        <comment>重命名表</comment>
        <sql>
            rename table doc_file to doc_file_old;
        </sql>
    </changeSet>

    <changeSet id="6" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `doc_file`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `pid` int(11) NOT NULL COMMENT '父节点ID',
            `pids` varchar(255) NOT NULL COMMENT '祖先ID,逗号分隔',
            `name` varchar(64) NOT NULL COMMENT '名称',
            `size` bigint(20) NULL COMMENT '文件大小（单位：b）',
            `file_url` varchar(255) NULL COMMENT '文件地址',
            `user_id` int(11) NOT NULL COMMENT '创建人ID',
            `user_name` varchar(30) NOT NULL COMMENT '创建人姓名',
            `upload_state` int(11) NOT NULL DEFAULT 0 COMMENT '上传状态（0-上传中 1-上传完成）',
            `type` int(11) NOT NULL COMMENT '文件类型（1-目录   2-文件）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除  1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '文件表';
        </sql>
    </changeSet>

    <changeSet id="7" author="qinzexing">
        <comment>删除旧表</comment>
        <sql>
            DROP TABLE doc_dir;
            DROP TABLE doc_file_old;
        </sql>
    </changeSet>

    <changeSet id="8" author="xuguocheng">
        <comment>新增文档审批</comment>
        <sql>
            CREATE TABLE `doc_check` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `name` varchar(64) NOT NULL COMMENT '名称',
            `remark` varchar(64) DEFAULT NULL COMMENT '备注',
            `commit_time` datetime NOT NULL COMMENT '提交时间',
            `commit_user_id` int(11) DEFAULT NULL COMMENT '提交人',
            `commit_user_name` varchar(32) DEFAULT NULL COMMENT '提交人姓名',
            `state` int(11) NOT NULL COMMENT '审批状态(1-待审核 2-审核未通过 100-审核通过)',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档审批记录';

            CREATE TABLE `doc_check_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `check_id` int(11) NOT NULL COMMENT '审核记录iD',
            `type` int(11) NOT NULL COMMENT '审批类型:1-提交 10-一审 20-二审 30-三审 40-四审 50-五审 60-六审 70-七审 80-八审',
            `result` int(11) NOT NULL COMMENT '审批结果:1-通过 2-不通过',
            `remark` varchar(64) DEFAULT NULL COMMENT '审批备注',
            `op_time` datetime DEFAULT NULL COMMENT '操作时间',
            `op_state` int(11) NOT NULL DEFAULT '0' COMMENT '操作状态:0-未操作 1-已操作',
            `op_user_id` int(11) DEFAULT NULL COMMENT '审批人',
            `op_user_name` varchar(32) DEFAULT NULL COMMENT '审批人姓名',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档审批日志';

            CREATE TABLE `doc_check_file` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `check_id` int(11) NOT NULL COMMENT '审批记录ID',
            `check_log_id` int(11) NOT NULL COMMENT '审批日志ID',
            `file_name` varchar(255) DEFAULT NULL,
            `file_url` varchar(255) NOT NULL,
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档审批附件';
        </sql>
    </changeSet>

    <changeSet id="9" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `doc_check_log`
            ADD COLUMN `op_user_phone`  varchar(32) NULL COMMENT '审批人手机号' AFTER `op_user_name`;
        </sql>
    </changeSet>

    <changeSet id="10" author="xuguocheng">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `doc_check`
            MODIFY COLUMN `remark`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' AFTER `name`;

            ALTER TABLE `doc_check_log`
            MODIFY COLUMN `remark`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批备注' AFTER `result`;
        </sql>
    </changeSet>

    <changeSet id="11" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `doc_check`
            MODIFY COLUMN `state`  int(11) NOT NULL COMMENT '审批状态(1-待审核 2-审核未通过 100-审核通过 101 废弃)' AFTER `commit_user_name`,
            ADD COLUMN `deadline`  datetime NULL COMMENT '截止日期' AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="12" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `doc_file`
                MODIFY COLUMN `type` int(11) NOT NULL COMMENT '文件类型（1-目录   2-文件）' AFTER `pids`,
                ADD COLUMN `guid` varchar(32) NULL COMMENT 'GUID' AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="13" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `doc_file`
                MODIFY COLUMN `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称' AFTER `type`;
        </sql>
    </changeSet>

</databaseChangeLog>
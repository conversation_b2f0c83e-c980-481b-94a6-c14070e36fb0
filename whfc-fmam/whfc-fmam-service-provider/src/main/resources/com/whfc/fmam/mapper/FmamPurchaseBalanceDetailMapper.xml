<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.fmam.dao.FmamPurchaseBalanceDetailMapper">
  <resultMap id="BaseResultMap" type="com.whfc.fmam.entity.bo.FmamPurchaseBalanceDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="balance_id" jdbcType="BIGINT" property="balanceId" />
    <result column="mat_type_id" jdbcType="BIGINT" property="matTypeId" />
    <result column="mat_type_code" jdbcType="VARCHAR" property="matTypeCode" />
    <result column="mat_type_name" jdbcType="VARCHAR" property="matTypeName" />
    <result column="mat_id" jdbcType="BIGINT" property="matId" />
    <result column="mat_code" jdbcType="VARCHAR" property="matCode" />
    <result column="mat_name" jdbcType="VARCHAR" property="matName" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="texture" jdbcType="VARCHAR" property="texture" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="recv_amount" jdbcType="DECIMAL" property="recvAmount" />
    <result column="contract_price" jdbcType="DECIMAL" property="contractPrice" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, balance_id, mat_type_id, mat_type_code, mat_type_name, mat_id, mat_code, 
    mat_name, spec, model, texture, unit_id, unit_name, recv_amount, contract_price, 
    amount, price, money, remark, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fmam_purchase_balance_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fmam_purchase_balance_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.fmam.entity.bo.FmamPurchaseBalanceDetail">
    insert into fmam_purchase_balance_detail (id, dept_id, balance_id, 
      mat_type_id, mat_type_code, mat_type_name, 
      mat_id, mat_code, mat_name, 
      spec, model, texture, 
      unit_id, unit_name, recv_amount, 
      contract_price, amount, price, 
      money, remark, del_flag, 
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{balanceId,jdbcType=BIGINT}, 
      #{matTypeId,jdbcType=BIGINT}, #{matTypeCode,jdbcType=VARCHAR}, #{matTypeName,jdbcType=VARCHAR}, 
      #{matId,jdbcType=BIGINT}, #{matCode,jdbcType=VARCHAR}, #{matName,jdbcType=VARCHAR}, 
      #{spec,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{texture,jdbcType=VARCHAR}, 
      #{unitId,jdbcType=INTEGER}, #{unitName,jdbcType=VARCHAR}, #{recvAmount,jdbcType=DECIMAL}, 
      #{contractPrice,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, #{price,jdbcType=DECIMAL}, 
      #{money,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.fmam.entity.bo.FmamPurchaseBalanceDetail">
    insert into fmam_purchase_balance_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="balanceId != null">
        balance_id,
      </if>
      <if test="matTypeId != null">
        mat_type_id,
      </if>
      <if test="matTypeCode != null">
        mat_type_code,
      </if>
      <if test="matTypeName != null">
        mat_type_name,
      </if>
      <if test="matId != null">
        mat_id,
      </if>
      <if test="matCode != null">
        mat_code,
      </if>
      <if test="matName != null">
        mat_name,
      </if>
      <if test="spec != null">
        spec,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="texture != null">
        texture,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="unitName != null">
        unit_name,
      </if>
      <if test="recvAmount != null">
        recv_amount,
      </if>
      <if test="contractPrice != null">
        contract_price,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="balanceId != null">
        #{balanceId,jdbcType=BIGINT},
      </if>
      <if test="matTypeId != null">
        #{matTypeId,jdbcType=BIGINT},
      </if>
      <if test="matTypeCode != null">
        #{matTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="matTypeName != null">
        #{matTypeName,jdbcType=VARCHAR},
      </if>
      <if test="matId != null">
        #{matId,jdbcType=BIGINT},
      </if>
      <if test="matCode != null">
        #{matCode,jdbcType=VARCHAR},
      </if>
      <if test="matName != null">
        #{matName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="texture != null">
        #{texture,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="recvAmount != null">
        #{recvAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractPrice != null">
        #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.fmam.entity.bo.FmamPurchaseBalanceDetail">
    update fmam_purchase_balance_detail
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="balanceId != null">
        balance_id = #{balanceId,jdbcType=BIGINT},
      </if>
      <if test="matTypeId != null">
        mat_type_id = #{matTypeId,jdbcType=BIGINT},
      </if>
      <if test="matTypeCode != null">
        mat_type_code = #{matTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="matTypeName != null">
        mat_type_name = #{matTypeName,jdbcType=VARCHAR},
      </if>
      <if test="matId != null">
        mat_id = #{matId,jdbcType=BIGINT},
      </if>
      <if test="matCode != null">
        mat_code = #{matCode,jdbcType=VARCHAR},
      </if>
      <if test="matName != null">
        mat_name = #{matName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        spec = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="texture != null">
        texture = #{texture,jdbcType=VARCHAR},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        unit_name = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="recvAmount != null">
        recv_amount = #{recvAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractPrice != null">
        contract_price = #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.fmam.entity.bo.FmamPurchaseBalanceDetail">
    update fmam_purchase_balance_detail
    set dept_id = #{deptId,jdbcType=INTEGER},
      balance_id = #{balanceId,jdbcType=BIGINT},
      mat_type_id = #{matTypeId,jdbcType=BIGINT},
      mat_type_code = #{matTypeCode,jdbcType=VARCHAR},
      mat_type_name = #{matTypeName,jdbcType=VARCHAR},
      mat_id = #{matId,jdbcType=BIGINT},
      mat_code = #{matCode,jdbcType=VARCHAR},
      mat_name = #{matName,jdbcType=VARCHAR},
      spec = #{spec,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      texture = #{texture,jdbcType=VARCHAR},
      unit_id = #{unitId,jdbcType=INTEGER},
      unit_name = #{unitName,jdbcType=VARCHAR},
      recv_amount = #{recvAmount,jdbcType=DECIMAL},
      contract_price = #{contractPrice,jdbcType=DECIMAL},
      amount = #{amount,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByBalanceId" resultType="com.whfc.fmam.entity.dto.FmamPurchaseBalanceDetailDTO">
    select mat_type_id,
           mat_type_code,
           mat_type_name,
           mat_id,
           mat_code,
           mat_name,
           spec,
           model,
           texture,
           unit_id,
           unit_name,
           recv_amount,
           contract_price,
           amount,
           price,
           money,
           remark
      from fmam_purchase_balance_detail
     where balance_id = #{balanceId}
  </select>

  <insert id="batchInsert">
    insert into fmam_purchase_balance_detail
    (
    balance_id,
    dept_id,
    mat_type_id,
    mat_type_code,
    mat_type_name,
    mat_id,
    mat_code,
    mat_name,
    spec,
    model,
    texture,
    unit_id,
    unit_name,
    recv_amount,
    contract_price,
    amount,
    price,
    money,
    remark
    )
    values
    <foreach collection="matList" item="item" separator=",">
      (
      #{item.balanceId},
      #{item.deptId},
      #{item.matTypeId} ,
      #{item.matTypeCode} ,
      #{item.matTypeName} ,
      #{item.matId} ,
      #{item.matCode} ,
      #{item.matName} ,
      #{item.spec},
      #{item.model},
      #{item.texture},
      #{item.unitId},
      #{item.unitName},
      #{item.recvAmount},
      #{item.contractPrice},
      #{item.amount},
      #{item.price},
      #{item.money},
      #{item.remark}
      )
    </foreach>
  </insert>

  <update id="deleteByBalanceId">
    update fmam_purchase_balance_detail
    set del_flag = 1
    where balance_id = #{balanceId}
      and del_flag = 0
  </update>
</mapper>
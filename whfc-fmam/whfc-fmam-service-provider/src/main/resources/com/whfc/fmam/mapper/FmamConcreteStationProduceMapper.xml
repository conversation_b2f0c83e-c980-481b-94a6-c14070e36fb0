<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.fmam.dao.FmamConcreteStationProduceMapper">
  <resultMap id="BaseResultMap" type="com.whfc.fmam.entity.bo.FmamConcreteStationProduce">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="contract" jdbcType="VARCHAR" property="contract" />
    <result column="customer" jdbcType="VARCHAR" property="customer" />
    <result column="projname" jdbcType="VARCHAR" property="projname" />
    <result column="projadr" jdbcType="VARCHAR" property="projadr" />
    <result column="conspos" jdbcType="VARCHAR" property="conspos" />
    <result column="pour" jdbcType="VARCHAR" property="pour" />
    <result column="recid" jdbcType="VARCHAR" property="recid" />
    <result column="recipe" jdbcType="VARCHAR" property="recipe" />
    <result column="vehicle" jdbcType="VARCHAR" property="vehicle" />
    <result column="driver" jdbcType="VARCHAR" property="driver" />
    <result column="prodmete" jdbcType="DECIMAL" property="prodmete" />
    <result column="totvehs" jdbcType="DECIMAL" property="totvehs" />
    <result column="totmete" jdbcType="DECIMAL" property="totmete" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    `time`,
    guid,
    code,
    contract,
    customer,
    projname,
    projadr,
    conspos,
    pour,
    recid,
    recipe,
    vehicle,
    driver,
    prodmete,
    totvehs,
    totmete,
    flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fmam_concrete_station_produce
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fmam_concrete_station_produce
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.fmam.entity.bo.FmamConcreteStationProduce">
    insert into fmam_concrete_station_produce (id, dept_id, time,
      guid, code, contract,
      customer, projname, projadr,
      conspos, pour, recipe,
      vehicle, driver, prodmete,
      totvehs, totmete, flag,
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP},
      #{guid,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{contract,jdbcType=VARCHAR},
      #{customer,jdbcType=VARCHAR}, #{projname,jdbcType=VARCHAR}, #{projadr,jdbcType=VARCHAR},
      #{conspos,jdbcType=VARCHAR}, #{pour,jdbcType=VARCHAR}, #{recipe,jdbcType=VARCHAR},
      #{vehicle,jdbcType=VARCHAR}, #{driver,jdbcType=VARCHAR}, #{prodmete,jdbcType=DECIMAL},
      #{totvehs,jdbcType=DECIMAL}, #{totmete,jdbcType=DECIMAL}, #{flag,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.fmam.entity.bo.FmamConcreteStationProduce">
    insert into fmam_concrete_station_produce
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="contract != null">
        contract,
      </if>
      <if test="customer != null">
        customer,
      </if>
      <if test="projname != null">
        projname,
      </if>
      <if test="projadr != null">
        projadr,
      </if>
      <if test="conspos != null">
        conspos,
      </if>
      <if test="pour != null">
        pour,
      </if>
      <if test="recid != null">
        recid,
      </if>
      <if test="recipe != null">
        recipe,
      </if>
      <if test="vehicle != null">
        vehicle,
      </if>
      <if test="driver != null">
        driver,
      </if>
      <if test="prodmete != null">
        prodmete,
      </if>
      <if test="totvehs != null">
        totvehs,
      </if>
      <if test="totmete != null">
        totmete,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="contract != null">
        #{contract,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        #{customer,jdbcType=VARCHAR},
      </if>
      <if test="projname != null">
        #{projname,jdbcType=VARCHAR},
      </if>
      <if test="projadr != null">
        #{projadr,jdbcType=VARCHAR},
      </if>
      <if test="conspos != null">
        #{conspos,jdbcType=VARCHAR},
      </if>
      <if test="pour != null">
        #{pour,jdbcType=VARCHAR},
      </if>
      <if test="recid != null">
        #{recid},
      </if>
      <if test="recipe != null">
        #{recipe,jdbcType=VARCHAR},
      </if>
      <if test="vehicle != null">
        #{vehicle,jdbcType=VARCHAR},
      </if>
      <if test="driver != null">
        #{driver,jdbcType=VARCHAR},
      </if>
      <if test="prodmete != null">
        #{prodmete,jdbcType=DECIMAL},
      </if>
      <if test="totvehs != null">
        #{totvehs,jdbcType=DECIMAL},
      </if>
      <if test="totmete != null">
        #{totmete,jdbcType=DECIMAL},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.fmam.entity.bo.FmamConcreteStationProduce">
    update fmam_concrete_station_produce
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="contract != null">
        contract = #{contract,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="projname != null">
        projname = #{projname,jdbcType=VARCHAR},
      </if>
      <if test="projadr != null">
        projadr = #{projadr,jdbcType=VARCHAR},
      </if>
      <if test="conspos != null">
        conspos = #{conspos,jdbcType=VARCHAR},
      </if>
      <if test="pour != null">
        pour = #{pour,jdbcType=VARCHAR},
      </if>
      <if test="recid != null">
        recid = #{recid},
      </if>
      <if test="recipe != null">
        recipe = #{recipe,jdbcType=VARCHAR},
      </if>
      <if test="vehicle != null">
        vehicle = #{vehicle,jdbcType=VARCHAR},
      </if>
      <if test="driver != null">
        driver = #{driver,jdbcType=VARCHAR},
      </if>
      <if test="prodmete != null">
        prodmete = #{prodmete,jdbcType=DECIMAL},
      </if>
      <if test="totvehs != null">
        totvehs = #{totvehs,jdbcType=DECIMAL},
      </if>
      <if test="totmete != null">
        totmete = #{totmete,jdbcType=DECIMAL},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.fmam.entity.bo.FmamConcreteStationProduce">
    update fmam_concrete_station_produce
    set dept_id = #{deptId,jdbcType=INTEGER},
      time = #{time,jdbcType=TIMESTAMP},
      guid = #{guid,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      contract = #{contract,jdbcType=VARCHAR},
      customer = #{customer,jdbcType=VARCHAR},
      projname = #{projname,jdbcType=VARCHAR},
      projadr = #{projadr,jdbcType=VARCHAR},
      conspos = #{conspos,jdbcType=VARCHAR},
      pour = #{pour,jdbcType=VARCHAR},
      recipe = #{recipe,jdbcType=VARCHAR},
      vehicle = #{vehicle,jdbcType=VARCHAR},
      driver = #{driver,jdbcType=VARCHAR},
      prodmete = #{prodmete,jdbcType=DECIMAL},
      totvehs = #{totvehs,jdbcType=DECIMAL},
      totmete = #{totmete,jdbcType=DECIMAL},
      flag = #{flag,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectLastTime" resultType="java.util.Date">
    select `time`
    from fmam_concrete_station_produce
    where dept_id = #{deptId}
    order by `time` desc
    limit 1
  </select>

  <select id="selecProducetList" resultType="com.whfc.fmam.entity.dto.FmamConcreteStationProduceDTO">
    select id as produceId,
           `time`,
           code,
           contract,
           customer,
           projname,
           projadr,
           conspos,
           pour,
           recid,
           recipe,
           vehicle,
           driver,
           prodmete,
           totvehs,
           totmete
      from fmam_concrete_station_produce
     where dept_id = #{deptId}
      <if test="recid !=null and recid.length()>0">
        and recid = #{recid}
      </if>
      <if test="startTime !=null">
        and `time` >= #{startTime}
      </if>
      <if test="endTime !=null">
        and #{endTime} >=`time`
      </if>
    order by `time` desc
  </select>

  <select id="countByGuid" resultType="java.lang.Integer">
    select  count(*)
      from fmam_concrete_station_produce
     where guid = #{guid}
  </select>
</mapper>
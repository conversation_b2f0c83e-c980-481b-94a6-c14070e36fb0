package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 */
@Data
public class FmamPlanMatDTO implements Serializable {

    private Integer id;

    private Long matId;

    private String matCode;

    private String matName;

    private String spec;

    private String model;

    private String texture;

    private Integer unitId;

    private String unitName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date useTime;

    private String usePart;

    private Double number;

    private String remark;

    /**
     * 材料单状态：0-合格,1-不合格
     */
    private Integer checkState;

}
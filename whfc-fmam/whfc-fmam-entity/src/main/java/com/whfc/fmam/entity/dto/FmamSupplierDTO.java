package com.whfc.fmam.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/11/28 9:53
 */
@Data
public class FmamSupplierDTO implements Serializable {

    private Integer id;

    private Integer deptId;
    /**
     * 组织机构名称
     */
    private String deptName;
    /**
     * 供应商类别
     **/
    private Integer category;
    /**
     * 供应商编码
     **/
    private String code;
    /**
     * 供应商名称
     **/
    private String name;

    private String ename;
    /**
     * 合作单位ID
     **/
    private Integer corpId;
    /**
     * 合作单位类型ID
     **/
    private Integer corpTypeId;
    /**
     * 合作单位类型名称
     **/
    private String corpTypeName;
    /**
     * 供应商联系人
     **/
    private String linkPerson;
    /**
     * 供应商电话
     **/
    private String linkPhone;
    /**
     * 供应商地址
     **/
    private String address;

    private Integer enableFlag;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    /**
     * 经营范围
     */
    private String bizScope;

    /**
     * 地址编码 省
     * */
    private String province;
    /**
     * 地址编码 区
     * */
    private String city;
    /**
     * 地址编码 市
     * */
    private String area;
}

package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 周转材料-进出场单据
 */
@Data
public class FmamRentBillDTO implements Serializable {

    /**
     * 单据ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billId;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 单据类型
     */
    private Integer billType;

    /**
     * 单据编码
     */
    private String code;

    /**
     * 编制日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 单据时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date billTime;

    /**
     * 供应商
     */
    private Integer supplierId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 合同
     */
    private String contract;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 提交人
     */
    private Integer commitUserId;

    /**
     * 提交人
     */
    private String commitUserName;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 材料列表
     */
    private List<FmamRentBillDetailDTO> matList;

    /**
     * 操作日志
     */
    private List<FmamStockOpLogDTO> logList;

    private Integer current;
}
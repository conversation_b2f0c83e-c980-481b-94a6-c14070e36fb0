package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 库存
 */
@Data
public class FmamStockDTO implements Serializable {

    /**
     * 材料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;

    /**
     * 材料编号
     */
    private String matCode;

    /**
     * 材料名称
     */
    private String matName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    /**
     * 材质
     */
    private String texture;

    /**
     * 单位ID
     */
    private Integer unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 成本科目
     */
    private Integer costType;

    /**
     * 价格
     */
    private Double price;

    /**
     * 数量
     */
    private Double amount;

    /**
     * 金额
     */
    private Double money;

    /**
     * 盘点数量
     */
    private Double checkAmount;

    /**
     * 盘点金额
     */
    private Double checkMoney;

    /**
     * 盈亏数量
     */
    private Double profitAmount;

    /**
     * 盈亏金额
     */
    private Double profitMoney;

    /**
     * 结存数量
     */
    private Double balanceAmount;

    /**
     * 结存金额
     */
    private Double balanceMoney;
}
package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 结存
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/3/15 16:41
 */
@Data
public class FmamBalanceDTO implements Serializable {

    /**
     * 材料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;

    /**
     * 材料编号
     */
    private String matCode;

    /**
     * 材料名称
     */
    private String matName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    /**
     * 材质
     */
    private String texture;

    /**
     * 单位ID
     */
    private Integer unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 期初-价格
     */
    private Double startPrice;

    /**
     * 期初-数量
     */
    private Double startAmount;

    /**
     * 期初-金额
     */
    private Double startMoney;

    /**
     * 收入-价格
     */
    private Double inPrice;

    /**
     * 收入-数量
     */
    private Double inAmount;

    /**
     * 收入-金额
     */
    private Double inMoney;

    /**
     * 支出-价格
     */
    private Double outPrice;

    /**
     * 支出-数量
     */
    private Double outAmount;

    /**
     * 支出-金额
     */
    private Double outMoney;

    /**
     * 调出-价格
     */
    private Double outerPrice;

    /**
     * 调出-数量
     */
    private Double outerAmount;

    /**
     * 调出-金额
     */
    private Double outerMoney;

    /**
     * 期末-价格
     */
    private Double endPrice;

    /**
     * 期末-数量
     */
    private Double endAmount;

    /**
     * 期末-金额
     */
    private Double endMoney;

    /**
     * 成本科目统计
     */
    private List<FmamCostDTO> costList;

    /**
     * 序号
     */
    private Integer seq;
}

package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 材料需求计划
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/3/8 16:33
 */
@Data
public class FmamStockMrpDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long mrpId;

    private Integer type;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    private String parentCode;

    private String period;

    private String code;

    private String name;

    private Date commitTime;

    private Integer commitUserId;

    private String commitUserName;

    /**
     * 当前用户
     */
    private Integer current;

    private Integer state;

    /**
     * 材料列表
     */
    private List<FmamStockMrpDetailDTO> matList;

    /**
     * 操作日志
     */
    private List<FmamStockOpLogDTO> logList;
}

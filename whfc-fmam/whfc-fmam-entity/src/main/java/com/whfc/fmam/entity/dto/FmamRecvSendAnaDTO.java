package com.whfc.fmam.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 按材料明细进行收料和发料汇总分析
 * @date 2019-12-18
 */
@Data
public class FmamRecvSendAnaDTO implements Serializable {
    /**
     * 运单数量
     */
    private Double deliveryAmount;
    /**
     * 运单重量
     */
    private Double deliveryWeight;
    /**
     * 实际数量
     */
    private Double actualAmount;
    /**
     * 确认重量
     */
    private Double confirmWeight;
    /**
     * 确认数量
     */
    private Double confirmAmount;
    /**
     * 实际重量
     */
    private Double actualWeight;
    /**
     * 扣量（吨）
     */
    private Double deductAmount;

    private List<FmamRecvSendAnaMatDTO> list;

    /**
     * 过磅材料数
     */
    private Integer actualMatNum;
    /**
     * 超正差数
     */
    private Integer posDiffNum;
    /**
     * 超负差数
     */
    private Integer negDiffNum;
    /**
     * 正常偏差数
     */
    private Integer norDiffNum;

    private List<FmamRecvSendAnaMatTypeDTO> matTypeDTOList;

    /**
     * 供应商列表集合
     */
    private List<FmamSendSupplierDTO> sendSupplierList;


}

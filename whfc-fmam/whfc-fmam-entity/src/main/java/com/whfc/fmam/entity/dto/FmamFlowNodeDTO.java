package com.whfc.fmam.entity.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class FmamFlowNodeDTO implements Serializable {
    private Integer nodeId;

    private Integer deptId;

    private Integer flowId;

    @NotNull
    private Integer type;

    @NotEmpty
    @Length(max = 32)
    private String name;

    private Integer opUserId;

    private String opUserName;

    private String sign;
}
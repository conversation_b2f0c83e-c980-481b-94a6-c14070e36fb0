package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-13 20:10
 */
@Data
public class FmamMatDetailDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matTypeId;

    private String code;

    private String fullCode;

    private String name;

    private String ename;

    private String matTypeCode;

    private String matTypeName;

    private String spec;

    private String model;

    private String texture;

    private Integer unitId;

    private String unitName;

    private String remark;

    private Integer diffType;

    private Double plusDiff;

    private Double minusDiff;

    private Integer delFlag;

    private Integer enableFlag;
}

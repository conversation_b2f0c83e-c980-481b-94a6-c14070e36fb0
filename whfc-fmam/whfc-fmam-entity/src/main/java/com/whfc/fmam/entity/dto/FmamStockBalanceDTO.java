package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class FmamStockBalanceDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long balanceId;

    private Integer deptId;

    private Integer warehouseId;

    private String warehouseName;

    private String code;

    private String period;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date balanceTime;

    private Date commitTime;

    private Integer commitUserId;

    private String commitUserName;

    private Integer state;

    /**
     * 材料列表
     */
    private List<FmamStockBalanceDetailDTO> matList;

    /**
     * 日志列表
     */
    private List<FmamStockOpLogDTO> logList;
}
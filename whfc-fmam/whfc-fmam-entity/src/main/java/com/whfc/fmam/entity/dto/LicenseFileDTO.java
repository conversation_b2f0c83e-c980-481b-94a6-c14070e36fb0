package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-15
 */
@Data
public class LicenseFileDTO implements Serializable {
    private Integer weighRoomId;
    private Integer activeFlag;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date activeDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;
    private String licenseFile;
}

package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 租赁周转材料-台账
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/7/18 14:46
 */
@Data
public class FmamRentStockDTO implements Serializable {

    /**
     * 材料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;

    /**
     * 材料编码
     */
    private String matCode;

    /**
     * 材料名称
     */
    private String matName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    /**
     * 材质
     */
    private String texture;

    /**
     * 计量单位
     */
    private Integer unitId;

    /**
     * 计量单位
     */
    private String unitName;

    /**
     * 计费单位
     */
    private String feeUnit;

    /**
     * 进场数量
     */
    private BigDecimal inAmount;

    /**
     * 退场数量
     */
    private BigDecimal outAmount;

    /**
     * 在场数量
     */
    private BigDecimal presentAmount;

    private BigDecimal startInAmout;

    private BigDecimal startOutAmout;

    private BigDecimal startAmount;

    private BigDecimal endInAmout;

    private BigDecimal endOutAmout;

    private BigDecimal endAmount;
}

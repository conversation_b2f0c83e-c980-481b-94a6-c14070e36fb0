package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description: 材料的计量单位转换
 * @author: sunguodong
 * @version: 1.0
 * @date: 2019/10/10 11:00
 */
@Getter
@Setter
@ToString
public class FmamMatUnitDTO implements Serializable {
    private Long id;

    private Integer deptId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;

    private Integer unitId;

    private String unitName;

    private Double convertFactor;

    private Integer convertUnitId;

    private String convertUnitName;
}

package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 混凝土小票
 */
@Data
public class FmamConcreteBillDTO implements Serializable {

    /**
     * 小票ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long billId;

    /**
     * 项目ID
     */
    @JsonIgnore
    private Integer deptId;

    /**
     * 单据编码
     */
    private String code;

    /**
     * 编制日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 编制人
     */
    private String editor;

    /**
     * 进场时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enterTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 任务单号
     */
    private String taskNo;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 合同
     */
    private String contract;

    /**
     * 小票数量
     */
    private Integer receiptNum;

    /**
     * 部位
     */
    private String partName;

    /**
     * 核算对象
     */
    private String checkObject;

    /**
     * 材料用途
     */
    private String use;

    /**
     * 商砼编号
     */
    private String concreteNo;

    /**
     * 商砼名称
     */
    private String concreteName;

    /**
     * 强度等级
     */
    private String strength;

    /**
     * 合同单价
     */
    private BigDecimal contractPrice;

    /**
     * 数量
     */
    private BigDecimal amount;

    /**
     * 金额
     */
    private BigDecimal money;

    /**
     * 单位
     */
    private Integer unitId;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 附加费
     */
    private String surcharge;

    /**
     * 塌落度
     */
    private String slump;

    /**
     * 泵送单位
     */
    private String pumpingUnit;

    /**
     * 使用单位
     */
    private String useUnit;

    /**
     * 运输方式
     */
    private String trafficType;

    /**
     * 运输距离
     */
    private String trafficDistance;

    /**
     * 发车时间
     */
    private Date trafficStartTime;

    /**
     * 到场时间
     */
    private Date trafficEndTime;

    /**
     * 浇筑方式
     */
    private String pourType;

    /**
     * 浇筑单位
     */
    private String pourUnit;

    /**
     * 浇筑开始日期
     */
    private Date pourStartTime;

    /**
     * 浇筑完成日期
     */
    private Date pourEndTime;

    /**
     * 车号
     */
    private String carNo;

    /**
     * 司机
     */
    private String dirver;

    /**
     * 发料人
     */
    private String sendUser;

    /**
     * 收料人
     */
    private String recvUser;

    private String remark;

    private Date commitTime;

    private Integer commitUserId;

    private String commitUserName;

    private Integer state;

    /**
     * 操作日志
     */
    List<FmamStockOpLogDTO> logList;

    /*****导出字段******/

    private Integer seq;

    private String enterTimeStr;

    private String dateStr;
}
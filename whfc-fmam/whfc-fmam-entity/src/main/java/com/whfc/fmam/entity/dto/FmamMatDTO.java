package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: sunguodong
 * @version: 1.0
 * @date: 2019/10/16 17:26
 */
@Data
public class FmamMatDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long matTypeId;

    private String matTypeCode;

    private String matTypeName;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long matId;

    private String matCode;

    private String matName;

    private String code;

    private String fullCode;

    private String name;

    private String ename;

    private String spec;

    private String model;

    private String texture;

    private Integer unitId;

    private String unitName;

    private String quality;

    private String remark;

    private Integer diffType;

    private Double plusDiff;

    private Double minusDiff;

    /**
     * 计量单位
     */
    private List<FmamMatUnitDTO> unitList;
}

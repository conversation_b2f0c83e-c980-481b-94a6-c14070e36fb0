package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * <AUTHOR>
 * @date 2019-11-26
 */
@Data
public class FmamWeighNoteImgDTO implements Serializable {
    /**
     * 图片类型:1-进场图片 2-出场图片 3-运单
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long imgId;
    private Integer type;
    private String imgUrl;
}

package com.whfc.fmam.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
@Data
public class FmamPlanDTO implements Serializable {

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 提交人
     */
    private String userName;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划编号
     */
    private String code;

    /**
     * 附件地址
     */
    private String fileUrl;

    /**
     * 材料计划状态
     *
     * @see com.whfc.fmam.entity.enums.MatPlanCheckState
     */
    private Integer state;

    /**
     * 计划提交时间
     */
    private Date planTime;

    /**
     * 材料清单
     */
    private List<FmamPlanMatDTO> matList;

    /**
     * 材料审批进度
     */
    private List<FmamPlanCheckLogDTO> checkLogList;

    /**
     * 用户允许操作标识
     */
    private Boolean isOpt;
}

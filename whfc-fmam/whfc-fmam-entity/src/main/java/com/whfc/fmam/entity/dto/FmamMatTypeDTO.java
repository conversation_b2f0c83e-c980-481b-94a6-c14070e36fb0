package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: sunguodong
 * @version: 1.0
 * @date: 2019/10/16 17:26
 */
@Setter
@Getter
@ToString
public class FmamMatTypeDTO implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    private String code;

    private String fullCode;

    private Integer level;

    private String name;

    private String remark;

    private Integer weighFlag;

    private Integer diffType;

    private Double plusDiff;

    private Double minusDiff;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    private List<FmamMatTypeDTO> children;
}

package com.whfc.fmam.entity.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description: 材料的计量单位转换
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/10 11:00
 */
@Getter
@Setter
@ToString
public class SyncFmamMatUnitDTO implements Serializable {
    private Long matId;

    private Integer unitId;

    private String unitName;

    private Double convertFactor;

    private Integer convertUnitId;

    private String convertUnitName;
}

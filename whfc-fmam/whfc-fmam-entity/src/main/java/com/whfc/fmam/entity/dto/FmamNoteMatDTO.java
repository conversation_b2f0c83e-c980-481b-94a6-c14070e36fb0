package com.whfc.fmam.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-25
 */
@Data
public class FmamNoteMatDTO implements Serializable {

    /**
     * 磅单材料id
     */
    private Long matId;

    /**
     * 材料名称
     */
    private String matName;

    /**
     * 材料编码
     */
    private String matCode;

    /**
     * 批次编号
     */
    private String batchCode;

    /**
     * 材料规格
     */
    private String spec;

    /**
     * 材料型号
     */
    private String model;

    /**
     * 偏差结果:0-正常 ,1-超正差 ,-1:超负差
     */
    private Integer diffResult;

    /**
     * 运单数量
     */
    private Double deliveryAmount;

    /**
     * 运单计量单位
     */
    private String deliveryUnitName;

    /**
     * 换算计量单位
     */
    private String convertUnitName;

    /**
     * 实际数量
     */
    private Double actualAmount;

    /**
     * 运单重量
     */
    private Double deliveryWeight;

    /**
     * 实际重量
     */
    private Double actualWeight;

    /**
     * 扣重
     */
    private Double deductAmount;

    /**
     * 偏差类型(1-范围偏差,2-比例偏差)
     */
    private Integer diffType;

    /**
     * 正偏差
     */
    private Double plusDiff;

    /**
     * 负偏差
     */
    private Double minusDiff;

    /**
     * 实际偏差
     */
    private Double actualDiff;

    /**
     * 确认数量
     */
    private Double confirmAmount;

    /**
     * 单价
     */
    private Double price;

    /**
     * 金额
     */
    private Double money;

    /**
     * 成本科目
     */
    private Integer cost_type;

    /**
     * 发票号
     */
    private String invoice_no;

}

package com.whfc.fmam.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class FmamRentBalanceDTO implements Serializable {

    /**
     * 结算ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long balanceId;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 单据编码
     */
    private String code;

    /**
     * 编制日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 结算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date balanceTime;

    /**
     * 结算方式
     */
    private String balanceType;

    /**
     * 供应商
     */
    private Integer supplierId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 银行类型
     */
    private Integer bankType;

    /**
     * 银行名称
     */
    private String bankTypeName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 合同
     */
    private String contract;

    /**
     * 结算金额
     */
    private BigDecimal money;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 提交人
     */
    private Integer commitUserId;

    /**
     * 提交人
     */
    private String commitUserName;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 材料列表
     */
    private List<FmamRentBalanceDetailDTO> matList;

    /**
     * 操作日志
     */
    private List<FmamStockOpLogDTO> logList;

    private Integer current;
}
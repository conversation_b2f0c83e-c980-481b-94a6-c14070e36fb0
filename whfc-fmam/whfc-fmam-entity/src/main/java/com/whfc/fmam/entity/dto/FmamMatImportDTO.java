package com.whfc.fmam.entity.dto;

import com.sargeraswang.util.ExcelUtil.ExcelCell;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


import java.io.Serializable;

/**
 * @Author: ；likang
 * @Description:
 * @Date:Create：in 2019/12/28 16:26
 * @Version：1.0
 */
@Setter
@Getter
@ToString
public class FmamMatImportDTO  implements Serializable {

    @ExcelCell(index = 0)
    private String fullCode;

    @ExcelCell(index = 1)
    private String name;

    @ExcelCell(index = 2)
    private String spec;

    @ExcelCell(index = 3)
    private String model;

    @ExcelCell(index = 4)
    private String texture;

    @ExcelCell(index = 5)
    private String unitName;

    @ExcelCell(index = 6)
    private Integer diffType;

    @ExcelCell(index = 7)
    private Double plusDiff;

    @ExcelCell(index = 8)
    private Double minusDiff;

    @ExcelCell(index = 9)
    private String remark;

    @ExcelCell(index = 10)
    private String result;

}

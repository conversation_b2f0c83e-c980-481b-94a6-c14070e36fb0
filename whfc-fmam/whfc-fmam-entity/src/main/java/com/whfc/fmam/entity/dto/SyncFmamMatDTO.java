package com.whfc.fmam.entity.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/16 17:26
 */
@Setter
@Getter
@ToString
public class SyncFmamMatDTO implements Serializable {
    private Long id;

    private Long matTypeId;

    private String code;

    private String fullCode;

    private String name;

    private String spec;

    private String model;

    private String texture;

    private Integer unitId;

    private String unitName;
    
    private String remark;

    private Integer diffType;

    private Double plusDiff;

    private Double minusDiff;

    private Integer delFlag;

    private Integer enableFlag;

    private Date updateTime;

    private Date createTime;

    /**
     * 计量单位
     */
    private List<SyncFmamMatUnitDTO> unitList;
}

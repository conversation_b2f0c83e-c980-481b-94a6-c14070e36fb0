package com.whfc.fmam.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fmam.entity.dto.FmamMatDTO;
import com.whfc.fmam.entity.dto.FmamMatImportResultDTO;
import com.whfc.fmam.entity.dto.FmamMatTypeDTO;
import com.whfc.fmam.entity.dto.SyncFmamUnitDTO;
import com.whfc.fmam.entity.qo.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 材料服务
 * @date 2019-11-19
 */
public interface FmamMatService {

    /**
     * 根据组织机构获取所有的材料类别
     *
     * @param deptId
     * @param keyword
     * @return
     * @throws BizException
     */
    List<FmamMatTypeDTO> getMatTypeListByDeptId(Integer deptId, String keyword) throws BizException;

    /**
     * 新增材料类别
     *
     * @param request
     */
    void matTypeAdd(MatTypeAddQO request) throws BizException;

    /**
     * 编辑材料类别
     *
     * @param request
     */
    void matTypeEdit(MatTypeEditQO request) throws BizException;

    /**
     * 删除材料类型
     *
     * @param id
     */
    void matTypeDel(Long id) throws BizException;


    /**
     * 材料列表
     *
     * @param matTypeId
     * @param pageNum
     * @param pageSize
     * @param keyword
     * @return
     */
    PageData<FmamMatDTO> matList(Long matTypeId, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 获取所有的材料列表
     *
     * @param deptId
     * @param matTypeId
     * @return
     */
    ListData<FmamMatDTO> matList(Integer deptId, Long matTypeId);

    /**
     * 材料列表
     *
     * @param deptId   deptId
     * @param pageNum  pageNum
     * @param pageSize pageSize
     * @param keyword  keyword
     * @return PageData<FmamMatDTO>
     * @throws BizException
     */
    PageData<FmamMatDTO> matList(Integer deptId, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 新增材料
     *
     * @param request
     */
    void matAdd(MatAddQO request) throws BizException;

    /**
     * 编辑材料
     *
     * @param request
     */
    void matEdit(MatEditQO request) throws BizException;

    /**
     * 删除材料
     *
     * @param id
     */
    void matDel(Long id) throws BizException;

    /**
     * 添加导入物资数据
     *
     * @param deptId
     * @param matImportQOs
     */
    FmamMatImportResultDTO addExcelMat(Integer deptId, Collection<MatImportQO> matImportQOs, List<Map<Integer, Integer>> list) throws BizException;

    /**
     * 根据pid获取所有的子孙材料类型id
     */
    List<Long> getDescendantMatTypeIdList(Long pid) throws BizException;

    /**
     * 查询所有的计量单位
     *
     * @return
     */
    List<SyncFmamUnitDTO> getAllFmamUnitList() throws BizException;
}

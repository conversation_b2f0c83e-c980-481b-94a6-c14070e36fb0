package com.whfc.fmam.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fmam.entity.dto.FmamScreenFinDTO;
import com.whfc.fmam.entity.dto.FmamScreenLaserDTO;
import com.whfc.fmam.entity.dto.FmamScreenRawDTO;
import com.whfc.fmam.entity.dto.FmamScreenStatDTO;

import java.util.Date;
import java.util.List;

/**
 * @Description: 数字筛分服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/9/13 9:12
 */
public interface FmamScreenService {

    /**
     * 原材料-列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FmamScreenRawDTO> screenRawList(Integer deptId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 原材料-开累
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenRawStatAll(Integer deptId) throws BizException;

    /**
     * 原材料-年累
     *
     * @param deptId
     * @param year
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenRawStatYear(Integer deptId, Integer year) throws BizException;

    /**
     * 原材料-月累
     *
     * @param deptId
     * @param month
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenRawStatMonth(Integer deptId, Date month) throws BizException;

    /**
     * 成品料-列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FmamScreenFinDTO> screenFinList(Integer deptId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 成品料-开累
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenFinStatAll(Integer deptId) throws BizException;

    /**
     * 成品料-年累
     *
     * @param deptId
     * @param year
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenFinStatYear(Integer deptId, Integer year) throws BizException;

    /**
     * 成品料-月累
     *
     * @param deptId
     * @param month
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenFinStatMonth(Integer deptId, Date month) throws BizException;

    /**
     * 成品料-三维激光扫描列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FmamScreenLaserDTO> screenLaserList(Integer deptId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 成品料-三维激光扫描-最后一次记录
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FmamScreenLaserDTO> screenLaserLast(Integer deptId) throws BizException;

    /**
     * 成品料-皮带机称重统计
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FmamScreenStatDTO> screenWeightStat(Integer deptId) throws BizException;
}

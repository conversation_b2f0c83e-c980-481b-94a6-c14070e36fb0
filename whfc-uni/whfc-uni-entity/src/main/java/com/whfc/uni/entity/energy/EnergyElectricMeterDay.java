package com.whfc.uni.entity.energy;

import java.util.Date;
import lombok.Data;

/**
 * 电表设备每日数据统计
 */
@Data
public class EnergyElectricMeterDay {
    private Integer id;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 水表id
     */
    private Integer rpeId;

    /**
     * 设备id
     */
    private Integer deviceId;

    /**
     * 上传时间
     */
    private Date time;

    /**
     * 正向有功总电能
     */
    private Double totalPositiveActive;

    /**
     * 反向有功总电能
     */
    private Double totalReverseActive;

    /**
     * 正向无功总电能
     */
    private Double totalPositiveReactive;

    /**
     * 反向无功总电能
     */
    private Double totalReverseReactive;

    /**
     * 删除标记
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
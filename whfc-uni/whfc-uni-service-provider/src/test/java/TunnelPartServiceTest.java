import com.alibaba.fastjson.JSONObject;
import com.whfc.uni.param.tunnel.TunnelPartAddParam;
import com.whfc.uni.param.tunnel.TunnelPartEditParam;
import com.whfc.uni.service.TunnelService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @Description 隧道部位测试
 * <AUTHOR>
 * @Date 2021-05-20 15:00
 * @Version 1.0
 */
public class TunnelPartServiceTest {

    private TunnelService appTunnelPartService;


    @Before
    public void setUp() {
        ReferenceConfig<TunnelService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(TunnelService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appTunnelPartService = referenceConfig.get();
    }

    @Test
    public void add() {
        TunnelPartAddParam param = new TunnelPartAddParam();
        param.setDeptId(2);
        param.setPartName("白沙坡右幅");
        appTunnelPartService.add(param);
    }

    @Test
    public void edit() {
        TunnelPartEditParam param = new TunnelPartEditParam();
        param.setPartId(1);
        param.setDeptId(2);
        param.setPartName("白沙坡右幅修改");
        appTunnelPartService.edit(param);
    }

    @Test
    public void list() {
        System.out.println(JSONObject.toJSONString(appTunnelPartService.list(2, "修改")));
    }

    @Test
    public void del() {
        appTunnelPartService.del(1);
    }

    @Test
    public void tunnelList(){
        System.out.println(JSONObject.toJSONString(appTunnelPartService.tunnelList(2)));
    }
}

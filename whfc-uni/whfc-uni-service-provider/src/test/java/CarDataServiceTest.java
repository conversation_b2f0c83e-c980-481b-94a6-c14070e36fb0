import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.uni.dto.car.CarBoardAnalysisDTO;
import com.whfc.uni.service.CarInoutRecordService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class CarDataServiceTest {

    private CarInoutRecordService service;

    @Before
    public void setUp() {
        ReferenceConfig<CarInoutRecordService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(CarInoutRecordService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void test1() {
        Date startTime = DateUtil.parseDateTime("2021-12-01 00:00:00");
        Date endTime = DateUtil.parseDateTime("2022-12-01 00:00:00");
        OssPathDTO ossPathDTO = service.export(2, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(ossPathDTO));
    }

    @Test
    public void test2() {
        Integer deptId = 469;
        Date startTime = DateUtil.parseDateTime("2022-06-16 00:00:00");
        Date endTime = DateUtil.parseDateTime("2022-06-16 23:59:59");
        CarBoardAnalysisDTO data1 = service.boardAnalysis(deptId, startTime, endTime);
        CarBoardAnalysisDTO data2 = service.boardAnalysis2(deptId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(data1));
        System.out.println(JSONUtil.toPrettyString(data2));
    }
}

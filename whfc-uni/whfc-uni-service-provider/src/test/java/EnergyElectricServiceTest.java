import com.whfc.common.util.DateUtil;
import com.whfc.uni.service.EnergyElectricService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class EnergyElectricServiceTest {

    private EnergyElectricService EnergyElectricService;

    @Before
    public void setUp() {
        ReferenceConfig<EnergyElectricService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(EnergyElectricService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        EnergyElectricService = referenceConfig.get();
    }

    @Test
    public void test1() {
        Date startTime = DateUtil.getDateBegin(new Date());
        Date endTime = DateUtil.getDateEnd(new Date());
        EnergyElectricService.deviceHoursStatistics(2, null, startTime, endTime);
    }
}

import com.alibaba.excel.EasyExcel;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.MathUtil;
import com.whfc.uni.dto.concrete.ConcreateImportDTO;
import com.whfc.uni.dto.concrete.ConcreteDataPointDTO;
import com.whfc.uni.dto.concrete.ConcreteItemDataDTO;
import com.whfc.uni.service.ConcreteService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/10/26 13:57
 */
public class ConcreteServiceTest {

    private ConcreteService service;

    private static final String format = "yyyy/M/d H:m:s";

    @Before
    public void setUp() {
        ReferenceConfig<ConcreteService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(ConcreteService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void testImport() throws Exception {
        Integer concreteId = 9;
        String filepath = "C:\\Users\\<USER>\\Desktop\\1#风机.xlsx";
        List<ConcreateImportDTO> list = EasyExcel.read(new FileInputStream(filepath))
                .head(ConcreateImportDTO.class)
                .sheet()
                .headRowNumber(1)
                .doReadSync();

        List<ConcreteItemDataDTO> itemList = new ArrayList<>(list.size());
        for (ConcreateImportDTO dto : list) {

            List<ConcreteDataPointDTO> pointList = new ArrayList<>(12);
            pointList.add(new ConcreteDataPointDTO("通道01", dto.getT1()));
            pointList.add(new ConcreteDataPointDTO("通道02", dto.getT2()));
            pointList.add(new ConcreteDataPointDTO("通道03", dto.getT3()));
            pointList.add(new ConcreteDataPointDTO("通道04", dto.getT4()));
            pointList.add(new ConcreteDataPointDTO("通道05", dto.getT5()));
            pointList.add(new ConcreteDataPointDTO("通道06", dto.getT6()));
            pointList.add(new ConcreteDataPointDTO("通道07", dto.getT7()));
            pointList.add(new ConcreteDataPointDTO("通道08", dto.getT8()));
            pointList.add(new ConcreteDataPointDTO("通道09", dto.getT9()));
            pointList.add(new ConcreteDataPointDTO("通道10", dto.getT10()));
            pointList.add(new ConcreteDataPointDTO("通道11", dto.getT11()));
            pointList.add(new ConcreteDataPointDTO("通道12", dto.getT12()));

            List<Double> values = pointList.stream().map(ConcreteDataPointDTO::getValue).collect(Collectors.toList());
            Collections.sort(values);
            Double tempDiff = MathUtil.round(values.get(values.size() - 1) - values.get(0), 1);

            ConcreteItemDataDTO item = new ConcreteItemDataDTO();
            item.setTime(DateUtil.parseDate(dto.getTime(),format));
            item.setData(JSONUtil.toString(pointList));
            item.setTempDiff(tempDiff);
            item.setTempOver(tempDiff > 25.0 ? 1 : 0);
            itemList.add(item);
        }
        service.importItemData(concreteId, itemList);
    }
}

import com.whfc.uni.service.MonDataService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class MonDataServiceTest {

    private MonDataService service;

    @Before
    public void setUp() {
        ReferenceConfig<MonDataService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(MonDataService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void test1() {
        service.getMonInfoTree(2, 1);
    }
}

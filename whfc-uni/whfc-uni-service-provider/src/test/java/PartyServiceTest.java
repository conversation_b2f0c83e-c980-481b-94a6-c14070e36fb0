import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.uni.param.party.PartyActAdd;
import com.whfc.uni.param.party.PartyMemberAdd;
import com.whfc.uni.param.party.PartyMemberEdit;
import com.whfc.uni.service.PartyService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class PartyServiceTest {

    private PartyService service;

    @Before
    public void setUp() {
        ReferenceConfig<PartyService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(PartyService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void testPartyMemberList() {
        List<?> list = service.partyMemberList(2);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddPartyMember() {
        PartyMemberAdd add = new PartyMemberAdd();
        add.setDeptId(2);
        add.setName("李四");
        add.setGender(1);
        add.setNation("回族");
        add.setPartyBranch("风潮党支部");
        add.setPartyTime1(DateUtil.parseDate("2021-12-13", "yyyy-MM-dd"));
        add.setPartyTime2(DateUtil.parseDate("2021-12-31", "yyyy-MM-dd"));
        service.addPartyMember(add);
    }

    @Test
    public void testEditPartyMember() {
        PartyMemberEdit edit = new PartyMemberEdit();
        edit.setMemberId(1);
        edit.setName("张三");
        edit.setGender(1);
        edit.setNation("汉族");
        edit.setPartyBranch("风潮党支部-1");
        edit.setPartyTime1(DateUtil.parseDate("2021-12-13", "yyyy-MM-dd"));
        edit.setPartyTime2(DateUtil.parseDate("2021-12-31", "yyyy-MM-dd"));
        service.editPartyMember(edit);
    }

    @Test
    public void testDelPartyMember() {
        service.delPartyMember(1);
    }

    @Test
    public void testPartyActList() {
        List<?> list = service.partyActList(2, 1);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddAct() {
        PartyActAdd param = new PartyActAdd();
        param.setDeptId(2);
        param.setActTypeId(1);
        param.setActTypeName("党建学习");
        param.setActTime(new Date());
        param.setTitle("2022-01-11 党建学习");
        param.setContent("xxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
        service.addAct(param);
    }
}

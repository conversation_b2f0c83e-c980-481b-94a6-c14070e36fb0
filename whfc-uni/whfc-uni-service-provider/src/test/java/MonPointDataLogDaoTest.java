import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.uni.influx.MonPointDataLogDao;
import com.whfc.uni.dto.mon.MonPointDataDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/12/21 12:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class MonPointDataLogDaoTest {

    @Autowired
    private MonPointDataLogDao logDao;

    @Test
    public void testInsert1() {
        String pintId = "1";
        String sn = "20190705#0#1#0";
        MonPointDataDTO log = buildData(pintId, sn, new Date());
        logDao.insert(log);
    }

    @Test
    public void testInsert2() throws Exception {
        String pintId = "1";
        String sn = "20190705#0#1#1";
        MonPointDataDTO log1 = buildData(pintId, sn, new Date());
        TimeUnit.SECONDS.sleep(1);
        MonPointDataDTO log2 = buildData(pintId, sn, new Date());
        logDao.batchInsert(Arrays.asList(log1, log2));
    }

    @Test
    public void testSelectPointDataLogByPointId() {
        Integer pointId = 1;
        Date startDate = DateUtil.parseDateTime("2021-11-02 00:00:00");
        Date endDate = DateUtil.parseDateTime("2021-11-02 23:00:00");
        List<MonPointDataDTO> list = logDao.selectPointDataLogByPointId(pointId, startDate, endDate);
        Assert.assertTrue(list.size() > 0);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testSelectPointDataLogBySn() {
        String sn = "20190705#0#1#0";
        Date startDate = DateUtil.parseDateTime("2021-11-02 00:00:00");
        Date endDate = DateUtil.parseDateTime("2021-11-02 23:00:00");
        List<MonPointDataDTO> list = logDao.selectPointDataLogBySn(sn, startDate, endDate);
        Assert.assertTrue(list.size() > 0);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    private MonPointDataDTO buildData(String pointId, String sn, Date time) {
        MonPointDataDTO log = new MonPointDataDTO();
        log.setPointId(pointId);
        log.setSn(sn);
        log.setTime(time);
        log.setDevType(11);
        log.setMonType(15);
        log.setDepth(193D);
        log.setData1(193.36D);
        log.setData2(-123.36D);
        log.setData3(3.36D);
        log.setData4(-8.36D);
        log.setData1this(193.36D);
        log.setData2this(-123.36D);
        log.setData3this(3.36D);
        log.setData4this(-8.36D);
        log.setData1total(193.36D);
        log.setData2total(-123.36D);
        log.setData3total(3.36D);
        log.setData4total(-8.36D);
        log.setData1rate(193.36D);
        log.setData2rate(-123.36D);
        log.setData3rate(3.36D);
        log.setData4rate(-8.36D);
        log.setAlarmState(1);
        log.setWarning("data1,data2");
        log.setError("data1this,data2this");
        log.setControl("data3,data4");
        return log;
    }
}

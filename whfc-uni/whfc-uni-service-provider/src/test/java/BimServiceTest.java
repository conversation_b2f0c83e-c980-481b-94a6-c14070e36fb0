import com.whfc.common.util.JSONUtil;
import com.whfc.uni.param.bim.BimModelAdd;
import com.whfc.uni.service.BimService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class BimServiceTest {

    private BimService service;

    @Before
    public void setUp() {
        ReferenceConfig<BimService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(BimService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void testModelList() {
        List<?> list = service.modelList(2);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddModel() {
        BimModelAdd param = new BimModelAdd();
        param.setDeptId(2);
        param.setName("盾构管片.dgn");
        param.setFileId("10000723195758");
        service.addModel(param);
    }

    @Test
    public void testDelModel() {
        service.delModel(5);
    }

    @Test
    public void testViewToken() {
        String token = service.viewToken(1);
        System.out.println(token);
    }

    @Test
    public void testRefreshModelStatus() {
        service.refreshModelStatus();
    }
}

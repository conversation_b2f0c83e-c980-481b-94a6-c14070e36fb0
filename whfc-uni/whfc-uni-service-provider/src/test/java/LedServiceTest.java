import com.whfc.common.util.JSONUtil;
import com.whfc.uni.dto.led.LedDeviceDTO;
import com.whfc.uni.service.LedService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/10/26 13:57
 */
public class LedServiceTest {

    private LedService service;

    private static final String format = "yyyy/M/d H:m:s";

    @Before
    public void setUp() {
        ReferenceConfig<LedService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(LedService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void test() throws Exception {
        LedDeviceDTO data = service.ledDeviceInfo("lx", "960401949002732");
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

import com.whfc.common.enums.DevicePlatform;
import com.whfc.common.result.PageData;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.uni.dto.energy.EnergyElectricBoxLog;
import com.whfc.uni.param.energy.EnergyElectricBoxAdd;
import com.whfc.uni.service.EnergyEletricBoxService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-09-18 14:07
 * @Version 1.0
 */
public class EnergyElectricBoxServiceTest {

    private EnergyEletricBoxService service;

    @Before
    public void setUp() {
        ReferenceConfig<EnergyEletricBoxService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(EnergyEletricBoxService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        referenceConfig.setTimeout(30 * 1000);
        service = referenceConfig.get();
    }

    @Test
    public void testGetElectricBoxList() {
        Integer deptId = 2;
        Integer pageNum = 1;
        Integer pageSize = 10;
        PageData<?> list = service.getElectricBoxList(deptId, pageNum, pageSize);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddElectricBox() {
        EnergyElectricBoxAdd param = new EnergyElectricBoxAdd();
        param.setDeptId(2);
        param.setName("测试1");
        param.setSn("fc0001");
        service.addElectricBox(param);
    }

    @Test
    public void testDelElectricBox() {
        Integer boxId = 1;
        service.delElectricBox(boxId);
    }

    @Test
    public void testGetElectricBoxLogList() {
        Integer boxId = 1;
        Date startTime = DateUtil.parseDateTime("2021-11-17 00:00:00");
        Date endTime = DateUtil.parseDateTime("2021-11-18 00:00:00");
        List<?> list = service.getElectricBoxLogList(boxId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddElectricBoxLog() {

        String sn = "fc0001";
        EnergyElectricBoxLog log = new EnergyElectricBoxLog();
        log.setBoxId("1");
        log.setTime(new Date());

        log.setT1(3.2);
        log.setT2(3.3);
        log.setT3(3.4);
        log.setT4(3.5);
        log.setV1(4.1);
        log.setV2(4.2);
        log.setV3(4.3);
        log.setC1(5.1);
        log.setC2(5.2);
        log.setC3(5.3);
        log.setLeak(6.6);
        service.addElectricBoxLog(DevicePlatform.jjyip.name(), sn, log);
    }
}

import com.whfc.common.mail.Email;
import com.whfc.common.util.JSONUtil;
import com.whfc.uni.manager.EmailReceiver;
import org.junit.Test;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;
import java.util.Properties;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/22 15:46
 */
public class EmailReceiverTest {

    @Test
    public void testQQ() {
        String username = "<EMAIL>";
        String password = "s33wAnmiwafhD2Vk";

        List<Email> emailList = EmailReceiver.recvEmail("qyqq", username, password);
        System.out.println("emailList:" + emailList.size());
        System.out.println(JSONUtil.toPrettyString(emailList));
    }

    @Test
    public void testGmail() {
        String username = "<EMAIL>";
        String password = "omiqlbjjbcvehhvf";

        List<Email> emailList = EmailReceiver.recvEmail("gmail", username, password);
        System.out.println("emailList:" + emailList.size());
        System.out.println(JSONUtil.toPrettyString(emailList));
    }

    @Test
    public void testSend1() {
        String host = "smtp.163.com";
        String username = "<EMAIL>";
        String password = "BUJQJYZXMSDDLKBE";


        Properties properties = System.getProperties();
        properties.put("mail.smtp.host", host);
        properties.put("mail.smtp.port", "465"); // 通常是587或465
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true"); // 使用TLS加密spring.mail.properties.mail.smtp.auth=true
        properties.put("mail.smtp.ssl.enable", "true");
        properties.put("mail.smtp.socketFactory.port", "465");
        properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        properties.put("mail.smtp.socketFactory.fallback", "false");

        // 创建一个邮箱会话
        Session session = Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 这里填写你的邮箱账号和密码
                return new PasswordAuthentication(username, password);
            }
        });
        try {
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse("<EMAIL>"));
            message.setSubject("test");
            message.setText("this is a test");
            Transport.send(message);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSend2() throws Exception {
        String host = "smtp.163.com";
        Integer port = 465;
        String username = "<EMAIL>";
        String password = "BUJQJYZXMSDDLKBE";

        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(host);
        javaMailSender.setPort(port);
        javaMailSender.setUsername(username);
        javaMailSender.setPassword(password);

        Properties properties = javaMailSender.getJavaMailProperties();
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.ssl.enable", "true");
        properties.put("mail.smtp.socketFactory.port", "465");
        properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        properties.put("mail.smtp.socketFactory.fallback", "false");

//        SimpleMailMessage mail = new SimpleMailMessage();
//        mail.setFrom(username);
//        mail.setTo("<EMAIL>");
//        mail.setSubject("TEST");
//        mail.setText("this is a Test");
//        javaMailSender.send(mail);

        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(username);
        helper.setTo("<EMAIL>");
        helper.setSubject("Test1");
        helper.setText("<h1>this is a test</>", true);
        helper.addAttachment("test.pdf", new File("C:\\Users\\<USER>\\Desktop\\test\\test.pdf"));
        javaMailSender.send(message);
    }
}

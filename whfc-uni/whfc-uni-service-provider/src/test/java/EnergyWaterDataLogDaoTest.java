import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.uni.influx.EnergyWaterLogDao;
import com.whfc.uni.dto.energy.EnergyWaterStat;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/12/21 12:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class EnergyWaterDataLogDaoTest {

    @Autowired
    private EnergyWaterLogDao logDao;

    @Test
    public void testInsert1() {
        Date startTime = DateUtil.parseDateTime("2024-03-14 00:00:00");
        Date endTime = DateUtil.parseDateTime("2024-03-14 23:58:59");
        EnergyWaterStat data = logDao.selectEnergyStatByWaterId(1, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

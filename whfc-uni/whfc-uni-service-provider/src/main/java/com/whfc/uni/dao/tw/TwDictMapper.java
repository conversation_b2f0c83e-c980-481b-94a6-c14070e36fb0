package com.whfc.uni.dao.tw;

import com.whfc.uni.dto.tw.TwDictDTO;
import com.whfc.uni.entity.tw.TwDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TwDictMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(TwDict record);

    TwDict selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TwDict record);

    /**
     * 查询字典数据
     *
     * @param deptId
     * @param code
     * @return
     */
    TwDictDTO selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 查询数据字典
     *
     * @param pid
     * @return
     */
    List<TwDictDTO> selectPid(@Param("pid") Integer pid);
}
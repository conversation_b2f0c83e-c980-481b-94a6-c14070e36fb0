package com.whfc.uni.dao.tw;

import com.whfc.uni.dto.tw.TwLogDTO;
import com.whfc.uni.dto.tw.TwOpLogDTO;
import com.whfc.uni.entity.tw.TwOpLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TwOpLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(TwOpLog record);

    TwOpLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TwOpLog record);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    int batchInsert(@Param("logList") List<TwOpLogDTO> logList);

    /**
     * 查询需要我审核的业务对象ID
     *
     * @param deptId
     * @param objType
     * @param userId
     * @return
     */
    List<Integer> selectMyAuditingObjIds(@Param("deptId") Integer deptId, @Param("objType") Integer objType, @Param("userId") Integer userId);

    /**
     * 查询操作日志
     *
     * @param objType
     * @param objId
     * @return
     */
    List<TwOpLogDTO> selectOpLogList(@Param("objType") Integer objType, @Param("objId") Integer objId);

    /**
     * 查询审批流程
     *
     * @param objType
     * @param objId
     * @return
     */
    List<TwOpLogDTO> selectOpLogFlow(@Param("objType") Integer objType, @Param("objId") Integer objId);

    /**
     * 查找下一个审批人
     *
     * @param objType
     * @param objId
     * @return
     */
    TwOpLogDTO selectNextOpLog(@Param("objType") Integer objType, @Param("objId") Integer objId);

    /**
     * 逻辑删除
     *
     * @param objType
     * @param objId
     * @return
     */
    int logicDelete(@Param("objType") Integer objType, @Param("objId") Integer objId);

    /**
     * 统计未处理流程
     *
     * @param objType
     * @param objId
     * @return
     */
    int countUnHandle(@Param("objType") Integer objType, @Param("objId") Integer objId);

    /**
     * 删除未处理流程(-重新提交,处理未处理的日志流程)
     *
     * @param objType
     * @param objId
     * @return
     */
    int logicDeleteUnHandle(@Param("objType") Integer objType, @Param("objId") Integer objId);
}
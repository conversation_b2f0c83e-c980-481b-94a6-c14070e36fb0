package com.whfc.uni.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.base.param.AppFileExportParam;
import com.whfc.base.service.AppExportService;
import com.whfc.common.constant.AppMsgObjType;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.entity.DefaultProperties;
import com.whfc.common.enums.AppWarnModuleType;
import com.whfc.common.enums.AppWarnMsgChannel;
import com.whfc.common.enums.MsgType;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FileUploadUtil;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.pdf.ImageInfo;
import com.whfc.common.pdf.OfficeUtil;
import com.whfc.common.pdf.PdfUtil;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.*;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import com.whfc.entity.dto.msg.PushMsgDTO;
import com.whfc.fuum.dto.SysUserDTO;
import com.whfc.fuum.service.SysUserService;
import com.whfc.uni.dao.tw.*;
import com.whfc.uni.dto.tw.*;
import com.whfc.uni.entity.tw.*;
import com.whfc.uni.enums.tw.*;
import com.whfc.uni.param.tw.*;
import com.whfc.uni.service.TwService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/2/4 9:50
 */
@DubboService(interfaceClass = TwService.class, version = "1.0.0", timeout = 300 * 1000)
public class TwServiceImpl implements TwService {

    private Logger logger = LoggerFactory.getLogger(TwServiceImpl.class);

    private static final String RISK_CAT = "risk_cat";

    private static final String CONTENT = "There is a new Temporary Works that requires your approval";

    private static List<Integer> msgChannelList = Arrays.asList(AppWarnMsgChannel.MS.value(), AppWarnMsgChannel.WXMP.value());

    private static Object t1Lock = new Object();

    private static Object t3Lock = new Object();

    private static Object t4Lock = new Object();

    private static Map<TwT2State, TwT2State> t2StateMap = new HashMap<>();
    private static Map<TwT3State, TwT3State> t3StateMap = new HashMap<>();
    private static Map<TwT4State, TwT4State> t4StateMap = new HashMap<>();

    static {
        t2StateMap.put(TwT2State.init, TwT2State.issue);
        t2StateMap.put(TwT2State.issue, TwT2State.acknowledge);
        t2StateMap.put(TwT2State.acknowledge, TwT2State.agree);

        t3StateMap.put(TwT3State.init, TwT3State.issue);
        t3StateMap.put(TwT3State.issue, TwT3State.agreedByTwd);
        t3StateMap.put(TwT3State.agreedByTwd, TwT3State.agreedByIce);

        t4StateMap.put(TwT4State.init, TwT4State.certified);
    }

    @Autowired
    private TwBaseMapper baseMapper;

    @Autowired
    private TwDictMapper dictMapper;

    @Autowired
    private TwT1Mapper t1Mapper;

    @Autowired
    private TwT2Mapper t2Mapper;

    @Autowired
    private TwT3Mapper t3Mapper;

    @Autowired
    private TwT4Mapper t4Mapper;

    @Autowired
    private TwTzMapper tzMapper;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private OfficeUtil officeUtil;

    @Autowired
    private DefaultProperties defaultProps;

    @DubboReference(interfaceClass = AppExportService.class, version = "1.0.0")
    private AppExportService appExportService;

    @DubboReference(interfaceClass = SysUserService.class, version = "1.0.0")
    private SysUserService sysUserService;

    @Override
    public TwBaseDTO twBaseInfo(Integer deptId) throws BizException {

        TwBase base = baseMapper.selectByDeptId(deptId);
        if (base == null) {
            base = new TwBase();
            base.setDeptId(deptId);
            base.setGuid(RandomUtil.getGuid());
            baseMapper.insertSelective(base);
        }
        TwBaseDTO dto = this.getBaseInfo(base);
        return dto;
    }

    @Override
    public void twBaseInfoEdit(TwBaseDTO param) throws BizException {
        TwBase base = baseMapper.selectByGuid(param.getGuid());
        if (base != null) {
            base.setEm(param.getEm());
            base.setEmUserId(param.getEmUserId());
            base.setEmUserName(param.getEmUserName());
            base.setTwc(param.getTwc());
            base.setTwcUserId(param.getTwcUserId());
            base.setTwcUserName(param.getTwcUserName());
            base.setTwd(param.getTwd());
            base.setTwdUserId(param.getTwdUserId());
            base.setTwdUserName(param.getTwdUserName());
            base.setTws(param.getTws());
            base.setTwsUserId(param.getTwsUserId());
            base.setTwsUserName(param.getTwsUserName());
            base.setIce(param.getIce());
            base.setIceUserId(param.getIceUserId());
            base.setIceUserName(param.getIceUserName());
            base.setCr(param.getCr());
            base.setCrUserId(param.getCrUserId());
            base.setCrUserName(param.getCrUserName());
            baseMapper.updateByPrimaryKeySelective(base);
        }
    }

    @Override
    public List<TwDictDTO> getTwRiskCategory(Integer deptId) throws BizException {

        TwDictDTO parent = dictMapper.selectByDeptIdAndCode(deptId, RISK_CAT);
        if (parent != null) {
            return dictMapper.selectPid(parent.getId());
        }
        return Collections.emptyList();
    }

    @Override
    public List<TwDictDTO> getTwDesignPackage(Integer deptId, String riskCat) throws BizException {

        String code = StringUtils.lowerCase(RISK_CAT + "_" + riskCat);
        TwDictDTO parent = dictMapper.selectByDeptIdAndCode(deptId, code);
        if (parent != null) {
            List<TwDictDTO> dictDTOList = dictMapper.selectPid(parent.getId());
            List<TwDictDTO> list = new ArrayList<>(64);
            list.addAll(dictDTOList);
            for (TwDictDTO dictDTO : dictDTOList) {
                List<TwDictDTO> childList = dictMapper.selectPid(dictDTO.getId());
                dictDTO.setChildList(childList);
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public PageData<TwT1DTO> getT1List(T1Query query, Integer pageNum, Integer pageSize) throws BizException {

        Integer deptId = query.getDeptId();
        String riskCat = query.getRiskCat();
        Integer designPackageId = query.getDesignPackageId();
        String keyword = query.getKeyword();

        PageHelper.startPage(pageNum, pageSize);
        List<TwT1DTO> list = t1Mapper.selectList(deptId, riskCat, designPackageId, keyword);
        PageHelper.clearPage();

        //基本信息
        this.setBaseInfo(list);

        //t2,t3,t4
        this.setTwInfo(list);

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<TwT1DTO> getT1List(T1Query query) throws BizException {
        Integer deptId = query.getDeptId();
        String riskCat = query.getRiskCat();
        Integer designPackageId = query.getDesignPackageId();
        String keyword = query.getKeyword();
        List<TwT1DTO> list = t1Mapper.selectList(deptId, riskCat, designPackageId, keyword);
        return list;
    }

    @Override
    public TwT1DTO getT1Detail(String guid) throws BizException {
        TwT1DTO t1 = t1Mapper.selectDetail(guid);
        if (t1 != null) {
            TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getT1Id());
            if (base != null) {
                TwBaseDTO baseDTO = this.getBaseInfo(base);
                t1.setBase(baseDTO);
            }
        }
        return t1;
    }

    @Override
    public void addT1(T1Add param) throws BizException {
        Integer deptId = param.getDeptId();
        String t1Name = param.getT1Name();
        String riskCat = param.getRiskCat();
        Integer designPackageId = param.getDesignPackageId();
        String designPackage = param.getDesignPackage();

        TwT1 t1NoRecord = t1Mapper.selectByDeptIdAndT1Name(deptId, t1Name);
        if (t1NoRecord != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 Name duplicate");
        }

        //生成t1No
        String t1No = param.getT1No();
        if (StringUtils.isBlank(t1No)) {
            synchronized (t1Lock) {
                String lastT1No = t1Mapper.selectLastT1No(deptId, riskCat);
                if (StringUtils.isEmpty(lastT1No)) {
                    t1No = riskCat + "-" + 1;
                } else {
                    String[] lastArr = lastT1No.split("-");
                    if (lastArr.length == 2) {
                        t1No = riskCat + "-" + (Integer.parseInt(lastArr[1]) + 1);
                    } else {
                        t1No = riskCat + "-" + 1;
                    }
                }
            }
        }

        TwT1 t1 = new TwT1();
        t1.setGuid(RandomUtil.getGuid());
        t1.setDeptId(deptId);
        t1.setT1No(t1No);
        t1.setT1Name(t1Name);
        t1.setRiskCat(riskCat);
        t1.setDesignPackageId(designPackageId);
        t1.setDesignPackage(designPackage);
        t1.setRemark(param.getRemark());
        t1.setDataType(param.getDataType());
        t1Mapper.insertSelective(t1);

        //保存t1-base
        TwBaseDTO base = param.getBase();
        TwBase t1Base = new TwBase();
        t1Base.setGuid(RandomUtil.getGuid());
        t1Base.setDeptId(deptId);
        t1Base.setT1Id(t1.getId());
        if (base != null) {
            t1Base.setCr(base.getCr());
            t1Base.setCrUserId(base.getCrUserId());
            t1Base.setCrUserName(base.getCrUserName());
            t1Base.setEm(base.getEm());
            t1Base.setEmUserId(base.getEmUserId());
            t1Base.setEmUserName(base.getEmUserName());
            t1Base.setIce(base.getIce());
            t1Base.setIceUserId(base.getIceUserId());
            t1Base.setIceUserName(base.getIceUserName());
            t1Base.setTwc(base.getTwc());
            t1Base.setTwcUserId(base.getTwcUserId());
            t1Base.setTwcUserName(base.getTwcUserName());
            t1Base.setTwd(base.getTwd());
            t1Base.setTwdUserId(base.getTwdUserId());
            t1Base.setTwdUserName(base.getTwdUserName());
            t1Base.setTws(base.getTws());
            t1Base.setTwsUserId(base.getTwsUserId());
            t1Base.setTwsUserName(base.getTwsUserName());
        }
        baseMapper.insertSelective(t1Base);
    }

    @Override
    public void editT1(T1Edit param) throws BizException {
        String guid = param.getGuid();
        String t1No = param.getT1No();
        String t1Name = param.getT1Name();
        String riskCat = param.getRiskCat();
        Integer designPackageId = param.getDesignPackageId();
        String designPackage = param.getDesignPackage();

        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        if (!t1.getT1Name().equalsIgnoreCase(t1Name)) {
            TwT1 t1NoRecord = t1Mapper.selectByDeptIdAndT1Name(t1.getDeptId(), t1Name);
            if (t1NoRecord != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 Name duplicate");
            }
        }
        t1.setT1Name(t1Name);
        t1.setT1No(t1No);
        //t1.setRiskCat(riskCat);
        t1.setDesignPackageId(designPackageId);
        t1.setDesignPackage(designPackage);
        t1.setRemark(param.getRemark());
        t1.setDataType(param.getDataType());
        t1Mapper.updateByPrimaryKeySelective(t1);

        //t1-base
        TwBaseDTO base = param.getBase();
        if (base != null) {
            TwBase t1Base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
            if (t1Base == null) {
                t1Base = new TwBase();
                t1Base.setGuid(RandomUtil.getGuid());
                t1Base.setDeptId(t1.getDeptId());
                t1Base.setT1Id(t1.getId());
            }
            t1Base.setCr(base.getCr());
            t1Base.setCrUserId(base.getCrUserId());
            t1Base.setCrUserName(base.getCrUserName());
            t1Base.setEm(base.getEm());
            t1Base.setEmUserId(base.getEmUserId());
            t1Base.setEmUserName(base.getEmUserName());
            t1Base.setIce(base.getIce());
            t1Base.setIceUserId(base.getIceUserId());
            t1Base.setIceUserName(base.getIceUserName());
            t1Base.setTwc(base.getTwc());
            t1Base.setTwcUserId(base.getTwcUserId());
            t1Base.setTwcUserName(base.getTwcUserName());
            t1Base.setTwd(base.getTwd());
            t1Base.setTwdUserId(base.getTwdUserId());
            t1Base.setTwdUserName(base.getTwdUserName());
            t1Base.setTws(base.getTws());
            t1Base.setTwsUserId(base.getTwsUserId());
            t1Base.setTwsUserName(base.getTwsUserName());
            if (t1Base.getId() != null) {
                baseMapper.updateByPrimaryKeySelective(t1Base);
            } else {
                baseMapper.insertSelective(t1Base);
            }
        }
    }

    @Override
    public void delT1(String guid) throws BizException {


        //todo 根据t2,t3,t4是否存在,判断是否可删除

        t1Mapper.logicDeleteByGuid(guid);
    }

    @Override
    public OssPathDTO exportT1(T1Query query) throws BizException {

        Integer deptId = query.getDeptId();
        String riskCat = query.getRiskCat();
        Integer designPackageId = query.getDesignPackageId();
        String keyword = query.getKeyword();
        List<TwT1DTO> aList = t1Mapper.selectList(deptId, "A", designPackageId, keyword);
        List<TwT1DTO> bList = t1Mapper.selectList(deptId, "B", designPackageId, keyword);
        List<TwT1DTO> cList = t1Mapper.selectList(deptId, "C", designPackageId, keyword);

        //基本信息
        this.setBaseInfo(aList);
        this.setBaseInfo(bList);
        this.setBaseInfo(cList);
        this.setTwInfo(aList);
        this.setTwInfo(bList);
        this.setTwInfo(cList);

        //导出数据
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String fileName = RandomUtil.getGuid();
            File excel = File.createTempFile(fileName, ".xls");
            ClassPathResource resource = new ClassPathResource("templates/tw-t1-template.xls");
            ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = new HashMap<>(16);

            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(new FillWrapper("a", aList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("b", bList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("c", cList), fillConfig, writeSheet);

            excelWriter.finish();

            //excel轉pdf
            File pdf = File.createTempFile(fileName, ".pdf");
            officeUtil.office2Pdf(excel.getPath(), pdf.getPath());

            //上传oss
            String ossKey = "tw/t1/temp/" + fileName + ".pdf";
            String upload = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("导出excel失败", e);
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "导出失败");
        }
        return ossPathDTO;
    }

    @Override
    public OssPathDTO exportT1Async(T1Query query) throws BizException {

        Integer deptId = query.getDeptId();
        String riskCat = query.getRiskCat();
        Integer designPackageId = query.getDesignPackageId();
        String keyword = query.getKeyword();
        List<TwT1DTO> aList = t1Mapper.selectList(deptId, "A", designPackageId, keyword);
        List<TwT1DTO> bList = t1Mapper.selectList(deptId, "B", designPackageId, keyword);
        List<TwT1DTO> cList = t1Mapper.selectList(deptId, "C", designPackageId, keyword);

        //基本信息
        this.setBaseInfo(aList);
        this.setBaseInfo(bList);
        this.setBaseInfo(cList);
        this.setTwInfo(aList);
        this.setTwInfo(bList);
        this.setTwInfo(cList);

        //导出记录
        String fileDesc = String.format("临时工程T1");
        String suffix = "pdf";
        String fileName = RandomUtil.getRandomFileName();
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(deptId);
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        CompletableFuture.runAsync(() -> {
            try {
                File excel = File.createTempFile(fileName, ".xls");
                ClassPathResource resource = new ClassPathResource("templates/tw-t1-template.xls");
                ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                Map<String, Object> map = new HashMap<>(16);

                excelWriter.fill(map, writeSheet);

                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                excelWriter.fill(new FillWrapper("a", aList), fillConfig, writeSheet);
                excelWriter.fill(new FillWrapper("b", bList), fillConfig, writeSheet);
                excelWriter.fill(new FillWrapper("c", cList), fillConfig, writeSheet);

                excelWriter.finish();

                //excel轉pdf
                File pdf = File.createTempFile(fileName, ".pdf");
                officeUtil.office2Pdf(excel.getPath(), pdf.getPath());

                //上传oss
                String ossKey = "tw/t1/temp/" + fileName + ".pdf";
                String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);

                //导出成功
                logger.info("{}导出成功,文件路径:{}", fileDesc, fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);

            } catch (Exception ex) {
                logger.error("临时工程T2导出异常", ex);
                appExportService.fileExportFailure(exportId);
            }
        });
        return new OssPathDTO();
    }

    @Override
    public TwT2DTO t2DesignBrief(String guid, Integer userId) throws BizException {

        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }


        TwT2DTO t2 = t2Mapper.selectDetail(t1.getId());
        if (t2 != null) {
            //t1信息
            t2.setGuid(t1.getGuid());
            t2.setT1Name(t1.getT1Name());
            t2.setT1No(t1.getT1No());
            t2.setRiskCat(t1.getRiskCat());
            t2.setDesignPackage(t1.getDesignPackage());

            //附件信息
            TwT2DetailDTO detailDTO = JSONUtil.parseObject(t2.getDetail(), TwT2DetailDTO.class);
            if (detailDTO != null) {
                t2.setAttachList(detailDTO.getAttachList());
            }
            t2.setDetail(null);

            TwLogDTO logDTO = this.getT2NextState(base, t2.getState());
            t2.setCurrent(logDTO != null && logDTO.getOpUserIdList().contains(userId) ? 1 : 0);
        } else {
            t2 = new TwT2DTO();
            t2.setGuid(t1.getGuid());
            t2.setT1Name(t1.getT1Name());
            t2.setT1No(t1.getT1No());
            t2.setRiskCat(t1.getRiskCat());
            t2.setDesignPackage(t1.getDesignPackage());
            t2.setAttachList(Collections.emptyList());
            t2.setCurrent(isTws(base, userId) ? 1 : 0); //Initiated by TWS
        }

        return t2;
    }

    @Override
    public void t2DesignEdit(T2Design param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwT2 t2 = t2Mapper.selectByT1Id(t1.getId());

        //验证T2
        TwStageState twState = this.getT2StageState(t2);
        if (TwStageState.FINISHED.equals(twState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 has been completed");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //新增
        if (t2 == null) {

            //Initiated by TWS
            if (!isTws(base, param.getUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWS");
            }

            //附件信息
            TwT2DetailDTO detailDTO = new TwT2DetailDTO();
            detailDTO.setAttachList(param.getAttachList());
            String detail = JSONUtil.toString(detailDTO);

            t2 = new TwT2();
            t2.setDeptId(t1.getDeptId());
            t2.setT1Id(t1.getId());
            t2.setT2No("T2-" + t1.getT1No());
            t2.setDescription(param.getDescription());
            t2.setDesignAllocatedTo(param.getDesignAllocatedTo());
            t2.setDesignDeliveryDate(param.getDesignDeliveryDate());
            t2.setDesignNoticeGiven(param.getDesignNoticeGiven());
            t2.setReviewWorkshopDate(param.getReviewWorkshopDate());
            t2.setReviewWorkshopOption(param.getReviewWorkshopOption());
            t2.setReviewConculsion(param.getReviewConculsion());
            t2.setInitiated(param.getInitiated());
            t2.setSign1(param.getSign1());
            t2.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t2.setDetail(detail);
            t2.setState(TwT2State.init.getValue());
            t2Mapper.insertSelective(t2);
        }
        //编辑
        else {
            //附件信息
            TwT2DetailDTO detailDTO = JSONUtil.parseObject(t2.getDetail(), TwT2DetailDTO.class);
            detailDTO.setAttachList(param.getAttachList());
            String detail = JSONUtil.toString(detailDTO);

            t2.setDescription(param.getDescription());
            t2.setDesignAllocatedTo(param.getDesignAllocatedTo());
            t2.setDesignDeliveryDate(param.getDesignDeliveryDate());
            t2.setDesignNoticeGiven(param.getDesignNoticeGiven());
            t2.setReviewWorkshopDate(param.getReviewWorkshopDate());
            t2.setReviewWorkshopOption(param.getReviewWorkshopOption());
            t2.setReviewConculsion(param.getReviewConculsion());

            TwLogDTO logDTO = this.getT2NextState(base, t2.getState());
            Integer nextState = logDTO.getNextState();
            //Reviewd and Issued by TWC
            if (TwT2State.issue.getValue().equals(nextState)) {
                if (!isTwc(base, param.getUserId())) {
                    throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWC");
                }
                t2.setIssued(param.getIssued());
                t2.setSign2(param.getSign2());
                t2.setTime2(param.getTime2() != null ? param.getTime2() : now);
            }
            //Reviewd and acknowledged by TWD
            if (TwT2State.acknowledge.getValue().equals(nextState)) {
                if (!isTwd(base, param.getUserId())) {
                    throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWD");
                }
                t2.setAcknowledged(param.getAcknowledged());
                t2.setSign3(param.getSign3());
                t2.setTime3(param.getTime3() != null ? param.getTime3() : now);
            }
            //Agreed by TWS or EM
            if (TwT2State.agree.getValue().equals(nextState)) {
                if (!isTws(base, param.getUserId()) && !isEm(base, param.getUserId())) {
                    throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWS or EM");
                }
                t2.setAgreed(param.getAgreed());
                t2.setSign4(param.getSign4());
                t2.setTime4(param.getTime4() != null ? param.getTime4() : now);
            }
            t2.setState(nextState);
            t2.setDetail(detail);
            t2Mapper.updateByPrimaryKeySelective(t2);
        }

        //发送通知
        this.sendT2Notice(base, t1, t2);
    }

    @Override
    public void t2DesignEditAdr(T2Design param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //T2
        TwT2 t2 = t2Mapper.selectByT1Id(t1.getId());
        //新增
        if (t2 == null) {
            //附件信息
            TwT2DetailDTO detailDTO = new TwT2DetailDTO();
            detailDTO.setAttachList(param.getAttachList());
            String detail = JSONUtil.toString(detailDTO);

            t2 = new TwT2();
            t2.setDeptId(t1.getDeptId());
            t2.setT1Id(t1.getId());
            t2.setT2No("T2-" + t1.getT1No());
            t2.setDescription(param.getDescription());
            t2.setDesignAllocatedTo(param.getDesignAllocatedTo());
            t2.setDesignDeliveryDate(param.getDesignDeliveryDate());
            t2.setDesignNoticeGiven(param.getDesignNoticeGiven());
            t2.setReviewWorkshopDate(param.getReviewWorkshopDate());
            t2.setReviewWorkshopOption(param.getReviewWorkshopOption());
            t2.setReviewConculsion(param.getReviewConculsion());
            t2.setInitiated(param.getInitiated());
            t2.setSign1(param.getSign1());
            t2.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t2.setIssued(param.getIssued());
            t2.setSign2(param.getSign2());
            t2.setTime2(param.getTime2() != null ? param.getTime2() : now);
            t2.setAcknowledged(param.getAcknowledged());
            t2.setSign3(param.getSign3());
            t2.setTime3(param.getTime3() != null ? param.getTime3() : now);
            t2.setAgreed(param.getAgreed());
            t2.setSign4(param.getSign4());
            t2.setTime4(param.getTime4() != null ? param.getTime4() : now);
            t2.setState(TwT2State.agree.getValue());
            t2.setDetail(detail);
            t2Mapper.insertSelective(t2);
        }
        //编辑
        else {
            //附件信息
            TwT2DetailDTO detailDTO = JSONUtil.parseObject(t2.getDetail(), TwT2DetailDTO.class);
            if (detailDTO == null) {
                detailDTO = new TwT2DetailDTO();
            }
            detailDTO.setAttachList(param.getAttachList());
            String detail = JSONUtil.toString(detailDTO);

            t2.setDescription(param.getDescription());
            t2.setDesignAllocatedTo(param.getDesignAllocatedTo());
            t2.setDesignDeliveryDate(param.getDesignDeliveryDate());
            t2.setDesignNoticeGiven(param.getDesignNoticeGiven());
            t2.setReviewWorkshopDate(param.getReviewWorkshopDate());
            t2.setReviewWorkshopOption(param.getReviewWorkshopOption());
            t2.setReviewConculsion(param.getReviewConculsion());
            t2.setInitiated(param.getInitiated());
            t2.setSign1(param.getSign1());
            t2.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t2.setIssued(param.getIssued());
            t2.setSign2(param.getSign2());
            t2.setTime2(param.getTime2() != null ? param.getTime2() : now);
            t2.setAcknowledged(param.getAcknowledged());
            t2.setSign3(param.getSign3());
            t2.setTime3(param.getTime3() != null ? param.getTime3() : now);
            t2.setAgreed(param.getAgreed());
            t2.setSign4(param.getSign4());
            t2.setTime4(param.getTime4() != null ? param.getTime4() : now);
            t2.setState(TwT2State.agree.getValue());
            t2.setDetail(detail);
            t2Mapper.updateByPrimaryKeySelective(t2);
        }
    }

    @Override
    public void t2Del(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        t2Mapper.logicDeleteByT1Id(t1.getId());
    }

    @Override
    public OssPathDTO t2ExportAsync(String guid) throws BizException {

        //查询数据
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwT2DTO t2 = t2Mapper.selectDetail(t1.getId());
        if (t2 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 not exists");
        }

        //附件信息
        TwT2DetailDTO detailDTO = JSONUtil.parseObject(t2.getDetail(), TwT2DetailDTO.class);
        if (detailDTO != null) {
            t2.setAttachList(detailDTO.getAttachList());
        }
        Map<String, TwT2Attach> attachMap = CollectionUtil.list2Map(t2.getAttachList(), TwT2Attach::getInfo);
        TwT2AttachType[] attachTypeList = TwT2AttachType.values();
        List<TwT2Attach> list = new ArrayList<>(attachTypeList.length);
        for (TwT2AttachType attachType : attachTypeList) {
            TwT2Attach attach = attachMap.get(attachType.getInfo());
            TwT2Attach obj = new TwT2Attach();
            obj.setIndex(attachType.getIndex());
            obj.setInfo(attachType.getInfo());
            obj.setAttached(attach != null && "1".equals(attach.getAttached()) ? "ý" : "o");
            obj.setApplicable(attach != null && "1".equals(attach.getApplicable()) ? "ý" : "o");
            obj.setRemark(attach != null ? attach.getRemark() : "");
            list.add(obj);
        }

        //导出记录
        String fileDesc = String.format("临时工程T2(%s)", t2.getT2No());
        String suffix = "pdf";
        String fileName = RandomUtil.getRandomFileName();
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(t1.getDeptId());
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        CompletableFuture.runAsync(() -> {
            try {
                File excel = File.createTempFile(fileName, ".xls");
                ClassPathResource resource = new ClassPathResource("templates/tw-t2-template.xls");
                ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                Map<String, Object> map = this.getT2ExportMap(t1, t2);
                excelWriter.fill(map, writeSheet);

                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
                excelWriter.fill(list, fillConfig, writeSheet);

                excelWriter.finish();

                //excel轉pdf
                File pdf = File.createTempFile(fileName, ".pdf");
                logger.info("t2-excel-pdf,{},{}", excel.getPath(), pdf.getPath());
                officeUtil.office2Pdf(excel.getPath(), pdf.getPath());

                //附件处理
                List<ImageInfo> imageInfos = t2.getAttachList().stream()
                        .filter(o -> FileUtil.isImage(o.getPath()))
                        .map(o -> new ImageInfo(o.getName(), fileHandler.getDownloadUrl(o.getPath())))
                        .collect(Collectors.toList());
                List<String> pdfList = t2.getAttachList().stream()
                        .filter(o -> FileUtil.isPdf(o.getPath()))
                        .map(o -> fileHandler.getDownloadUrl(o.getPath()))
                        .collect(Collectors.toList());

                //文件合并
                List<String> mergeList = new ArrayList<>();
                mergeList.add(pdf.getPath());
                mergeList.addAll(pdfList);

                //多个图片合成pdf
                if (imageInfos.size() > 0) {
                    File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                    PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                    logger.info("t2-image2Pdf path:{}", image2Pdf.getPath());
                    mergeList.add(image2Pdf.getPath());
                }
                //合并多个pdf
                if (mergeList.size() > 1) {
                    File mergePdf = File.createTempFile("mergePdf", ".pdf");
                    PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                    logger.info("t2-mergePdf path:{}", mergePdf.getPath());
                    pdf = mergePdf;
                }

                //上传oss
                String ossKey = "tw/t2/temp/" + fileName + ".pdf";
                String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);

                //导出成功
                logger.info("{}导出成功,文件路径:{}", fileDesc, fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);

            } catch (Exception ex) {
                logger.error("临时工程T2导出异常", ex);
                appExportService.fileExportFailure(exportId);
            }
        });
        return new OssPathDTO();
    }

    @Override
    public OssPathDTO t2Export(String guid) throws BizException {

        //查询数据
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwT2DTO t2 = t2Mapper.selectDetail(t1.getId());
        if (t2 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 not exists");
        }

        //附件信息
        TwT2DetailDTO detailDTO = JSONUtil.parseObject(t2.getDetail(), TwT2DetailDTO.class);
        if (detailDTO != null) {
            t2.setAttachList(detailDTO.getAttachList());
        }
        Map<String, TwT2Attach> attachMap = CollectionUtil.list2Map(t2.getAttachList(), TwT2Attach::getInfo);
        TwT2AttachType[] attachTypeList = TwT2AttachType.values();
        List<TwT2Attach> list = new ArrayList<>(attachTypeList.length);
        for (TwT2AttachType attachType : attachTypeList) {
            TwT2Attach attach = attachMap.get(attachType.getInfo());
            TwT2Attach obj = new TwT2Attach();
            obj.setIndex(attachType.getIndex());
            obj.setInfo(attachType.getInfo());
            obj.setAttached(attach != null && "1".equals(attach.getAttached()) ? "ý" : "o");
            obj.setApplicable(attach != null && "1".equals(attach.getApplicable()) ? "ý" : "o");
            obj.setRemark(attach != null ? attach.getRemark() : "");
            list.add(obj);
        }

        //导出记录
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String fileName = RandomUtil.getRandomFileName();
            File excel = File.createTempFile(fileName, ".xls");
            ClassPathResource resource = new ClassPathResource("templates/tw-t2-template.xls");
            ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = this.getT2ExportMap(t1, t2);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(list, fillConfig, writeSheet);

            excelWriter.finish();

            //excel轉pdf
            File pdf = File.createTempFile(fileName, ".pdf");
            logger.info("t2-excel-pdf,{},{}", excel.getPath(), pdf.getPath());
            officeUtil.office2Pdf(excel.getPath(), pdf.getPath());

            //附件处理
            List<ImageInfo> imageInfos = t2.getAttachList().stream()
                    .filter(o -> FileUtil.isImage(o.getPath()))
                    .map(o -> new ImageInfo(o.getName(), fileHandler.getDownloadUrl(o.getPath())))
                    .collect(Collectors.toList());
            List<String> pdfList = t2.getAttachList().stream()
                    .filter(o -> FileUtil.isPdf(o.getPath()))
                    .map(o -> fileHandler.getDownloadUrl(o.getPath()))
                    .collect(Collectors.toList());

            //文件合并
            List<String> mergeList = new ArrayList<>();
            mergeList.add(pdf.getPath());
            mergeList.addAll(pdfList);

            //多个图片合成pdf
            if (imageInfos.size() > 0) {
                File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                logger.info("t2-image2Pdf path:{}", image2Pdf.getPath());
                mergeList.add(image2Pdf.getPath());
            }
            //合并多个pdf
            if (mergeList.size() > 1) {
                File mergePdf = File.createTempFile("mergePdf", ".pdf");
                PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                logger.info("t2-mergePdf path:{}", mergePdf.getPath());
                pdf = mergePdf;
            }

            //上传oss
            String ossKey = "tw/t2/temp/" + fileName + ".pdf";
            String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);
            ossPathDTO.setPath(fileUrl);
        } catch (Exception ex) {
            logger.error("临时工程T2导出异常", ex);
        }
        return ossPathDTO;
    }

    @Override
    public TzCheck tzCheck(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        if (tz != null) {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO != null && detailDTO.getCheck() != null) {
                TzCheck check = detailDTO.getCheck();
                check.setSketches(detailDTO.getSketches());
                check.setCheckFlag(tz.getCheckFlag());
                return check;
            }
        }
        TzCheck check = new TzCheck();
        check.setCheckFlag(0);
        return check;
    }

    @Override
    public TzReview tzReview(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        if (tz != null) {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO != null && detailDTO.getReview() != null) {
                TzReview review = detailDTO.getReview();
                review.setSketches(detailDTO.getSketches());
                review.setReviewFlag(tz.getReviewFlag());
                return review;
            } else {
                TzReview review = new TzReview();
                review.setSketches(detailDTO.getSketches());
                review.setReviewFlag(tz.getReviewFlag());
                return review;
            }
        }
        TzReview review = new TzReview();
        review.setReviewFlag(0);
        return review;
    }

    @Override
    public TzRelease tzRelease(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        if (tz != null) {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO != null && detailDTO.getRelease() != null) {
                TzRelease release = detailDTO.getRelease();
                release.setSketches(detailDTO.getSketches());
                release.setReleaseFlag(tz.getReleaseFlag());
                return release;
            } else {
                TzRelease release = new TzRelease();
                release.setSketches(detailDTO.getSketches());
                release.setReleaseFlag(tz.getReleaseFlag());
                return release;
            }
        }
        TzRelease release = new TzRelease();
        release.setReleaseFlag(0);
        return release;
    }

    @Override
    public void tzCheck(TzCheck param) throws BizException {
        List<TwSketch> sketches = param.getSketches();
        param.setSketches(null);
        param.setCheckTime(new Date());
        TwT1 t1 = t1Mapper.selectByGuid(param.getGuid());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //验证t2信息
        TwT2 t2 = t2Mapper.selectByT1Id(t1.getId());
        TwStageState t2State = this.getT2StageState(t2);
        if (!TwStageState.FINISHED.equals(t2State)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 not completed");
        }

        //验证tz-design
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        TwStageState tzCheckState = this.getTzCheckState(tz);
        if (TwStageState.FINISHED.equals(tzCheckState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Design has been completed");
        }

        if (tz == null) {
            TwTzDetailDTO detailDTO = new TwTzDetailDTO();
            detailDTO.setCheck(param);
            detailDTO.setSketches(sketches);

            tz = new TwTz();
            tz.setDeptId(t1.getDeptId());
            tz.setT1Id(t1.getId());
            tz.setCheckFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.insertSelective(tz);
        } else {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO == null) {
                detailDTO = new TwTzDetailDTO();
            }
            detailDTO.setCheck(param);
            if (sketches != null && sketches.size() > 0) {
                detailDTO.setSketches(sketches);
            }

            tz.setCheckFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.updateByPrimaryKeySelective(tz);
        }

        //发送消息
        this.sendTzReviewNotice(base, t1);
    }

    @Override
    public void tzReview(TzReview param) throws BizException {
        List<TwSketch> sketches = param.getSketches();
        param.setSketches(null);
        param.setReviewTime(new Date());
        TwT1 t1 = t1Mapper.selectByGuid(param.getGuid());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //验证t2信息
        TwT2 t2 = t2Mapper.selectByT1Id(t1.getId());
        TwStageState t2State = this.getT2StageState(t2);
        if (!TwStageState.FINISHED.equals(t2State)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 not completed");
        }

        //验证tz-design/review
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        TwStageState tzCheckState = this.getTzCheckState(tz);
        if (!TwStageState.FINISHED.equals(tzCheckState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Design not completed");
        }

        TwStageState tzReviewState = this.getTzReviewState(tz);
        if (TwStageState.FINISHED.equals(tzReviewState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Review has been completed");
        }

        if (tz == null) {
            TwTzDetailDTO detailDTO = new TwTzDetailDTO();
            detailDTO.setReview(param);
            detailDTO.setSketches(sketches);

            tz = new TwTz();
            tz.setDeptId(t1.getDeptId());
            tz.setT1Id(t1.getId());
            tz.setReviewFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.insertSelective(tz);
        } else {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO == null) {
                detailDTO = new TwTzDetailDTO();
            }
            detailDTO.setReview(param);
            if (sketches != null && sketches.size() > 0) {
                detailDTO.setSketches(sketches);
            }
            tz.setDetail(JSONUtil.toString(detailDTO));

            //refuse-拒绝
            if (TwResult.REFUSE.getValue().equals(param.getResult())) {
                tz.setCheckFlag(TwStageState.IN_PROCESS.getValue());
                tzMapper.updateByPrimaryKeySelective(tz);

                //发送消息
                this.sendTzRefuseNotice(base, t1, param);
            }
            //pass-通过
            else {
                tz.setReviewFlag(TwStageState.FINISHED.getValue());
                tzMapper.updateByPrimaryKeySelective(tz);

                //发送消息
                this.sendTzReleaseNotice(base, t1);
            }
        }


    }

    @Override
    public void tzRelease(TzRelease param) throws BizException {
        List<TwSketch> sketches = param.getSketches();
        param.setSketches(null);
        param.setReleaseTime(new Date());
        TwT1 t1 = t1Mapper.selectByGuid(param.getGuid());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //验证t2信息
        TwT2 t2 = t2Mapper.selectByT1Id(t1.getId());
        TwStageState t2State = this.getT2StageState(t2);
        if (!TwStageState.FINISHED.equals(t2State)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T2 not completed");
        }

        //验证tz-design/review/release
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        TwStageState tzCheckState = this.getTzCheckState(tz);
        if (!TwStageState.FINISHED.equals(tzCheckState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Design not completed");
        }

        TwStageState tzReviewState = this.getTzReviewState(tz);
        if (!TwStageState.FINISHED.equals(tzReviewState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Review not completed");
        }

        TwStageState tzReleaseState = this.getTzReleaseState(tz);
        if (TwStageState.FINISHED.equals(tzReleaseState)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Release has been completed");
        }

        if (tz == null) {
            TwTzDetailDTO detailDTO = new TwTzDetailDTO();
            detailDTO.setRelease(param);
            detailDTO.setSketches(sketches);

            tz = new TwTz();
            tz.setDeptId(t1.getDeptId());
            tz.setT1Id(t1.getId());
            tz.setReleaseFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.insertSelective(tz);
        } else {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO == null) {
                detailDTO = new TwTzDetailDTO();
            }
            detailDTO.setRelease(param);
            if (sketches != null && sketches.size() > 0) {
                detailDTO.setSketches(sketches);
            }

            tz.setReleaseFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.updateByPrimaryKeySelective(tz);
        }

        //图纸加签章
        String sealUrl = param.getSealUrl();
        if (StringUtils.isNotBlank(sealUrl) && sketches != null && sketches.size() > 0) {
            CompletableFuture.runAsync(() -> {
                for (TwSketch sketch : sketches) {
                    try {
                        sketch.setSealPath(sketch.getPath());
                        //图纸-加签章
                        if (sketch.getPath().endsWith(".pdf")) {
                            File sealFile = File.createTempFile(RandomUtil.getRandomFileName(), ".pdf");
                            PdfUtil.addImageWaterMarker(sketch.getPath(), sealFile.getPath(), sealUrl);

                            //上传oss
                            String fileName = FileUtil.getFileName(sketch.getPath());
                            String[] strArr = fileName.split("\\.");
                            String sealKey = new StringBuilder().append("tw/attach/").append(t1.getGuid()).append("/").append(strArr[0]).append("_seal.").append(strArr[1]).toString();
                            String sealPath = fileUploadUtil.upload(sealKey, new FileInputStream(sealFile));
                            sketch.setSealPath(sealPath);
                            logger.info("tw-release,图纸加签章,{},{}", sketch.getPath(), sketch.getSealPath());
                        }
                        //DesignCert-加签章
                        if (StringUtils.isNotEmpty(sketch.getDesignCertPath())) {
                            File sealFile = File.createTempFile(RandomUtil.getRandomFileName(), ".pdf");
                            PdfUtil.addImageWaterMarker(sketch.getDesignCertPath(), sealFile.getPath(), sealUrl);

                            //上传oss
                            String fileName = FileUtil.getFileName(sketch.getDesignCertPath());
                            String[] strArr = fileName.split("\\.");
                            String sealKey = new StringBuilder().append("tw/attach/").append(t1.getGuid()).append("/").append(strArr[0]).append("_seal.").append(strArr[1]).toString();
                            String sealPath = fileUploadUtil.upload(sealKey, new FileInputStream(sealFile));
                            sketch.setDesignCertSealPath(sealPath);
                            sketch.setDesignCertPath(sealPath);
                            logger.info("tw-release,DesignCert加签章,{},{}", sketch.getDesignCertPath(), sketch.getDesignCertSealPath());

                            //合并图纸+DesignCert
                            File mergePdf = File.createTempFile("mergePdf", ".pdf");
                            PdfUtil.mergePdf(Arrays.asList(sketch.getSealPath(), sketch.getDesignCertPath()), mergePdf.getPath());
                            logger.info("tw-release,mergePdf:{}", mergePdf.getPath());
                            //上传oss
                            String ossKey = new StringBuilder().append("tw/tz/").append(t1.getGuid()).append("/merge_").append(RandomUtil.getRandomFileName()).append(".pdf").toString();
                            String mergePdfPath = fileUploadUtil.upload(ossKey, new FileInputStream(mergePdf));
                            sketch.setMergePath(mergePdfPath);
                        }
                    } catch (Exception ex) {
                        logger.error("tw-release,图纸加签章error", ex);
                    }
                }

                TwTz tz1 = tzMapper.selectByT1Id(t1.getId());
                if (tz1 != null) {
                    TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz1.getDetail(), TwTzDetailDTO.class);
                    detailDTO.setSketches(sketches);
                    tz1.setDetail(JSONUtil.toString(detailDTO));
                    tzMapper.updateByPrimaryKeySelective(tz1);
                }
            });
        }
    }

    @Override
    public void tzCheckAdr(TzCheck param) throws BizException {
        List<TwSketch> sketches = param.getSketches();
        param.setSketches(null);
        param.setCheckTime(new Date());
        TwT1 t1 = t1Mapper.selectByGuid(param.getGuid());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //验证tz-design
        TwTz tz = tzMapper.selectByT1Id(t1.getId());
        if (tz == null) {
            TwTzDetailDTO detailDTO = new TwTzDetailDTO();
            detailDTO.setCheck(param);
            detailDTO.setSketches(sketches);

            tz = new TwTz();
            tz.setDeptId(t1.getDeptId());
            tz.setT1Id(t1.getId());
            tz.setCheckFlag(TwStageState.FINISHED.getValue());
            tz.setReviewFlag(TwStageState.FINISHED.getValue());
            tz.setReleaseFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.insertSelective(tz);
        } else {
            TwTzDetailDTO detailDTO = JSONUtil.parseObject(tz.getDetail(), TwTzDetailDTO.class);
            if (detailDTO == null) {
                detailDTO = new TwTzDetailDTO();
            }
            detailDTO.setCheck(param);
            detailDTO.setSketches(sketches);

            tz.setCheckFlag(TwStageState.FINISHED.getValue());
            tz.setReviewFlag(TwStageState.FINISHED.getValue());
            tz.setReleaseFlag(TwStageState.FINISHED.getValue());
            tz.setDetail(JSONUtil.toString(detailDTO));
            tzMapper.updateByPrimaryKeySelective(tz);
        }
    }

    @Override
    public List<TwT3DTO> t3List(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        List<TwT3DTO> list = t3Mapper.selectList(t1.getId());
        return list;
    }

    @Override
    public TwT3DTO t3Detail(String t3Guid, Integer userId) throws BizException {

        TwT3DTO t3 = t3Mapper.selectDetail(t3Guid);
        if (t3 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T3 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t3.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        t3.setT1No(t1.getT1No());
        t3.setT1Name(t1.getT1Name());
        t3.setDesignPackage(t1.getDesignPackage());
        t3.setRiskCat(t1.getRiskCat());

        //图纸信息
        TwT3DetailDTO detailDTO = JSONUtil.parseObject(t3.getDetail(), TwT3DetailDTO.class);
        if (detailDTO != null) {
            t3.setSketches(detailDTO.getSketches());
            t3.setSealUrl(detailDTO.getSealUrl());
            t3.setSealParam(detailDTO.getSealParam());
            t3.setDetail(null);
        }

        TwLogDTO logDTO = this.getT3NextState(base, t3.getState());
        t3.setCurrent(logDTO != null && logDTO.getOpUserIdList().contains(userId) ? 1 : 0);

        return t3;
    }

    @Override
    public void t3Add(T3Add param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //Initated by TWS/TWD
        if (!isTws(base, param.getUserId()) && !isTwd(base, param.getUserId())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWS/TWD");
        }

        //t3编号
        String t3No = "";
        TwT3 t3 = null;
        synchronized (t3Lock) {
            String lastT3No = t3Mapper.selectLastT3No(t1.getId());
            if (StringUtils.isEmpty(lastT3No)) {
                t3No = "T3-" + t1.getT1No() + "-" + 1;
            } else {
                String[] lastArr = lastT3No.split("-");
                Integer lastNo = Integer.parseInt(lastArr[3]);
                t3No = "T3-" + t1.getT1No() + "-" + (lastNo + 1);
            }

            //图纸信息
            TwT3DetailDTO detailDTO = new TwT3DetailDTO();
            detailDTO.setSketches(param.getSketches());
            detailDTO.setSealUrl(param.getSealUrl());
            detailDTO.setSealParam(param.getSealParam());
            String detail = JSONUtil.toString(detailDTO);

            t3 = new TwT3();
            t3.setDeptId(t1.getDeptId());
            t3.setT1Id(t1.getId());
            t3.setGuid(RandomUtil.getGuid());
            t3.setT3No(t3No);
            t3.setLocation(param.getLocation());
            t3.setElement(param.getElement());
            t3.setChange(param.getChange());
            t3.setReason(param.getReason());
            t3.setChecker(param.getChecker());
            t3.setApprovalDate(param.getApprovalDate());
            t3.setState(TwT3State.init.getValue());
            t3.setSign1(param.getSign1());
            t3.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t3.setDetail(detail);
            t3Mapper.insertSelective(t3);
        }

        //发送消息
        this.sendT3Notice(base, t1, t3);
    }

    @Override
    public void t3AddAdr(T3Add param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //t3编号
        String t3No = "";
        TwT3 t3 = null;
        synchronized (t3Lock) {
            String lastT3No = t3Mapper.selectLastT3No(t1.getId());
            if (StringUtils.isEmpty(lastT3No)) {
                t3No = "T3-" + t1.getT1No() + "-" + 1;
            } else {
                String[] lastArr = lastT3No.split("-");
                Integer lastNo = Integer.parseInt(lastArr[3]);
                t3No = "T3-" + t1.getT1No() + "-" + (lastNo + 1);
            }

            //图纸信息
            TwT3DetailDTO detailDTO = new TwT3DetailDTO();
            detailDTO.setSketches(param.getSketches());
            detailDTO.setSealUrl(param.getSealUrl());
            detailDTO.setSealParam(param.getSealParam());
            String detail = JSONUtil.toString(detailDTO);

            t3 = new TwT3();
            t3.setDeptId(t1.getDeptId());
            t3.setT1Id(t1.getId());
            t3.setGuid(RandomUtil.getGuid());
            t3.setT3No(t3No);
            t3.setLocation(param.getLocation());
            t3.setElement(param.getElement());
            t3.setChange(param.getChange());
            t3.setReason(param.getReason());
            t3.setChecker(param.getChecker());
            t3.setApprovalDate(param.getApprovalDate());
            t3.setState(TwT3State.agreedByIce.getValue());
            t3.setSign1(param.getSign1());
            t3.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t3.setSign2(param.getSign2());
            t3.setTime2(param.getTime2() != null ? param.getTime2() : now);
            t3.setSign3(param.getSign3());
            t3.setTime3(param.getTime3() != null ? param.getTime3() : now);
            t3.setSign4(param.getSign4());
            t3.setTime4(param.getTime4() != null ? param.getTime4() : now);
            t3.setDetail(detail);
            t3Mapper.insertSelective(t3);
        }
    }

    @Override
    public void t3Edit(T3Edit param) throws BizException {
        Date now = new Date();
        String t3Guid = param.getT3Guid();
        TwT3 t3 = t3Mapper.selectByGuid(t3Guid);
        if (t3 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T3 not exists");
        }

        //T1
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t3.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //图纸信息
        TwT3DetailDTO detailDTO = new TwT3DetailDTO();
        detailDTO.setSketches(param.getSketches());
        detailDTO.setSealUrl(param.getSealUrl());
        detailDTO.setSealParam(param.getSealParam());
        String detail = JSONUtil.toString(detailDTO);

        t3.setLocation(param.getLocation());
        t3.setElement(param.getElement());
        t3.setChange(param.getChange());
        t3.setReason(param.getReason());
        t3.setChecker(param.getChecker());
        t3.setApprovalDate(param.getApprovalDate());

        TwLogDTO logDTO = this.getT3NextState(base, t3.getState());
        Integer nextState = logDTO.getNextState();
        if (TwT3State.issue.getValue().equals(nextState)) {
            if (!isTwc(base, param.getUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWC");
            }
            t3.setSign2(param.getSign2());
            t3.setTime2(param.getTime2() != null ? param.getTime2() : now);
        }
        if (TwT3State.agreedByTwd.getValue().equals(nextState)) {
            if (!isTwd(base, param.getUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWD");
            }
            t3.setSign3(param.getSign3());
            t3.setTime3(param.getTime3() != null ? param.getTime3() : now);
        }
        if (TwT3State.agreedByIce.getValue().equals(nextState)) {
            if (!isIce(base, param.getUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not ICE");
            }
            t3.setSign4(param.getSign4());
            t3.setTime4(param.getTime4() != null ? param.getTime4() : now);
        }
        t3.setState(nextState);
        t3.setDetail(detail);
        t3Mapper.updateByPrimaryKeySelective(t3);

        //发送消息
        this.sendT3Notice(base, t1, t3);
    }

    @Override
    public void t3EditAdr(T3Edit param) throws BizException {
        Date now = new Date();
        String t3Guid = param.getT3Guid();
        TwT3 t3 = t3Mapper.selectByGuid(t3Guid);
        if (t3 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T3 not exists");
        }

        //T1
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t3.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //图纸信息
        TwT3DetailDTO detailDTO = new TwT3DetailDTO();
        detailDTO.setSketches(param.getSketches());
        detailDTO.setSealUrl(param.getSealUrl());
        detailDTO.setSealParam(param.getSealParam());
        String detail = JSONUtil.toString(detailDTO);

        t3.setLocation(param.getLocation());
        t3.setElement(param.getElement());
        t3.setChange(param.getChange());
        t3.setReason(param.getReason());
        t3.setChecker(param.getChecker());
        t3.setApprovalDate(param.getApprovalDate());
        t3.setState(TwT3State.agreedByIce.getValue());
        t3.setSign1(param.getSign1());
        t3.setTime1(param.getTime1() != null ? param.getTime1() : now);
        t3.setSign2(param.getSign2());
        t3.setTime2(param.getTime2() != null ? param.getTime2() : now);
        t3.setSign3(param.getSign3());
        t3.setTime3(param.getTime3() != null ? param.getTime3() : now);
        t3.setSign4(param.getSign4());
        t3.setTime4(param.getTime4() != null ? param.getTime4() : now);
        t3.setDetail(detail);


        t3Mapper.updateByPrimaryKeySelective(t3);
    }

    @Override
    public void t3Del(String t3Guid) throws BizException {
        t3Mapper.logicDeleteByGuid(t3Guid);
    }

    @Override
    public OssPathDTO t3ExportAsync(String t3Guid) throws BizException {

        TwT3DTO t3 = t3Mapper.selectDetail(t3Guid);
        if (t3 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T3 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t3.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        t3.setT1No(t1.getT1No());
        t3.setT1Name(t1.getT1Name());
        t3.setDesignPackage(t1.getDesignPackage());
        t3.setRiskCat(t1.getRiskCat());

        //基础信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());

        //附件信息
        List<TwSketch> list = new ArrayList<>();
        TwT3DetailDTO detailDTO = JSONUtil.parseObject(t3.getDetail(), TwT3DetailDTO.class);
        if (detailDTO != null) {
            List<TwSketch> sketches = detailDTO.getSketches();
            if (sketches != null && sketches.size() > 0) {
                int size = sketches.size();
                if (size % 2 == 1) {
                    sketches.add(new TwSketch());
                }
                for (int i = 0; i < sketches.size() - 1; i = i + 2) {
                    TwSketch sketch1 = sketches.get(i);
                    TwSketch sketch2 = sketches.get(i + 1);
                    sketch1.setSketchNo1(sketch1.getSketchNo());
                    sketch1.setSketchNo2(sketch2.getSketchNo());
                    sketch1.setRev1(sketch1.getRev());
                    sketch1.setRev2(sketch2.getRev());
                    list.add(sketch1);
                }
            }
        }

        //导出记录
        String fileDesc = String.format("临时工程T3(%s)", t3.getT3No());
        String suffix = "pdf";
        String fileName = RandomUtil.getRandomFileName();
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(t1.getDeptId());
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        CompletableFuture.runAsync(() -> {
            try {
                File excel = File.createTempFile(fileName, ".xls");
                ClassPathResource resource = new ClassPathResource("templates/tw-t3-template.xls");
                ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                Map<String, Object> map = this.getT3ExportMap(t1, t3, base);
                excelWriter.fill(map, writeSheet);

                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
                excelWriter.fill(new FillWrapper("a", list), fillConfig, writeSheet);

                excelWriter.finish();

                //excel轉pdf
                File pdf = File.createTempFile(fileName, ".pdf");
                officeUtil.office2Pdf(excel.getPath(), pdf.getPath());
                logger.info("t3-excel-pdf,{},{}", excel.getPath(), pdf.getPath());

                //pdf加水印
                if (detailDTO != null && StringUtils.isNotBlank(detailDTO.getSealUrl())) {
                    String sealUrl = fileHandler.getDownloadUrl(detailDTO.getSealUrl());
                    String sealPdf = pdf.getPath() + "_seal.pdf";
                    boolean sealed = PdfUtil.addImageWaterMarker(pdf.getPath(), sealPdf, sealUrl);
                    logger.info("t3-pdf-seal,{},{}", pdf.getPath(), sealed);
                    if (sealed) {
                        pdf = new File(sealPdf);
                    }
                }

                if (detailDTO != null) {
                    //附件处理
                    List<ImageInfo> imageInfos = detailDTO.getSketches().stream()
                            .filter(o -> FileUtil.isImage(o.getPath()))
                            .map(o -> new ImageInfo(o.getSketchNo(), fileHandler.getDownloadUrl(o.getSealPath())))
                            .collect(Collectors.toList());
                    List<String> pdfList = detailDTO.getSketches().stream()
                            .filter(o -> FileUtil.isPdf(o.getPath()))
                            .map(o -> fileHandler.getDownloadUrl(o.getSealPath()))
                            .collect(Collectors.toList());

                    //文件合并
                    List<String> mergeList = new ArrayList<>();
                    mergeList.add(pdf.getPath());
                    mergeList.addAll(pdfList);

                    //多个图片合成pdf
                    if (imageInfos.size() > 0) {
                        File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                        PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                        logger.info("t3-image2Pdf path:{}", image2Pdf.getPath());
                        mergeList.add(image2Pdf.getPath());
                    }
                    //合并多个pdf
                    if (mergeList.size() > 1) {
                        File mergePdf = File.createTempFile("mergePdf", ".pdf");
                        PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                        logger.info("t3-mergePdf path:{}", mergePdf.getPath());
                        pdf = mergePdf;
                    }
                }

                //上传oss
                String ossKey = "tw/t3/temp/" + fileName + ".pdf";
                String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);

                //导出成功
                logger.info("{}导出成功,文件路径:{}", fileDesc, fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);

            } catch (Exception ex) {
                logger.error("临时工程T3导出异常", ex);
                appExportService.fileExportFailure(exportId);
            }
        });
        return new OssPathDTO();
    }

    @Override
    public OssPathDTO t3Export(String t3Guid) throws BizException {

        TwT3DTO t3 = t3Mapper.selectDetail(t3Guid);
        if (t3 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T3 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t3.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        t3.setT1No(t1.getT1No());
        t3.setT1Name(t1.getT1Name());
        t3.setDesignPackage(t1.getDesignPackage());
        t3.setRiskCat(t1.getRiskCat());

        //基础信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());

        //附件信息
        List<TwSketch> list = new ArrayList<>();
        TwT3DetailDTO detailDTO = JSONUtil.parseObject(t3.getDetail(), TwT3DetailDTO.class);
        if (detailDTO != null) {
            List<TwSketch> sketches = detailDTO.getSketches();
            if (sketches != null && sketches.size() > 0) {
                int size = sketches.size();
                if (size % 2 == 1) {
                    sketches.add(new TwSketch());
                }
                for (int i = 0; i < sketches.size() - 1; i = i + 2) {
                    TwSketch sketch1 = sketches.get(i);
                    TwSketch sketch2 = sketches.get(i + 1);
                    sketch1.setSketchNo1(sketch1.getSketchNo());
                    sketch1.setSketchNo2(sketch2.getSketchNo());
                    sketch1.setRev1(sketch1.getRev());
                    sketch1.setRev2(sketch2.getRev());
                    list.add(sketch1);
                }
            }
        }

        //导出记录
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String fileName = RandomUtil.getRandomFileName();
            File excel = File.createTempFile(fileName, ".xls");
            ClassPathResource resource = new ClassPathResource("templates/tw-t3-template.xls");
            ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = this.getT3ExportMap(t1, t3, base);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(new FillWrapper("a", list), fillConfig, writeSheet);

            excelWriter.finish();

            //excel轉pdf
            File pdf = File.createTempFile(fileName, ".pdf");
            officeUtil.office2Pdf(excel.getPath(), pdf.getPath());
            logger.info("t3-excel-pdf,{},{}", excel.getPath(), pdf.getPath());

            //pdf加水印
            if (detailDTO != null && StringUtils.isNotBlank(detailDTO.getSealUrl())) {
                String sealUrl = fileHandler.getDownloadUrl(detailDTO.getSealUrl());
                String sealPdf = pdf.getPath() + "_seal.pdf";
                boolean sealed = PdfUtil.addImageWaterMarker(pdf.getPath(), sealPdf, sealUrl);
                logger.info("t3-pdf-seal,{},{}", pdf.getPath(), sealed);
                if (sealed) {
                    pdf = new File(sealPdf);
                }
            }

            if (detailDTO != null) {
                //附件处理
                List<ImageInfo> imageInfos = detailDTO.getSketches().stream()
                        .filter(o -> FileUtil.isImage(o.getPath()))
                        .map(o -> new ImageInfo(o.getSketchNo(), fileHandler.getDownloadUrl(o.getSealPath())))
                        .collect(Collectors.toList());
                List<String> pdfList = detailDTO.getSketches().stream()
                        .filter(o -> FileUtil.isPdf(o.getPath()))
                        .map(o -> fileHandler.getDownloadUrl(o.getSealPath()))
                        .collect(Collectors.toList());

                //文件合并
                List<String> mergeList = new ArrayList<>();
                mergeList.add(pdf.getPath());
                mergeList.addAll(pdfList);

                //多个图片合成pdf
                if (imageInfos.size() > 0) {
                    File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                    PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                    logger.info("t3-image2Pdf path:{}", image2Pdf.getPath());
                    mergeList.add(image2Pdf.getPath());
                }
                //合并多个pdf
                if (mergeList.size() > 1) {
                    File mergePdf = File.createTempFile("mergePdf", ".pdf");
                    PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                    logger.info("t3-mergePdf path:{}", mergePdf.getPath());
                    pdf = mergePdf;
                }
            }

            //上传oss
            String ossKey = "tw/t3/temp/" + fileName + ".pdf";
            String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);
            ossPathDTO.setPath(fileUrl);
        } catch (Exception ex) {
            logger.error("临时工程T3导出异常", ex);
        }
        return ossPathDTO;
    }

    @Override
    public List<TwT4DTO> t4List(String guid) throws BizException {
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        List<TwT4DTO> list = t4Mapper.selectList(t1.getId());
        return list;
    }

    @Override
    public TwT4DTO t4Detail(String t4Guid, Integer userId) throws BizException {
        TwT4DTO t4 = t4Mapper.selectDetail(t4Guid);
        if (t4 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T4 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t4.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        t4.setT1No(t1.getT1No());
        t4.setT1Name(t1.getT1Name());
        t4.setDesignPackage(t1.getDesignPackage());
        t4.setRiskCat(t1.getRiskCat());

        //图纸信息
        TwT4DetailDTO detailDTO = JSONUtil.parseObject(t4.getDetail(), TwT4DetailDTO.class);
        if (detailDTO != null) {
            t4.setSketches(detailDTO.getSketches());
            t4.setT3NoList(detailDTO.getT3NoList());
            t4.setDetail(null);
        }

        TwLogDTO logDTO = this.getT4NextState(base, t4.getState());
        t4.setCurrent(logDTO != null && logDTO.getOpUserIdList().contains(userId) ? 1 : 0);

        return t4;
    }

    @Override
    public void t4Add(T4Add param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //Initiated by TWS
        if (!isTws(base, param.getUserId())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWS");
        }

        //t4编号
        String permitNo = "";
        TwT4 t4 = null;
        synchronized (t4Lock) {
            String lastT4No = t4Mapper.selectLastT4No(t1.getId());
            if (StringUtils.isEmpty(lastT4No)) {
                permitNo = "T4-" + t1.getT1No() + "-" + 1;
            } else {
                String[] lastArr = lastT4No.split("-");
                Integer lastNo = Integer.parseInt(lastArr[3]);
                permitNo = "T4-" + t1.getT1No() + "-" + (lastNo + 1);
            }
            //图纸信息
            TwT4DetailDTO detailDTO = new TwT4DetailDTO();
            detailDTO.setSketches(param.getSketches());
            detailDTO.setT3NoList(param.getT3NoList());
            String detail = JSONUtil.toString(detailDTO);

            t4 = new TwT4();
            t4.setDeptId(t1.getDeptId());
            t4.setT1Id(t1.getId());
            t4.setGuid(RandomUtil.getGuid());
            t4.setPermitNo(permitNo);
            t4.setPermitValidUtil(param.getPermitValidUtil());
            t4.setLocation(param.getLocation());
            t4.setElement(param.getElement());
            t4.setOperation(param.getOperation());
            t4.setOtherRefer(param.getOtherRefer());
            t4.setRemoval(param.getRemoval());
            t4.setConstructionCert(param.getConstructionCert());
            t4.setConstructionCertPath(param.getConstructionCertPath());
            t4.setState(TwT4State.init.getValue());
            t4.setSign1(param.getSign1());
            t4.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t4.setDetail(detail);
            t4Mapper.insertSelective(t4);
        }

        //发送消息
        this.sendT4Notice(base, t1, t4);
    }

    @Override
    public void t4AddAdr(T4Add param) throws BizException {
        Date now = new Date();
        String guid = param.getGuid();
        TwT1 t1 = t1Mapper.selectByGuid(guid);
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //t4编号
        String permitNo = "";
        TwT4 t4 = null;
        synchronized (t4Lock) {
            String lastT4No = t4Mapper.selectLastT4No(t1.getId());
            if (StringUtils.isEmpty(lastT4No)) {
                permitNo = "T4-" + t1.getT1No() + "-" + 1;
            } else {
                String[] lastArr = lastT4No.split("-");
                Integer lastNo = Integer.parseInt(lastArr[3]);
                permitNo = "T4-" + t1.getT1No() + "-" + (lastNo + 1);
            }
            //图纸信息
            TwT4DetailDTO detailDTO = new TwT4DetailDTO();
            detailDTO.setSketches(param.getSketches());
            detailDTO.setT3NoList(param.getT3NoList());
            String detail = JSONUtil.toString(detailDTO);

            t4 = new TwT4();
            t4.setDeptId(t1.getDeptId());
            t4.setT1Id(t1.getId());
            t4.setGuid(RandomUtil.getGuid());
            t4.setPermitNo(permitNo);
            t4.setPermitValidUtil(param.getPermitValidUtil());
            t4.setLocation(param.getLocation());
            t4.setElement(param.getElement());
            t4.setOperation(param.getOperation());
            t4.setOtherRefer(param.getOtherRefer());
            t4.setRemoval(param.getRemoval());
            t4.setConstructionCert(param.getConstructionCert());
            t4.setConstructionCertPath(param.getConstructionCertPath());
            t4.setState(TwT4State.certified.getValue());
            t4.setSign1(param.getSign1());
            t4.setTime1(param.getTime1() != null ? param.getTime1() : now);
            t4.setSign3(param.getSign3());
            t4.setTime3(param.getTime3() != null ? param.getTime3() : now);
            t4.setDetail(detail);
            t4Mapper.insertSelective(t4);
        }
    }

    @Override
    public void t4Edit(T4Edit param) throws BizException {
        Date now = new Date();
        String t4Guid = param.getT4Guid();
        TwT4 t4 = t4Mapper.selectByGuid(t4Guid);
        if (t4 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T4 not exists");
        }

        //T1
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t4.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //图纸信息
        TwT4DetailDTO detailDTO = new TwT4DetailDTO();
        detailDTO.setSketches(param.getSketches());
        detailDTO.setT3NoList(param.getT3NoList());
        String detail = JSONUtil.toString(detailDTO);

        t4.setPermitValidUtil(param.getPermitValidUtil());
        t4.setLocation(param.getLocation());
        t4.setElement(param.getElement());
        t4.setOperation(param.getOperation());
        t4.setOtherRefer(param.getOtherRefer());
        t4.setRemoval(param.getRemoval());
        t4.setConstructionCert(param.getConstructionCert());
        t4.setConstructionCertPath(param.getConstructionCertPath());

        TwLogDTO logDTO = this.getT4NextState(base, t4.getState());
        Integer nextState = logDTO.getNextState();
        if (TwT4State.certified.getValue().equals(nextState)) {
            if (!isTwc(base, param.getUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Not TWC");
            }
            t4.setSign3(param.getSign3());
            t4.setTime3(param.getTime3() != null ? param.getTime3() : now);
        }
        t4.setState(nextState);
        t4.setDetail(detail);
        t4Mapper.updateByPrimaryKeySelective(t4);

        //发送消息
        this.sendT4Notice(base, t1, t4);
    }

    @Override
    public void t4EditAdr(T4Edit param) throws BizException {
        Date now = new Date();
        String t4Guid = param.getT4Guid();
        TwT4 t4 = t4Mapper.selectByGuid(t4Guid);
        if (t4 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T4 not exists");
        }

        //T1
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t4.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }

        //基本信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());
        if (base == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "Tw base not exists");
        }

        //图纸信息
        TwT4DetailDTO detailDTO = new TwT4DetailDTO();
        detailDTO.setSketches(param.getSketches());
        detailDTO.setT3NoList(param.getT3NoList());
        String detail = JSONUtil.toString(detailDTO);

        t4.setPermitValidUtil(param.getPermitValidUtil());
        t4.setLocation(param.getLocation());
        t4.setElement(param.getElement());
        t4.setOperation(param.getOperation());
        t4.setOtherRefer(param.getOtherRefer());
        t4.setRemoval(param.getRemoval());
        t4.setConstructionCert(param.getConstructionCert());
        t4.setConstructionCertPath(param.getConstructionCertPath());
        t4.setState(TwT4State.certified.getValue());
        t4.setSign1(param.getSign1());
        t4.setTime1(param.getTime1() != null ? param.getTime1() : now);
        t4.setSign3(param.getSign3());
        t4.setTime3(param.getTime3() != null ? param.getTime3() : now);
        t4.setDetail(detail);
        t4Mapper.updateByPrimaryKeySelective(t4);
    }

    @Override
    public void t4Del(String t4Guid) throws BizException {
        t4Mapper.logicDeleteByGuid(t4Guid);
    }

    @Override
    public OssPathDTO t4ExportAsync(String t4Guid) throws BizException {

        TwT4DTO t4 = t4Mapper.selectDetail(t4Guid);
        if (t4 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T4 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t4.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        t4.setT1No(t1.getT1No());
        t4.setT1Name(t1.getT1Name());
        t4.setDesignPackage(t1.getDesignPackage());
        t4.setRiskCat(t1.getRiskCat());

        //基础信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());

        //附件信息
        List<TwSketch> aList = new ArrayList<>();
        List<TwT4T3No> bList = new ArrayList<>();
        TwT4DetailDTO detailDTO = JSONUtil.parseObject(t4.getDetail(), TwT4DetailDTO.class);
        if (detailDTO != null) {
            //图纸
            List<TwSketch> sketches = detailDTO.getSketches();
            if (sketches != null && sketches.size() > 0) {
                int size = sketches.size();
                if (size % 2 == 1) {
                    sketches.add(new TwSketch());
                }
                for (int i = 0; i < sketches.size() - 1; i = i + 2) {
                    TwSketch sketch1 = sketches.get(i);
                    TwSketch sketch2 = sketches.get(i + 1);
                    sketch1.setSketchNo1(sketch1.getSketchNo());
                    sketch1.setSketchNo2(sketch2.getSketchNo());
                    sketch1.setRev1(sketch1.getRev());
                    sketch1.setRev2(sketch2.getRev());
                    aList.add(sketch1);
                }
            }
            //t3No
            List<String> t3NoList = detailDTO.getT3NoList();
            if (t3NoList != null && t3NoList.size() > 0) {
                int size = t3NoList.size();
                if (size % 3 == 1) {
                    t3NoList.add("");
                    t3NoList.add("");
                }
                if (size % 3 == 2) {
                    t3NoList.add("");
                }
                for (int i = 0; i < t3NoList.size() - 2; i = i + 3) {
                    TwT4T3No no = new TwT4T3No();
                    no.setT3No1(t3NoList.get(i));
                    no.setT3No2(t3NoList.get(i + 1));
                    no.setT3No3(t3NoList.get(i + 2));
                    bList.add(no);
                }
            }
        }

        //导出记录
        String fileDesc = String.format("临时工程T4(%s)", t4.getPermitNo());
        String suffix = "pdf";
        String fileName = RandomUtil.getRandomFileName();
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(t1.getDeptId());
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        CompletableFuture.runAsync(() -> {
            try {
                File excel = File.createTempFile(fileName, ".xls");
                ClassPathResource resource = new ClassPathResource("templates/tw-t4-template.xls");
                ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();

                Map<String, Object> map = this.getT4ExportMap(t1, t4, base);
                excelWriter.fill(map, writeSheet);

                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
                excelWriter.fill(new FillWrapper("a", aList), fillConfig, writeSheet);
                excelWriter.fill(new FillWrapper("b", bList), fillConfig, writeSheet);

                excelWriter.finish();

                //excel轉pdf
                File pdf = File.createTempFile(fileName, ".pdf");
                officeUtil.office2Pdf(excel.getPath(), pdf.getPath());
                logger.info("t4-excel-pdf,{},{}", excel.getPath(), pdf.getPath());

                if (detailDTO != null) {
                    //附件处理
                    List<ImageInfo> imageInfos = detailDTO.getSketches().stream()
                            .filter(o -> FileUtil.isImage(o.getPath()))
                            .map(o -> new ImageInfo(o.getSketchNo(), fileHandler.getDownloadUrl(o.getSealPath())))
                            .collect(Collectors.toList());
                    List<String> pdfList = detailDTO.getSketches().stream()
                            .filter(o -> FileUtil.isPdf(o.getPath()))
                            .map(o -> fileHandler.getDownloadUrl(o.getSealPath()))
                            .collect(Collectors.toList());

                    //文件合并
                    List<String> mergeList = new ArrayList<>();
                    mergeList.add(pdf.getPath());
                    mergeList.addAll(pdfList);

                    //多个图片合成pdf
                    if (imageInfos.size() > 0) {
                        File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                        PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                        logger.info("t4-image2Pdf path:{}", image2Pdf.getPath());
                        mergeList.add(image2Pdf.getPath());
                    }
                    //design cert
                    for (TwSketch sketch : detailDTO.getSketches()) {
                        if (StringUtils.isNotBlank(sketch.getDesignCertPath())) {
                            String designCertPath = fileHandler.getDownloadUrl(sketch.getDesignCertPath());
                            File designCertPdf = File.createTempFile("designCertPdf", ".pdf");
                            HttpUtil.download(designCertPath, designCertPdf.getPath());
                            logger.info("t4-designCertPdf path:{}", designCertPdf.getPath());
                            mergeList.add(designCertPdf.getPath());
                        }
                    }
                    //construction cert
                    if (StringUtils.isNotBlank(t4.getConstructionCertPath())) {
                        String constructionCertPath = fileHandler.getDownloadUrl(t4.getConstructionCertPath());
                        File consCertPdf = File.createTempFile("consCertPdf", ".pdf");
                        HttpUtil.download(constructionCertPath, consCertPdf.getPath());
                        logger.info("t4-consCertPdf path:{}", consCertPdf.getPath());
                        mergeList.add(consCertPdf.getPath());
                    }
                    //合并多个pdf
                    if (mergeList.size() > 1) {
                        File mergePdf = File.createTempFile("mergePdf", ".pdf");
                        PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                        logger.info("t4-mergePdf path:{}", mergePdf.getPath());
                        pdf = mergePdf;
                    }
                }

                //上传oss
                String ossKey = "tw/t4/temp/" + fileName + ".pdf";
                String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);

                //导出成功
                logger.info("{}导出成功,文件路径:{}", fileDesc, fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);

            } catch (Exception ex) {
                logger.error("临时工程T4导出异常", ex);
                appExportService.fileExportFailure(exportId);
            }
        });
        return new OssPathDTO();
    }

    @Override
    public OssPathDTO t4Export(String t4Guid) throws BizException {

        TwT4DTO t4 = t4Mapper.selectDetail(t4Guid);
        if (t4 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T4 not exists");
        }
        TwT1 t1 = t1Mapper.selectByPrimaryKey(t4.getT1Id());
        if (t1 == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "T1 not exists");
        }
        t4.setT1No(t1.getT1No());
        t4.setT1Name(t1.getT1Name());
        t4.setDesignPackage(t1.getDesignPackage());
        t4.setRiskCat(t1.getRiskCat());

        //基础信息
        TwBase base = baseMapper.selectByT1Id(t1.getDeptId(), t1.getId());

        //附件信息
        List<TwSketch> aList = new ArrayList<>();
        List<TwT4T3No> bList = new ArrayList<>();
        TwT4DetailDTO detailDTO = JSONUtil.parseObject(t4.getDetail(), TwT4DetailDTO.class);
        if (detailDTO != null) {
            //图纸
            List<TwSketch> sketches = detailDTO.getSketches();
            if (sketches != null && sketches.size() > 0) {
                int size = sketches.size();
                if (size % 2 == 1) {
                    sketches.add(new TwSketch());
                }
                for (int i = 0; i < sketches.size() - 1; i = i + 2) {
                    TwSketch sketch1 = sketches.get(i);
                    TwSketch sketch2 = sketches.get(i + 1);
                    sketch1.setSketchNo1(sketch1.getSketchNo());
                    sketch1.setSketchNo2(sketch2.getSketchNo());
                    sketch1.setRev1(sketch1.getRev());
                    sketch1.setRev2(sketch2.getRev());
                    aList.add(sketch1);
                }
            }
            //t3No
            List<String> t3NoList = detailDTO.getT3NoList();
            if (t3NoList != null && t3NoList.size() > 0) {
                int size = t3NoList.size();
                if (size % 3 == 1) {
                    t3NoList.add("");
                    t3NoList.add("");
                }
                if (size % 3 == 2) {
                    t3NoList.add("");
                }
                for (int i = 0; i < t3NoList.size() - 2; i = i + 3) {
                    TwT4T3No no = new TwT4T3No();
                    no.setT3No1(t3NoList.get(i));
                    no.setT3No2(t3NoList.get(i + 1));
                    no.setT3No3(t3NoList.get(i + 2));
                    bList.add(no);
                }
            }
        }

        //导出记录
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String fileName = RandomUtil.getRandomFileName();
            File excel = File.createTempFile(fileName, ".xls");
            ClassPathResource resource = new ClassPathResource("templates/tw-t4-template.xls");
            ExcelWriter excelWriter = EasyExcel.write(excel).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = this.getT4ExportMap(t1, t4, base);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(new FillWrapper("a", aList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("b", bList), fillConfig, writeSheet);

            excelWriter.finish();

            //excel轉pdf
            File pdf = File.createTempFile(fileName, ".pdf");
            officeUtil.office2Pdf(excel.getPath(), pdf.getPath());
            logger.info("t4-excel-pdf,{},{}", excel.getPath(), pdf.getPath());

            if (detailDTO != null) {
                //附件处理
                List<ImageInfo> imageInfos = detailDTO.getSketches().stream()
                        .filter(o -> FileUtil.isImage(o.getPath()))
                        .map(o -> new ImageInfo(o.getSketchNo(), fileHandler.getDownloadUrl(o.getSealPath())))
                        .collect(Collectors.toList());
                List<String> pdfList = detailDTO.getSketches().stream()
                        .filter(o -> FileUtil.isPdf(o.getPath()))
                        .map(o -> fileHandler.getDownloadUrl(o.getSealPath()))
                        .collect(Collectors.toList());

                //文件合并
                List<String> mergeList = new ArrayList<>();
                mergeList.add(pdf.getPath());
                mergeList.addAll(pdfList);

                //多个图片合成pdf
                if (imageInfos.size() > 0) {
                    File image2Pdf = File.createTempFile("image2Pdf", ".pdf");
                    PdfUtil.createPdf(imageInfos, image2Pdf.getPath());
                    logger.info("t4-image2Pdf path:{}", image2Pdf.getPath());
                    mergeList.add(image2Pdf.getPath());
                }
                //design cert
                for (TwSketch sketch : detailDTO.getSketches()) {
                    if (StringUtils.isNotBlank(sketch.getDesignCertPath())) {
                        String designCertPath = fileHandler.getDownloadUrl(sketch.getDesignCertPath());
                        File designCertPdf = File.createTempFile("designCertPdf", ".pdf");
                        HttpUtil.download(designCertPath, designCertPdf.getPath());
                        logger.info("t4-designCertPdf path:{}", designCertPdf.getPath());
                        mergeList.add(designCertPdf.getPath());
                    }
                }
                //construction cert
                if (StringUtils.isNotBlank(t4.getConstructionCertPath())) {
                    String constructionCertPath = fileHandler.getDownloadUrl(t4.getConstructionCertPath());
                    File consCertPdf = File.createTempFile("consCertPdf", ".pdf");
                    HttpUtil.download(constructionCertPath, consCertPdf.getPath());
                    logger.info("t4-consCertPdf path:{}", consCertPdf.getPath());
                    mergeList.add(consCertPdf.getPath());
                }
                //合并多个pdf
                if (mergeList.size() > 1) {
                    File mergePdf = File.createTempFile("mergePdf", ".pdf");
                    PdfUtil.mergePdf(mergeList, mergePdf.getPath());
                    logger.info("t4-mergePdf path:{}", mergePdf.getPath());
                    pdf = mergePdf;
                }
            }

            //上传oss
            String ossKey = "tw/t4/temp/" + fileName + ".pdf";
            String fileUrl = fileUploadUtil.upload(ossKey, new FileInputStream(pdf), FileExpirationRules.DAYS_7);
            ossPathDTO.setPath(fileUrl);

        } catch (Exception ex) {
            logger.error("临时工程T4导出异常", ex);
        }
        return ossPathDTO;
    }

    /**
     * 获取基本信息
     *
     * @param base
     * @return
     */
    private TwBaseDTO getBaseInfo(TwBase base) {
        TwBaseDTO dto = new TwBaseDTO();
        BeanUtils.copyProperties(base, dto);

        //查询用户签名
        List<Integer> userIdList = new ArrayList<>(5);
        if (dto.getEmUserId() != null) {
            userIdList.add(dto.getEmUserId());
        }
        if (dto.getTwcUserId() != null) {
            userIdList.add(dto.getTwcUserId());
        }
        if (dto.getTwdUserId() != null) {
            userIdList.add(dto.getTwdUserId());
        }
        if (dto.getTwsUserId() != null) {
            userIdList.add(dto.getTwsUserId());
        }
        if (dto.getIceUserId() != null) {
            userIdList.add(dto.getIceUserId());
        }
        List<SysUserDTO> userList = sysUserService.getUserList(userIdList);
        Map<Integer, String> userMap = CollectionUtil.list2map(userList, SysUserDTO::getId, SysUserDTO::getSign);

        //返回用户签名
        if (dto.getEmUserId() != null) {
            String sign = userMap.get(dto.getEmUserId());
            dto.setEmSign(sign);
            dto.setEmSignBase64(Base64Util.getUrlImageToBase64WithPrefix(sign));
        }
        if (dto.getTwcUserId() != null) {
            String sign = userMap.get(dto.getTwcUserId());
            dto.setTwcSign(sign);
            dto.setTwcSignBase64(Base64Util.getUrlImageToBase64WithPrefix(sign));
        }
        if (dto.getTwdUserId() != null) {
            String sign = userMap.get(dto.getTwdUserId());
            dto.setTwdSign(sign);
            dto.setTwdSignBase64(Base64Util.getUrlImageToBase64WithPrefix(sign));
        }
        if (dto.getTwsUserId() != null) {
            String sign = userMap.get(dto.getTwsUserId());
            dto.setTwsSign(sign);
            dto.setTwsSignBase64(Base64Util.getUrlImageToBase64WithPrefix(sign));
        }
        if (dto.getIceUserId() != null) {
            String sign = userMap.get(dto.getIceUserId());
            dto.setIceSign(sign);
            dto.setIceSignBase64(Base64Util.getUrlImageToBase64WithPrefix(sign));
        }
        return dto;
    }

    private TwBaseDTO toBaseDTO(TwBase base) {
        TwBaseDTO dto = new TwBaseDTO();
        BeanUtils.copyProperties(base, dto);
        return dto;
    }

    /**
     * 设置基本信息
     *
     * @param list
     */
    private void setBaseInfo(List<TwT1DTO> list) {

        List<Integer> t1IdList = list.stream().map(TwT1DTO::getT1Id).collect(Collectors.toList());
        t1IdList.add(0);
        List<TwBase> baseList = baseMapper.selectByT1IdList(t1IdList);
        Map<Integer, TwBase> baseMap = CollectionUtil.list2Map(baseList, TwBase::getT1Id);

        for (TwT1DTO t1 : list) {
            Integer t1Id = t1.getT1Id();
            TwBase base = baseMap.containsKey(t1Id) ? baseMap.get(t1Id) : baseMap.get(0);
            if (base != null) {
                t1.setTwc(base.getTwcUserName());
                t1.setTwd(base.getTwdUserName());
                t1.setTws(base.getTwsUserName());
                t1.setIce(base.getIceUserName());
            }
            t1.setBase(toBaseDTO(base));
        }
    }

    /**
     * 设置tw各阶段状态
     *
     * @param list
     */
    private void setTwInfo(List<TwT1DTO> list) {

        List<Integer> t1IdList = list.stream().map(TwT1DTO::getT1Id).collect(Collectors.toList());
        if (t1IdList.size() > 0) {
            List<TwTz> tzList = tzMapper.selectByT1IdList(t1IdList);
            Map<Integer, TwTz> tzMap = CollectionUtil.list2Map(tzList, TwTz::getT1Id);

            List<TwT2> t2List = t2Mapper.selectByT1IdList(t1IdList);
            Map<Integer, TwT2> t2Map = CollectionUtil.list2Map(t2List, TwT2::getT1Id);

            List<TwT3DTO> t3List = t3Mapper.selectByT1IdList(t1IdList);
            Map<Integer, List<String>> t3Map = CollectionUtil.groupBy(t3List, TwT3DTO::getT1Id, TwT3DTO::getT3No);

            List<TwT4DTO> t4List = t4Mapper.selectByT1IdList(t1IdList);
            Map<Integer, List<String>> t4Map = CollectionUtil.groupBy(t4List, TwT4DTO::getT1Id, TwT4DTO::getPermitNo);

            for (TwT1DTO t1 : list) {
                //tz信息
                TwTz tz = tzMap.get(t1.getT1Id());
                TwStageState check = this.getTzCheckState(tz);
                TwStageState review = this.getTzReviewState(tz);
                TwStageState release = this.getTzReleaseState(tz);
                t1.setCheckFlag(check.getValue());
                t1.setReviewFlag(review.getValue());
                t1.setReleaseFlag(release.getValue());
                t1.setCheckFlagName(TwStageState.FINISHED.equals(check) ? "Check" : "-");
                t1.setReviewFlagName(TwStageState.FINISHED.equals(review) ? "Review" : "-");
                t1.setReleaseFlagName(TwStageState.FINISHED.equals(release) ? "Release" : "-");

                //t2信息
                TwT2 t2 = t2Map.get(t1.getT1Id());
                TwStageState t2State = this.getT2StageState(t2);
                t1.setT2DesignBrief(t2State.getValue());
                t1.setT2DesignBriefName(t2 == null || StringUtils.isEmpty(t2.getT2No()) ? "-" : t2.getT2No());
                t1.setDesignDeliveryDate(t2 == null || t2.getDesignDeliveryDate() == null ? "-" : DateUtil.formatDate(t2.getDesignDeliveryDate()));


                //t3信息
                List<String> t3NoList = t3Map.getOrDefault(t1.getT1Id(), Collections.emptyList());
                int t3Modification = t3NoList.size();
                t1.setT3Modification(t3Modification);
                t1.setT3ModificationName(StringUtils.join(t3NoList, ","));

                //t4信息
                List<String> t4NoList = t4Map.getOrDefault(t1.getT1Id(), Collections.emptyList());
                int t4PermitToUse = t4NoList.size();
                t1.setT4PermitToUse(t4PermitToUse);
                t1.setT4PermitToUseName(StringUtils.join(t4NoList, ","));
            }

        }
    }

    private Map<String, Object> getT2ExportMap(TwT1 t1, TwT2DTO t2) {
        String riskCat = t1.getRiskCat();
        Map<String, Object> map = new HashMap<>(16);
        map.put("t1No", t1.getT1No());
        map.put("designPackage", t1.getDesignPackage());
        map.put("t2No", t2.getT2No());
        map.put("description", t2.getDescription());
        map.put("designAllocatedTo", t2.getDesignAllocatedTo());
        map.put("designDeliveryDate", formatDateEN(t2.getDesignDeliveryDate()));
        map.put("designNoticeGiven", t2.getDesignNoticeGiven());
        map.put("reviewWorkshopDate", formatDateEN(t2.getReviewWorkshopDate()));
        map.put("reviewWorkshopOption", t2.getReviewWorkshopOption());
        map.put("reviewConculsion", t2.getReviewConculsion());
        map.put("initiated", t2.getInitiated());
        map.put("issued", t2.getIssued());
        map.put("acknowledged", t2.getAcknowledged());
        map.put("agreed", t2.getAgreed());
        map.put("A", "A".equals(riskCat) ? "ý" : "o");
        map.put("B", "B".equals(riskCat) ? "ý" : "o");
        map.put("C", "C".equals(riskCat) ? "ý" : "o");
        if (StringUtils.isNotEmpty(t2.getSign1())) {
            map.put("sign1", this.getImageCellData(t2.getSign1(), 1, 1));
        }
        if (StringUtils.isNotEmpty(t2.getSign2())) {
            map.put("sign2", this.getImageCellData(t2.getSign2(), 1, 1));
        }
        if (StringUtils.isNotEmpty(t2.getSign3())) {
            map.put("sign3", this.getImageCellData(t2.getSign3(), 1, 1));
        }
        if (StringUtils.isNotEmpty(t2.getSign4())) {
            map.put("sign4", this.getImageCellData(t2.getSign4(), 1, 1));
        }
        if (t2.getTime1() != null) {
            map.put("time1", formatDateEN(t2.getTime1()));
        }
        if (t2.getTime2() != null) {
            map.put("time2", formatDateEN(t2.getTime2()));
        }
        if (t2.getTime3() != null) {
            map.put("time3", formatDateEN(t2.getTime3()));
        }
        if (t2.getTime4() != null) {
            map.put("time4", formatDateEN(t2.getTime4()));
        }

        return map;
    }

    private Map<String, Object> getT3ExportMap(TwT1 t1, TwT3DTO t3, TwBase base) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("t1No", t1.getT1No());
        map.put("designPackage", t1.getDesignPackage());
        map.put("t3No", t3.getT3No());
        map.put("location", t3.getLocation());
        map.put("element", t3.getElement());
        map.put("change", t3.getChange());
        map.put("reason", t3.getReason());
        map.put("checker", t3.getChecker());
        map.put("riskCat", t1.getRiskCat());
        map.put("approvalDate", formatDateEN(t3.getApprovalDate()));
        map.put("twd", base != null ? base.getTwdUserName() : null);
        map.put("twc", base != null ? base.getTwcUserName() : null);
        map.put("tws", base != null ? base.getTwsUserName() : null);
        map.put("ice", base != null ? base.getIceUserName() : null);
        if (!StringUtils.isEmpty(t3.getSign1())) {
            map.put("sign1", this.getImageCellData(t3.getSign1(), 1, 2));
        }
        if (!StringUtils.isEmpty(t3.getSign2())) {
            map.put("sign2", this.getImageCellData(t3.getSign2(), 1, 2));
        }
        if (!StringUtils.isEmpty(t3.getSign3())) {
            map.put("sign3", this.getImageCellData(t3.getSign3(), 1, 2));
        }
        if (!StringUtils.isEmpty(t3.getSign4())) {
            map.put("sign4", this.getImageCellData(t3.getSign4(), 1, 2));
        }
        if (t3.getTime1() != null) {
            map.put("time1", formatDateEN(t3.getTime1()));
        }
        if (t3.getTime2() != null) {
            map.put("time2", formatDateEN(t3.getTime2()));
        }
        if (t3.getTime3() != null) {
            map.put("time3", formatDateEN(t3.getTime3()));
        }
        if (t3.getTime4() != null) {
            map.put("time4", formatDateEN(t3.getTime4()));
        }
        return map;
    }

    private Map<String, Object> getT4ExportMap(TwT1 t1, TwT4DTO t4, TwBase base) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("t1No", t1.getT1No());
        map.put("designPackage", t1.getDesignPackage());
        map.put("permitNo", t4.getPermitNo());
        map.put("location", t4.getLocation());
        map.put("element", t4.getElement());
        map.put("operation", t4.getOperation());
        map.put("otherRefer", t4.getOtherRefer());
        map.put("permitValidUtil", formatDateEN(t4.getPermitValidUtil()));
        map.put("riskCat", t1.getRiskCat());
        map.put("yes", t4.getRemoval() == 1 ? "√" : "");
        map.put("no", t4.getRemoval() == 0 ? "√" : "");
        map.put("twd", base != null ? base.getTwdUserName() : null);
        map.put("twc", base != null ? base.getTwcUserName() : null);
        map.put("tws", base != null ? base.getTwsUserName() : null);
        map.put("ice", base != null ? base.getIceUserName() : null);
        if (!StringUtils.isEmpty(t4.getSign1())) {
            map.put("sign1", this.getImageCellData(t4.getSign1(), 1, 2));
        }
        if (!StringUtils.isEmpty(t4.getSign2())) {
            map.put("sign2", this.getImageCellData(t4.getSign2(), 1, 2));
        }
        if (!StringUtils.isEmpty(t4.getSign3())) {
            map.put("sign3", this.getImageCellData(t4.getSign3(), 1, 2));
        }
        if (!StringUtils.isEmpty(t4.getSign4())) {
            map.put("sign4", this.getImageCellData(t4.getSign4(), 1, 2));
        }
        if (t4.getTime1() != null) {
            map.put("time1", formatDateEN(t4.getTime1()));
        }
        if (t4.getTime2() != null) {
            map.put("time2", formatDateEN(t4.getTime2()));
        }
        if (t4.getTime3() != null) {
            map.put("time3", formatDateEN(t4.getTime3()));
        }
        if (t4.getTime4() != null) {
            map.put("time4", formatDateEN(t4.getTime4()));
        }

        return map;
    }

    private WriteCellData<Void> getImageCellData(String url, Integer firstCol, Integer lastCol) {
        return getImageCellData(url, 0, 0, firstCol, lastCol);
    }

    private WriteCellData<Void> getImageCellData(String url, Integer firstRow, Integer lastRow, Integer firstCol, Integer lastCol) {
        url = fileHandler.getDownloadUrl(url);
        ImageData imageData = new ImageData();
        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_PNG);
        imageData.setImage(ImageUtil.getFileBytes(url));
        imageData.setRelativeFirstRowIndex(firstRow);
        imageData.setRelativeLastRowIndex(lastRow);
        imageData.setRelativeFirstColumnIndex(firstCol);
        imageData.setRelativeLastColumnIndex(lastCol);

        WriteCellStyle style = new WriteCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setHorizontalAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);

        WriteCellData<Void> cellData = new WriteCellData<>();
        cellData.setImageDataList(Arrays.asList(imageData));
        cellData.setWriteCellStyle(style);
        return cellData;
    }

    /**
     * 获取t2状态
     *
     * @param t2
     * @return
     */
    private TwStageState getT2StageState(TwT2 t2) {
        //t2信息
        TwStageState state = TwStageState.NOT_START;
        if (t2 != null && StringUtils.isNotEmpty(t2.getT2No())) {
            state = TwT2State.agree.getValue().equals(t2.getState()) ? TwStageState.FINISHED : TwStageState.IN_PROCESS;
        }
        return state;
    }

    /**
     * 获取T3状态
     *
     * @param t3
     * @return
     */
    private TwStageState getT3StageState(TwT3 t3) {
        TwStageState state = TwStageState.NOT_START;
        if (t3 != null && StringUtils.isNotEmpty(t3.getT3No())) {
            state = TwT3State.agreedByIce.getValue().equals(t3.getState()) ? TwStageState.FINISHED : TwStageState.IN_PROCESS;
        }
        return state;
    }

    /**
     * 获取T4状态
     *
     * @param t4
     * @return
     */
    private TwStageState getT4StageState(TwT4 t4) {
        TwStageState state = TwStageState.NOT_START;
        if (t4 != null && StringUtils.isNotEmpty(t4.getPermitNo())) {
            state = TwT4State.certified.getValue().equals(t4.getState()) ? TwStageState.FINISHED : TwStageState.IN_PROCESS;
        }
        return state;
    }

    /**
     * 获取tz design状态
     *
     * @param tz
     * @return
     */
    private TwStageState getTzCheckState(TwTz tz) {
        return tz != null ? TwStageState.getByValue(tz.getCheckFlag()) : TwStageState.NOT_START;
    }

    /**e
     * 获取tz review状态
     *
     * @param tz
     * @return
     */
    private TwStageState getTzReviewState(TwTz tz) {
        return tz != null ? TwStageState.getByValue(tz.getReviewFlag()) : TwStageState.NOT_START;
    }

    /**
     * 获取tz release状态
     *
     * @param tz
     * @return
     */
    private TwStageState getTzReleaseState(TwTz tz) {
        return tz != null ? TwStageState.getByValue(tz.getReleaseFlag()) : TwStageState.NOT_START;
    }

    /**
     * 获取T2下一个状态
     *
     * @param base
     * @param t2State
     * @return
     */
    private TwLogDTO getT2NextState(TwBase base, Integer t2State) {

        TwT2State currState = TwT2State.getByValue(t2State);
        TwT2State nextState = t2StateMap.get(currState);

        List<Integer> opUserIdList = new ArrayList<>();
        if (TwT2State.issue.equals(nextState)) {
            opUserIdList.add(base.getTwcUserId());
        }
        if (TwT2State.acknowledge.equals(nextState)) {
            opUserIdList.add(base.getTwdUserId());
        }
        if (TwT2State.agree.equals(nextState)) {
            opUserIdList.add(base.getTwsUserId());
            opUserIdList.add(base.getEmUserId());
        }

        TwLogDTO logDTO = new TwLogDTO();
        logDTO.setCurrState(currState.getValue());
        logDTO.setNextState(nextState != null ? nextState.getValue() : null);
        logDTO.setOpUserIdList(opUserIdList);

        return logDTO;
    }

    /**
     * 获取T3下一个状态
     *
     * @param base
     * @param t3State
     * @return
     */
    private TwLogDTO getT3NextState(TwBase base, Integer t3State) {

        TwT3State currState = TwT3State.getByValue(t3State);
        TwT3State nextState = t3StateMap.get(currState);

        List<Integer> opUserIdList = new ArrayList<>();
        if (TwT3State.issue.equals(nextState)) {
            opUserIdList.add(base.getTwcUserId());
        }
        if (TwT3State.agreedByTwd.equals(nextState)) {
            opUserIdList.add(base.getTwdUserId());
        }
        if (TwT3State.agreedByIce.equals(nextState)) {
            opUserIdList.add(base.getIceUserId());
        }

        TwLogDTO logDTO = new TwLogDTO();
        logDTO.setCurrState(currState.getValue());
        logDTO.setNextState(nextState != null ? nextState.getValue() : null);
        logDTO.setOpUserIdList(opUserIdList);

        return logDTO;
    }

    /**
     * 获取T3下一个状态
     *
     * @param base
     * @param t4State
     * @return
     */
    private TwLogDTO getT4NextState(TwBase base, Integer t4State) {
        TwT4State currState = TwT4State.getByValue(t4State);
        TwT4State nextState = t4StateMap.get(currState);

        List<Integer> opUserIdList = new ArrayList<>();
        if (TwT4State.certified.equals(nextState)) {
            opUserIdList.add(base.getTwcUserId());
        }

        TwLogDTO logDTO = new TwLogDTO();
        logDTO.setCurrState(currState.getValue());
        logDTO.setNextState(nextState != null ? nextState.getValue() : null);
        logDTO.setOpUserIdList(opUserIdList);

        return logDTO;
    }

    private boolean isTwc(TwBase base, Integer userId) {
        return userId.equals(base.getTwcUserId());
    }

    private boolean isTwd(TwBase base, Integer userId) {
        return userId.equals(base.getTwdUserId());
    }

    private boolean isTws(TwBase base, Integer userId) {
        return userId.equals(base.getTwsUserId());
    }

    private boolean isEm(TwBase base, Integer userId) {
        return userId.equals(base.getEmUserId());
    }

    private boolean isIce(TwBase base, Integer userId) {
        return userId.equals(base.getIceUserId());
    }

    private AppMsgToUserDTO getTwcUser(TwBase base) {
        return new AppMsgToUserDTO(base.getTwcUserId(), base.getTwcUserName());
    }

    private AppMsgToUserDTO getTwdUser(TwBase base) {
        return new AppMsgToUserDTO(base.getTwdUserId(), base.getTwdUserName());
    }

    private AppMsgToUserDTO getTwsUser(TwBase base) {
        return new AppMsgToUserDTO(base.getTwsUserId(), base.getTwsUserName());
    }

    private AppMsgToUserDTO getEmUser(TwBase base) {
        return new AppMsgToUserDTO(base.getEmUserId(), base.getEmUserName());
    }

    private AppMsgToUserDTO getIceUser(TwBase base) {
        return new AppMsgToUserDTO(base.getIceUserId(), base.getIceUserName());
    }

    private void sendT2Notice(TwBase base, TwT1 t1, TwT2 t2) {

        //下一个步骤
        TwLogDTO logDTO = this.getT2NextState(base, t2.getState());

        //审核中,推送T2审批消息
        if (logDTO.getNextState() != null) {
            //消息ID
            String guid = RandomUtil.getGuid();

            //接收人
            List<AppMsgToUserDTO> toUserList = logDTO.getOpUserIdList().stream().map(userId -> {
                String html = this.getMailhtml(userId, CONTENT, guid);
                AppMsgToUserDTO toUser = new AppMsgToUserDTO();
                toUser.setUserId(userId);
                toUser.setHtml(html);
                return toUser;
            }).collect(Collectors.toList());

            //title
            String title = String.format("TW: %s - T2 approval: %s", t1.getT1Name(), t2.getT2No());

            //推送消息
            PushMsgDTO pushMsgDTO = new PushMsgDTO();
            pushMsgDTO.setGuid(guid);
            pushMsgDTO.setDeptId(t2.getDeptId());
            pushMsgDTO.setTitle(title);
            pushMsgDTO.setContent(CONTENT);
            pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
            pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
            pushMsgDTO.setMsgObjectType(AppMsgObjType.T2);
            pushMsgDTO.setMsgObjectId(t1.getGuid());
            pushMsgDTO.setMsgChannelList(msgChannelList);
            pushMsgDTO.setToUserList(toUserList);
            pushMsgDTO.setTime(new Date());
            pushMsgDTO.setSingle(true);

            try {
                amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
            } catch (Exception ex) {
                logger.error("推送消息失败", ex);
            }
        }
        //审核完成,推送TzCheck
        else if (logDTO.getNextState() == null) {
            this.sendTzCheckNotice(base, t1);
        }
    }

    private void sendTzCheckNotice(TwBase base, TwT1 t1) {
        //推送人列表
        AppMsgToUserDTO toUser = this.getTwdUser(base);

        //html
        String guid = RandomUtil.getGuid();
        String html = this.getMailhtml(toUser.getUserId(), CONTENT, guid);

        //title
        String title = String.format("TW: %s - Design/Check", t1.getT1Name());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(t1.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(CONTENT);
        pushMsgDTO.setHtml(html);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.TZ_CHECK);
        pushMsgDTO.setMsgObjectId(t1.getGuid());
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setToUserList(Arrays.asList(toUser));
        pushMsgDTO.setTime(new Date());

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    private void sendTzReviewNotice(TwBase base, TwT1 t1) {
        //接收人
        AppMsgToUserDTO toUser = this.getIceUser(base);

        //html
        String guid = RandomUtil.getGuid();
        String html = this.getMailhtml(toUser.getUserId(), CONTENT, guid);

        //title
        String title = String.format("TW: %s - Design Review", t1.getT1Name());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(t1.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(CONTENT);
        pushMsgDTO.setHtml(html);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.TZ_REVIEW);
        pushMsgDTO.setMsgObjectId(t1.getGuid());
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setToUserList(Arrays.asList(toUser));
        pushMsgDTO.setTime(new Date());

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    /**
     * 发送tz release
     *
     * @param base
     * @param t1
     */
    private void sendTzReleaseNotice(TwBase base, TwT1 t1) {
        //接收人
        AppMsgToUserDTO toUser = this.getTwcUser(base);

        //html
        String guid = RandomUtil.getGuid();
        String html = this.getMailhtml(toUser.getUserId(), CONTENT, guid);

        //title
        String title = String.format("TW: %s - Release For Construction", t1.getT1Name());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(t1.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(CONTENT);
        pushMsgDTO.setHtml(html);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.TZ_RELEASE);
        pushMsgDTO.setMsgObjectId(t1.getGuid());
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setToUserList(Arrays.asList(toUser));
        pushMsgDTO.setTime(new Date());

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    /**
     * 发送 tz review-refuse(拒绝)消息
     *
     * @param base
     * @param t1
     */
    private void sendTzRefuseNotice(TwBase base, TwT1 t1, TzReview review) {
        //接收人
        AppMsgToUserDTO toUser = this.getTwdUser(base);

        //html
        String content = String.format("Your temporary work design is refused by ICE. Comment from ICE:%s. Please revise as soon as possible!）", review.getComment());
        String guid = RandomUtil.getGuid();
        String html = this.getMailhtml(toUser.getUserId(), content, guid);

        //title
        String title = String.format("TW: %s - Design Revise", t1.getT1Name());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(t1.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(content);
        pushMsgDTO.setHtml(html);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.TZ_REFUSE);
        pushMsgDTO.setMsgObjectId(t1.getGuid());
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setToUserList(Arrays.asList(toUser));
        pushMsgDTO.setTime(new Date());

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    private void sendT3Notice(TwBase base, TwT1 t1, TwT3 t3) {
        if (t3 == null) {
            return;
        }

        TwLogDTO logDTO = this.getT3NextState(base, t3.getState());
        if (logDTO.getNextState() == null) {
            return;
        }

        //消息ID
        String guid = RandomUtil.getGuid();

        //接收人
        List<AppMsgToUserDTO> toUserList = logDTO.getOpUserIdList().stream().map(userId -> {
            String html = this.getMailhtml(userId, CONTENT, guid);
            AppMsgToUserDTO toUser = new AppMsgToUserDTO();
            toUser.setUserId(userId);
            toUser.setHtml(html);
            return toUser;
        }).collect(Collectors.toList());

        //title
        String title = String.format("TW: %s - T3 approval: %s", t1.getT1Name(), t3.getT3No());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(base.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(CONTENT);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.T3);
        pushMsgDTO.setMsgObjectId(t3.getGuid());
        pushMsgDTO.setToUserList(toUserList);
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setTime(new Date());
        pushMsgDTO.setSingle(true);

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    private void sendT4Notice(TwBase base, TwT1 t1, TwT4 t4) {

        if (t4 == null) {
            return;
        }
        TwLogDTO logDTO = this.getT4NextState(base, t4.getState());
        if (logDTO.getNextState() == null) {
            return;
        }

        //消息ID
        String guid = RandomUtil.getGuid();

        //接收人
        List<AppMsgToUserDTO> toUserList = logDTO.getOpUserIdList().stream().map(userId -> {
            String html = this.getMailhtml(userId, CONTENT, guid);
            AppMsgToUserDTO toUser = new AppMsgToUserDTO();
            toUser.setUserId(userId);
            toUser.setHtml(html);
            return toUser;
        }).collect(Collectors.toList());

        //title
        String title = String.format("TW: %s - T4 approval: %s", t1.getT1Name(), t4.getPermitNo());

        //推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setGuid(guid);
        pushMsgDTO.setDeptId(base.getDeptId());
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(CONTENT);
        pushMsgDTO.setModuleType(AppWarnModuleType.TW.value());
        pushMsgDTO.setMsgType(MsgType.FLOW.getValue());
        pushMsgDTO.setMsgObjectType(AppMsgObjType.T4);
        pushMsgDTO.setMsgObjectId(t4.getGuid());
        pushMsgDTO.setMsgChannelList(msgChannelList);
        pushMsgDTO.setToUserList(toUserList);
        pushMsgDTO.setTime(new Date());
        pushMsgDTO.setSingle(true);

        try {
            amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONUtil.toString(pushMsgDTO));
        } catch (Exception ex) {
            logger.error("推送消息失败", ex);
        }
    }

    /**
     * 生成邮件html
     *
     * @param guid
     * @param toUserId
     * @return
     */
    private String getMailhtml(Integer toUserId, String content, String guid) {
        String code = sysUserService.getUserCode(toUserId);
        String url = new StringBuffer().append(defaultProps.getHost()).append("/#third-login").append("?redirect_uri=/msg/process").append("&code=").append(code).append("&guid=").append(guid).toString();
        String html = new StringBuilder().append("<h2>").append(content).append("</h2>").append("<h2></h2>").append("<a href=\"").append(url).append("\">Click to process!</a>").toString();
        return html;
    }

    /**
     * 格式化日期
     *
     * @param date
     * @return
     */
    private String formatDateEN(Date date) {
        return date != null ? DateUtil.formatDate(date, "d-MMM-yy", java.util.Locale.ENGLISH) : "";
    }
}

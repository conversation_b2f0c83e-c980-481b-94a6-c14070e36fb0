package com.whfc.uni.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.exception.BizException;
import com.whfc.common.generator.KeyGeneratorUtil;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.spring.AppContextUtil;
import com.whfc.common.third.bimface.entity.BimComponentType;
import com.whfc.common.util.*;
import com.whfc.uni.dao.fsdm.*;
import com.whfc.uni.dto.fsdm.*;
import com.whfc.uni.entity.FsdmTaskElement;
import com.whfc.uni.entity.bim.BimComponent;
import com.whfc.uni.entity.fsdm.*;
import com.whfc.uni.enums.fsdm.*;
import com.whfc.uni.event.UpdateProcessEvent;
import com.whfc.uni.manager.FsdmConfigMgr;
import com.whfc.uni.manager.FsdmTaskMgr;
import com.whfc.uni.param.fsdm.*;
import com.whfc.uni.redis.BimRedisDao;
import com.whfc.uni.service.FsdmTaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-02-03 14:37
 */
@DubboService(interfaceClass = FsdmTaskService.class, version = "1.0.0", timeout = 30 * 1000)
public class FsdmTaskServiceImpl implements FsdmTaskService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 进度最大值
     */
    private static final Double MAX_PROCESS = 100D;

    /**
     * 炸药资源名称
     */
    private static List<String> BLAST_RESOURCE_NAMES = Arrays.asList("炸药");

    @Autowired
    private FsdmPlanMapper planMapper;

    @Autowired
    private FsdmTaskMapper taskMapper;

    @Autowired
    private FsdmTaskDutyMapper taskDutyMapper;

    @Autowired
    private FsdmTaskRelationMapper taskRelationMapper;

    @Autowired
    private FsdmTaskMgr fsdmTaskMgr;

    @Autowired
    private FsdmTaskMapper fsdmTaskMapper;

    @Autowired
    private FsdmTaskResourceMapper fsdmTaskResourceMapper;

    @Autowired
    private FsdmTaskQuantityMapper fsdmTaskQuantityMapper;

    @Autowired
    private FsdmTaskProcessLogMapper taskProcessLogMapper;

    @Autowired
    private FsdmStageTemplateMapper fsdmStageTemplateMapper;

    @Autowired
    private FsdmStageMapper fsdmStageMapper;

    @Autowired
    private FsdmTaskStageMapper fsdmTaskStageMapper;

    @Autowired
    private FsdmTaskStageExtMapper fsdmTaskStageExtMapper;

    @Autowired
    private FsdmConfigMgr fsdmConfigMgr;

    @Autowired
    private BimRedisDao bimRedisDao;

    @Autowired
    private FsdmTaskElementMapper fsdmTaskElementMapper;

    @Autowired
    private FsdmPlanMapper fsdmPlanMapper;


    /********WBS管理*********/

    @Override
    public List<FsdmTaskDTO> getTaskList(Long planId, String keyword) throws BizException {
        logger.info("获取任务列表服务,planId:{},keyword:{}", planId, keyword);

        FsdmPlan fsdmPlan = fsdmPlanMapper.selectByPrimaryKey(planId);
        if (ObjectUtils.isEmpty(fsdmPlan)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该计划不存在。");
        }

        List<FsdmTaskDTO> taskDTOList = taskMapper.selectTaskListByPlanId(planId, keyword);
        if (taskDTOList.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询前置关系
        List<FsdmTaskRelationDTO> taskRelationList = taskRelationMapper.selectByPlanId(planId);
        Map<Long, List<FsdmTaskRelationDTO>> relationMap = CollectionUtil.groupBy(taskRelationList, FsdmTaskRelationDTO::getPostTaskId);

        // 查询责任人
        List<FsdmTaskDutyDTO> allDutyList = taskDutyMapper.selectByPlanId(planId);
        Map<Long, List<FsdmTaskDutyDTO>> dutyMap = CollectionUtil.groupBy(allDutyList, FsdmTaskDutyDTO::getTaskId);

        // 查询任务资源
        List<FsdmTaskResourceDTO> resourceList = fsdmTaskResourceMapper.selectResourceDTO(null, planId);
        Map<Long, List<FsdmTaskResourceDTO>> resourceMap = CollectionUtil.groupBy(resourceList, FsdmTaskResourceDTO::getTaskId);

        // 查询任务工程量
        List<FsdmTaskQuantityDTO> quantityList = fsdmTaskQuantityMapper.selectByPlanId(planId);
        Map<Long, FsdmTaskQuantityDTO> quantityMap = CollectionUtil.list2Map(quantityList, FsdmTaskQuantityDTO::getTaskId);

        // 查询任务绑定构件
        List<FsdmTaskElementDTO> elementList = fsdmTaskElementMapper.selectByPlanId(planId, fsdmPlan.getBimModel());
        Map<Long, List<FsdmTaskElementDTO>> elementMap = elementList.stream()
                .collect(Collectors.groupingBy(FsdmTaskElementDTO::getTaskId, Collectors.toList()));

        for (FsdmTaskDTO fsdmTaskDTO : taskDTOList) {
            Long taskId = fsdmTaskDTO.getTaskId();

            //前置任务
            fsdmTaskDTO.setTaskRelationList(relationMap.getOrDefault(taskId, Collections.emptyList()));

            // 设置责任人
            fsdmTaskDTO.setDutyList(dutyMap.getOrDefault(taskId, Collections.emptyList()));

            // 设置任务资源
            fsdmTaskDTO.setTaskResourceList(resourceMap.get(taskId));

            // 设置任务工程量
            fsdmTaskDTO.setTaskQuantityDTO(quantityMap.get(taskId));

            // 设置任务构件
            fsdmTaskDTO.setTaskElementList(elementMap.getOrDefault(taskId, Collections.emptyList()));
        }

        return taskDTOList;
    }

    @Override
    public FsdmTaskDTO getTaskDetail(Long taskId) throws BizException {
        logger.info("获取任务详情服务,taskId:{}", taskId);
        FsdmTaskDTO taskDTO = taskMapper.selectTaskByTaskId(taskId);
        if (ObjectUtils.isEmpty(taskDTO)) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        // 查找责任人
        List<FsdmTaskDutyDTO> dutyList = taskDutyMapper.selectByTaskId(taskId);
        taskDTO.setDutyList(dutyList);
        return taskDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTask(TaskAddParam param) throws BizException {
        logger.info("添加任务服务,param:{}", param.toString());
        Long planId = param.getPlanId();
        Long parentId = param.getParentId() == null ? 0 : param.getParentId();
        String name = param.getName();
        Double process = param.getProcess();

        // 计划信息
        FsdmPlan plan = planMapper.selectByPrimaryKey(planId);
        if (ObjectUtils.isEmpty(plan)) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "计划不存在");
        }

        // 验证name(一个子节点下的名字不同)
        FsdmTaskDTO fsdmTaskDTO = taskMapper.selectByParentIdAndName(planId, parentId, name);
        if (fsdmTaskDTO != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务名称已存在");
        }

        // 计算level
        FsdmTaskDTO parent = taskMapper.selectTaskByTaskId(parentId);
        int level = parent == null ? 1 : parent.getLevel() + 1;

        // 计算uid
        Long maxUid = taskMapper.selectMaxUid(planId, parent == null ? null : parent.getCode());
        Long uid = maxUid == null ? 1 : maxUid + 1;
        // 批量更新uid
        taskMapper.batchUpdateUid(planId, uid, 1);

        // code 和innerCode
        String code = RandomUtil.getRandomStr(6);
        String innerCode = parent == null ? code : parent.getInnerCode() + "," + code;
        long taskId = KeyGeneratorUtil.genLongId();

        FsdmTask task = new FsdmTask();
        task.setId(taskId);
        task.setDeptId(plan.getDeptId());
        task.setUid(uid);
        task.setPlanId(planId);
        task.setParentId(parentId);
        task.setType(param.getType());
        task.setName(name);
        task.setLevel(level);
        task.setCode(code);
        task.setInnerCode(innerCode);
        task.setPlanStart(param.getPlanStart());
        task.setPlanFinish(param.getPlanFinish());
        task.setActualStart(param.getActualStart());
        task.setActualFinish(param.getActualFinish());
        task.setProcess(process == null ? 0 : process > MAX_PROCESS ? MAX_PROCESS : process);
        task.setUpdateUserId(param.getUserId());
        task.setUpdateUserName(param.getUserName());
        task.setTemplateId(param.getTemplateId());
        task.setTemplateName(param.getTemplateName());
        task.setSubcontractorId(param.getSubcontractorId());
        task.setSubcontractorName(param.getSubcontractorName());
        task.setStageRound(param.getStageRound());
        this.computeDurationAndRunningState(task);
        taskMapper.insertSelective(task);

        // 添加责任人
        List<FsdmTaskDutyDTO> dutyList = param.getDutyList();
        this.insertTaskDuty(plan.getDeptId(), planId, taskId, dutyList);

        // 发布更新进度事件
        UpdateProcessEvent event = new UpdateProcessEvent(taskId);
        AppContextUtil.context().publishEvent(event);

        // 插入进度记录
        FsdmTaskProcessLog processLog = new FsdmTaskProcessLog();
        processLog.setProcess(task.getProcess());
        processLog.setTaskId(taskId);
        processLog.setTime(new Date());
        processLog.setUserId(param.getUserId());
        processLog.setUserName(param.getUserName());
        taskProcessLogMapper.insertSelective(processLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTask(TaskInsertParam param) throws BizException {
        logger.info("插入任务服务,param:{}", param.toString());
        Long taskId1 = param.getTaskId();
        FsdmTask fsdmTask = taskMapper.selectByPrimaryKey(taskId1);
        if (fsdmTask == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "被插入的任务不存在");
        }
        Integer deptId = fsdmTask.getDeptId();
        Long planId = fsdmTask.getPlanId();
        Integer level = fsdmTask.getLevel();
        Long parentId = fsdmTask.getParentId();
        Long uid = fsdmTask.getUid();
        String innerCode1 = fsdmTask.getInnerCode();
        String code1 = fsdmTask.getCode();

        Double process = param.getProcess();
        String name = param.getName();
        // 验证name(一个子节点下的名字不同)
        FsdmTaskDTO fsdmTaskDTO = taskMapper.selectByParentIdAndName(planId, parentId, name);
        if (fsdmTaskDTO != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务名称已存在");
        }

        // 更新uid
        taskMapper.batchUpdateUid(planId, uid + 1, 1);

        // code 和innerCode
        String code = RandomUtil.getRandomStr(6);
        String innerCode = innerCode1.replaceAll(code1, code);
        long taskId = KeyGeneratorUtil.genLongId();

        FsdmTask task = new FsdmTask();
        task.setId(taskId);
        task.setDeptId(deptId);
        task.setUid(uid + 1);
        task.setPlanId(planId);
        task.setParentId(parentId);
        task.setType(param.getType());
        task.setName(name);
        task.setLevel(level);
        task.setCode(code);
        task.setInnerCode(innerCode);
        task.setPlanFinish(param.getPlanFinish());
        task.setPlanStart(param.getPlanStart());
        task.setActualStart(param.getActualStart());
        task.setActualFinish(param.getActualFinish());
        task.setProcess(process == null ? 0 : process > MAX_PROCESS ? MAX_PROCESS : process);
        task.setUpdateUserId(param.getUserId());
        task.setUpdateUserName(param.getUserName());
        task.setTemplateId(param.getTemplateId());
        task.setTemplateName(param.getTemplateName());
        task.setSubcontractorId(param.getSubcontractorId());
        task.setSubcontractorName(param.getSubcontractorName());
        task.setStageRound(param.getStageRound());
        this.computeDurationAndRunningState(task);
        taskMapper.insertSelective(task);

        // 添加责任人
        List<FsdmTaskDutyDTO> dutyList = param.getDutyList();
        this.insertTaskDuty(deptId, planId, taskId, dutyList);

        // 插入进度记录
        FsdmTaskProcessLog processLog = new FsdmTaskProcessLog();
        processLog.setProcess(task.getProcess());
        processLog.setTaskId(taskId);
        processLog.setTime(new Date());
        processLog.setUserId(param.getUserId());
        processLog.setUserName(param.getUserName());
        taskProcessLogMapper.insertSelective(processLog);

        // 发布更新进度事件
        UpdateProcessEvent event = new UpdateProcessEvent(taskId);
        AppContextUtil.context().publishEvent(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editTask(TaskEditParam param) throws BizException {
        logger.info("编辑任务服务,param:{}", param.toString());
        Long taskId = param.getTaskId();
        Double process = param.getProcess();
        String name = param.getName();
        //验证task
        FsdmTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        Double oldProcess = task.getProcess();
        Long planId = task.getPlanId();
        Integer deptId = task.getDeptId();

        //验证任务名称(一个子节点下的名字不同)
        FsdmTaskDTO fsdmTaskDTO = taskMapper.selectByParentIdAndName(planId, task.getParentId(), name);
        if (fsdmTaskDTO != null && !fsdmTaskDTO.getTaskId().equals(taskId)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务名称已存在");
        }

        Date planStart = param.getPlanStart() == null ? task.getPlanStart() : param.getPlanStart();
        Date planFinish = param.getPlanFinish() == null ? task.getPlanFinish() : param.getPlanFinish();
        Date actualStart = param.getActualStart();
        Date actualFinish = param.getActualFinish();
        RunningState runningState = this.computeRunningState(planStart, planFinish, actualStart, actualFinish);

        task.setType(param.getType());
        task.setName(name);
        task.setPlanStart(planStart);
        task.setPlanFinish(planFinish);
        task.setActualStart(actualStart);
        task.setActualFinish(actualFinish);
        task.setRunningState(runningState.getValue());
        task.setProcess(process == null ? 0 : process > MAX_PROCESS ? MAX_PROCESS : process);
        task.setUpdateState(UpdateState.YES.getValue());
        task.setUpdateUserId(param.getUpdateUserId());
        task.setUpdateUserName(param.getUpdateUserName());
        task.setUpdateTime(new Date());
        task.setRemark(param.getRemark());
        task.setTemplateId(param.getTemplateId());
        task.setTemplateName(param.getTemplateName());
        task.setSubcontractorId(param.getSubcontractorId());
        task.setSubcontractorName(param.getSubcontractorName());
        task.setStageRound(param.getStageRound());
        //this.computeDurationAndRunningState(task);
        taskMapper.updateByPrimaryKeySelective(task);
        taskMapper.updateActualProcess(task);

        // 更新责任人
        taskDutyMapper.deleteByTaskId(taskId);
        this.insertTaskDuty(deptId, planId, taskId, param.getDutyList());

        // 发布更新进度事件
        UpdateProcessEvent event = new UpdateProcessEvent(taskId);
        AppContextUtil.context().publishEvent(event);

        // 插入进度更新记录
        if (oldProcess == null && !oldProcess.equals(task.getProcess())) {
            FsdmTaskProcessLog processLog = new FsdmTaskProcessLog();
            processLog.setProcess(task.getProcess());
            processLog.setTaskId(taskId);
            processLog.setTime(new Date());
            processLog.setUserId(param.getUpdateUserId());
            processLog.setUserName(param.getUpdateUserName());
            taskProcessLogMapper.insertSelective(processLog);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(TaskDeleteParam param) throws BizException {
        logger.info("删除任务服务,param:{}", param.toString());
        List<FsdmTaskDTO> list = taskMapper.selectByParentId(param.getTaskId());
        if (list.size() > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务下有子任务,不能删除");
        }
        FsdmTask fsdmTask = taskMapper.selectByPrimaryKey(param.getTaskId());
        if (fsdmTask == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "该任务不存在");
        }
        Long planId = fsdmTask.getPlanId();
        Long uid = fsdmTask.getUid();

        taskMapper.deleteLogic(planId, fsdmTask.getCode(), param.getUpdateUserId(), param.getUpdateUserName());
        taskMapper.batchUpdateUid(planId, uid, -1);

        // 发布更新进度事件
        UpdateProcessEvent event = new UpdateProcessEvent(param.getTaskId());
        AppContextUtil.context().publishEvent(event);
    }

    @Override
    public void moveUp(Long taskId) throws BizException {
        logger.info("任务向上移动服务,taskId:{}", taskId);
        FsdmTask fsdmTask = taskMapper.selectByPrimaryKey(taskId);
        if (fsdmTask == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "未找到该任务");
        }
        Long uid = fsdmTask.getUid();

        // 查找该节点的上一个任务
        FsdmTask lastTask = taskMapper.selectLastByPlanIdAndUid(fsdmTask.getParentId(), uid);
        if (lastTask == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务节点不能移动");
        }
        Long lastTaskUid = lastTask.getUid();

        // 查找子孙节点
        List<FsdmTask> list = taskMapper.selectDescendantByCode(fsdmTask.getCode());
        List<FsdmTask> lastList = taskMapper.selectDescendantByCode(lastTask.getCode());
        list.addAll(lastList);
        for (FsdmTask task : list) {
            task.setUid(lastTaskUid);
            lastTaskUid++;
        }
        // 批量更新uid
        taskMapper.batchInsertOrUpdate(list);
    }

    @Override
    public void moveDown(Long taskId) throws BizException {
        logger.info("任务向下移动服务,taskId:{}", taskId);
        FsdmTask fsdmTask = taskMapper.selectByPrimaryKey(taskId);
        if (fsdmTask == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "未找到该任务");
        }
        Long uid = fsdmTask.getUid();
        String code = fsdmTask.getCode();

        // 查找该节点的下一个任务
        FsdmTask nextTask = taskMapper.selectNextByPlanIdAndUid(fsdmTask.getParentId(), uid);
        if (nextTask == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务节点不能移动");
        }
        // 查找子孙节点
        List<FsdmTask> list = taskMapper.selectDescendantByCode(code);
        List<FsdmTask> nextList = taskMapper.selectDescendantByCode(nextTask.getCode());
        nextList.addAll(list);
        for (FsdmTask task : nextList) {
            task.setUid(uid);
            uid++;
        }
        // 批量更新uid
        taskMapper.batchInsertOrUpdate(nextList);
    }

    /********前置任务*********/

    @Override
    public ListData<FsdmTaskRelationDTO> taskRelationList(Long taskId) throws BizException {
        logger.info("获取前置任务列表服务,taskId:{}", taskId);
        List<FsdmTaskRelationDTO> list = taskRelationMapper.selectByPostTaskId(taskId);
        return new ListData<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTaskRelation(TaskRelationAddParam param) throws BizException {
        logger.info("添加前置任务服务,param:{}", param.toString());
        Long postTaskId = param.getPostTaskId();
        Long preTaskId = param.getPreTaskId();
        FsdmTaskRelation fsdmTaskRelation = taskRelationMapper.selectByPreTaskIdAndPostTaskId(preTaskId, postTaskId);
        if (fsdmTaskRelation != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该前置关系已存在");
        }
        FsdmTaskRelation taskRelation = taskRelationMapper.selectByPreTaskIdAndPostTaskId(postTaskId, preTaskId);
        if (taskRelation != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务不能设为前置任务");
        }

        boolean b = this.checkRelation(preTaskId, postTaskId);
        if (b) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务不能设为前置任务");
        }
        FsdmTask preTask = fsdmTaskMapper.selectByPrimaryKey(preTaskId);
        FsdmTaskRelation record = new FsdmTaskRelation();
        record.setId(KeyGeneratorUtil.genLongId());
        record.setDeptId(preTask.getDeptId());
        record.setPlanId(preTask.getPlanId());
        record.setPreTaskId(preTaskId);
        record.setPostTaskId(postTaskId);
        record.setType(param.getType());
        record.setInterval(param.getInterval());
        taskRelationMapper.insertSelective(record);
        // 修改任务变更状态
        fsdmTaskMapper.updateUpdateState(postTaskId, UpdateState.YES.getValue(), param.getUpdateUserId(), param.getUpdateUserName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editTaskRelation(TaskRelationEditParam param) throws BizException {
        logger.info("编辑前置任务服务,param:{}", param.toString());
        Long postTaskId = param.getPostTaskId();
        Long preTaskId = param.getPreTaskId();
        FsdmTaskRelation fsdmTaskRelation = taskRelationMapper.selectByPreTaskIdAndPostTaskId(preTaskId, postTaskId);
        if (fsdmTaskRelation != null && !fsdmTaskRelation.getId().equals(param.getId())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该前置关系已存在");
        }
        FsdmTaskRelation taskRelation = taskRelationMapper.selectByPreTaskIdAndPostTaskId(postTaskId, preTaskId);
        if (taskRelation != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务不能设为前置任务");
        }

        boolean b = this.checkRelation(preTaskId, postTaskId);
        if (b) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "该任务不能设为前置任务");
        }

        FsdmTaskRelation record = new FsdmTaskRelation();
        record.setId(param.getId());
        record.setPreTaskId(preTaskId);
        record.setPostTaskId(postTaskId);
        record.setType(param.getType());
        record.setInterval(param.getInterval());
        taskRelationMapper.updateByPrimaryKeySelective(record);
        // 修改任务变更状态
        fsdmTaskMapper.updateUpdateState(postTaskId, UpdateState.YES.getValue(), param.getUpdateUserId(), param.getUpdateUserName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTaskRelation(TaskRelationDelParam param) throws BizException {
        logger.info("删除前置任务服务,param:{}", param.toString());
        FsdmTaskRelation fsdmTaskRelation = taskRelationMapper.selectByPrimaryKey(param.getId());
        if (fsdmTaskRelation == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "该任务前置关系不存在");
        }
        taskRelationMapper.deleteLogicById(param.getId());
        // 修改任务变更状态
        fsdmTaskMapper.updateUpdateState(fsdmTaskRelation.getPostTaskId(), UpdateState.YES.getValue(), param.getUpdateUserId(), param.getUpdateUserName());
    }

    /********里程碑*********/

    @Override
    public PageData<FsdmTaskDTO> milestoneList(Long planId, Integer pageNum, Integer pageSize, Integer level, Integer runningState, Integer state) throws BizException {
        logger.info("查询里程碑列表服务,planId:{},pageNum:{},pageSize:{},level:{},runningState:{},state:{}", planId, pageNum, pageSize, level, runningState, state);
        PageHelper.startPage(pageNum, pageSize);
        List<FsdmTaskDTO> list = taskMapper.selectMilestone(planId, level, runningState, state);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<FsdmTaskDTO> milestoneList(Long planId) throws BizException {
        logger.info("查找大屏里程碑数据,planId:{}", planId);
        return taskMapper.selectBoardByParentId(planId);
    }

    @Override
    public List<FsdmTaskDTO> milestoneList(Integer deptId) throws BizException {
        String actPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.ACT_ID.getCode());
        if (StringUtils.isNotBlank(actPlanId)) {
            logger.info("actPlanId:{}", actPlanId);
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectBoardByParentId(Long.valueOf(actPlanId));
            return list;
        }
        return Collections.emptyList();
    }

    /********我的任务*********/

    @Override
    public NumStatDTO myTaskStat(Integer userId, Integer deptId) throws BizException {
        List<FsdmTaskStatDTO> list = taskMapper.selectMyTaskStat(userId, deptId);
        Map<Integer, Integer> map = CollectionUtil.list2map(list, FsdmTaskStatDTO::getType, FsdmTaskStatDTO::getNum);

        NumStatDTO stat = new NumStatDTO();
        stat.setUnActiveNum(map.getOrDefault(RunningState.UNACTIVE.getValue(), 0));
        stat.setUnFinishNum(map.getOrDefault(RunningState.UNFINISHED.getValue(), 0));
        stat.setFinishNum(map.getOrDefault(RunningState.FINISHED.getValue(), 0));
        stat.setTotal(stat.getUnActiveNum() + stat.getUnFinishNum() + stat.getFinishNum());
        return stat;
    }

    @Override
    public PageData<MyTaskDTO> myTaskList(Integer userId, Integer deptId, Integer pageNum, Integer pageSize, Integer updateState, Integer runningState, Integer state) throws BizException {
        logger.info("我的任务列表服务,userId:{},deptId:{},pageNum:{},pageSize:{},updateState:{},runningState:{},state:{}", userId, deptId, pageNum, pageSize, updateState, runningState, state);
        PageHelper.startPage(pageNum, pageSize);
        List<MyTaskDTO> list = taskMapper.selectMyTaskList(userId, deptId, updateState, runningState, state);
        PageHelper.clearPage();

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<MyTaskDTO> myTaskList(Integer userId, Long planId, Integer pageNum, Integer pageSize, Integer updateState, Integer state) throws BizException {
        logger.info("我的任务列表服务(按计划查找),userId:{},planId:{},pageNum:{},pageSize:{},updateState:{},state:{}", userId, planId, pageNum, pageSize, updateState, state);
        PageHelper.startPage(pageNum, pageSize);
        List<MyTaskDTO> list = taskMapper.selectMyTaskListByPlanId(userId, planId, updateState, state);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importProjectFile(Long planId, List<FsdmTaskDTO> list, Integer userId, String userName) throws BizException {
        logger.info("导入project文件服务,planId:{}", planId);
        FsdmPlan fsdmPlan = planMapper.selectByPrimaryKey(planId);
        if (fsdmPlan == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "未找到该计划");
        }
        Integer deptId = fsdmPlan.getDeptId();
        for (FsdmTaskDTO fsdmTaskDTO : list) {
            FsdmTask fsdmTask = new FsdmTask();
            fsdmTask.setPlanStart(fsdmTaskDTO.getPlanStart());
            fsdmTask.setPlanFinish(fsdmTaskDTO.getPlanFinish());
            fsdmTask.setActualStart(fsdmTaskDTO.getActualStart());
            fsdmTask.setActualFinish(fsdmTaskDTO.getActualFinish());
            this.computeDurationAndRunningState(fsdmTask);

            fsdmTaskDTO.setPlanDuration(fsdmTask.getPlanDuration());
            fsdmTaskDTO.setActualDuration(fsdmTask.getActualDuration());
            fsdmTaskDTO.setRunningState(fsdmTask.getRunningState());
            fsdmTaskDTO.setState(fsdmTask.getState());
            fsdmTaskDTO.setDeptId(deptId);
            fsdmTaskDTO.setPlanId(planId);
            fsdmTaskDTO.setUserId(userId);
            fsdmTaskDTO.setUserName(userName);
        }

        // 删除本计划已有的任务
        taskMapper.deleteLogic(planId, "", userId, userName);

        // 批量插入
        taskMapper.batchInsert(list);

        // 设置前置任务
        taskRelationMapper.deleteLogicByPlanId(planId);
        List<FsdmTaskRelationDTO> relationDTOS = new ArrayList<>();
        for (FsdmTaskDTO fsdmTaskDTO : list) {
            List<FsdmTaskRelationDTO> taskRelationList = fsdmTaskDTO.getTaskRelationList();
            relationDTOS.addAll(taskRelationList);
        }
        if (relationDTOS.size() > 0) {
            taskRelationMapper.batchInsert(deptId, planId, relationDTOS);
        }

        // 更新计划工期
        fsdmTaskMgr.updatePlanProcess(planId);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMyTask(MyTaskEditParam param) throws BizException {

        logger.info("编辑我的任务服务(小程序),param:{}", JSONUtil.toString(param));

        Double process = param.getProcess();
        Date actualStart = param.getActualStart();
        Date actualFinish = param.getActualFinish();
        Long taskId = param.getTaskId();

        //验证任务信息
        FsdmTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        Double oldProcess = task.getProcess();

        //更新任务信息
        task.setActualStart(actualStart);
        task.setActualFinish(actualFinish);
        task.setProcess(process == null ? 0 : process > MAX_PROCESS ? MAX_PROCESS : process);
        task.setUpdateState(UpdateState.YES.getValue());
        task.setUpdateUserId(param.getUpdateUserId());
        task.setUpdateUserName(param.getUpdateUserName());
        task.setUpdateTime(new Date());
        task.setRemark(param.getRemark());
        this.computeDurationAndRunningState(task);
        taskMapper.updateByPrimaryKeySelective(task);
        taskMapper.updateActualProcess(task);

        // 发布更新进度事件
        UpdateProcessEvent event = new UpdateProcessEvent(taskId);
        AppContextUtil.context().publishEvent(event);

        if (!oldProcess.equals(task.getProcess())) {
            FsdmTaskProcessLog processLog = new FsdmTaskProcessLog();
            processLog.setProcess(task.getProcess());
            processLog.setTaskId(taskId);
            processLog.setTime(new Date());
            processLog.setUserId(param.getUpdateUserId());
            processLog.setUserName(param.getUpdateUserName());
            taskProcessLogMapper.insertSelective(processLog);
        }
    }

    @Override
    public ListData<FsdmProcessLogDTO> processLog(Long taskId) throws BizException {
        logger.info("查找任务进度更新记录,taskId:{}", taskId);
        List<FsdmProcessLogDTO> list = taskProcessLogMapper.selectByTaskId(taskId);
        return new ListData<>(list);
    }

    /********工序管理*********/

    @Override
    public List<FsdmStageTemplateDTO> stageTemplateList(Integer deptId) throws BizException {
        List<FsdmStageTemplateDTO> list = fsdmStageTemplateMapper.selectByDeptId(deptId);
        return list;
    }

    @Override
    public void addStageTemplate(FsdmStageTemplateAdd param) throws BizException {
        FsdmStageTemplate record = new FsdmStageTemplate();
        record.setDeptId(param.getDeptId());
        record.setCode(param.getCode());
        record.setName(param.getName());
        fsdmStageTemplateMapper.insertSelective(record);
    }

    @Override
    public void editStageTemplate(FsdmStageTemplateEdit param) throws BizException {
        Integer templateId = param.getTemplateId();
        FsdmStageTemplate record = fsdmStageTemplateMapper.selectByPrimaryKey(templateId);
        if (record == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "工序模版不存在");
        }
        record.setCode(param.getCode());
        record.setName(param.getName());
        fsdmStageTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void delStageTemplate(Integer templateId) throws BizException {
        fsdmStageTemplateMapper.logicDeleteById(templateId);
    }

    @Override
    public List<FsdmStageDTO> stageList(Integer templateId) throws BizException {
        List<FsdmStageDTO> list = fsdmStageMapper.selectByTemplateId(templateId);
        return list;
    }

    @Override
    public void addStage(FsdmStageAdd param) throws BizException {
        FsdmStage record = new FsdmStage();
        record.setDeptId(param.getDeptId());
        record.setTemplateId(param.getTemplateId());
        record.setCode(param.getCode());
        record.setName(param.getName());
        fsdmStageMapper.insertSelective(record);
    }

    @Override
    public void editStage(FsdmStageEdit param) throws BizException {
        Integer stageId = param.getStageId();
        FsdmStage record = fsdmStageMapper.selectByPrimaryKey(stageId);
        if (record == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "工序不存在");
        }
        record.setCode(param.getCode());
        record.setName(param.getName());
        fsdmStageMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void delStage(Integer stageId) throws BizException {
        fsdmStageMapper.logicDeleteById(stageId);
    }

    /********工序执行*********/

    @Override
    public List<FsdmTaskStageDTO> getTaskStageStat(Integer deptId) throws BizException {
        List<FsdmTaskStageDTO> list = fsdmTaskStageMapper.selectTaskStageStat(deptId);
        return list;
    }

    @Override
    public List<FsdmTaskStageDTO> getTaskStageList(Long taskId) throws BizException {
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        Integer templateId = task.getTemplateId();
        if (templateId != null) {
            return this.getTaskStageList(task);
        }


        return Collections.EMPTY_LIST;
    }

    @Override
    public void addTaskStageRound(Long taskId) throws BizException {
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        fsdmTaskMapper.addStageRound(taskId);
    }

    @Override
    public void subTaskStageRound(Long taskId) throws BizException {
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }

        //已执行轮数
        Integer maxStageRound = fsdmTaskStageMapper.selectMaxStageRound(taskId);

        //确定工序轮数
        int stageRound = 1;
        if (task.getStageRound() != null) {
            stageRound = task.getStageRound();
        }

        if (stageRound - maxStageRound > 1) {
            fsdmTaskMapper.subStageRound(taskId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commitTaskStage(FsdmTaskStageCommit param) throws BizException {
        Long taskId = param.getTaskId();
        Integer stageRound = param.getStageRound();
        Integer stageId = param.getStageId();
        FsdmTaskStage taskStage = fsdmTaskStageMapper.selectByStageId(taskId, stageRound, stageId);
        if (taskStage != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "本轮工序已完成");
        }
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        if (task.getTemplateId() == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "该任务未设置工序模版");
        }

        Date startTime = null;
        Date endTime = new Date();
        int duration = 0;

        //查询上一道工序
        Integer lastRound = null;
        Integer lastStageId = null;
        List<FsdmStageDTO> stageList = fsdmStageMapper.selectByTemplateId(task.getTemplateId());
        for (int i = 0; i < stageList.size(); i++) {
            if (stageList.get(i).getStageId().equals(stageId)) {
                if (i > 0) {
                    lastRound = stageRound;
                    lastStageId = stageList.get(i - 1).getStageId();
                }
                if (i == 0 && stageRound > 1) {
                    lastRound = stageRound - 1;
                    lastStageId = stageList.get(stageList.size() - 1).getStageId();
                }
            }
        }
        if (lastRound != null && lastStageId != null) {
            FsdmTaskStage lastStage = fsdmTaskStageMapper.selectByStageId(taskId, lastRound, lastStageId);
            if (lastStage == null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "上一道工序还未完成");
            }
            startTime = lastStage != null ? lastStage.getEndTime() : null;
        }
        //计算时长
        if (startTime != null) {
            duration = (int) ((endTime.getTime() - startTime.getTime()) / 1000);
        }
        //保存工序执行
        taskStage = new FsdmTaskStage();
        taskStage.setDeptId(task.getDeptId());
        taskStage.setPlanId(task.getPlanId());
        taskStage.setTaskId(taskId);
        taskStage.setStageRound(stageRound);
        taskStage.setStageId(stageId);
        taskStage.setStageName(param.getStageName());
        taskStage.setStartTime(startTime);
        taskStage.setEndTime(endTime);
        taskStage.setDuration(duration);
        taskStage.setState(RunningState.FINISHED.getValue());
        fsdmTaskStageMapper.insertSelective(taskStage);

        //保存工序执行-扩展信息
        FsdmTaskStageExt ext = new FsdmTaskStageExt();
        ext.setDeptId(task.getDeptId());
        ext.setTaskStageId(taskStage.getId());
        ext.setHoleNum(param.getHoleNum());
        ext.setBlastAmount(param.getActualAmount());
        fsdmTaskStageExtMapper.insertSelective(ext);
    }

    /**
     * 获取任务工序执行列表
     *
     * @param task
     * @return
     */
    private List<FsdmTaskStageDTO> getTaskStageList(FsdmTask task) {
        Long taskId = task.getId();
        Integer templateId = task.getTemplateId();
        //工序模版
        List<FsdmStageDTO> stageList = fsdmStageMapper.selectByTemplateId(templateId);

        //任务已执行情况
        List<FsdmTaskStageDTO> taskStageList = fsdmTaskStageMapper.selectTaskStageList(taskId);
        Map<String, FsdmTaskStageDTO> taskStageMap = CollectionUtil.list2Map(taskStageList, stage -> stage.getStageRound() + ":" + stage.getStageId());
        Integer maxStageRound = fsdmTaskStageMapper.selectMaxStageRound(taskId);

        //确定工序轮数
        int stageRound = 1;
        if (task.getStageRound() != null && task.getStageRound() > 0) {
            stageRound = Math.max(maxStageRound, task.getStageRound());
        }

        //工序执行列表
        List<FsdmTaskStageDTO> list = new ArrayList<>(stageRound * stageList.size());

        //工序轮数
        for (int round = 1; round <= stageRound; round++) {
            //工序
            for (FsdmStageDTO stage : stageList) {
                String key = round + ":" + stage.getStageId();
                if (taskStageMap.containsKey(key)) {
                    list.add(taskStageMap.get(key));
                } else {
                    FsdmTaskStageDTO taskStageDTO = new FsdmTaskStageDTO();
                    taskStageDTO.setStageRound(round);
                    taskStageDTO.setStageId(stage.getStageId());
                    taskStageDTO.setStageName(stage.getName());
                    taskStageDTO.setState(RunningState.UNACTIVE.getValue());
                    list.add(taskStageDTO);
                }
            }
        }
        return list;
    }

    /********爆破管理*********/

    @Override
    public FsdmTaskBlastDTO getTaskBlastStat(Integer deptId) throws BizException {
        //计划炸药用量
        Double planAmount = this.getTaskBlastPlanAmount(deptId);
        //实际炸药用量
        FsdmTaskBlastDTO dto = fsdmTaskStageMapper.selectTaskBlastStat(deptId);
        dto.setPlanAmount(planAmount);
        dto.setActualHoleNum(dto.getHoleNum());
        return dto;
    }

    @Override
    public PageData<FsdmTaskBlastDTO> getTaskBlastList(Integer deptId, String keyword, Integer pageNum, Integer pageSize) throws BizException {

        PageHelper.startPage(pageNum, pageSize);
        List<FsdmTaskBlastDTO> taskList = taskMapper.selectStageTask(deptId, keyword);
        PageHelper.clearPage();

        List<Long> taskIds = taskList.stream().map(FsdmTaskBlastDTO::getTaskId).collect(Collectors.toList());
        if (taskIds.size() > 0) {
            //工作资源查询

            //工序执行查询
            List<FsdmTaskBlastDTO> taskStageList = fsdmTaskStageMapper.selectTaskBlastByTaskIds(taskIds);
            Map<String, FsdmTaskBlastDTO> taskStageMap = CollectionUtil.list2Map(taskStageList, dto -> String.valueOf(dto.getTaskId()));
            logger.info("taskStageMap:{}", taskStageMap);
            for (FsdmTaskBlastDTO task : taskList) {
                FsdmTaskBlastDTO taskStage = taskStageMap.get(String.valueOf(task.getTaskId()));
                task.setHoleNum(taskStage != null ? taskStage.getHoleNum() : 0);
                task.setActualAmount(taskStage != null ? taskStage.getActualAmount() : 0D);

                Double planAmount = this.getTaskBlastPlanAmount(task.getTaskId());
                task.setPlanAmount(planAmount);
            }
        }

        return PageUtil.pageData(PageInfo.of(taskList));
    }

    @Override
    public FsdmTaskBlastDTO getTaskBlastDetail(Long taskId) throws BizException {

        FsdmTaskDTO task = taskMapper.selectTaskByTaskId(taskId);
        if (task == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务信息不存在");
        }
        List<FsdmTaskStageDTO> stateList = fsdmTaskStageMapper.selectTaskStageList(taskId);
        FsdmTaskBlastDTO blast = fsdmTaskStageMapper.selectTaskBlastByTaskId(taskId);
        if (blast == null) {
            blast = new FsdmTaskBlastDTO();
            blast.setTaskId(taskId);
        }
        blast.setTaskName(task.getName());
        blast.setStageList(stateList);

        //任务资源->炸药计划使用量
        Double planAmount = this.getTaskBlastPlanAmount(taskId);
        blast.setPlanAmount(planAmount);

        return blast;
    }

    /********分层分块*********/

    @Override
    public List<FsdmTaskDTO> getTaskLatest(Integer deptId) throws BizException {
        String actPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.ACT_ID.getCode());
        if (StringUtils.isNotBlank(actPlanId)) {
            logger.info("actPlanId:{}", actPlanId);
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(actPlanId));
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getTaskHistory(Integer deptId, Date date) throws BizException {
        String actPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.ACT_ID.getCode());
        if (StringUtils.isNotBlank(actPlanId)) {
            logger.info("actPlanId:{}", actPlanId);
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(actPlanId));
            for (FsdmTaskDTO task : list) {
                RunningState planState = RunningState.UNACTIVE;
                RunningState actualState = RunningState.UNACTIVE;
                Date planStart = DateUtil.getDate(task.getPlanStart());
                Date planFinish = DateUtil.getDate(task.getPlanFinish());
                Date actualStart = DateUtil.getDate(task.getActualStart());
                Date actualFinish = DateUtil.getDate(task.getActualFinish());

                if (planStart != null && date.getTime() < planStart.getTime()) {
                    planState = RunningState.UNACTIVE;
                } else if (planFinish != null && date.getTime() >= planFinish.getTime()) {
                    planState = RunningState.FINISHED;
                } else if (planStart != null && planFinish != null
                        && date.getTime() >= planStart.getTime() && date.getTime() < planFinish.getTime()) {
                    planState = RunningState.UNFINISHED;
                }

                if (actualStart != null && date.getTime() < actualStart.getTime()) {
                    actualState = RunningState.UNACTIVE;
                } else if (actualFinish != null && date.getTime() >= actualFinish.getTime()) {
                    actualState = RunningState.FINISHED;
                } else if (actualStart != null && actualFinish != null
                        && date.getTime() >= actualStart.getTime() && date.getTime() < actualFinish.getTime()) {
                    actualState = RunningState.UNFINISHED;
                }

                task.setPlanState(planState.getValue());
                task.setActualState(actualState.getValue());
                task.setRunningState(actualState.getValue());
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getActTaskLatest(Integer deptId) throws BizException {
        String actPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.ACT_ID.getCode());
        if (StringUtils.isNotBlank(actPlanId)) {
            logger.info("actPlanId:{}", actPlanId);
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(actPlanId));
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getActTaskHistory(Integer deptId, Date date) throws BizException {
        String actPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.ACT_ID.getCode());
        if (StringUtils.isNotBlank(actPlanId)) {
            logger.info("actPlanId:{}", actPlanId);
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(actPlanId));
            for (FsdmTaskDTO task : list) {
                RunningState planState = RunningState.UNACTIVE;
                RunningState actualState = RunningState.UNACTIVE;
                Date planStart = DateUtil.getDate(task.getPlanStart());
                Date planFinish = DateUtil.getDate(task.getPlanFinish());
                Date actualStart = DateUtil.getDate(task.getActualStart());
                Date actualFinish = DateUtil.getDate(task.getActualFinish());

                if (planStart != null && date.getTime() < planStart.getTime()) {
                    planState = RunningState.UNACTIVE;
                } else if (planFinish != null && date.getTime() >= planFinish.getTime()) {
                    planState = RunningState.FINISHED;
                } else if (planStart != null && planFinish != null
                        && date.getTime() >= planStart.getTime() && date.getTime() < planFinish.getTime()) {
                    planState = RunningState.UNFINISHED;
                }

                if (actualStart != null && date.getTime() < actualStart.getTime()) {
                    actualState = RunningState.UNACTIVE;
                } else if (actualFinish != null && date.getTime() >= actualFinish.getTime()) {
                    actualState = RunningState.FINISHED;
                } else if (actualStart != null && actualFinish != null
                        && date.getTime() >= actualStart.getTime() && date.getTime() < actualFinish.getTime()) {
                    actualState = RunningState.UNFINISHED;
                }

                task.setPlanState(actualState.getValue());
                task.setActualState(actualState.getValue());
                task.setRunningState(actualState.getValue());
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getPlanTaskLatest(Integer deptId) throws BizException {
        String planPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.PLAN_ID.getCode());
        if (StringUtils.isNotBlank(planPlanId)) {
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(planPlanId));
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getPlanTaskHistory(Integer deptId, Date date) throws BizException {
        String planPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.PLAN_ID.getCode());
        if (StringUtils.isNotBlank(planPlanId)) {
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectFamilyTaskListByPlanId(Long.valueOf(planPlanId));
            for (FsdmTaskDTO task : list) {
                RunningState planState = RunningState.UNACTIVE;
                RunningState actualState = RunningState.UNACTIVE;
                Date planStart = DateUtil.getDate(task.getPlanStart());
                Date planFinish = DateUtil.getDate(task.getPlanFinish());
                Date actualStart = DateUtil.getDate(task.getActualStart());
                Date actualFinish = DateUtil.getDate(task.getActualFinish());

                if (planStart != null && date.getTime() < planStart.getTime()) {
                    planState = RunningState.UNACTIVE;
                } else if (planFinish != null && date.getTime() >= planFinish.getTime()) {
                    planState = RunningState.FINISHED;
                } else if (planStart != null && planFinish != null
                        && date.getTime() >= planStart.getTime() && date.getTime() < planFinish.getTime()) {
                    planState = RunningState.UNFINISHED;
                }

                if (actualStart != null && date.getTime() < actualStart.getTime()) {
                    actualState = RunningState.UNACTIVE;
                } else if (actualFinish != null && date.getTime() >= actualFinish.getTime()) {
                    actualState = RunningState.FINISHED;
                } else if (actualStart != null && actualFinish != null
                        && date.getTime() >= actualStart.getTime() && date.getTime() < actualFinish.getTime()) {
                    actualState = RunningState.UNFINISHED;
                }

                task.setPlanState(planState.getValue());
                task.setActualState(planState.getValue());
                task.setRunningState(planState.getValue());
            }
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public List<FsdmTaskDTO> getPlanTaskList(Integer deptId) throws BizException {
        String planPlanId = fsdmConfigMgr.getConfig(deptId, FsdmConfigType.PLAN_ID.getCode());
        if (StringUtils.isNotBlank(planPlanId)) {
            List<FsdmTaskDTO> list = fsdmTaskMapper.selectTaskListByPlanId(Long.valueOf(planPlanId), null);
            return list;
        }
        return Collections.emptyList();
    }

    /********BIM构件关联*********/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateElementBind(FsdmTaskElementParam param) {
        Long taskId = param.getTaskId();
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (ObjectUtil.isEmpty(task)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务不存在");
        }
        List<FsdmTaskElementParam.Element> elements = param.getElements();
        Long planId = task.getPlanId();
        FsdmPlan fsdmPlan = fsdmPlanMapper.selectByPrimaryKey(planId);
        if (ObjectUtil.isEmpty(fsdmPlan)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "计划不存在");
        }
        // 传入的元素集合
        List<String> inputElementIds = elements.stream().map(FsdmTaskElementParam.Element::getElement).collect(Collectors.toList());

        // 查询当前任务关联的绑定元素
        List<FsdmTaskElement> elementDTOList = fsdmTaskElementMapper.selectByTaskId(taskId, fsdmPlan.getBimModel());
        List<String> dbElementIds = elementDTOList.stream().map(FsdmTaskElement::getElement).collect(Collectors.toList());

        // 比较传入后的数据与数据库的数据  多的元素为新增 少的元素为解绑
        // 找出需要新增的元素
        List<FsdmTaskElementParam.Element> toAdd = elements.stream()
                .filter(element -> !dbElementIds.contains(element.getElement()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toAdd)) {
            // 保存新增的
            fsdmTaskElementMapper.batchBindElement(planId, fsdmPlan.getBimModel(), taskId, toAdd);
        }

        // 找出需要解除绑定的元素
        List<String> toDelete = dbElementIds.stream()
                .filter(element -> !inputElementIds.contains(element))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toDelete)) {
            // 保存新增的
            fsdmTaskElementMapper.unBindElement(planId, fsdmPlan.getBimModel(), taskId, toDelete);
        }
    }


    @Override
    public List<BimComponent> elementList(Integer deptId, Long planId, Integer modelId) {
        List<BimComponent> bimComponents = bimRedisDao.getComponent(modelId);
        if (CollectionUtils.isEmpty(bimComponents)) {
            return Collections.emptyList();
        }
        // 获取任务关联的BIM构件
        List<FsdmTaskElementDTO> elementDTOList = fsdmTaskElementMapper.selectByPlanId(planId, modelId + "");
        Map<String, Long> elementTaskMap = elementDTOList.stream()
                .collect(Collectors.toMap(FsdmTaskElementDTO::getElement, FsdmTaskElementDTO::getTaskId));
        Set<String> elementIds = elementTaskMap.keySet();
        // 标记已经关联的BIM构件
        for (BimComponent bimComponent : bimComponents) {
            if (!BimComponentType.ELEMENT.getType().equals(bimComponent.getNodeType())) {
                continue;
            }
            String nodeId = bimComponent.getNodeId();
            if (elementIds.contains(nodeId)) {
                bimComponent.setIsBind(1);
                bimComponent.setTaskId(elementTaskMap.get(nodeId));
            } else {
                bimComponent.setIsBind(0);
            }
        }
        return bimComponents;
    }

    @Override
    public List<FsdmTaskElementDTO> taskProcess(Long planId, Date date) throws BizException {
        FsdmPlan fsdmPlan = fsdmPlanMapper.selectByPrimaryKey(planId);
        if (ObjectUtil.isEmpty(fsdmPlan)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "计划不存在");
        }
        // 获取元素及其所属任务的时间
        List<FsdmTaskElementDTO> list = fsdmTaskElementMapper.selectElementTime(planId, fsdmPlan.getBimModel());
        for (FsdmTaskElementDTO fsdmTaskElementDTO : list) {
            Date planStart = fsdmTaskElementDTO.getPlanStart();
            Date planFinish = fsdmTaskElementDTO.getPlanFinish();

            Integer planProcessStatus = FsdmElementProcessStatus.DEFAULT.getCode();
            Integer actualProcessStatus = FsdmElementProcessStatus.DEFAULT.getCode();
            // 计划开始时间不为空 并且传入时间小于计划开始时间 则状态为未开始
            if (planStart != null && date.getTime() < planStart.getTime()) {
                planProcessStatus = FsdmElementProcessStatus.NOT_START.getCode();
            }
            // 计划完成时间不为空 并且传入时间大于计划结束时间 则状态为已完成
            if (planFinish != null && date.getTime() >= planFinish.getTime()) {
                planProcessStatus = FsdmElementProcessStatus.FINISHED.getCode();
            }
            // 计划开始时间和计划完成时间不为空 并且传入时间大于计划开始时间 小于计划结束时间 则状态为进行中
            if (planStart != null && planFinish != null && date.getTime() >= planStart.getTime()
                    && date.getTime() < planFinish.getTime()) {
                planProcessStatus = FsdmElementProcessStatus.IN_PROGRESS.getCode();
            }

            Date actualStart = fsdmTaskElementDTO.getActualStart();
            Date actualFinish = fsdmTaskElementDTO.getActualFinish();
            if (actualStart != null && date.getTime() < actualStart.getTime()) {
                actualProcessStatus = FsdmElementProcessStatus.NOT_START.getCode();
            }
            if (actualFinish != null && date.getTime() >= actualFinish.getTime()) {
                actualProcessStatus = FsdmElementProcessStatus.FINISHED.getCode();
            }
            // 实际开始时间不为空 并且实际结束时间不为空 并且传入时间大于实际开始时间 小于实际结束时间 则状态为进行中
            if (actualStart != null && actualFinish != null && date.getTime() >= actualStart.getTime()
                    && date.getTime() < actualFinish.getTime()) {
                actualProcessStatus = FsdmElementProcessStatus.IN_PROGRESS.getCode();
            }
            fsdmTaskElementDTO.setPlanProcessStatus(planProcessStatus);
            fsdmTaskElementDTO.setActualProcessStatus(actualProcessStatus);
        }
        return list;
    }

    @Override
    public void bindElement(FsdmTaskElementParam param) {
        Long taskId = param.getTaskId();
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (ObjectUtil.isEmpty(task)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务不存在");
        }
        Long planId = task.getPlanId();
        FsdmPlan fsdmPlan = fsdmPlanMapper.selectByPrimaryKey(planId);
        if (ObjectUtil.isEmpty(fsdmPlan)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "计划不存在");
        }

        String element = param.getElement();
        if (StringUtils.isBlank(element)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "构件不能为空");
        }
        String family = param.getFamily();

        FsdmTaskElement fsdmTaskElement = new FsdmTaskElement();
        fsdmTaskElement.setPlanId(fsdmPlan.getId());
        Integer modelId = StringUtils.isNotBlank(fsdmPlan.getBimModel()) ? Integer.valueOf(fsdmPlan.getBimModel()) : null;
        fsdmTaskElement.setModelId(modelId);
        fsdmTaskElement.setTaskId(taskId);
        fsdmTaskElement.setElement(element);
        fsdmTaskElement.setFamily(family);
        fsdmTaskElementMapper.insertSelective(fsdmTaskElement);
    }

    @Override
    public void unbindElement(FsdmTaskElementParam param) {
        Long taskId = param.getTaskId();
        FsdmTask task = fsdmTaskMapper.selectByPrimaryKey(taskId);
        if (ObjectUtil.isEmpty(task)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "任务不存在");
        }
        String element = param.getElement();
        if (StringUtils.isBlank(element)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "构件不能为空");
        }
        fsdmTaskElementMapper.logicDelete(taskId, element);
    }

    /********private*********/

    /**
     * 插入责任人
     *
     * @param deptId
     * @param planId
     * @param taskId
     * @param dutyList
     */
    private void insertTaskDuty(Integer deptId, Long planId, Long taskId, List<FsdmTaskDutyDTO> dutyList) {
        if (dutyList != null && dutyList.size() > 0) {
            for (FsdmTaskDutyDTO taskDutyDTO : dutyList) {
                taskDutyDTO.setId(KeyGeneratorUtil.genLongId());
            }
            taskDutyMapper.batchInsert(deptId, planId, taskId, dutyList);
        }
    }

    /**
     * 计算工期
     *
     * @param fsdmTask
     */
    private void computeDurationAndRunningState(FsdmTask fsdmTask) {
        Date actualStart = fsdmTask.getActualStart();
        Date actualFinish = fsdmTask.getActualFinish();
        Date planStart = fsdmTask.getPlanStart();
        Date planFinish = fsdmTask.getPlanFinish();
        Date today = new Date();
        // 若进度100%,则任务已完成
        if (MAX_PROCESS.equals(fsdmTask.getProcess())) {
            if (actualFinish == null) {
                actualFinish = today;
                fsdmTask.setActualFinish(today);
            }
            if (actualStart == null) {
                actualStart = today;
                fsdmTask.setActualStart(today);
            }
        }

        // 任务工期
        if (planStart != null && planFinish != null) {
            fsdmTask.setPlanDuration(DateUtil.getDaysBettweenDate(planStart, planFinish));
        }
        if (actualStart != null && actualFinish != null) {
            fsdmTask.setActualDuration(DateUtil.getDaysBettweenDate(actualStart, actualFinish));
        }

        // 任务执行状态
        if (actualStart == null) {
            fsdmTask.setRunningState(RunningState.UNACTIVE.getValue());
        } else if (actualFinish == null) {
            fsdmTask.setRunningState(RunningState.UNFINISHED.getValue());
        }
        if (actualFinish != null) {
            fsdmTask.setRunningState(RunningState.FINISHED.getValue());
            fsdmTask.setProcess(MAX_PROCESS);
        }

        // 完成状态
        if (planFinish != null && actualFinish != null) {
            if (planFinish.before(actualFinish)) {
                fsdmTask.setState(CompletionState.OVERDUE.getValue());
            } else if (actualFinish.before(planFinish)) {
                fsdmTask.setState(CompletionState.ADVANCE.getValue());
            } else {
                fsdmTask.setState(CompletionState.NORMAL.getValue());
            }
        }
        // 未完成
        Date now = new Date();
        if (planFinish != null && actualFinish == null) {
            if (planFinish.before(now)) {
                fsdmTask.setState(CompletionState.OVERDUE.getValue());
            } else {
                fsdmTask.setState(CompletionState.NORMAL.getValue());
            }
        }
    }

    private RunningState computeRunningState(Date planStart, Date planFinish, Date actualStart, Date actualFinish) {
        RunningState runningState = RunningState.UNACTIVE;
        if (actualStart != null) {
            runningState = RunningState.UNFINISHED;
            if (actualFinish != null) {
                runningState = RunningState.FINISHED;
            }
        }
        return runningState;
    }

    private CompletionState computeState(Date planStart, Date planFinish, Date actualStart, Date actualFinish) {
        return null;
    }

    /**
     * 检测任务的前置关系是否正确
     * 在同一支节点上返回true,不能作为前置任务
     *
     * @param preTaskId
     * @param postTaskId
     * @return
     */
    private boolean checkRelation(Long preTaskId, Long postTaskId) {
        FsdmTask preTask = fsdmTaskMapper.selectByPrimaryKey(preTaskId);
        FsdmTask postTask = fsdmTaskMapper.selectByPrimaryKey(postTaskId);
        if (preTask == null || postTask == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "任务不存在");
        }
        String innerCode1 = preTask.getInnerCode();
        String innerCode2 = postTask.getInnerCode();
        return innerCode1.contains(innerCode2) || innerCode2.contains(innerCode1);
    }

    /**
     * 获取任务炸药计划使用量
     *
     * @param taskId
     * @return
     */
    private Double getTaskBlastPlanAmount(Long taskId) {
        Double planAmount = 0D;
        List<FsdmTaskResourceDTO> resourceList = fsdmTaskResourceMapper.selectResourceByTaskId(taskId);
        for (FsdmTaskResourceDTO resource : resourceList) {
            if (BLAST_RESOURCE_NAMES.contains(resource.getName())) {
                planAmount += resource.getPlanAmount();
            }
        }
        return planAmount;
    }

    /**
     * 获取任务炸药计划使用量
     *
     * @param deptId
     * @return
     */
    private Double getTaskBlastPlanAmount(Integer deptId) {
        Double planAmount = 0D;
        List<FsdmTaskResourceDTO> resourceList = fsdmTaskResourceMapper.selectResourceByDeptId(deptId);
        for (FsdmTaskResourceDTO resource : resourceList) {
            if (BLAST_RESOURCE_NAMES.contains(resource.getName())) {
                planAmount += resource.getPlanAmount();
            }
        }
        return planAmount;
    }

}

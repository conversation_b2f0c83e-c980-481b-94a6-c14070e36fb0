package com.whfc.uni.dao.app;

import com.whfc.uni.dto.app.AppDictDTO;
import com.whfc.uni.entity.app.AppDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppDictMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppDict record);

    AppDict selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppDict record);

    /**
     * 查询配置
     *
     * @param deptId
     * @param code
     * @return
     */
    AppDictDTO selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 查询数据字典
     *
     * @param pid
     * @return
     */
    List<AppDictDTO> selectPid(@Param("pid") Integer pid);

    /**
     * 查询数据字典
     * @param deptId
     * @param code
     * @return
     */
    List<AppDictDTO> selectByPCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 根据code查询数据字典
     * @param code
     * @return
     */
    List<AppDictDTO> selectByCode(@Param("code") String code);

}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelClearanceStakeMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelClearanceStake">
        <!--@mbg.generated-->
        <!--@Table tunnel_clearance_stake-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="part_id" jdbcType="INTEGER" property="partId"/>
        <result column="stake_no" jdbcType="VARCHAR" property="stakeNo"/>
        <result column="test_stake_no" jdbcType="VARCHAR" property="testStakeNo"/>
        <result column="instrument" jdbcType="VARCHAR" property="instrument"/>
        <result column="temp_amend" jdbcType="VARCHAR" property="tempAmend"/>
        <result column="temp_amend_coefficient" jdbcType="VARCHAR" property="tempAmendCoefficient"/>
        <result column="setting_time" jdbcType="TIMESTAMP" property="settingTime"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, part_id, stake_no, test_stake_no, instrument, temp_amend, temp_amend_coefficient,
        setting_time, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_clearance_stake
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_clearance_stake
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStake"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_clearance_stake (part_id, stake_no, test_stake_no,
        instrument, temp_amend, temp_amend_coefficient,
        setting_time, del_flag, update_time,
        create_time)
        values (#{partId,jdbcType=INTEGER}, #{stakeNo,jdbcType=VARCHAR}, #{testStakeNo,jdbcType=VARCHAR},
        #{instrument,jdbcType=VARCHAR}, #{tempAmend,jdbcType=VARCHAR}, #{tempAmendCoefficient,jdbcType=VARCHAR},
        #{settingTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStake" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_clearance_stake
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partId != null">
                part_id,
            </if>
            <if test="stakeNo != null">
                stake_no,
            </if>
            <if test="testStakeNo != null">
                test_stake_no,
            </if>
            <if test="instrument != null">
                instrument,
            </if>
            <if test="tempAmend != null">
                temp_amend,
            </if>
            <if test="tempAmendCoefficient != null">
                temp_amend_coefficient,
            </if>
            <if test="settingTime != null">
                setting_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partId != null">
                #{partId,jdbcType=INTEGER},
            </if>
            <if test="stakeNo != null">
                #{stakeNo,jdbcType=VARCHAR},
            </if>
            <if test="testStakeNo != null">
                #{testStakeNo,jdbcType=VARCHAR},
            </if>
            <if test="instrument != null">
                #{instrument,jdbcType=VARCHAR},
            </if>
            <if test="tempAmend != null">
                #{tempAmend,jdbcType=VARCHAR},
            </if>
            <if test="tempAmendCoefficient != null">
                #{tempAmendCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="settingTime != null">
                #{settingTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStake">
        <!--@mbg.generated-->
        update tunnel_clearance_stake
        <set>
            <if test="partId != null">
                part_id = #{partId,jdbcType=INTEGER},
            </if>
            <if test="stakeNo != null">
                stake_no = #{stakeNo,jdbcType=VARCHAR},
            </if>
            <if test="testStakeNo != null">
                test_stake_no = #{testStakeNo,jdbcType=VARCHAR},
            </if>
            <if test="instrument != null">
                instrument = #{instrument,jdbcType=VARCHAR},
            </if>
            <if test="tempAmend != null">
                temp_amend = #{tempAmend,jdbcType=VARCHAR},
            </if>
            <if test="tempAmendCoefficient != null">
                temp_amend_coefficient = #{tempAmendCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="settingTime != null">
                setting_time = #{settingTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStake">
        <!--@mbg.generated-->
        update tunnel_clearance_stake
        set part_id = #{partId,jdbcType=INTEGER},
        stake_no = #{stakeNo,jdbcType=VARCHAR},
        test_stake_no = #{testStakeNo,jdbcType=VARCHAR},
        instrument = #{instrument,jdbcType=VARCHAR},
        temp_amend = #{tempAmend,jdbcType=VARCHAR},
        temp_amend_coefficient = #{tempAmendCoefficient,jdbcType=VARCHAR},
        setting_time = #{settingTime,jdbcType=TIMESTAMP},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPartId" resultType="com.whfc.uni.dto.tunnel.TunnelClearanceStakeDTO">
        select
        id as stakeId,
        part_id,
        stake_no,
        test_stake_no,
        instrument,
        temp_amend,
        temp_amend_coefficient,
        setting_time
        from tunnel_clearance_stake
        where
        part_id = #{partId}
        and del_flag=0
        order by create_time desc
    </select>
    <delete id="deleteByStakeIds">
        delete from tunnel_clearance_stake
        where id in
        <foreach collection="stakeIds" item="stakeId" open="(" separator="," close=")">
            #{stakeId}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstOutsideCheckImgMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstOutsideCheckImg">
    <!--@mbg.generated-->
    <!--@Table const_outside_check_img-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="deptId" jdbcType="INTEGER" property="deptid" />
    <result column="outside_check_id" jdbcType="INTEGER" property="outsideCheckId" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, deptId, outside_check_id, img_url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from const_outside_check_img
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from const_outside_check_img
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.constlog.ConstOutsideCheckImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into const_outside_check_img (deptId, outside_check_id, img_url, 
      del_flag, update_time, create_time
      )
    values (#{deptid,jdbcType=INTEGER}, #{outsideCheckId,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.constlog.ConstOutsideCheckImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into const_outside_check_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptid != null">
        deptId,
      </if>
      <if test="outsideCheckId != null">
        outside_check_id,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptid != null">
        #{deptid,jdbcType=INTEGER},
      </if>
      <if test="outsideCheckId != null">
        #{outsideCheckId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstOutsideCheckImg">
    <!--@mbg.generated-->
    update const_outside_check_img
    <set>
      <if test="deptid != null">
        deptId = #{deptid,jdbcType=INTEGER},
      </if>
      <if test="outsideCheckId != null">
        outside_check_id = #{outsideCheckId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.constlog.ConstOutsideCheckImg">
    <!--@mbg.generated-->
    update const_outside_check_img
    set deptId = #{deptid,jdbcType=INTEGER},
      outside_check_id = #{outsideCheckId,jdbcType=INTEGER},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <insert id="install">
        insert into const_outside_check_img (deptId, outside_check_id, img_url)
                values
        <foreach collection="imgUrls" item="imgUrl" separator=",">
            (#{deptId,jdbcType=INTEGER}, #{outsideCheckId,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="deleteByOutsideCheckId">
        update const_outside_check_img
        set del_flag= 1
        where outside_check_id = #{outsideCheckId}
          and del_flag = 0
    </update>

    <select id="selectByOutsideCheckId" resultType="java.lang.String">
        select img_url
        from const_outside_check_img
        where outside_check_id = #{outsideCheckId}
        and del_flag = 0
    </select>
</mapper>
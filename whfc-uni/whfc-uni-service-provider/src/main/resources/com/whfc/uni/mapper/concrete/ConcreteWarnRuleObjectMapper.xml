<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteWarnRuleObjectMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteWarnRuleObject">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, rule_type, object_id, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_warn_rule_object
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_warn_rule_object
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleObject">
    insert into concrete_warn_rule_object (id, rule_id, rule_type, 
      object_id, update_time, create_time
      )
    values (#{id,jdbcType=INTEGER}, #{ruleId,jdbcType=INTEGER}, #{ruleType,jdbcType=INTEGER}, 
      #{objectId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleObject">
    insert into concrete_warn_rule_object
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="objectId != null">
        object_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleObject">
    update concrete_warn_rule_object
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleObject">
    update concrete_warn_rule_object
    set rule_id = #{ruleId,jdbcType=INTEGER},
      rule_type = #{ruleType,jdbcType=INTEGER},
      object_id = #{objectId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectObjectIdByRuleId" resultType="java.lang.String">
    select object_id
    from concrete_warn_rule_object
    where rule_id = #{ruleId}
  </select>

  <select id="selectWarnRuleByObjectId" resultType="com.whfc.uni.dto.concrete.ConcreteWarnRuleDTO">
    select lwr.id as ruleId,
           lwr.dept_id,
           lwr.rule_name,
           lwr.rule_type,
           lwr.rule_param
    from concrete_warn_rule lwr
    inner join concrete_warn_rule_object lwro on lwr.id = lwro.rule_id
    where lwr.del_flag = 0
      and lwr.enable_flag = 1
      and lwro.object_id = #{objectId}
  </select>

  <delete id="deleteByRuleId">
    delete from concrete_warn_rule_object
    where rule_id = #{ruleId}
  </delete>

  <insert id="batchInsert">
    INSERT INTO concrete_warn_rule_object
    (
    rule_id,
    rule_type,
    object_id
    )
    values
    <foreach collection="list" item="item" separator="," >
      (
      #{item.ruleId},
      #{item.ruleType},
      #{item.objectId}
      )
    </foreach>
  </insert>
</mapper>
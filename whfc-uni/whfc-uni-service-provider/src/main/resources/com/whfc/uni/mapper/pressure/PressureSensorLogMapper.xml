<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.pressure.PressureSensorLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.pressure.PressureSensorLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sensor_id" jdbcType="INTEGER" property="sensorId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="pressure" jdbcType="DOUBLE" property="pressure" />
    <result column="battery" jdbcType="DOUBLE" property="battery" />
    <result column="sig" jdbcType="INTEGER" property="sig" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sensor_id, time, pressure, battery, sig, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pressure_sensor_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from pressure_sensor_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.pressure.PressureSensorLog">
    insert into pressure_sensor_log (id, sensor_id, time, 
      pressure, battery, sig, 
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{sensorId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, 
      #{pressure,jdbcType=DOUBLE}, #{battery,jdbcType=DOUBLE}, #{sig,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.pressure.PressureSensorLog">
    insert into pressure_sensor_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sensorId != null">
        sensor_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="pressure != null">
        pressure,
      </if>
      <if test="battery != null">
        battery,
      </if>
      <if test="sig != null">
        sig,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sensorId != null">
        #{sensorId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="pressure != null">
        #{pressure,jdbcType=DOUBLE},
      </if>
      <if test="battery != null">
        #{battery,jdbcType=DOUBLE},
      </if>
      <if test="sig != null">
        #{sig,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.pressure.PressureSensorLog">
    update pressure_sensor_log
    <set>
      <if test="sensorId != null">
        sensor_id = #{sensorId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="pressure != null">
        pressure = #{pressure,jdbcType=DOUBLE},
      </if>
      <if test="battery != null">
        battery = #{battery,jdbcType=DOUBLE},
      </if>
      <if test="sig != null">
        sig = #{sig,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.pressure.PressureSensorLog">
    update pressure_sensor_log
    set sensor_id = #{sensorId,jdbcType=INTEGER},
      time = #{time,jdbcType=TIMESTAMP},
      pressure = #{pressure,jdbcType=DOUBLE},
      battery = #{battery,jdbcType=DOUBLE},
      sig = #{sig,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectSensorLog" resultType="com.whfc.uni.dto.pressure.PressureSensorDataDTO">
    select `time`,pressure,battery,sig
      from pressure_sensor_log
    where sensor_id= #{sensorId}
      <if test="startTime != null">
      and `time` >=#{startTime}
      </if>
      <if test="endTime != null">
        <![CDATA[
      and `time` <= #{endTime}
        ]]>
      </if>
    order by `time`
  </select>

  <select id="selectSensorData" resultType="com.whfc.uni.dto.pressure.PressureSensorDataDTO">
    select `time`,pressure,battery,sig
     from pressure_sensor_log
    where sensor_id= #{sensorId}
      and `time` = #{time}
  </select>
</mapper>
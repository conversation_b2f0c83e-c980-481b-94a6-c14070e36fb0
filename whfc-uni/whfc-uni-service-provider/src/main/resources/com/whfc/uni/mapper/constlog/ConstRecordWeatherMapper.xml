<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstRecordWeatherMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstRecordWeather">
        <!--@mbg.generated-->
        <!--@Table const_record_weather-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="weather" jdbcType="VARCHAR" property="weather"/>
        <result column="am_weather" jdbcType="VARCHAR" property="amWeather"/>
        <result column="pm_weather" jdbcType="VARCHAR" property="pmWeather"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, `date`, weather, am_weather, pm_weather, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_record_weather
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from const_record_weather
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insertSelective" parameterType="com.whfc.uni.entity.constlog.ConstRecordWeather">
        <!--@mbg.generated-->
        insert into const_record_weather
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="weather != null">
                weather,
            </if>
            <if test="amWeather != null">
                am_weather,
            </if>
            <if test="pmWeather != null">
                pm_weather,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="weather != null">
                #{weather,jdbcType=VARCHAR},
            </if>
            <if test="amWeather != null">
                #{amWeather,jdbcType=VARCHAR},
            </if>
            <if test="pmWeather != null">
                #{pmWeather,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstRecordWeather">
        <!--@mbg.generated-->
        update const_record_weather
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="weather != null">
                weather = #{weather,jdbcType=VARCHAR},
            </if>
            <if test="amWeather != null">
                am_weather = #{amWeather,jdbcType=VARCHAR},
            </if>
            <if test="pmWeather != null">
                pm_weather = #{pmWeather,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.constlog.ConstRecordWeather">
        <!--@mbg.generated-->
        update const_record_weather
        set dept_id     = #{deptId,jdbcType=INTEGER},
            `date`      = #{date,jdbcType=DATE},
            weather     = #{weather,jdbcType=VARCHAR},
            am_weather  = #{amWeather,jdbcType=VARCHAR},
            pm_weather  = #{pmWeather,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptIdAndDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_record_weather
        WHERE dept_id = #{deptId}
          AND date = DATE(#{date})
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstReportTemplateMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstReportTemplate">
        <!--@mbg.generated-->
        <!--@Table const_report_template-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="template" jdbcType="VARCHAR" property="template"/>
        <result column="language" jdbcType="VARCHAR" property="language"/>
        <result column="date_format" jdbcType="VARCHAR" property="dateFormat"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, `template`, language, date_format
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_report_template
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.constlog.ConstReportTemplate" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_report_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="template != null">
                `template`,
            </if>
            <if test="language != null">
                language,
            </if>
            <if test="dateFormat != null">
                date_format,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="template != null">
                #{template,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="dateFormat != null">
                #{dateFormat,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstReportTemplate">
        <!--@mbg.generated-->
        update const_report_template
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="template != null">
                `template` = #{template,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="dateFormat != null">
                date_format = #{dateFormat,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptId" resultType="com.whfc.uni.dto.constlog.ConstReportTemplateDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_report_template
        WHERE dept_id IN (0, #{deptId})
        ORDER BY dept_id DESC
        LIMIT 1;
    </select>
</mapper>
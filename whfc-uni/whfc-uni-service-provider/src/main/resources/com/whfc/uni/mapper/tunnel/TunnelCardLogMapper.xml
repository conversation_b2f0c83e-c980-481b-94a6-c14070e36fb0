<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelCardLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelCardLog">
    <!--@mbg.generated-->
    <!--@Table tunnel_card_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="tunnel_id" jdbcType="INTEGER" property="tunnelId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="work_name" jdbcType="VARCHAR" property="workName" />
    <result column="hole" jdbcType="VARCHAR" property="hole" />
    <result column="distance" jdbcType="DOUBLE" property="distance" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, tunnel_id, `time`, sn, `name`, group_name, work_name, hole, distance, 
    del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tunnel_card_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tunnel_card_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelCardLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tunnel_card_log (dept_id, tunnel_id, `time`, 
      sn, `name`, group_name, 
      work_name, hole, distance, 
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{tunnelId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, 
      #{sn,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, 
      #{workName,jdbcType=VARCHAR}, #{hole,jdbcType=VARCHAR}, #{distance,jdbcType=DOUBLE}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelCardLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tunnel_card_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="tunnelId != null">
        tunnel_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="workName != null">
        work_name,
      </if>
      <if test="hole != null">
        hole,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tunnelId != null">
        #{tunnelId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="workName != null">
        #{workName,jdbcType=VARCHAR},
      </if>
      <if test="hole != null">
        #{hole,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelCardLog">
    <!--@mbg.generated-->
    update tunnel_card_log
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tunnelId != null">
        tunnel_id = #{tunnelId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="workName != null">
        work_name = #{workName,jdbcType=VARCHAR},
      </if>
      <if test="hole != null">
        hole = #{hole,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelCardLog">
    <!--@mbg.generated-->
    update tunnel_card_log
    set dept_id = #{deptId,jdbcType=INTEGER},
      tunnel_id = #{tunnelId,jdbcType=INTEGER},
      `time` = #{time,jdbcType=TIMESTAMP},
      sn = #{sn,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      group_name = #{groupName,jdbcType=VARCHAR},
      work_name = #{workName,jdbcType=VARCHAR},
      hole = #{hole,jdbcType=VARCHAR},
      distance = #{distance,jdbcType=DOUBLE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
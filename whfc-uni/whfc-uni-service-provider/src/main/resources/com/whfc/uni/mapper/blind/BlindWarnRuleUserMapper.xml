<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.blind.BlindWarnRuleUserMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.blind.BlindWarnRuleUser">
        <!--@Table blind_warn_rule_user-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="to_user_id" jdbcType="INTEGER" property="toUserId"/>
        <result column="to_user_name" jdbcType="VARCHAR" property="toUserName"/>
        <result column="to_user_phone" jdbcType="VARCHAR" property="toUserPhone"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, rule_id, to_user_id, to_user_name, to_user_phone, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from blind_warn_rule_user
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.blind.BlindWarnRuleUser" useGeneratedKeys="true">
        insert into blind_warn_rule_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="toUserId != null">
                to_user_id,
            </if>
            <if test="toUserName != null">
                to_user_name,
            </if>
            <if test="toUserPhone != null">
                to_user_phone,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="toUserId != null">
                #{toUserId,jdbcType=INTEGER},
            </if>
            <if test="toUserName != null">
                #{toUserName,jdbcType=VARCHAR},
            </if>
            <if test="toUserPhone != null">
                #{toUserPhone,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.blind.BlindWarnRuleUser">
        update blind_warn_rule_user
        <set>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="toUserId != null">
                to_user_id = #{toUserId,jdbcType=INTEGER},
            </if>
            <if test="toUserName != null">
                to_user_name = #{toUserName,jdbcType=VARCHAR},
            </if>
            <if test="toUserPhone != null">
                to_user_phone = #{toUserPhone,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByWarnRuleId" resultType="com.whfc.entity.dto.msg.AppMsgToUserDTO">
        select to_user_id    as userId,
               to_user_name  as nickName,
               to_user_phone as phone
        from blind_warn_rule_user
        where rule_id = #{ruleId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByWarnRuleId">
        delete
        from blind_warn_rule_user
        where rule_id = #{ruleId,jdbcType=INTEGER}
    </delete>

    <insert id="batchInsert">
        INSERT INTO blind_warn_rule_user
        (rule_id,
         to_user_id,
         to_user_name,
         to_user_phone)
        values
        <foreach collection="userList" item="item" separator=",">
            (#{item.ruleId},
             #{item.userId},
             #{item.username},
             #{item.phone})
        </foreach>
    </insert>
</mapper>
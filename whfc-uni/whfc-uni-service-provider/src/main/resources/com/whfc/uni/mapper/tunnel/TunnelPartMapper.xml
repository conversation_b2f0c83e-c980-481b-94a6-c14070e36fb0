<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelPartMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelPart">
        <!--@mbg.generated-->
        <!--@Table tunnel_part-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, part_name, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_part
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_part
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelPart"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_part (dept_id, part_name, del_flag,
        update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{partName,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelPart"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_part
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="partName != null">
                part_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                #{partName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelPart">
        <!--@mbg.generated-->
        update tunnel_part
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                part_name = #{partName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelPart">
        <!--@mbg.generated-->
        update tunnel_part
        set dept_id = #{deptId,jdbcType=INTEGER},
        part_name = #{partName,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByDeptId" resultType="com.whfc.uni.dto.tunnel.TunnelPartDTO">
        select
        id as partId,
        part_name
        from tunnel_part
        where
        dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        and del_flag = 0
        <if test="partName != null and partName != ''">
           and part_name like concat('%',#{partName},'%')
        </if>
        order by create_time desc
    </select>
    <update id="del">
        update tunnel_part
        set del_flag= 1
        where
        id = #{id}
        and del_flag = 0
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteItemDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteItemData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="concrete_id" jdbcType="INTEGER" property="concreteId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="net_state" jdbcType="INTEGER" property="netState" />
    <result column="battery" jdbcType="INTEGER" property="battery" />
    <result column="signal" jdbcType="INTEGER" property="signal" />
    <result column="cycle" jdbcType="INTEGER" property="cycle" />
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, concrete_id, `time`, net_state, battery, `signal`, `cycle`, `data`, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_item_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_item_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteItemData">
    insert into concrete_item_data (
      id, dept_id, concrete_id,
      time, net_state, battery, 
      signal, cycle, data, update_time,
      create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{concreteId,jdbcType=INTEGER}, 
      #{time,jdbcType=TIMESTAMP}, #{netState,jdbcType=INTEGER}, #{battery,jdbcType=INTEGER}, 
      #{signal,jdbcType=INTEGER}, #{cycle,jdbcType=INTEGER}, #{data}, #{updateTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItemData">
    insert into concrete_item_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="concreteId != null">
        concrete_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="netState != null">
        net_state,
      </if>
      <if test="battery != null">
        battery,
      </if>
      <if test="signal != null">
        `signal`,
      </if>
      <if test="cycle != null">
        `cycle`,
      </if>
      <if test="data != null">
        `data`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="concreteId != null">
        #{concreteId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="netState != null">
        #{netState,jdbcType=INTEGER},
      </if>
      <if test="battery != null">
        #{battery,jdbcType=INTEGER},
      </if>
      <if test="signal != null">
        #{signal,jdbcType=INTEGER},
      </if>
      <if test="cycle != null">
        #{cycle,jdbcType=INTEGER},
      </if>
      <if test="data != null">
        #{data},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItemData">
    update concrete_item_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="concreteId != null">
        concrete_id = #{concreteId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="netState != null">
        net_state = #{netState,jdbcType=INTEGER},
      </if>
      <if test="battery != null">
        battery = #{battery,jdbcType=INTEGER},
      </if>
      <if test="signal != null">
        `signal` = #{signal,jdbcType=INTEGER},
      </if>
      <if test="cycle != null">
        `cycle` = #{cycle,jdbcType=INTEGER},
      </if>
      <if test="data != null">
        `data` = #{data,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteItemData">
    update concrete_item_data
    set dept_id = #{deptId,jdbcType=INTEGER},
      concrete_id = #{concreteId,jdbcType=INTEGER},
      time = #{time,jdbcType=TIMESTAMP},
      net_state = #{netState,jdbcType=INTEGER},
      battery = #{battery,jdbcType=INTEGER},
      signal = #{signal,jdbcType=INTEGER},
      cycle = #{cycle,jdbcType=INTEGER},
      data = #{data},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectItemData" resultType="com.whfc.uni.dto.concrete.ConcreteItemDataDTO">
     select td.id as concreteId,
            td.name,
            td.platform,
            td.sn,
            tdd.time,
            tdd.net_state,
            tdd.battery,
            tdd.signal,
            tdd.cycle,
            tdd.temp_diff,
            tdd.data
    from concrete_item_data tdd
    inner join concrete_item td on tdd.concrete_id = td.id
    where td.dept_id = #{deptId,jdbcType=INTEGER}
    and td.del_flag = 0
    <if test="groupId != null">
      td.group_id = #{groupId,jdbcType=INTEGER}
    </if>
    <if test="keyword !=null and keyword.length()>0">
      td.name = concat('%',#{keyword},'%')
    </if>
  </select>

  <select id="selectDTOByConcreteId" resultType="com.whfc.uni.dto.concrete.ConcreteItemDataDTO">
    select tdd.concrete_id,
           tdd.time,
           tdd.net_state,
           tdd.battery,
           tdd.signal,
           tdd.cycle,
           tdd.data
    from concrete_item_data tdd
    where tdd.concrete_id = #{concreteId}
  </select>

  <select id="selectByConcreteId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from concrete_item_data
    where concrete_id = #{concreteId}
    order by id desc limit 1
  </select>
</mapper>
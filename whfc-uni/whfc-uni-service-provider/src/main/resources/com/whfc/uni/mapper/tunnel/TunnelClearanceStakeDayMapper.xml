<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelClearanceStakeDayMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelClearanceStakeDay">
        <!--@mbg.generated-->
        <!--@Table tunnel_clearance_stake_day-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="stake_id" jdbcType="INTEGER" property="stakeId"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="temp_correct_t" jdbcType="DOUBLE" property="tempCorrectT"/>
        <result column="temp_correct_ditt" jdbcType="DOUBLE" property="tempCorrectDitt"/>
        <result column="temp_correct_rt" jdbcType="DOUBLE" property="tempCorrectRt"/>
        <result column="ruler_hole_value" jdbcType="DOUBLE" property="rulerHoleValue"/>
        <result column="display_value1" jdbcType="DOUBLE" property="displayValue1"/>
        <result column="display_value2" jdbcType="DOUBLE" property="displayValue2"/>
        <result column="display_value3" jdbcType="DOUBLE" property="displayValue3"/>
        <result column="display_value_avg" jdbcType="DOUBLE" property="displayValueAvg"/>
        <result column="modify_value" jdbcType="DOUBLE" property="modifyValue"/>
        <result column="convergence" jdbcType="DOUBLE" property="convergence"/>
        <result column="convergence_total" jdbcType="DOUBLE" property="convergenceTotal"/>
        <result column="convergence_rate" jdbcType="DOUBLE" property="convergenceRate"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stake_id, `time`, temp_correct_t, temp_correct_ditt, temp_correct_rt, ruler_hole_value,
        display_value1, display_value2, display_value3, display_value_avg, modify_value,
        convergence, convergence_total, convergence_rate, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_clearance_stake_day
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_clearance_stake_day
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStakeDay"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_clearance_stake_day (stake_id, `time`, temp_correct_t,
        temp_correct_ditt, temp_correct_rt, ruler_hole_value,
        display_value1, display_value2, display_value3,
        display_value_avg, modify_value, convergence,
        convergence_total, convergence_rate, del_flag,
        update_time, create_time)
        values (#{stakeId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, #{tempCorrectT,jdbcType=DOUBLE},
        #{tempCorrectDitt,jdbcType=DOUBLE}, #{tempCorrectRt,jdbcType=DOUBLE}, #{rulerHoleValue,jdbcType=DOUBLE},
        #{displayValue1,jdbcType=DOUBLE}, #{displayValue2,jdbcType=DOUBLE}, #{displayValue3,jdbcType=DOUBLE},
        #{displayValueAvg,jdbcType=DOUBLE}, #{modifyValue,jdbcType=DOUBLE}, #{convergence,jdbcType=DOUBLE},
        #{convergenceTotal,jdbcType=DOUBLE}, #{convergenceRate,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStakeDay" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_clearance_stake_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stakeId != null">
                stake_id,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="tempCorrectT != null">
                temp_correct_t,
            </if>
            <if test="tempCorrectDitt != null">
                temp_correct_ditt,
            </if>
            <if test="tempCorrectRt != null">
                temp_correct_rt,
            </if>
            <if test="rulerHoleValue != null">
                ruler_hole_value,
            </if>
            <if test="displayValue1 != null">
                display_value1,
            </if>
            <if test="displayValue2 != null">
                display_value2,
            </if>
            <if test="displayValue3 != null">
                display_value3,
            </if>
            <if test="displayValueAvg != null">
                display_value_avg,
            </if>
            <if test="modifyValue != null">
                modify_value,
            </if>
            <if test="convergence != null">
                convergence,
            </if>
            <if test="convergenceTotal != null">
                convergence_total,
            </if>
            <if test="convergenceRate != null">
                convergence_rate,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stakeId != null">
                #{stakeId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="tempCorrectT != null">
                #{tempCorrectT,jdbcType=DOUBLE},
            </if>
            <if test="tempCorrectDitt != null">
                #{tempCorrectDitt,jdbcType=DOUBLE},
            </if>
            <if test="tempCorrectRt != null">
                #{tempCorrectRt,jdbcType=DOUBLE},
            </if>
            <if test="rulerHoleValue != null">
                #{rulerHoleValue,jdbcType=DOUBLE},
            </if>
            <if test="displayValue1 != null">
                #{displayValue1,jdbcType=DOUBLE},
            </if>
            <if test="displayValue2 != null">
                #{displayValue2,jdbcType=DOUBLE},
            </if>
            <if test="displayValue3 != null">
                #{displayValue3,jdbcType=DOUBLE},
            </if>
            <if test="displayValueAvg != null">
                #{displayValueAvg,jdbcType=DOUBLE},
            </if>
            <if test="modifyValue != null">
                #{modifyValue,jdbcType=DOUBLE},
            </if>
            <if test="convergence != null">
                #{convergence,jdbcType=DOUBLE},
            </if>
            <if test="convergenceTotal != null">
                #{convergenceTotal,jdbcType=DOUBLE},
            </if>
            <if test="convergenceRate != null">
                #{convergenceRate,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStakeDay">
        <!--@mbg.generated-->
        update tunnel_clearance_stake_day
        <set>
            <if test="stakeId != null">
                stake_id = #{stakeId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="tempCorrectT != null">
                temp_correct_t = #{tempCorrectT,jdbcType=DOUBLE},
            </if>
            <if test="tempCorrectDitt != null">
                temp_correct_ditt = #{tempCorrectDitt,jdbcType=DOUBLE},
            </if>
            <if test="tempCorrectRt != null">
                temp_correct_rt = #{tempCorrectRt,jdbcType=DOUBLE},
            </if>
            <if test="rulerHoleValue != null">
                ruler_hole_value = #{rulerHoleValue,jdbcType=DOUBLE},
            </if>
            <if test="displayValue1 != null">
                display_value1 = #{displayValue1,jdbcType=DOUBLE},
            </if>
            <if test="displayValue2 != null">
                display_value2 = #{displayValue2,jdbcType=DOUBLE},
            </if>
            <if test="displayValue3 != null">
                display_value3 = #{displayValue3,jdbcType=DOUBLE},
            </if>
            <if test="displayValueAvg != null">
                display_value_avg = #{displayValueAvg,jdbcType=DOUBLE},
            </if>
            <if test="modifyValue != null">
                modify_value = #{modifyValue,jdbcType=DOUBLE},
            </if>
            <if test="convergence != null">
                convergence = #{convergence,jdbcType=DOUBLE},
            </if>
            <if test="convergenceTotal != null">
                convergence_total = #{convergenceTotal,jdbcType=DOUBLE},
            </if>
            <if test="convergenceRate != null">
                convergence_rate = #{convergenceRate,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelClearanceStakeDay">
        <!--@mbg.generated-->
        update tunnel_clearance_stake_day
        set stake_id = #{stakeId,jdbcType=INTEGER},
        `time` = #{time,jdbcType=TIMESTAMP},
        temp_correct_t = #{tempCorrectT,jdbcType=DOUBLE},
        temp_correct_ditt = #{tempCorrectDitt,jdbcType=DOUBLE},
        temp_correct_rt = #{tempCorrectRt,jdbcType=DOUBLE},
        ruler_hole_value = #{rulerHoleValue,jdbcType=DOUBLE},
        display_value1 = #{displayValue1,jdbcType=DOUBLE},
        display_value2 = #{displayValue2,jdbcType=DOUBLE},
        display_value3 = #{displayValue3,jdbcType=DOUBLE},
        display_value_avg = #{displayValueAvg,jdbcType=DOUBLE},
        modify_value = #{modifyValue,jdbcType=DOUBLE},
        convergence = #{convergence,jdbcType=DOUBLE},
        convergence_total = #{convergenceTotal,jdbcType=DOUBLE},
        convergence_rate = #{convergenceRate,jdbcType=DOUBLE},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByStakeId" resultType="com.whfc.uni.dto.tunnel.TunnelClearanceStakeDayDTO">
        select
        id,
        `time`,
        temp_correct_t,
        temp_correct_ditt,
        temp_correct_rt,
        ruler_hole_value,
        display_value1,
        display_value2,
        display_value3,
        display_value_avg,
        modify_value,
        convergence,
        convergence_total,
        convergence_rate
        from
        tunnel_clearance_stake_day
        where
        stake_id = #{stakeId}
        <if test="startTime != null and endTime != null">
            and `time` between #{startTime} and #{endTime}
        </if>
        and del_flag = 0
    </select>
    <delete id="deleteByStakeId">
        delete from tunnel_clearance_stake_day
        where stake_id = #{stakeId}
    </delete>
    <delete id="deleteByStakeIds">
        delete from tunnel_clearance_stake_day
        where stake_id in
        <foreach collection="stakeIds" item="stakeId" open="(" separator="," close=")">
            #{stakeId}
        </foreach>
    </delete>
    <insert id="insertAll">
        insert into tunnel_clearance_stake_day
        (stake_id,
        `time`,
        temp_correct_t,
        temp_correct_ditt,
        temp_correct_rt,
        ruler_hole_value,
        display_value1,
        display_value2,
        display_value3,
        display_value_avg,
        modify_value,
        convergence,
        convergence_total,
        convergence_rate)
        values
        <foreach collection="list" item="day" separator="," >
            (#{day.stakeId,jdbcType=INTEGER},
            #{day.time,jdbcType=TIMESTAMP},
            #{day.tempCorrectT,jdbcType=DOUBLE},
            #{day.tempCorrectDitt,jdbcType=DOUBLE},
            #{day.tempCorrectRt,jdbcType=DOUBLE},
            #{day.rulerHoleValue,jdbcType=DOUBLE},
            #{day.displayValue1,jdbcType=DOUBLE},
            #{day.displayValue2,jdbcType=DOUBLE},
            #{day.displayValue3,jdbcType=DOUBLE},
            #{day.displayValueAvg,jdbcType=DOUBLE},
            #{day.modifyValue,jdbcType=DOUBLE},
            #{day.convergence,jdbcType=DOUBLE},
            #{day.convergenceTotal,jdbcType=DOUBLE},
            #{day.convergenceRate,jdbcType=DOUBLE})
        </foreach>
    </insert>
</mapper>
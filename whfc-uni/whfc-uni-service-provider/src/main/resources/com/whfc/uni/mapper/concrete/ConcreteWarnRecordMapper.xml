<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteWarnRecordMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteWarnRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="rule_param" jdbcType="LONGVARCHAR" property="ruleParam" />
    <result column="rule_express" jdbcType="LONGVARCHAR" property="ruleExpress" />
    <result column="trigger_param" jdbcType="LONGVARCHAR" property="triggerParam" />
    <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime" />
    <result column="trigger_object_id" jdbcType="VARCHAR" property="triggerObjectId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handle_result" jdbcType="VARCHAR" property="handleResult" />
    <result column="handle_remark" jdbcType="VARCHAR" property="handleRemark" />
    <result column="handle_user_id" jdbcType="INTEGER" property="handleUserId" />
    <result column="handle_user_name" jdbcType="VARCHAR" property="handleUserName" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, rule_id, rule_type, rule_param, rule_express,
    trigger_param,trigger_time, trigger_object_id, state, handle_time,
    handle_result, handle_remark, handle_user_id, handle_user_name, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_warn_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_warn_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRecord">
    insert into concrete_warn_record (id, dept_id, rule_id, 
      rule_type, trigger_time, trigger_object_id, 
      state, handle_time, handle_result, 
      handle_remark, handle_user_id, handle_user_name, 
      del_flag, update_time, create_time, 
      rule_param, rule_express, trigger_param
      )
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{ruleId,jdbcType=INTEGER}, 
      #{ruleType,jdbcType=INTEGER}, #{triggerTime,jdbcType=TIMESTAMP}, #{triggerObjectId,jdbcType=VARCHAR}, 
      #{state,jdbcType=INTEGER}, #{handleTime,jdbcType=TIMESTAMP}, #{handleResult,jdbcType=VARCHAR}, 
      #{handleRemark,jdbcType=VARCHAR}, #{handleUserId,jdbcType=INTEGER}, #{handleUserName,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{ruleParam,jdbcType=LONGVARCHAR}, #{ruleExpress,jdbcType=LONGVARCHAR}, #{triggerParam,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRecord" useGeneratedKeys="true" keyProperty="id">
    insert into concrete_warn_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="triggerTime != null">
        trigger_time,
      </if>
      <if test="triggerObjectId != null">
        trigger_object_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="handleTime != null">
        handle_time,
      </if>
      <if test="handleResult != null">
        handle_result,
      </if>
      <if test="handleRemark != null">
        handle_remark,
      </if>
      <if test="handleUserId != null">
        handle_user_id,
      </if>
      <if test="handleUserName != null">
        handle_user_name,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="ruleParam != null">
        rule_param,
      </if>
      <if test="ruleExpress != null">
        rule_express,
      </if>
      <if test="triggerParam != null">
        trigger_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="triggerTime != null">
        #{triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerObjectId != null">
        #{triggerObjectId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleResult != null">
        #{handleResult,jdbcType=VARCHAR},
      </if>
      <if test="handleRemark != null">
        #{handleRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleUserId != null">
        #{handleUserId,jdbcType=INTEGER},
      </if>
      <if test="handleUserName != null">
        #{handleUserName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleParam != null">
        #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
      <if test="triggerParam != null">
        #{triggerParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRecord">
    update concrete_warn_record
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleParam != null">
        rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        rule_express = #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
      <if test="triggerParam != null">
        trigger_param = #{triggerParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="triggerTime != null">
        trigger_time = #{triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerObjectId != null">
        trigger_object_id = #{triggerObjectId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        handle_time = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleResult != null">
        handle_result = #{handleResult,jdbcType=VARCHAR},
      </if>
      <if test="handleRemark != null">
        handle_remark = #{handleRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleUserId != null">
        handle_user_id = #{handleUserId,jdbcType=INTEGER},
      </if>
      <if test="handleUserName != null">
        handle_user_name = #{handleUserName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleParam != null">
        rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        rule_express = #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
      <if test="triggerParam != null">
        trigger_param = #{triggerParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRecord">
    update concrete_warn_record
    set dept_id = #{deptId,jdbcType=INTEGER},
      rule_id = #{ruleId,jdbcType=INTEGER},
      rule_type = #{ruleType,jdbcType=INTEGER},
      trigger_time = #{triggerTime,jdbcType=TIMESTAMP},
      trigger_object_id = #{triggerObjectId,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      handle_time = #{handleTime,jdbcType=TIMESTAMP},
      handle_result = #{handleResult,jdbcType=VARCHAR},
      handle_remark = #{handleRemark,jdbcType=VARCHAR},
      handle_user_id = #{handleUserId,jdbcType=INTEGER},
      handle_user_name = #{handleUserName,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      rule_express = #{ruleExpress,jdbcType=LONGVARCHAR},
      trigger_param = #{triggerParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateState">
    update concrete_warn_record
    set state = 1,
    handle_time = now(),
    handle_result = #{handleResult},
    handle_remark = #{handleRemark},
    handle_user_id = #{userId},
    handle_user_name = #{userName}
    where 1 = 0
    <foreach collection="warnIdList" item="warnId">
      or id = #{warnId}
    </foreach>
  </update>

  <select id="selectWarnRuleTypeStat" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
    select rule_type,count(*) as warnNum
    from concrete_warn_record
    where dept_id = #{deptId,jdbcType=INTEGER}
    and del_flag = 0
    <if test="state != null">
      and state = #{state}
    </if>
    <if test="startTime!=null">
      and trigger_time >= #{startTime}
    </if>
    <if test="endTime!=null">
      and #{endTime} >= trigger_time
    </if>
    group by rule_type
  </select>

  <select id="selectWarnRecordList" resultType="com.whfc.uni.dto.concrete.ConcreteWarnRecordDTO">
    select id as warnId,
    rule_id,
    rule_type,
    rule_param,
    trigger_param,
    trigger_time,
    trigger_object_id,
    state,
    handle_time,
    handle_result,
    handle_remark,
    handle_user_id,
    handle_user_name
    from concrete_warn_record
    where dept_id = #{deptId}
    <if test="state != null">
      and state = #{state}
    </if>
    and del_flag = 0
    <if test="ruleType!=null">
      and rule_type = #{ruleType}
    </if>
    <if test="startTime!=null">
      and trigger_time >= #{startTime}
    </if>
    <if test="endTime!=null">
      and #{endTime} >= trigger_time
    </if>
    order by id desc
  </select>

  <select id="selectWarnRecordById" resultType="com.whfc.uni.dto.concrete.ConcreteWarnRecordDTO">
    select id as warnId,
           rule_id,
           rule_type,
           rule_param,
           trigger_param,
           trigger_time,
           trigger_object_id,
           state,
           handle_time,
           handle_result,
           handle_remark,
           handle_user_id,
           handle_user_name
    from concrete_warn_record
    where id = #{id}
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstProgressMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstProgress">
        <!--@mbg.generated-->
        <!--@Table const_progress-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="position_id" jdbcType="INTEGER" property="positionId"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="index" jdbcType="INTEGER" property="index"/>
        <result column="part_id" jdbcType="INTEGER" property="partId"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="total_qty" jdbcType="DOUBLE" property="totalQty"/>
        <result column="yesterday_done_qty" jdbcType="DOUBLE" property="yesterdayDoneQty"/>
        <result column="today_done_qty" jdbcType="DOUBLE" property="todayDoneQty"/>
        <result column="total_done_qty" jdbcType="DOUBLE" property="totalDoneQty"/>
        <result column="total_done_rate" jdbcType="DOUBLE" property="totalDoneRate"/>
        <result column="oper_user_id" jdbcType="INTEGER" property="operUserId"/>
        <result column="oper_username" jdbcType="VARCHAR" property="operUsername"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        `date`,
        dept_id,
        position_id,
        position_name,
        `index`,
        part_id,
        part_name,
        unit,
        total_qty,
        yesterday_done_qty,
        today_done_qty,
        total_done_qty,
        total_done_rate,
        oper_user_id,
        oper_username,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_progress
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.constlog.ConstProgress" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="date != null">
                `date`,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="positionId != null">
                position_id,
            </if>
            <if test="positionName != null">
                position_name,
            </if>
            <if test="index != null">
                `index`,
            </if>
            <if test="partId != null">
                part_id,
            </if>
            <if test="partName != null">
                part_name,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="totalQty != null">
                total_qty,
            </if>
            <if test="yesterdayDoneQty != null">
                yesterday_done_qty,
            </if>
            <if test="todayDoneQty != null">
                today_done_qty,
            </if>
            <if test="totalDoneQty != null">
                total_done_qty,
            </if>
            <if test="totalDoneRate != null">
                total_done_rate,
            </if>
            <if test="operUserId != null">
                oper_user_id,
            </if>
            <if test="operUsername != null">
                oper_username,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="positionId != null">
                #{positionId,jdbcType=INTEGER},
            </if>
            <if test="positionName != null">
                #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="index != null">
                #{index,jdbcType=INTEGER},
            </if>
            <if test="partId != null">
                #{partId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                #{partName,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="totalQty != null">
                #{totalQty,jdbcType=DOUBLE},
            </if>
            <if test="yesterdayDoneQty != null">
                #{yesterdayDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="todayDoneQty != null">
                #{todayDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="totalDoneQty != null">
                #{totalDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="totalDoneRate != null">
                #{totalDoneRate,jdbcType=DOUBLE},
            </if>
            <if test="operUserId != null">
                #{operUserId,jdbcType=INTEGER},
            </if>
            <if test="operUsername != null">
                #{operUsername,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstProgress">
        <!--@mbg.generated-->
        update const_progress
        <set>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="positionId != null">
                position_id = #{positionId,jdbcType=INTEGER},
            </if>
            <if test="positionName != null">
                position_name = #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="index != null">
                `index` = #{index,jdbcType=INTEGER},
            </if>
            <if test="partId != null">
                part_id = #{partId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                part_name = #{partName,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="totalQty != null">
                total_qty = #{totalQty,jdbcType=DOUBLE},
            </if>
            <if test="yesterdayDoneQty != null">
                yesterday_done_qty = #{yesterdayDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="todayDoneQty != null">
                today_done_qty = #{todayDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="totalDoneQty != null">
                total_done_qty = #{totalDoneQty,jdbcType=DOUBLE},
            </if>
            <if test="totalDoneRate != null">
                total_done_rate = #{totalDoneRate,jdbcType=DOUBLE},
            </if>
            <if test="operUserId != null">
                oper_user_id = #{operUserId,jdbcType=INTEGER},
            </if>
            <if test="operUsername != null">
                oper_username = #{operUsername,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_progress
        (`date`, dept_id, position_id, position_name, `index`, part_id, part_name, unit,
         total_qty, yesterday_done_qty, today_done_qty, total_done_qty, total_done_rate, oper_user_id, oper_username)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.date,jdbcType=DATE}, #{item.deptId,jdbcType=INTEGER}, #{item.positionId,jdbcType=INTEGER},
             #{item.positionName,jdbcType=VARCHAR}, #{item.index,jdbcType=INTEGER}, #{item.partId,jdbcType=INTEGER},
             #{item.partName,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.totalQty,jdbcType=DOUBLE},
             #{item.yesterdayDoneQty,jdbcType=DOUBLE}, #{item.todayDoneQty,jdbcType=DOUBLE},
             #{item.totalDoneQty,jdbcType=DOUBLE}, #{item.totalDoneRate,jdbcType=DOUBLE},
             #{item.operUserId,jdbcType=INTEGER}, #{item.operUsername,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="countList" resultType="int">
        SELECT COUNT(*)
        FROM const_progress
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND date = DATE(#{date})
    </select>

    <select id="selectList" resultType="com.whfc.uni.entity.constlog.ConstProgress">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_progress
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND date = DATE(#{date})
        <if test="positionId != null">
            AND position_id = #{positionId}
        </if>
        ORDER BY position_name, `index`
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_progress
        WHERE del_flag = 0
          AND date = DATE(#{date})
          AND id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPartIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_progress
        WHERE del_flag = 0
          AND date = DATE(#{date})
          AND part_id IN
        <foreach collection="partIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectDtoList" resultType="com.whfc.uni.dto.constlog.ConstProgressDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_progress
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND date = DATE(#{date})
        <if test="positionId != null">
            AND position_id = #{positionId}
        </if>
        ORDER BY position_name, `index`
    </select>

    <update id="batchDelete">
        UPDATE const_progress
        SET del_flag = 1
        WHERE id IN
        <foreach collection="progressIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateIndex">
        UPDATE const_progress
        SET `index` = #{index}
        WHERE id = #{id}
    </update>

    <update id="logicDel">
        UPDATE const_progress
        SET del_flag = 1
        WHERE id = #{id}
    </update>

    <select id="selectPositionList" resultType="com.whfc.uni.dto.constlog.ConstProgressDTO">
        SELECT position_id, position_name
        FROM const_progress
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND date = DATE(#{date})
        GROUP BY position_id
    </select>

    <select id="selectMaxIndex" resultType="java.lang.Integer">
        SELECT MAX(`index`)
        FROM const_progress
        WHERE del_flag = 0
          AND date = DATE(#{date})
          AND position_id = #{positionId}
    </select>
</mapper>
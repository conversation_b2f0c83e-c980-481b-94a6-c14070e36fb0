<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.panorama.PanoramaInfoMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.panorama.PanoramaInfo">
    <!--@mbg.generated-->
    <!--@Table panorama_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="panorama_name" jdbcType="VARCHAR" property="panoramaName" />
    <result column="panorama_url" jdbcType="VARCHAR" property="panoramaUrl" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, panorama_name, panorama_url, `desc`, cover_url, qr_code, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from panorama_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from panorama_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.panorama.PanoramaInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into panorama_info (dept_id, panorama_name, panorama_url, 
      `desc`, cover_url, qr_code, 
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{panoramaName,jdbcType=VARCHAR}, #{panoramaUrl,jdbcType=VARCHAR}, 
      #{desc,jdbcType=VARCHAR}, #{coverUrl,jdbcType=VARCHAR}, #{qrCode,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.panorama.PanoramaInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into panorama_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="panoramaName != null">
        panorama_name,
      </if>
      <if test="panoramaUrl != null">
        panorama_url,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="qrCode != null">
        qr_code,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="panoramaName != null">
        #{panoramaName,jdbcType=VARCHAR},
      </if>
      <if test="panoramaUrl != null">
        #{panoramaUrl,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.panorama.PanoramaInfo">
    <!--@mbg.generated-->
    update panorama_info
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="panoramaName != null">
        panorama_name = #{panoramaName,jdbcType=VARCHAR},
      </if>
      <if test="panoramaUrl != null">
        panorama_url = #{panoramaUrl,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        qr_code = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.panorama.PanoramaInfo">
    <!--@mbg.generated-->
    update panorama_info
    set dept_id = #{deptId,jdbcType=INTEGER},
      panorama_name = #{panoramaName,jdbcType=VARCHAR},
      panorama_url = #{panoramaUrl,jdbcType=VARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      cover_url = #{coverUrl,jdbcType=VARCHAR},
      qr_code = #{qrCode,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="logicDelById">
    UPDATE panorama_info SET del_flag = 1 WHERE id = #{panoramaId}
  </update>

  <select id="selectByDeptIdAndName" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM panorama_info
    WHERE del_flag = 0
    AND dept_id = #{deptId}
    AND panorama_name = #{panoramaName}
  </select>

  <select id="selectPanoramaList" resultType="com.whfc.uni.dto.panorama.PanoramaInfoDTO">
    SELECT
        id AS panoramaId,
        panorama_name,
        panorama_url,
        `desc`,
        cover_url,
        qr_code,
        update_time
    FROM panorama_info
    WHERE del_flag = 0
    AND dept_id = #{deptId}
    <if test="keyword != null and keyword != ''">
      AND panorama_name LIKE CONCAT('%',#{keyword},'%')
    </if>
    ORDER BY update_time DESC
  </select>

  <select id="selectByDeptIdAndPanoramaUrl" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM panorama_info
    WHERE del_flag = 0
    AND dept_id = #{deptId}
    AND panorama_url = #{panoramaUrl}
  </select>
</mapper>
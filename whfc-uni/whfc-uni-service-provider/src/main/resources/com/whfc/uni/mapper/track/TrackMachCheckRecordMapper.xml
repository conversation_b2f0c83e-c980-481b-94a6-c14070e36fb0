<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.track.TrackMachCheckRecordMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.track.TrackMachCheckRecord">
        <!--@mbg.generated-->
        <!--@Table track_mach_check_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="mach_id" jdbcType="INTEGER" property="machId"/>
        <result column="mach_code" jdbcType="VARCHAR" property="machCode"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="check_part" jdbcType="VARCHAR" property="checkPart"/>
        <result column="check_result" jdbcType="INTEGER" property="checkResult"/>
        <result column="check_remark" jdbcType="VARCHAR" property="checkRemark"/>
        <result column="check_user_id" jdbcType="INTEGER" property="checkUserId"/>
        <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        mach_id,
        mach_code,
        check_time,
        check_part,
        check_result,
        check_remark,
        check_user_id,
        check_user_name,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from track_mach_check_record
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.track.TrackMachCheckRecord" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into track_mach_check_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="machId != null">
                mach_id,
            </if>
            <if test="machCode != null">
                mach_code,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="checkPart != null">
                check_part,
            </if>
            <if test="checkResult != null">
                check_result,
            </if>
            <if test="checkRemark != null">
                check_remark,
            </if>
            <if test="checkUserId != null">
                check_user_id,
            </if>
            <if test="checkUserName != null">
                check_user_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="machId != null">
                #{machId,jdbcType=INTEGER},
            </if>
            <if test="machCode != null">
                #{machCode,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkPart != null">
                #{checkPart,jdbcType=VARCHAR},
            </if>
            <if test="checkResult != null">
                #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkRemark != null">
                #{checkRemark,jdbcType=VARCHAR},
            </if>
            <if test="checkUserId != null">
                #{checkUserId,jdbcType=INTEGER},
            </if>
            <if test="checkUserName != null">
                #{checkUserName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.track.TrackMachCheckRecord">
        <!--@mbg.generated-->
        update track_mach_check_record
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="machId != null">
                mach_id = #{machId,jdbcType=INTEGER},
            </if>
            <if test="machCode != null">
                mach_code = #{machCode,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkPart != null">
                check_part = #{checkPart,jdbcType=VARCHAR},
            </if>
            <if test="checkResult != null">
                check_result = #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkRemark != null">
                check_remark = #{checkRemark,jdbcType=VARCHAR},
            </if>
            <if test="checkUserId != null">
                check_user_id = #{checkUserId,jdbcType=INTEGER},
            </if>
            <if test="checkUserName != null">
                check_user_name = #{checkUserName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectList" resultType="com.whfc.uni.dto.track.TrackMachCheckDTO">
        select
            tmi.guid as machGuid,
            tmi.mach_name,
            tmi.mach_code,
            tmcr.*
        from track_mach_check_record tmcr left join track_mach_info tmi on tmcr.mach_id = tmi.id
        where tmcr.del_flag = 0
          and tmcr.dept_id = #{deptId,jdbcType=INTEGER}
        <if test="state != null">
            and tmcr.check_result = #{state,jdbcType=INTEGER}
        </if>
        <if test="keyword != null">
            and tmcr.mach_code like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        <if test="startTime != null">
            and tmcr.check_time between #{startTime} and #{endTime}
        </if>
        order by tmcr.create_time DESC
    </select>


    <select id="selectCheckRecord" resultType="com.whfc.uni.dto.track.TrackMachCheckDTO">
        select
        tmi.guid as machGuid,
        tmi.mach_name,
        tmi.mach_code,
        tmcr.*
        from track_mach_check_record tmcr left join track_mach_info tmi on tmcr.mach_id = tmi.id
        where tmcr.del_flag = 0
        and tmcr.id = #{checkId}
    </select>

    <select id="selectByMachId" resultType="com.whfc.uni.dto.track.TrackMachCheckDTO">
        select
        <include refid="Base_Column_List"/>
        from track_mach_check_record
        where del_flag = 0
          and mach_id = #{machId,jdbcType=INTEGER}
        order by create_time DESC
    </select>

    <update id="logicDeleteById">
        update track_mach_check_record
        set del_flag = 1
        where id = #{checkId,jdbcType=INTEGER}
    </update>

    <select id="selectLastByMachId" resultType="com.whfc.uni.dto.track.TrackMachCheckDTO">
        select
        <include refid="Base_Column_List"/>
        from track_mach_check_record
        where del_flag = 0
          and mach_id = #{machId,jdbcType=INTEGER}
        ordeR by create_time DESC
        limit 1
    </select>

    <select id="statByDay" resultType="com.whfc.uni.dto.track.TrackMachStatDTO">
        select date_format(check_time, '%Y-%m-%d') as date,
               count(1)                            as total
        from track_mach_check_record
        where del_flag = 0
          and dept_id = #{deptId,jdbcType=INTEGER}
          and check_time between #{startDate} and #{endDate}
        group by date_format(check_time, '%Y-%m-%d')
    </select>

    <select id="countCheckRecord" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
        SELECT dept_id,
               COUNT(id) warnNum
        FROM track_mach_check_record
        WHERE del_flag = 0
          AND check_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY dept_id
    </select>

</mapper>
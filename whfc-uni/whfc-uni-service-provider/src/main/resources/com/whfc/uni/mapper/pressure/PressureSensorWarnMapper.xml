<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.pressure.PressureSensorWarnMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.pressure.PressureSensorWarn">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="sensor_id" jdbcType="INTEGER" property="sensorId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="pressure" jdbcType="DOUBLE" property="pressure"/>
    <result column="battery" jdbcType="DOUBLE" property="battery"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, sensor_id, time, content, pressure,battery, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pressure_sensor_warn
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from pressure_sensor_warn
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.pressure.PressureSensorWarn">
    insert into pressure_sensor_warn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="sensorId != null">
        sensor_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="pressure != null">
        pressure,
      </if>
      <if test="battery != null">
        battery,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="sensorId != null">
        #{sensorId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="pressure != null">
        #{pressure},
      </if>
      <if test="battery != null">
        #{battery},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.pressure.PressureSensorWarn">
    update pressure_sensor_warn
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="sensorId != null">
        sensor_id = #{sensorId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="pressure != null">
        pressure = #{pressure,jdbcType=DOUBLE},
      </if>
      <if test="battery != null">
        battery = #{battery,jdbcType=DOUBLE},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectWarnListByDeptIdAndTime" resultType="com.whfc.uni.dto.pressure.PressureSensorWarnDTO">
    select sensor_id,`time`,content,pressure,battery
      from pressure_sensor_warn
     where dept_id = #{deptId}
       and `time` >= #{startTime}
       <![CDATA[
       and `time` <= #{endTime}
       ]]>
      order by id desc
  </select>

  <select id="selectWarnListBySensorIdAndTime" resultType="com.whfc.uni.dto.pressure.PressureSensorWarnDTO">
    select sensor_id,`time`,content,pressure,battery
    from pressure_sensor_warn
    where sensor_id = #{sensorId}
      and `time` >= #{startTime}
       <![CDATA[
      and `time` <= #{endTime}
       ]]>
      order by id desc
  </select>

  <select id="selectLastTime" resultType="java.util.Date">
    select `time`
      from pressure_sensor_warn
    where sensor_id = #{sensorId}
    order by `time` desc
    limit 1
  </select>

  <insert id="batchInsert">
    insert into pressure_sensor_warn
    (
        dept_id,
        sensor_id,
        `time`,
        content,
        pressure,
        battery
    )
    values
    <foreach collection="warnList" item="item" separator=",">
    (
        #{item.deptId},
        #{item.sensorId},
        #{item.time},
        #{item.content},
        #{item.pressure},
        #{item.battery}
    )
    </foreach>
  </insert>
</mapper>
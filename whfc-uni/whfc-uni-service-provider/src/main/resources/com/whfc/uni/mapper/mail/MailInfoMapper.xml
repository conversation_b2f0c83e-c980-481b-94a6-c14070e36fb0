<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.mail.MailInfoMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.mail.MailInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="mail_type_id" jdbcType="INTEGER" property="mailTypeId" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="sent_date" jdbcType="TIMESTAMP" property="sentDate" />
    <result column="received_date" jdbcType="TIMESTAMP" property="receivedDate" />
    <result column="from" jdbcType="VARCHAR" property="from" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
    <result column="archive" jdbcType="INTEGER" property="archive" />
    <result column="reply" jdbcType="INTEGER" property="reply" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    guid,
    mail_type_id,
    subject,
    sent_date,
    received_date,
    `from`,
    message_id,
    email,
    detail,
    archive,
    reply,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mail_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mail_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.mail.MailInfo" useGeneratedKeys="true" keyProperty="id">
    insert into mail_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="mailTypeId != null">
        mail_type_id,
      </if>
      <if test="subject != null">
        subject,
      </if>
      <if test="sentDate != null">
        sent_date,
      </if>
      <if test="receivedDate != null">
        received_date,
      </if>
      <if test="from != null">
        `from`,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="archive != null">
        archive,
      </if>
      <if test="reply != null">
        reply,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="mailTypeId != null">
        #{mailTypeId,jdbcType=INTEGER},
      </if>
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="sentDate != null">
        #{sentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receivedDate != null">
        #{receivedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="from != null">
        #{from,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="archive != null">
        #{archive,jdbcType=INTEGER},
      </if>
      <if test="reply != null">
        #{reply,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.mail.MailInfo">
    update mail_info
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="mailTypeId != null">
        mail_type_id = #{mailTypeId,jdbcType=INTEGER},
      </if>
      <if test="subject != null">
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="sentDate != null">
        sent_date = #{sentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receivedDate != null">
        received_date = #{receivedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="from != null">
        `from` = #{from,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="archive != null">
        archive = #{archive,jdbcType=INTEGER},
      </if>
      <if test="reply != null">
        reply = #{reply,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByMailTypeId" resultType="java.lang.Integer">
    select count(*)
      from mail_info
     where mail_type_id = #{mailTypeId}
       and del_flag = 0
  </select>

  <select id="countReByMailTypeId" resultType="java.lang.Integer">
    select count(*)
    from mail_info
    where mail_type_id = #{mailTypeId}
      and del_flag = 0
      and subject like concat('RE:','%')
  </select>

  <select id="selectByMailTypeId" resultType="com.whfc.uni.dto.mail.MailInfoDTO">
    select id as mailInfoId,
           guid,
           subject,
           sent_date,
           received_date,
           `from`,
           email,
           detail,
           archive
      from mail_info
     where mail_type_id = #{mailTypeId}
       and del_flag = 0
       <if test="keyword != null and keyword.length()>0">
         and (subject like concat('%', #{keyword}, '%')
              or `from` like concat('%', #{keyword}, '%'))
       </if>
      order by sent_date
  </select>

  <select id="selectFirstByMailTypeId" resultType="com.whfc.uni.dto.mail.MailInfoDTO">
    select id as mailInfoId,
           guid,
           subject,
           sent_date,
           received_date,
           `from`,
           email,
           detail,
           archive
      from mail_info
     where mail_type_id = #{mailTypeId}
       and del_flag = 0
       and subject not like concat('RE:','%')
       order by id
      limit 1
  </select>

  <select id="selectLastReByMailTypeId" resultType="com.whfc.uni.dto.mail.MailInfoDTO">
    select id as mailInfoId,
           guid,
           subject,
           sent_date,
           received_date,
           `from`,
           email,
           detail,
           archive
    from mail_info
    where mail_type_id = #{mailTypeId}
      and del_flag = 0
      and subject like concat('RE:','%')
    order by id desc
    limit 1
  </select>

  <select id="selectByGuid" resultType="com.whfc.uni.dto.mail.MailInfoDTO">
    select id as mailInfoId,
           mail_type_id,
           guid,
           subject,
           sent_date,
           received_date,
           `from`,
           email,
           detail
      from mail_info
     where guid = #{guid}
  </select>

  <select id="selectByMessageId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from mail_info
     where message_id = #{messageId}
       and del_flag = 0
  </select>

  <select id="selectMessageIdList" resultType="java.lang.String">
    select message_id
      from mail_info
     where dept_id = #{deptId}
       and del_flag = 0
  </select>

  <update id="updateMailTypeId">
    update mail_info
       set mail_type_id = #{mailTypeId}
     where id = #{mailInfoId}
  </update>

  <update id="updateMailArchive">
    update mail_info
       set archive = #{archive}
     where id = #{mailInfoId}
  </update>
</mapper>
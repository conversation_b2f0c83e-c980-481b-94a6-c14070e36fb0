<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tender.TenderComparisonBaseMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tender.TenderComparisonBase">
    <!--@mbg.generated-->
    <!--@Table tender_comparison_base-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="project_guid" jdbcType="VARCHAR" property="projectGuid" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="subcontract_no" jdbcType="VARCHAR" property="subcontractNo" />
    <result column="trade" jdbcType="VARCHAR" property="trade" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, dept_id, project_guid,project_name, `date`, subcontract_no, trade, currency, del_flag, update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tender_comparison_base
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tender_comparison_base
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderComparisonBase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_base (guid, dept_id, project_guid,project_name,
      `date`, subcontract_no, trade, currency,
      del_flag, update_time, create_time
      )
    values (#{guid,jdbcType=VARCHAR}, #{deptId,jdbcType=INTEGER}, #{projectGuid,jdbcType=VARCHAR},
      #{projectName,jdbcType=VARCHAR}, #{date,jdbcType=DATE}, #{subcontractNo,jdbcType=VARCHAR}, #{trade,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR},
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderComparisonBase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="projectGuid != null">
        project_guid,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="subcontractNo != null">
        subcontract_no,
      </if>
      <if test="trade != null">
        trade,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="projectGuid != null">
        #{projectGuid,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="subcontractNo != null">
        #{subcontractNo,jdbcType=VARCHAR},
      </if>
      <if test="trade != null">
        #{trade,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tender.TenderComparisonBase">
    <!--@mbg.generated-->
    update tender_comparison_base
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="projectGuid != null">
        project_guid = #{projectGuid,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="subcontractNo != null">
        subcontract_no = #{subcontractNo,jdbcType=VARCHAR},
      </if>
      <if test="trade != null">
        trade = #{trade,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tender.TenderComparisonBase">
    <!--@mbg.generated-->
    update tender_comparison_base
    set guid = #{guid,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=INTEGER},
      project_guid = #{projectGuid,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      `date` = #{date,jdbcType=DATE},
      subcontract_no = #{subcontractNo,jdbcType=VARCHAR},
      trade = #{trade,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_base
    (guid, dept_id, project_guid, project_name, `date`, subcontract_no, trade, currency, del_flag, update_time,
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.projectGuid,jdbcType=VARCHAR},
        #{item.projectName}, #{item.date,jdbcType=DATE}, #{item.subcontractNo,jdbcType=VARCHAR}, #{item.trade,jdbcType=VARCHAR}, #{item.currency,jdbcType=VARCHAR},
        #{item.delFlag,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <select id="selectList" resultType="com.whfc.uni.dto.tender.TenderComparisonBaseDTO">
    SELECT
        <include refid="Base_Column_List"/>
    FROM tender_comparison_base
    WHERE del_flag = 0 AND dept_id = #{deptId}
    <if test="projectGuid != null and projectGuid != ''">
      AND project_guid = #{projectGuid}
    </if>
    <if test="subcontractNo != null and subcontractNo != ''">
      AND subcontract_no LIKE CONCAT('%',#{subcontractNo},'%')
    </if>
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List"/>
    FROM tender_comparison_base
    WHERE del_flag = 0 AND guid = #{guid}
  </select>

  <update id="logicDel">
    UPDATE tender_comparison_base
    SET del_flag = 1
    WHERE guid = #{guid}
      AND del_flag = 0
  </update>

  <select id="selectByGuids" resultType="com.whfc.uni.dto.tender.TenderComparisonBaseDTO">
    SELECT
      <include refid="Base_Column_List"/>
    FROM tender_comparison_base
    WHERE del_flag = 0 AND guid IN
    <foreach collection="guids" item="guid" open="(" close=")" separator=",">
      #{guid}
    </foreach>
  </select>
</mapper>
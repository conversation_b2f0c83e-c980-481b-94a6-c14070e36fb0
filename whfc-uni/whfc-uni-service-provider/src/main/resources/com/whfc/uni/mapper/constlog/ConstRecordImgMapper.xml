<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstRecordImgMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstRecordImg">
    <!--@mbg.generated-->
    <!--@Table const_record_img-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="img_name" jdbcType="VARCHAR" property="imgName" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, record_id, img_url, img_name, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from const_record_img
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from const_record_img
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.constlog.ConstRecordImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into const_record_img (dept_id, record_id, img_url, img_name,
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{recordId,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR}, #{imgName,jdbcType=VARCHAR},
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.constlog.ConstRecordImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into const_record_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="imgName != null">
        img_name,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstRecordImg">
    <!--@mbg.generated-->
    update const_record_img
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="imgName != null">
        img_name = #{imgName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.constlog.ConstRecordImg">
    <!--@mbg.generated-->
    update const_record_img
    set dept_id = #{deptId,jdbcType=INTEGER},
      record_id = #{recordId,jdbcType=INTEGER},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      img_name = #{imgName,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <insert id="batchInsert">
        insert into const_record_img
        (
        dept_id,
        record_id,
        img_url,
        img_name
        )
        values
        <foreach collection="imgList" item="item" separator=",">
        (
          #{item.deptId,jdbcType=INTEGER},
          #{item.recordId,jdbcType=INTEGER},
          #{item.imgUrl,jdbcType=VARCHAR},
          #{item.imgName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <update id="deleteByRecordId">
        update const_record_img
        set del_flag = 1
        where record_id = #{recordId}
          and del_flag = 0
    </update>

    <select id="selectImgUrlByRecordId" resultType="java.lang.String">
        select img_url
        from const_record_img
        where record_id = #{recordId}
        and del_flag = 0
    </select>

  <select id="selectImgUrlByRecordIdList" resultType="com.whfc.uni.dto.constlog.ConstRecordImgDTO">
    select record_id, img_url, img_name
      from const_record_img
     where del_flag = 0
      and record_id in (
      <foreach collection="recordIdList" item="recordId" separator=",">
        #{recordId}
      </foreach>
      )
  </select>
</mapper>
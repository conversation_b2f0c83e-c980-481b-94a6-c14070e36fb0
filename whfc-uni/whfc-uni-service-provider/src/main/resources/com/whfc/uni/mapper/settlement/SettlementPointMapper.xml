<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.settlement.SettlementPointMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.settlement.SettlementPoint">
        <!--@mbg.generated-->
        <!--@Table settlement_point-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="pid" jdbcType="INTEGER" property="pid"/>
        <result column="pids" jdbcType="VARCHAR" property="pids"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, pid, pids, `name`, `type`, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from settlement_point
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from settlement_point
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.settlement.SettlementPoint"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into settlement_point (dept_id, pid, pids,
                                      `name`, `type`, del_flag,
                                      update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{pid,jdbcType=INTEGER}, #{pids,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.settlement.SettlementPoint"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into settlement_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="pids != null">
                pids,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=INTEGER},
            </if>
            <if test="pids != null">
                #{pids,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.settlement.SettlementPoint">
        <!--@mbg.generated-->
        update settlement_point
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=INTEGER},
            </if>
            <if test="pids != null">
                pids = #{pids,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.settlement.SettlementPoint">
        <!--@mbg.generated-->
        update settlement_point
        set dept_id     = #{deptId,jdbcType=INTEGER},
            pid         = #{pid,jdbcType=INTEGER},
            pids        = #{pids,jdbcType=VARCHAR},
            `name`      = #{name,jdbcType=VARCHAR},
            `type`      = #{type,jdbcType=VARCHAR},
            del_flag    = #{delFlag,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectPartList" resultType="com.whfc.uni.dto.settlement.SettlementPointDTO">
        SELECT id   AS partId,
               name AS partName
        FROM settlement_point
                WHERE del_flag = 0
                  AND type = 'PART'
                  AND dept_id = #{deptId}
        <if test="keyword != null">
            AND name LIKE CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <select id="selectPointList" resultType="com.whfc.uni.dto.settlement.SettlementPointDTO">
        SELECT id   AS pointId,
               name AS pointName
        FROM settlement_point
        WHERE del_flag = 0
          AND type = 'POINT'
          AND pid = #{partId}
        ORDER BY name
    </select>

    <select id="selectPartByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settlement_point
        WHERE del_flag = 0
          AND type = 'PART'
          AND dept_id = #{deptId}
          AND name = #{partName}
    </select>

    <select id="selectPointByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settlement_point
        WHERE del_flag = 0
          AND type = 'POINT'
          AND dept_id = #{deptId}
          AND name = #{pointName}
    </select>

    <select id="selectPart" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settlement_point
        WHERE del_flag = 0
          AND type = 'PART'
          AND id = #{partId}
    </select>

    <update id="logicDel">
        UPDATE settlement_point
        SET del_flag = 1
        WHERE type = #{type}
          AND id = #{id}
    </update>
</mapper>
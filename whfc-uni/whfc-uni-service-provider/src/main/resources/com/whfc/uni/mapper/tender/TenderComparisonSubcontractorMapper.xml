<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tender.TenderComparisonSubcontractorMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tender.TenderComparisonSubcontractor">
    <!--@mbg.generated-->
    <!--@Table tender_comparison_subcontractor-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="tender_guid" jdbcType="INTEGER" property="tenderGuid" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="subcontractor_guid" jdbcType="INTEGER" property="subcontractorGuid" />
    <result column="subcontractor_name" jdbcType="VARCHAR" property="subcontractorName" />
    <result column="total" jdbcType="DOUBLE" property="total" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, dept_id, tender_guid, seq, subcontractor_guid, subcontractor_name, total, del_flag,
    update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tender_comparison_subcontractor
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tender_comparison_subcontractor
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderComparisonSubcontractor" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_subcontractor (guid, dept_id, tender_guid,
      seq, subcontractor_guid, subcontractor_name,
      total, del_flag, update_time, 
      create_time)
    values (#{guid,jdbcType=VARCHAR}, #{deptId,jdbcType=INTEGER}, #{tenderGuid,jdbcType=VARCHAR},
      #{seq,jdbcType=INTEGER}, #{subcontractorGuid,jdbcType=VARCHAR}, #{subcontractorName,jdbcType=VARCHAR},
      #{total,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderComparisonSubcontractor" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_subcontractor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="tenderGuid != null">
        tender_guid,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="subcontractorGuid != null">
        subcontractor_guid,
      </if>
      <if test="subcontractorName != null">
        subcontractor_name,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tenderGuid != null">
        #{tenderGuid,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="subcontractorGuid != null">
        #{subcontractorGuid,jdbcType=VARCHAR},
      </if>
      <if test="subcontractorName != null">
        #{subcontractorName,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        #{total,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tender.TenderComparisonSubcontractor">
    <!--@mbg.generated-->
    update tender_comparison_subcontractor
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tenderGuid != null">
        tender_guid = #{tenderGuid,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="subcontractorGuid != null">
        subcontractor_guid = #{subcontractorGuid,jdbcType=VARCHAR},
      </if>
      <if test="subcontractorName != null">
        subcontractor_name = #{subcontractorName,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tender.TenderComparisonSubcontractor">
    <!--@mbg.generated-->
    update tender_comparison_subcontractor
    set guid = #{guid,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=INTEGER},
      tender_guid = #{tenderGuid,jdbcType=VARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      subcontractor_guid = #{subcontractorGuid,jdbcType=VARCHAR},
      subcontractor_name = #{subcontractorName,jdbcType=VARCHAR},
      total = #{total,jdbcType=DOUBLE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_comparison_subcontractor
    (guid, dept_id, tender_guid, seq, subcontractor_guid, subcontractor_name, total)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.tenderGuid,jdbcType=VARCHAR},
        #{item.seq,jdbcType=INTEGER}, #{item.subcontractorGuid,jdbcType=VARCHAR}, #{item.subcontractorName,jdbcType=VARCHAR},
        #{item.total,jdbcType=DOUBLE})
    </foreach>
  </insert>

  <update id="delByTenderGuid">
    UPDATE tender_comparison_subcontractor SET del_flag = 1 WHERE tender_guid = #{tenderGuid} AND del_flag = 0
  </update>

  <select id="selectByTenderGuids" resultType="com.whfc.uni.dto.tender.TenderComparisonSubcontractorDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_comparison_subcontractor
    WHERE tender_guid IN
    <foreach collection="baseIds" item="baseId" open="(" close=")" separator=",">
      #{baseId}
    </foreach>
    AND del_flag = 0
    ORDER BY seq
  </select>

  <update id="updateTotal">
    UPDATE tender_comparison_subcontractor
    SET total = #{totalValue} WHERE tender_guid = #{tenderGuid} AND seq = #{seq} AND del_flag = 0
  </update>
</mapper>
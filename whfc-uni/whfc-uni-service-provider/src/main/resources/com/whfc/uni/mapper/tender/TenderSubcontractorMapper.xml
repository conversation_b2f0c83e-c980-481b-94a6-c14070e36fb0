<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tender.TenderSubcontractorMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tender.TenderSubcontractor">
    <!--@mbg.generated-->
    <!--@Table tender_subcontractor-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="contact_person" jdbcType="VARCHAR" property="contactPerson" />
    <result column="office" jdbcType="VARCHAR" property="office" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, dept_id, `name`, contact_person, office, mobile, email, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tender_subcontractor
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tender_subcontractor
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderSubcontractor" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_subcontractor (guid, dept_id, `name`, 
      contact_person, office, mobile, 
      email, del_flag, update_time, 
      create_time)
    values (#{guid,jdbcType=VARCHAR}, #{deptId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{contactPerson,jdbcType=VARCHAR}, #{office,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderSubcontractor" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_subcontractor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="contactPerson != null">
        contact_person,
      </if>
      <if test="office != null">
        office,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="contactPerson != null">
        #{contactPerson,jdbcType=VARCHAR},
      </if>
      <if test="office != null">
        #{office,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tender.TenderSubcontractor">
    <!--@mbg.generated-->
    update tender_subcontractor
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="contactPerson != null">
        contact_person = #{contactPerson,jdbcType=VARCHAR},
      </if>
      <if test="office != null">
        office = #{office,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tender.TenderSubcontractor">
    <!--@mbg.generated-->
    update tender_subcontractor
    set guid = #{guid,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      contact_person = #{contactPerson,jdbcType=VARCHAR},
      office = #{office,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_subcontractor
    (guid, dept_id, `name`, contact_person, office, mobile, email, del_flag, update_time, 
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR}, 
        #{item.contactPerson,jdbcType=VARCHAR}, #{item.office,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, 
        #{item.email,jdbcType=VARCHAR}, #{item.delFlag,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectList" resultType="com.whfc.uni.dto.tender.TenderSubcontractorDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_subcontractor
    WHERE del_flag = 0 AND dept_id = #{deptId}
    <if test="subcontractorName != null and subcontractorName != ''">
      AND `name` LIKE CONCAT('%', #{subcontractorName}, '%')
    </if>
  </select>

  <select id="selectListOrderByName" resultType="com.whfc.uni.dto.tender.TenderSubcontractorDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_subcontractor
    WHERE del_flag = 0 AND dept_id = #{deptId}
    <if test="subcontractorName != null and subcontractorName != ''">
      AND `name` LIKE CONCAT('%', #{subcontractorName}, '%')
    </if>
    ORDER BY `name`
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM tender_subcontractor
    WHERE del_flag = 0 AND dept_id = #{deptId} AND `name` = #{subcontractorName}
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM tender_subcontractor
    WHERE del_flag = 0 AND guid = #{guid}
  </select>

  <update id="logicDel">
    UPDATE tender_subcontractor SET del_flag = 1 WHERE guid = #{guid}
  </update>
</mapper>
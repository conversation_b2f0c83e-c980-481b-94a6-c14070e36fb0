<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteItemDataLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteItemDataLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="concrete_id" jdbcType="INTEGER" property="concreteId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="data" jdbcType="LONGVARCHAR" property="data" />
    <result column="temp_diff" jdbcType="DOUBLE" property="tempDiff" />
    <result column="temp_over" jdbcType="INTEGER" property="tempOver" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, concrete_id, `time`, `data`, temp_diff, temp_over, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_item_data_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_item_data_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteItemDataLog">
    insert into concrete_item_data_log (
        id,
        concrete_id,
        time,
        data,
        temp_diff,
        temp_over,
        update_time,
        create_time
      )
    values (
        #{id,jdbcType=INTEGER},
        #{concreteId,jdbcType=INTEGER},
        #{time,jdbcType=TIMESTAMP},
        #{data},
        #{tempDiff,jdbcType=DOUBLE},
        #{tempOver,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItemDataLog">
    insert into concrete_item_data_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="concreteId != null">
        concrete_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="data != null">
        `data`,
      </if>
      <if test="tempDiff != null">
        temp_diff,
      </if>
      <if test="tempOver != null">
        temp_over,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="concreteId != null">
        #{concreteId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        #{data},
      </if>
      <if test="tempDiff != null">
        #{tempDiff,jdbcType=DOUBLE},
      </if>
      <if test="tempOver != null">
        #{tempOver,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItemDataLog">
    update concrete_item_data_log
    <set>
      <if test="concreteId != null">
        concrete_id = #{concreteId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        `data` = #{data},
      </if>
      <if test="tempDiff != null">
        temp_diff = #{tempDiff,jdbcType=DOUBLE},
      </if>
      <if test="tempOver != null">
        temp_over = #{tempOver,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteItemDataLog">
    update concrete_item_data_log
    set concrete_id = #{concreteId,jdbcType=INTEGER},
      `time` = #{time,jdbcType=TIMESTAMP},
      `data` = #{data},
      temp_diff = #{tempDiff,jdbcType=DOUBLE},
        temp_over = #{tempOver,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectItemDataLog" resultType="com.whfc.uni.dto.concrete.ConcreteItemDataDTO">
    select `time`,`data`
    from concrete_item_data_log
    where concrete_id = #{concreteId}
      <if test="startTime != null">
        and `time` >= #{startTime}
      </if>
      <if test="endTime != null">
      <![CDATA[
      and `time` <= #{endTime}
      ]]>
      </if>
      order by `time`
  </select>

  <select id="selectItemTempDiffList" resultType="com.whfc.uni.dto.concrete.ConcreteItemDataDTO">
    select `time`,temp_diff,temp_over
    from concrete_item_data_log
    where concrete_id = #{concreteId}
    order by `time`
  </select>

  <select id="countByConcreteIdAndTime" resultType="java.lang.Integer">
    select count(*)
    from concrete_item_data_log
    where concrete_id = #{concreteId}
      and `time` = #{time}
  </select>

  <update id="updateTempOver">
    update concrete_item_data_log
       set temp_over = #{tempOver}
      where concrete_id = #{concreteId}
        and `time` = #{time}
  </update>
</mapper>
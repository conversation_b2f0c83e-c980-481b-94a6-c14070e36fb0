<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelVaultStakeMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelVaultStake">
        <!--@mbg.generated-->
        <!--@Table tunnel_vault_stake-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="part_id" jdbcType="INTEGER" property="partId"/>
        <result column="stake_no" jdbcType="VARCHAR" property="stakeNo"/>
        <result column="instrument" jdbcType="VARCHAR" property="instrument"/>
        <result column="utility_method" jdbcType="VARCHAR" property="utilityMethod"/>
        <result column="benchmark_max" jdbcType="VARCHAR" property="benchmarkMax"/>
        <result column="point_time" jdbcType="TIMESTAMP" property="pointTime"/>
        <result column="check_start_time" jdbcType="TIMESTAMP" property="checkStartTime"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, part_id, stake_no, instrument, utility_method, benchmark_max, point_time, check_start_time,
        del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_vault_stake
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_vault_stake
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStake"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_vault_stake (part_id, stake_no, instrument,
        utility_method, benchmark_max, point_time,
        check_start_time, del_flag, update_time,
        create_time)
        values (#{partId,jdbcType=INTEGER}, #{stakeNo,jdbcType=VARCHAR}, #{instrument,jdbcType=VARCHAR},
        #{utilityMethod,jdbcType=VARCHAR}, #{benchmarkMax,jdbcType=VARCHAR}, #{pointTime,jdbcType=TIMESTAMP},
        #{checkStartTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStake"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_vault_stake
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partId != null">
                part_id,
            </if>
            <if test="stakeNo != null">
                stake_no,
            </if>
            <if test="instrument != null">
                instrument,
            </if>
            <if test="utilityMethod != null">
                utility_method,
            </if>
            <if test="benchmarkMax != null">
                benchmark_max,
            </if>
            <if test="pointTime != null">
                point_time,
            </if>
            <if test="checkStartTime != null">
                check_start_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partId != null">
                #{partId,jdbcType=INTEGER},
            </if>
            <if test="stakeNo != null">
                #{stakeNo,jdbcType=VARCHAR},
            </if>
            <if test="instrument != null">
                #{instrument,jdbcType=VARCHAR},
            </if>
            <if test="utilityMethod != null">
                #{utilityMethod,jdbcType=VARCHAR},
            </if>
            <if test="benchmarkMax != null">
                #{benchmarkMax,jdbcType=VARCHAR},
            </if>
            <if test="pointTime != null">
                #{pointTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStartTime != null">
                #{checkStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStake">
        <!--@mbg.generated-->
        update tunnel_vault_stake
        <set>
            <if test="partId != null">
                part_id = #{partId,jdbcType=INTEGER},
            </if>
            <if test="stakeNo != null">
                stake_no = #{stakeNo,jdbcType=VARCHAR},
            </if>
            <if test="instrument != null">
                instrument = #{instrument,jdbcType=VARCHAR},
            </if>
            <if test="utilityMethod != null">
                utility_method = #{utilityMethod,jdbcType=VARCHAR},
            </if>
            <if test="benchmarkMax != null">
                benchmark_max = #{benchmarkMax,jdbcType=VARCHAR},
            </if>
            <if test="pointTime != null">
                point_time = #{pointTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkStartTime != null">
                check_start_time = #{checkStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStake">
        <!--@mbg.generated-->
        update tunnel_vault_stake
        set part_id = #{partId,jdbcType=INTEGER},
        stake_no = #{stakeNo,jdbcType=VARCHAR},
        instrument = #{instrument,jdbcType=VARCHAR},
        utility_method = #{utilityMethod,jdbcType=VARCHAR},
        benchmark_max = #{benchmarkMax,jdbcType=VARCHAR},
        point_time = #{pointTime,jdbcType=TIMESTAMP},
        check_start_time = #{checkStartTime,jdbcType=TIMESTAMP},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPartId" resultType="com.whfc.uni.dto.tunnel.TunnelVaultStakeDTO">
        select
        id as stakeId,
        stake_no,
        instrument,
        utility_method,
        benchmark_max,
        point_time,
        check_start_time
	    from tunnel_vault_stake
	    where
        part_id = #{partId}
	    and del_flag=0
        order by create_time desc
    </select>
    <delete id="deleteByStakeIds">
       delete from tunnel_vault_stake
        where id in
        <foreach collection="stakeIds" item="stakeId" open="(" separator="," close=")">
            #{stakeId}
        </foreach>
    </delete>
</mapper>
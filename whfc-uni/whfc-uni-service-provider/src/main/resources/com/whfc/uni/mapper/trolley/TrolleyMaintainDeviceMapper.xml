<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.trolley.TrolleyMaintainDeviceMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.trolley.TrolleyMaintainDevice">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="net_state" jdbcType="INTEGER" property="netState" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    guid,
    `name`,
    platform,
    sn,
    `time`,
    net_state,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trolley_maintain_device
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from trolley_maintain_device
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.trolley.TrolleyMaintainDevice">
    insert into trolley_maintain_device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="netState != null">
        net_state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="netState != null">
        #{netState,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.trolley.TrolleyMaintainDevice">
    update trolley_maintain_device
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="netState != null">
        net_state = #{netState,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDeviceList" resultType="com.whfc.uni.dto.trolley.TrolleyMaintainDeviceDTO">
     select id as deviceId,
            dept_id,
            guid,
            name,
            platform,
            sn,
            `time`,
            net_state
    from trolley_maintain_device
    where dept_id = #{deptId,jdbcType=INTEGER}
      and del_flag = 0
    <if test="keyword != null and keyword != ''">
      and (name like concat('%',#{keyword},'%') or sn like concat('%',#{keyword},))
    </if>
  </select>

  <select id="selectByPlatformAndSn" resultType="com.whfc.uni.entity.trolley.TrolleyMaintainDevice">
    select
    <include refid="Base_Column_List" />
    from trolley_maintain_device
    where platform = #{platform,jdbcType=VARCHAR}
      and sn = #{sn,jdbcType=VARCHAR}
  </select>

  <select id="selectByGuid" resultType="com.whfc.uni.entity.trolley.TrolleyMaintainDevice">
    select
    <include refid="Base_Column_List" />
    from trolley_maintain_device
    where guid = #{guid,jdbcType=VARCHAR}
  </select>

  <update id="logicDeleteById">
    update trolley_maintain_device
    set del_flag = 1
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateOnline">
    update trolley_maintain_device
    set net_state = 1,
        `time` = #{time,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstPartMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstPart">
        <!--@mbg.generated-->
        <!--@Table const_part-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="pid" jdbcType="INTEGER" property="pid"/>
        <result column="unit_part_id" jdbcType="INTEGER" property="unitPartId"/>
        <result column="unit_part_name" jdbcType="VARCHAR" property="unitPartName"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="quantity" jdbcType="DOUBLE" property="quantity"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="status" javaType="INTEGER" property="status"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        guid,
        dept_id,
        pid,
        unit_part_id,
        unit_part_name,
        code,
        `name`,
        `type`,
        quantity,
        unit,
        status,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_part
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.constlog.ConstPart"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_part
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                guid,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="unitPartId != null">
                unit_part_id,
            </if>
            <if test="unitPartName != null">
                unit_part_name,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=INTEGER},
            </if>
            <if test="unitPartId != null">
                #{unitPartId,jdbcType=INTEGER},
            </if>
            <if test="unitPartName != null">
                #{unitPartName,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DOUBLE},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstPart">
        <!--@mbg.generated-->
        update const_part
        <set>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=INTEGER},
            </if>
            <if test="unitPartId != null">
                unit_part_id = #{unitPartId,jdbcType=INTEGER},
            </if>
            <if test="unitPartName != null">
                unit_part_name = #{unitPartName,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=DOUBLE},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_part
        (guid, dept_id, pid, unit_part_id, unit_part_name, code, `name`, `type`, quantity, unit)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.pid,jdbcType=INTEGER},
             #{item.unitPartId,jdbcType=INTEGER}, #{item.unitPartName,jdbcType=VARCHAR},
             #{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER},
             #{item.quantity,jdbcType=DOUBLE}, #{item.unit,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectList" resultType="com.whfc.uni.entity.constlog.ConstPart">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
        <if test="deptId != null">
            AND dept_id = #{deptId}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="unitPartId != null">
            AND unit_part_id = #{unitPartId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY (REPLACE(code, '.', '') * 1)
    </select>

    <select id="selectDtoList" resultType="com.whfc.uni.dto.constlog.ConstPartDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
        <if test="deptId != null">
            AND dept_id = #{deptId}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="unitPartId != null">
            AND unit_part_id = #{unitPartId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY (REPLACE(code, '.', '') * 1)
    </select>

    <select id="selectPartByCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND code = #{code}
    </select>

    <select id="selectByGuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
          AND guid = #{guid}
    </select>

    <update id="logicDel">
        UPDATE const_part
        SET del_flag = 1
        WHERE guid = #{guid}
    </update>

    <select id="selectByPid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
          AND pid = #{pid}
    </select>

    <select id="selectLikeCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND code LIKE CONCAT(#{code}, '%')
    </select>

    <select id="selectByNameAndUnitId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_part
        WHERE del_flag = 0
          AND name = #{partName}
          AND unit_part_id = #{unitId}
    </select>

    <select id="selectMaxCode" resultType="java.lang.Integer">
        SELECT MAX(CAST(SUBSTRING_INDEX(code, '.', -1) AS UNSIGNED))
        FROM const_part
        WHERE del_flag = 0
        AND type = 4
        AND unit_part_id = #{unitId};
    </select>
</mapper>
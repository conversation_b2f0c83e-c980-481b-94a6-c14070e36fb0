<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.energy.EnergyWaterMeterDayMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.energy.EnergyWaterMeterDay">
        <!--@mbg.generated-->
        <!--@Table energy_water_meter_day-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="water_id" jdbcType="INTEGER" property="waterId"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="total_dosage" jdbcType="DOUBLE" property="totalDosage"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, water_id, `time`, total_dosage, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from energy_water_meter_day
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from energy_water_meter_day
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterDay">
        <!--@mbg.generated-->
        insert into energy_water_meter_day (id, dept_id, water_id,
        `time`, total_dosage, del_flag,
        update_time, create_time)
        values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{waterId,jdbcType=INTEGER},
        #{time,jdbcType=TIMESTAMP}, #{totalDosage,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterDay">
        <!--@mbg.generated-->
        insert into energy_water_meter_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="waterId != null">
                water_id,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="totalDosage != null">
                total_dosage,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="waterId != null">
                #{waterId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="totalDosage != null">
                #{totalDosage,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterDay">
        <!--@mbg.generated-->
        update energy_water_meter_day
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="waterId != null">
                water_id = #{waterId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="totalDosage != null">
                total_dosage = #{totalDosage,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterDay">
        <!--@mbg.generated-->
        update energy_water_meter_day
        set dept_id = #{deptId,jdbcType=INTEGER},
        water_id = #{waterId,jdbcType=INTEGER},
        `time` = #{time,jdbcType=TIMESTAMP},
        total_dosage = #{totalDosage,jdbcType=DOUBLE},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectDeviceStatisticsByDeptId" resultType="com.whfc.uni.dto.energy.EnergyDeviceStatisticsDTO">
      SELECT ewm.name,
             round(ifnull(SUM(total_dosage),0),2) as num
      FROM energy_water_meter_day ewmd
	  INNER JOIN energy_water_meter ewm ON ewm.id = ewmd.water_id
      WHERE ewm.dept_id = #{deptId}
        <if test="startTime != null and endTime != null">
            and  `time` between #{startTime} and #{endTime}
        </if>
	    AND ewmd.del_flag = 0
	    AND ewm.del_flag =0
	  GROUP BY ewmd.water_id
    </select>

    <select id="selectDayStatisticsByDeptId" resultType="com.whfc.uni.dto.energy.EnergyDayStatisticsDTO">
        SELECT date_format(`time`, '%Y-%m') as `time`,
               round(ifnull(SUM(total_dosage),0),2) as num
	    FROM energy_water_meter_day
	    where dept_id = #{deptId}
        <if test="startTime != null and endTime != null">
            and  `time` between #{startTime} and #{endTime}
        </if>
        and del_flag = 0
        GROUP BY date_format(`time`, '%Y-%m')
    </select>

    <insert id="insertOrUpdate">
        insert into energy_water_meter_day
        (
            dept_id,
            water_id,
            `time`,
            total_dosage
        )
        values
        (
            #{deptId,jdbcType=INTEGER},
            #{waterId,jdbcType=INTEGER},
            #{time,jdbcType=TIMESTAMP},
            #{dosage,jdbcType=DOUBLE}
        )
        on duplicate key update
            total_dosage = #{dosage}
    </insert>

    <select id="selectTotalDosage" resultType="java.lang.Double">
        select round(ifnull(sum(wmd.total_dosage),0),1) as dosage
          from energy_water_meter_day wmd
          inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id = #{deptId}
          and wm.del_flag = 0
    </select>

    <select id="selectAreaDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatItem">
        select ifnull(wm.area_id,-1) as areaId,
               ifnull(wm.area_name,'未知') as areaName,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
          from energy_water_meter_day wmd
          inner join energy_water_meter wm on wmd.water_id = wm.id
          where wm.dept_id = #{deptId}
            and wm.del_flag = 0
          group by wm.area_id,wm.area_name
    </select>

    <select id="selectMonthDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatTime">
        select date_format(wmd.`time`,'%Y-%m') as `month`,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id = #{deptId}
          and wm.del_flag = 0
        group by date_format(wmd.`time`,'%Y-%m')
    </select>

    <select id="selectDateDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatTime">
        select date_format(wmd.`time`,'%Y-%m-%d') as `date`,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
                     inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id = #{deptId}
          and wm.del_flag = 0
        group by date_format(wmd.`time`,'%Y-%m-%d')
    </select>

    <select id="selectMonthAreaDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatTime">
        select date_format(wmd.`time`,'%Y-%m') as `month`,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id = #{deptId}
          and wm.area_id = #{areaId}
          and wm.del_flag = 0
        group by date_format(wmd.`time`,'%Y-%m')
    </select>

    <select id="selectEnterpriseTotalDosage" resultType="java.lang.Double">
        select round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        and wm.del_flag = 0
    </select>

    <select id="selectEnterpriseAreaDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatItem">
        select ifnull(wm.dept_id,-1) as deptId,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        and wm.del_flag = 0
        group by wm.dept_id
    </select>

    <select id="selectEnterpriseMonthDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatTime">
        select date_format(wmd.`time`,'%Y-%m') as `month`,
               round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        and wm.del_flag = 0
        group by date_format(wmd.`time`,'%Y-%m')
    </select>

    <select id="selectEnterpriseDateDosage" resultType="com.whfc.uni.dto.energy.EnergyDataStatTime">
        select date_format(wmd.`time`,'%Y-%m-%d') as `date`,
        round(ifnull(sum(wmd.total_dosage),0),1) as dosage
        from energy_water_meter_day wmd
        inner join energy_water_meter wm on wmd.water_id = wm.id
        where wm.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        and wm.del_flag = 0
        group by date_format(wmd.`time`,'%Y-%m-%d')
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.party.PartyMemberMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.party.PartyMember">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="nation" jdbcType="VARCHAR" property="nation" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="head_img" jdbcType="VARCHAR" property="headImg" />
    <result column="education" jdbcType="INTEGER" property="education" />
    <result column="degree" jdbcType="INTEGER" property="degree" />
    <result column="phone" jdbcType="VARCHAR" property="phone"/>
    <result column="party_branch" jdbcType="VARCHAR" property="partyBranch" />
    <result column="party_time_1" jdbcType="DATE" property="partyTime1" />
    <result column="party_time_2" jdbcType="DATE" property="partyTime2" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, name, gender, id_card_no, birthday, nation, address, head_img, education,
    degree,phone, party_branch, party_time_1, party_time_2, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from party_member
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from party_member
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.party.PartyMember">
    insert into party_member (id, dept_id, name,
      gender, id_card_no, birthday,
      nation, address, head_img,
      education, degree, phone,party_branch,
      party_time_1, party_time_2, del_flag,
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
      #{gender,jdbcType=INTEGER}, #{idCardNo,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE},
      #{nation,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR}, #{headImg,jdbcType=VARCHAR},
      #{education,jdbcType=INTEGER}, #{degree,jdbcType=INTEGER}, #{phone,jdbcType=VARCHAR},#{partyBranch,jdbcType=VARCHAR},
      #{partyTime1,jdbcType=DATE}, #{partyTime2,jdbcType=DATE}, #{delFlag,jdbcType=INTEGER},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.party.PartyMember">
    insert into party_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="idCardNo != null">
        id_card_no,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="nation != null">
        nation,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="headImg != null">
        head_img,
      </if>
      <if test="education != null">
        education,
      </if>
      <if test="degree != null">
        degree,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="partyBranch != null">
        party_branch,
      </if>
      <if test="partyTime1 != null">
        party_time_1,
      </if>
      <if test="partyTime2 != null">
        party_time_2,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="idCardNo != null">
        #{idCardNo,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="nation != null">
        #{nation,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="headImg != null">
        #{headImg,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        #{education,jdbcType=INTEGER},
      </if>
      <if test="degree != null">
        #{degree,jdbcType=INTEGER},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="partyBranch != null">
        #{partyBranch,jdbcType=VARCHAR},
      </if>
      <if test="partyTime1 != null">
        #{partyTime1,jdbcType=DATE},
      </if>
      <if test="partyTime2 != null">
        #{partyTime2,jdbcType=DATE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.party.PartyMember">
    update party_member
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="idCardNo != null">
        id_card_no = #{idCardNo,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="nation != null">
        nation = #{nation,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="headImg != null">
        head_img = #{headImg,jdbcType=VARCHAR},
      </if>
      <if test="education != null">
        education = #{education,jdbcType=INTEGER},
      </if>
      <if test="degree != null">
        degree = #{degree,jdbcType=INTEGER},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="partyBranch != null">
        party_branch = #{partyBranch,jdbcType=VARCHAR},
      </if>
      <if test="partyTime1 != null">
        party_time_1 = #{partyTime1,jdbcType=DATE},
      </if>
      <if test="partyTime2 != null">
        party_time_2 = #{partyTime2,jdbcType=DATE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.party.PartyMember">
    update party_member
    set dept_id = #{deptId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      id_card_no = #{idCardNo,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=DATE},
      nation = #{nation,jdbcType=INTEGER},
      address = #{address,jdbcType=VARCHAR},
      head_img = #{headImg,jdbcType=VARCHAR},
      education = #{education,jdbcType=INTEGER},
      degree = #{degree,jdbcType=INTEGER},
      phone = #{phone,jdbcType=VARCHAR},
      party_branch = #{partyBranch,jdbcType=VARCHAR},
      party_time_1 = #{partyTime1,jdbcType=DATE},
      party_time_2 = #{partyTime2,jdbcType=DATE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectMemberList" resultType="com.whfc.uni.dto.party.PartyMemberDTO">
    select id as memberId,
           name,
           gender,
           id_card_no,
           birthday,
           address,
           nation,
           head_img,
           education,
           degree,
           phone,
           party_branch,
           party_time_1,
           party_time_2
      from party_member
      where dept_id = #{deptId,jdbcType=INTEGER}
        and del_flag = 0
      <if test="keyword !=null and keyword.length>0">
        and name like concat('%',#{keyword},'%')
      </if>
      order by id desc
  </select>

  <update id="logicDeleteById">
    update party_member
       set del_flag = 1
     where id = #{memberId,jdbcType=INTEGER}
       and del_flag = 0
  </update>
</mapper>
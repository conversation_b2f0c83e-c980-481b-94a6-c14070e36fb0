<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstRecordMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstRecord">
        <!--@mbg.generated-->
        <!--@Table const_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="part_id" jdbcType="INTEGER" property="partId"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="plan" jdbcType="VARCHAR" property="plan"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="material_arrival" jdbcType="VARCHAR" property="materialArrival"/>
        <result column="const_acceptance" jdbcType="VARCHAR" property="constAcceptance"/>
        <result column="safety_followup" jdbcType="VARCHAR" property="safetyFollowup"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        part_id,
        part_name,
        content,
        remark,
        plan,
        `time`,
        material_arrival,
        const_acceptance,
        safety_followup,
        user_id,
        user_name,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_record
        where id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.constlog.ConstRecord" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into const_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="partId != null">
                part_id,
            </if>
            <if test="partName != null">
                part_name,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="plan != null">
                plan,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="materialArrival != null">
                material_arrival,
            </if>
            <if test="constAcceptance != null">
                const_acceptance,
            </if>
            <if test="safetyFollowup != null">
                safety_followup,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="partId != null">
                #{partId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                #{partName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="plan != null">
                #{plan,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="materialArrival != null">
                #{materialArrival,jdbcType=VARCHAR},
            </if>
            <if test="constAcceptance != null">
                #{constAcceptance,jdbcType=VARCHAR},
            </if>
            <if test="safetyFollowup != null">
                #{safetyFollowup,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstRecord">
        <!--@mbg.generated-->
        update const_record
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="partId != null">
                part_id = #{partId,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                part_name = #{partName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="plan != null">
                plan = #{plan,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="materialArrival != null">
                material_arrival = #{materialArrival,jdbcType=VARCHAR},
            </if>
            <if test="constAcceptance != null">
                const_acceptance = #{constAcceptance,jdbcType=VARCHAR},
            </if>
            <if test="safetyFollowup != null">
                safety_followup = #{safetyFollowup,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.constlog.ConstRecord">
        <!--@mbg.generated-->
        update const_record
        set dept_id          = #{deptId,jdbcType=INTEGER},
            part_id          = #{partId,jdbcType=INTEGER},
            part_name        = #{partName,jdbcType=VARCHAR},
            content          = #{content,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            `time`           = #{time,jdbcType=TIMESTAMP},
            material_arrival = #{materialArrival,jdbcType=VARCHAR},
            const_acceptance = #{constAcceptance,jdbcType=VARCHAR},
            safety_followup  = #{safetyFollowup,jdbcType=VARCHAR},
            user_id          = #{userId,jdbcType=INTEGER},
            user_name        = #{userName,jdbcType=VARCHAR},
            del_flag         = #{delFlag,jdbcType=INTEGER},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            create_time      = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="deleteById">
        update const_record
        set del_flag = 1
        where id = #{recordId}
          and del_flag = 0
    </update>

    <select id="selectList" resultType="com.whfc.uni.dto.constlog.ConstRecordDTO">
        select id     as recordId,
               part_id,
               part_name,
               content,
               remark,
               plan,
               material_arrival,
               const_acceptance,
               safety_followup,
               user_id,
               user_name,
               `time` as create_time
        from const_record
        where dept_id = #{deptId}
          AND DATE(time) = DATE(#{date})
        <if test="positionId != null">
            and part_id = #{positionId}
        </if>
        and del_flag = 0
        order by id desc
    </select>

    <select id="selectByPosition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM const_record
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND DATE(time) = DATE(#{time})
          AND part_id = #{partId}
    </select>

    <select id="selectPostionList" resultType="com.whfc.uni.dto.constlog.ConstProgressDTO">
        SELECT part_id,
            part_name
        FROM const_record
        WHERE del_flag = 0
          AND dept_id = #{deptId}
          AND DATE(time) = DATE(#{date})
        GROUP BY part_id
    </select>

    <select id="selectRecordDays" resultType="java.lang.String">
        SELECT DISTINCT DATE_FORMAT(`time`, '%Y-%m-%d') as date
        FROM const_record
        WHERE del_flag = 0
          AND dept_id = #{deptId}
    </select>
</mapper>
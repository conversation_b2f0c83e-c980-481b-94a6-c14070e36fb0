<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tender.TenderComparisonItemMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tender.TenderComparisonItem">
        <!--@mbg.generated-->
        <!--@Table tender_comparison_item-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="tender_guid" jdbcType="VARCHAR" property="tenderGuid"/>
        <result column="item" jdbcType="VARCHAR" property="item"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="description_lower" jdbcType="VARCHAR" property="descriptionLower"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="qty" jdbcType="DOUBLE" property="qty"/>
        <result column="budget_rate" jdbcType="DOUBLE" property="budgetRate"/>
        <result column="budget_value" jdbcType="DOUBLE" property="budgetValue"/>
        <result column="tender_rate_1" jdbcType="DOUBLE" property="tenderRate1"/>
        <result column="tender_value_1" jdbcType="DOUBLE" property="tenderValue1"/>
        <result column="tender_rate_2" jdbcType="DOUBLE" property="tenderRate2"/>
        <result column="tender_value_2" jdbcType="DOUBLE" property="tenderValue2"/>
        <result column="tender_rate_3" jdbcType="DOUBLE" property="tenderRate3"/>
        <result column="tender_value_3" jdbcType="DOUBLE" property="tenderValue3"/>
        <result column="tender_rate_4" jdbcType="DOUBLE" property="tenderRate4"/>
        <result column="tender_value_4" jdbcType="DOUBLE" property="tenderValue4"/>
        <result column="tender_rate_5" jdbcType="DOUBLE" property="tenderRate5"/>
        <result column="tender_value_5" jdbcType="DOUBLE" property="tenderValue5"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        guid,
        dept_id,
        tender_guid,
        item,
        description,
        description_lower,
        `type`,
        unit,
        qty,
        budget_rate,
        budget_value,
        tender_rate_1,
        tender_value_1,
        tender_rate_2,
        tender_value_2,
        tender_rate_3,
        tender_value_3,
        tender_rate_4,
        tender_value_4,
        tender_rate_5,
        tender_value_5,
        del_flag,
        update_time,
        create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tender_comparison_item
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from tender_comparison_item
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderComparisonItem"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tender_comparison_item (guid, dept_id, tender_guid,
                                            item, description, description_lower, `type`,
                                            unit, qty,
                                            budget_rate, budget_value, tender_rate_1,
                                            tender_value_1, tender_rate_2, tender_value_2,
                                            tender_rate_3, tender_value_3, tender_rate_4,
                                            tender_value_4, tender_rate_5, tender_value_5,
                                            del_flag, update_time, create_time)
        values (#{guid,jdbcType=VARCHAR}, #{deptId,jdbcType=INTEGER}, #{tenderGuid,jdbcType=VARCHAR},
                #{item,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{descriptionLower,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{qty,jdbcType=DOUBLE},
                #{budgetRate,jdbcType=DOUBLE}, #{budgetValue,jdbcType=DOUBLE}, #{tenderRate1,jdbcType=DOUBLE},
                #{tenderValue1,jdbcType=DOUBLE}, #{tenderRate2,jdbcType=DOUBLE}, #{tenderValue2,jdbcType=DOUBLE},
                #{tenderRate3,jdbcType=DOUBLE}, #{tenderValue3,jdbcType=DOUBLE}, #{tenderRate4,jdbcType=DOUBLE},
                #{tenderValue4,jdbcType=DOUBLE}, #{tenderRate5,jdbcType=DOUBLE}, #{tenderValue5,jdbcType=DOUBLE},
                #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.tender.TenderComparisonItem" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tender_comparison_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                guid,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="tenderGuid != null">
                tender_guid,
            </if>
            <if test="item != null">
                item,
            </if>
            <if test="description != null">
                description,
                description_lower,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="qty != null">
                qty,
            </if>
            <if test="budgetRate != null">
                budget_rate,
            </if>
            <if test="budgetValue != null">
                budget_value,
            </if>
            <if test="tenderRate1 != null">
                tender_rate_1,
            </if>
            <if test="tenderValue1 != null">
                tender_value_1,
            </if>
            <if test="tenderRate2 != null">
                tender_rate_2,
            </if>
            <if test="tenderValue2 != null">
                tender_value_2,
            </if>
            <if test="tenderRate3 != null">
                tender_rate_3,
            </if>
            <if test="tenderValue3 != null">
                tender_value_3,
            </if>
            <if test="tenderRate4 != null">
                tender_rate_4,
            </if>
            <if test="tenderValue4 != null">
                tender_value_4,
            </if>
            <if test="tenderRate5 != null">
                tender_rate_5,
            </if>
            <if test="tenderValue5 != null">
                tender_value_5,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="tenderGuid != null">
                #{tenderGuid,jdbcType=VARCHAR},
            </if>
            <if test="item != null">
                #{item,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
                LOWER(#{description,jdbcType=VARCHAR}),
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="qty != null">
                #{qty,jdbcType=DOUBLE},
            </if>
            <if test="budgetRate != null">
                #{budgetRate,jdbcType=DOUBLE},
            </if>
            <if test="budgetValue != null">
                #{budgetValue,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate1 != null">
                #{tenderRate1,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue1 != null">
                #{tenderValue1,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate2 != null">
                #{tenderRate2,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue2 != null">
                #{tenderValue2,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate3 != null">
                #{tenderRate3,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue3 != null">
                #{tenderValue3,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate4 != null">
                #{tenderRate4,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue4 != null">
                #{tenderValue4,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate5 != null">
                #{tenderRate5,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue5 != null">
                #{tenderValue5,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tender.TenderComparisonItem">
        <!--@mbg.generated-->
        update tender_comparison_item
        <set>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="tenderGuid != null">
                tender_guid = #{tenderGuid,jdbcType=VARCHAR},
            </if>
            <if test="item != null">
                item = #{item,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description       = #{description,jdbcType=VARCHAR},
                description_lower = LOWER(#{description,jdbcType=VARCHAR}),
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="qty != null">
                qty = #{qty,jdbcType=DOUBLE},
            </if>
            <if test="budgetRate != null">
                budget_rate = #{budgetRate,jdbcType=DOUBLE},
            </if>
            <if test="budgetValue != null">
                budget_value = #{budgetValue,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate1 != null">
                tender_rate_1 = #{tenderRate1,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue1 != null">
                tender_value_1 = #{tenderValue1,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate2 != null">
                tender_rate_2 = #{tenderRate2,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue2 != null">
                tender_value_2 = #{tenderValue2,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate3 != null">
                tender_rate_3 = #{tenderRate3,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue3 != null">
                tender_value_3 = #{tenderValue3,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate4 != null">
                tender_rate_4 = #{tenderRate4,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue4 != null">
                tender_value_4 = #{tenderValue4,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate5 != null">
                tender_rate_5 = #{tenderRate5,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue5 != null">
                tender_value_5 = #{tenderValue5,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tender.TenderComparisonItem">
        <!--@mbg.generated-->
        update tender_comparison_item
        set guid              = #{guid,jdbcType=VARCHAR},
            dept_id           = #{deptId,jdbcType=INTEGER},
            tender_guid       = #{tenderGuid,jdbcType=VARCHAR},
            item              = #{item,jdbcType=VARCHAR},
            description       = #{description,jdbcType=VARCHAR},
            description_lower = LOWER(#{description,jdbcType=VARCHAR}),
            `type`            = #{type,jdbcType=VARCHAR},
            unit              = #{unit,jdbcType=VARCHAR},
            qty               = #{qty,jdbcType=DOUBLE},
            budget_rate       = #{budgetRate,jdbcType=DOUBLE},
            budget_value      = #{budgetValue,jdbcType=DOUBLE},
            tender_rate_1     = #{tenderRate1,jdbcType=DOUBLE},
            tender_value_1    = #{tenderValue1,jdbcType=DOUBLE},
            tender_rate_2     = #{tenderRate2,jdbcType=DOUBLE},
            tender_value_2    = #{tenderValue2,jdbcType=DOUBLE},
            tender_rate_3     = #{tenderRate3,jdbcType=DOUBLE},
            tender_value_3    = #{tenderValue3,jdbcType=DOUBLE},
            tender_rate_4     = #{tenderRate4,jdbcType=DOUBLE},
            tender_value_4    = #{tenderValue4,jdbcType=DOUBLE},
            tender_rate_5     = #{tenderRate5,jdbcType=DOUBLE},
            tender_value_5    = #{tenderValue5,jdbcType=DOUBLE},
            del_flag          = #{delFlag,jdbcType=INTEGER},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            create_time       = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tender_comparison_item
        (guid, dept_id, tender_guid, item, description, description_lower, `type`, unit, qty, budget_rate,
         budget_value, tender_rate_1, tender_value_1, tender_rate_2, tender_value_2, tender_rate_3,
         tender_value_3, tender_rate_4, tender_value_4, tender_rate_5, tender_value_5)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.tenderGuid,jdbcType=VARCHAR},
             #{item.item,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, LOWER(#{item.description}),
             #{item.type,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.qty,jdbcType=DOUBLE},
             #{item.budgetRate,jdbcType=DOUBLE}, #{item.budgetValue,jdbcType=DOUBLE},
             #{item.tenderRate1,jdbcType=DOUBLE},
             #{item.tenderValue1,jdbcType=DOUBLE}, #{item.tenderRate2,jdbcType=DOUBLE},
             #{item.tenderValue2,jdbcType=DOUBLE},
             #{item.tenderRate3,jdbcType=DOUBLE}, #{item.tenderValue3,jdbcType=DOUBLE},
             #{item.tenderRate4,jdbcType=DOUBLE},
             #{item.tenderValue4,jdbcType=DOUBLE}, #{item.tenderRate5,jdbcType=DOUBLE},
             #{item.tenderValue5,jdbcType=DOUBLE})
        </foreach>
    </insert>

    <select id="selectExistsTenderGuids" resultType="java.lang.String">
        SELECT DISTINCT tender_guid
        FROM tender_comparison_item WHERE del_flag = 0
                                      AND tender_guid IN
        <foreach collection="tenderGuidList" item="tenderGuid" open="(" close=")" separator=",">
            #{tenderGuid}
        </foreach>
    </select>

    <update id="delByTenderGuid">
        UPDATE tender_comparison_item
        SET del_flag = 1
        WHERE tender_guid = #{tenderGuid}
    </update>

    <update id="updateByGuid" parameterType="com.whfc.uni.entity.tender.TenderComparisonItem">
        <!--@mbg.generated-->
        update tender_comparison_item
        <set>
            <if test="item != null">
                item = #{item,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
                description_lower = LOWER(#{description,jdbcType=VARCHAR}),
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="qty != null">
                qty = #{qty,jdbcType=DOUBLE},
            </if>
            <if test="budgetRate != null">
                budget_rate = #{budgetRate,jdbcType=DOUBLE},
            </if>
            <if test="budgetValue != null">
                budget_value = #{budgetValue,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate1 != null">
                tender_rate_1 = #{tenderRate1,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue1 != null">
                tender_value_1 = #{tenderValue1,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate2 != null">
                tender_rate_2 = #{tenderRate2,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue2 != null">
                tender_value_2 = #{tenderValue2,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate3 != null">
                tender_rate_3 = #{tenderRate3,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue3 != null">
                tender_value_3 = #{tenderValue3,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate4 != null">
                tender_rate_4 = #{tenderRate4,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue4 != null">
                tender_value_4 = #{tenderValue4,jdbcType=DOUBLE},
            </if>
            <if test="tenderRate5 != null">
                tender_rate_5 = #{tenderRate5,jdbcType=DOUBLE},
            </if>
            <if test="tenderValue5 != null">
                tender_value_5 = #{tenderValue5,jdbcType=DOUBLE},
            </if>
        </set>
        where guid = #{guid,jdbcType=VARCHAR}
          AND del_flag = 0
    </update>

    <select id="selectByTenderGuid" resultType="com.whfc.uni.dto.tender.TenderComparisonItemDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tender_comparison_item
        WHERE del_flag = 0
          AND tender_guid = #{tenderGuid}
    </select>

    <select id="searchItems" resultType="com.whfc.uni.dto.tender.TenderComparisonItemDTO">
        SELECT tci.guid,
               tci.dept_id,
               tci.tender_guid,
               tci.item,
               tci.description,
               tci.`type`,
               tci.unit,
               tci.qty,
               tci.budget_rate,
               tci.budget_value,
               tci.tender_rate_1,
               tci.tender_value_1,
               tci.tender_rate_2,
               tci.tender_value_2,
               tci.tender_rate_3,
               tci.tender_value_3,
               tci.tender_rate_4,
               tci.tender_value_4,
               tci.tender_rate_5,
               tci.tender_value_5,
               tcb.project_guid,
               tcb.project_name,
               tcb.date,
               tcb.subcontract_no,
               tcb.trade,
               tcb.currency
        FROM tender_comparison_item tci
                 LEFT JOIN tender_comparison_base tcb ON tcb.guid = tci.tender_guid
                 LEFT JOIN tender_project tp ON tp.guid = tcb.project_guid
        WHERE tci.del_flag = 0
        AND tci.dept_id = #{searchParam.deptId}
        <if test="searchParam.projectGuid != null and searchParam.projectGuid != ''">
            AND tp.guid = #{searchParam.projectGuid}
        </if>
        <if test="searchParam.keyword1 != null and searchParam.keyword1 != ''">
            AND tci.description_lower LIKE CONCAT('%', LOWER(#{searchParam.keyword1}), '%')
        </if>
        <if test="searchParam.keyword2 != null and searchParam.keyword2 != ''">
            AND description_lower LIKE CONCAT('%', LOWER(#{searchParam.keyword2}), '%')
        </if>
        <if test="searchParam.keyword3 != null and searchParam.keyword3 != ''">
            AND description_lower LIKE CONCAT('%', LOWER(#{searchParam.keyword3}), '%')
        </if>
        <if test="searchParam.keyword4 != null and searchParam.keyword4 != ''">
            AND description_lower LIKE CONCAT('%', LOWER(#{searchParam.keyword4}), '%')
        </if>
        <if test="searchParam.market != null and searchParam.market != ''">
            AND market = #{searchParam.market}
        </if>
        <if test="searchParam.country != null and searchParam.country != ''">
            AND country = #{searchParam.country}
        </if>
        <choose>
            <when test="searchParam.orderByRate != null and searchParam.orderByValue != null">
                ORDER BY tender_rate_${searchParam.orderByRate}
                <if test="param1.orderByType == 1">
                    DESC
                </if>
            </when>
            <when test="searchParam.orderByRate != null">
                ORDER BY tender_rate_${searchParam.orderByRate}
                <if test="param1.orderByType == 1">
                    DESC
                </if>
            </when>
            <when test="searchParam.orderByValue != null">
                ORDER BY tender_value_${searchParam.orderByValue}
                <if test="param1.orderByType == 1">
                    DESC
                </if>
            </when>
        </choose>
    </select>

    <select id="countTotalValue1BySeq1" resultType="java.lang.Double">
        SELECT SUM(tender_value_1)
        FROM tender_comparison_item
        WHERE del_flag = 0
          AND tender_guid = #{tenderGuid}
    </select>

    <select id="countTotalValue1BySeq2" resultType="java.lang.Double">
        SELECT SUM(tender_value_2)
        FROM tender_comparison_item
        WHERE del_flag = 0
          AND tender_guid = #{tenderGuid}
    </select>

    <select id="countTotalValue1BySeq3" resultType="java.lang.Double">
        SELECT SUM(tender_value_3)
        FROM tender_comparison_item
        WHERE del_flag = 0
          AND tender_guid = #{tenderGuid}
    </select>

    <select id="countTotalValue1BySeq4" resultType="java.lang.Double">
        SELECT SUM(tender_value_4)
        FROM tender_comparison_item
        WHERE del_flag = 0
          AND tender_guid = #{tenderGuid}
    </select>
</mapper>
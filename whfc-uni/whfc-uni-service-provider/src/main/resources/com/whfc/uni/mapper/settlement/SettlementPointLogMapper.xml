<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.settlement.SettlementPointLogMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.settlement.SettlementPointLog">
        <!--@mbg.generated-->
        <!--@Table settlement_point_log-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="point_id" jdbcType="INTEGER" property="pointId"/>
        <result column="no" jdbcType="VARCHAR" property="no"/>
        <result column="x" jdbcType="DOUBLE" property="x"/>
        <result column="y" jdbcType="DOUBLE" property="y"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, point_id, no, x, y, `time`, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from settlement_point_log
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from settlement_point_log
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.settlement.SettlementPointLog"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into settlement_point_log (point_id, no, x, y,
                                          `time`, del_flag, update_time,
                                          create_time)
        values (#{pointId,jdbcType=INTEGER}, #{no,jdbcType=VARCHAR}, #{x,jdbcType=DOUBLE},
                #{y,jdbcType=DOUBLE}, #{time,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.settlement.SettlementPointLog" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into settlement_point_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointId != null">
                point_id,
            </if>
            <if test="no != null">
                no,
            </if>
            <if test="x != null">
                x,
            </if>
            <if test="y != null">
                y,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointId != null">
                #{pointId,jdbcType=INTEGER},
            </if>
            <if test="no != null">
                #{no,jdbcType=VARCHAR},
            </if>
            <if test="x != null">
                #{x,jdbcType=DOUBLE},
            </if>
            <if test="y != null">
                #{y,jdbcType=DOUBLE},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.settlement.SettlementPointLog">
        <!--@mbg.generated-->
        update settlement_point_log
        <set>
            <if test="pointId != null">
                point_id = #{pointId,jdbcType=INTEGER},
            </if>
            <if test="no != null">
                no = #{no,jdbcType=VARCHAR},
            </if>
            <if test="x != null">
                x = #{x,jdbcType=DOUBLE},
            </if>
            <if test="y != null">
                y = #{y,jdbcType=DOUBLE},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.settlement.SettlementPointLog">
        <!--@mbg.generated-->
        update settlement_point_log
        set point_id    = #{pointId,jdbcType=INTEGER},
            no          = #{no,jdbcType=VARCHAR},
            x           = #{x,jdbcType=DOUBLE},
            y           = #{y,jdbcType=DOUBLE},
            `time`      = #{time,jdbcType=TIMESTAMP},
            del_flag    = #{delFlag,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectPointLog" resultType="com.whfc.uni.dto.settlement.SettlementPointLogDTO">
        SELECT spl.id  AS logId,
               sp.name AS pointName,
               spl.no,
               spl.x,
               spl.y,
               spl.time
        FROM settlement_point_log spl
                     LEFT JOIN settlement_point sp ON sp.id = spl.point_id
        WHERE spl.del_flag = 0
          AND spl.point_id = #{pointId}
          AND spl.time >= #{startTime}
          AND #{endTime} >= spl.time
        ORDER BY spl.time DESC
    </select>

    <select id="selectIsExistLog" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settlement_point_log
        WHERE del_flag = 0
          AND point_id = #{pointId}
          AND no = #{no}
          AND x = #{x}
          AND y = #{y}
          AND time = #{time}
    </select>
</mapper>
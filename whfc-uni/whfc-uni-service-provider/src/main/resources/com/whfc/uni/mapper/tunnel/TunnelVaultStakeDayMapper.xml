<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelVaultStakeDayMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelVaultStakeDay">
        <!--@mbg.generated-->
        <!--@Table tunnel_vault_stake_day-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="stake_id" jdbcType="INTEGER" property="stakeId"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="backsight_value" jdbcType="DOUBLE" property="backsightValue"/>
        <result column="forward_value" jdbcType="DOUBLE" property="forwardValue"/>
        <result column="height_diff" jdbcType="DOUBLE" property="heightDiff"/>
        <result column="single_sink_value" jdbcType="DOUBLE" property="singleSinkValue"/>
        <result column="single_sink_total" jdbcType="DOUBLE" property="singleSinkTotal"/>
        <result column="rate_change" jdbcType="DOUBLE" property="rateChange"/>
        <result column="mgmt_level" jdbcType="VARCHAR" property="mgmtLevel"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stake_id, `time`, backsight_value, forward_value, height_diff, single_sink_value,
        single_sink_total, rate_change, mgmt_level, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_vault_stake_day
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_vault_stake_day
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStakeDay"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_vault_stake_day (stake_id, `time`, backsight_value,
        forward_value, height_diff, single_sink_value,
        single_sink_total, rate_change, mgmt_level,
        del_flag, update_time, create_time
        )
        values (#{stakeId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, #{backsightValue,jdbcType=DOUBLE},
        #{forwardValue,jdbcType=DOUBLE}, #{heightDiff,jdbcType=DOUBLE}, #{singleSinkValue,jdbcType=DOUBLE},
        #{singleSinkTotal,jdbcType=DOUBLE}, #{rateChange,jdbcType=DOUBLE}, #{mgmtLevel,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStakeDay"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_vault_stake_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stakeId != null">
                stake_id,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="backsightValue != null">
                backsight_value,
            </if>
            <if test="forwardValue != null">
                forward_value,
            </if>
            <if test="heightDiff != null">
                height_diff,
            </if>
            <if test="singleSinkValue != null">
                single_sink_value,
            </if>
            <if test="singleSinkTotal != null">
                single_sink_total,
            </if>
            <if test="rateChange != null">
                rate_change,
            </if>
            <if test="mgmtLevel != null">
                mgmt_level,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stakeId != null">
                #{stakeId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="backsightValue != null">
                #{backsightValue,jdbcType=DOUBLE},
            </if>
            <if test="forwardValue != null">
                #{forwardValue,jdbcType=DOUBLE},
            </if>
            <if test="heightDiff != null">
                #{heightDiff,jdbcType=DOUBLE},
            </if>
            <if test="singleSinkValue != null">
                #{singleSinkValue,jdbcType=DOUBLE},
            </if>
            <if test="singleSinkTotal != null">
                #{singleSinkTotal,jdbcType=DOUBLE},
            </if>
            <if test="rateChange != null">
                #{rateChange,jdbcType=DOUBLE},
            </if>
            <if test="mgmtLevel != null">
                #{mgmtLevel,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStakeDay">
        <!--@mbg.generated-->
        update tunnel_vault_stake_day
        <set>
            <if test="stakeId != null">
                stake_id = #{stakeId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="backsightValue != null">
                backsight_value = #{backsightValue,jdbcType=DOUBLE},
            </if>
            <if test="forwardValue != null">
                forward_value = #{forwardValue,jdbcType=DOUBLE},
            </if>
            <if test="heightDiff != null">
                height_diff = #{heightDiff,jdbcType=DOUBLE},
            </if>
            <if test="singleSinkValue != null">
                single_sink_value = #{singleSinkValue,jdbcType=DOUBLE},
            </if>
            <if test="singleSinkTotal != null">
                single_sink_total = #{singleSinkTotal,jdbcType=DOUBLE},
            </if>
            <if test="rateChange != null">
                rate_change = #{rateChange,jdbcType=DOUBLE},
            </if>
            <if test="mgmtLevel != null">
                mgmt_level = #{mgmtLevel,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelVaultStakeDay">
        <!--@mbg.generated-->
        update tunnel_vault_stake_day
        set stake_id = #{stakeId,jdbcType=INTEGER},
        `time` = #{time,jdbcType=TIMESTAMP},
        backsight_value = #{backsightValue,jdbcType=DOUBLE},
        forward_value = #{forwardValue,jdbcType=DOUBLE},
        height_diff = #{heightDiff,jdbcType=DOUBLE},
        single_sink_value = #{singleSinkValue,jdbcType=DOUBLE},
        single_sink_total = #{singleSinkTotal,jdbcType=DOUBLE},
        rate_change = #{rateChange,jdbcType=DOUBLE},
        mgmt_level = #{mgmtLevel,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByStakeId" resultType="com.whfc.uni.dto.tunnel.TunnelVaultStakeDayDTO">
        select
        id,
        `time`,
        backsight_value,
        forward_value,
        height_diff,
        single_sink_value,
        single_sink_total,
        rate_change,
        mgmt_level
        from
        tunnel_vault_stake_day
        where
        stake_id = #{stakeId}
        <if test="startTime != null and endTime != null">
            and `time` between #{startTime} and #{endTime}
        </if>
        and del_flag = 0
    </select>
    <delete id="deleteByStakeId">
        delete from tunnel_vault_stake_day
        where stake_id = #{stakeId}
    </delete>
    <delete id="deleteByStakeIds">
        delete from tunnel_vault_stake_day
        where stake_id in
        <foreach collection="stakeIds" item="stakeId" open="(" separator="," close=")">
            #{stakeId}
        </foreach>
    </delete>
    <insert id="insertAll">
        insert into tunnel_vault_stake_day
        (stake_id,
        `time`,
        backsight_value,
        forward_value,
        height_diff,
        single_sink_value,
        single_sink_total,
        rate_change,
        mgmt_level)
        values
        <foreach collection="list" item="day" separator=",">
            (#{day.stakeId,jdbcType=INTEGER},
            #{day.time,jdbcType=TIMESTAMP},
            #{day.backsightValue,jdbcType=DOUBLE},
            #{day.forwardValue,jdbcType=DOUBLE},
            #{day.heightDiff,jdbcType=DOUBLE},
            #{day.singleSinkValue,jdbcType=DOUBLE},
            #{day.singleSinkTotal,jdbcType=DOUBLE},
            #{day.rateChange,jdbcType=DOUBLE},
            #{day.mgmtLevel,jdbcType=DOUBLE})
        </foreach>
    </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteWarnRuleChannelMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteWarnRuleChannel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="msg_channel" jdbcType="INTEGER" property="msgChannel" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, msg_channel, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_warn_rule_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_warn_rule_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleChannel">
    insert into concrete_warn_rule_channel (id, rule_id, msg_channel, 
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{ruleId,jdbcType=INTEGER}, #{msgChannel,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleChannel">
    insert into concrete_warn_rule_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="msgChannel != null">
        msg_channel,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="msgChannel != null">
        #{msgChannel,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleChannel">
    update concrete_warn_rule_channel
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="msgChannel != null">
        msg_channel = #{msgChannel,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRuleChannel">
    update concrete_warn_rule_channel
    set rule_id = #{ruleId,jdbcType=INTEGER},
      msg_channel = #{msgChannel,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByWarnRuleId" resultType="java.lang.Integer">
    select msg_channel
    from concrete_warn_rule_channel
    where rule_id = #{ruleId}
  </select>

  <delete id="deleteByWarnRuleId">
    delete from concrete_warn_rule_channel
    where rule_id = #{ruleId}
  </delete>

  <insert id="batchInsert">
    INSERT INTO concrete_warn_rule_channel
    (
    rule_id,
    msg_channel
    )
    values
    <foreach collection="list" item="item" separator="," >
      (
      #{item.ruleId},
      #{item.msgChannel}
      )
    </foreach>
  </insert>
</mapper>
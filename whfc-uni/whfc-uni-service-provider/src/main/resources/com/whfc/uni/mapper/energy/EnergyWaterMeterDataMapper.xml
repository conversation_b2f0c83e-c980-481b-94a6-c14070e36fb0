<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.energy.EnergyWaterMeterDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.energy.EnergyWaterMeterData">
    <!--@mbg.generated-->
    <!--@Table energy_water_meter_data-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="water_id" jdbcType="INTEGER" property="waterId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="dosage" jdbcType="DOUBLE" property="dosage" />
    <result column="switch_state" jdbcType="INTEGER" property="switchState" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, water_id, device_id, `time`, dosage, switch_state, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from energy_water_meter_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from energy_water_meter_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterData">
    <!--@mbg.generated-->
    insert into energy_water_meter_data (id, dept_id, water_id, 
      device_id, `time`, dosage, 
      switch_state, del_flag, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{waterId,jdbcType=INTEGER}, 
      #{deviceId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, #{dosage,jdbcType=DOUBLE}, 
      #{switchState,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterData">
    <!--@mbg.generated-->
    insert into energy_water_meter_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="waterId != null">
        water_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="dosage != null">
        dosage,
      </if>
      <if test="switchState != null">
        switch_state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="waterId != null">
        #{waterId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="switchState != null">
        #{switchState,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterData">
    <!--@mbg.generated-->
    update energy_water_meter_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="waterId != null">
        water_id = #{waterId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="dosage != null">
        dosage = #{dosage,jdbcType=DOUBLE},
      </if>
      <if test="switchState != null">
        switch_state = #{switchState,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.energy.EnergyWaterMeterData">
    <!--@mbg.generated-->
    update energy_water_meter_data
    set dept_id = #{deptId,jdbcType=INTEGER},
      water_id = #{waterId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=INTEGER},
      `time` = #{time,jdbcType=TIMESTAMP},
      dosage = #{dosage,jdbcType=DOUBLE},
      switch_state = #{switchState,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <insert id="insertOrUpdate">
      insert into energy_water_meter_data
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">
          id,
        </if>
        <if test="deptId != null">
          dept_id,
        </if>
        <if test="waterId != null">
          water_id,
        </if>
        <if test="deviceId != null">
          device_id,
        </if>
        <if test="time != null">
          `time`,
        </if>
        <if test="dosage != null">
          dosage,
        </if>
        <if test="switchState != null">
          switch_state,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="id != null">
          #{id,jdbcType=INTEGER},
        </if>
        <if test="deptId != null">
          #{deptId,jdbcType=INTEGER},
        </if>
        <if test="waterId != null">
          #{waterId,jdbcType=INTEGER},
        </if>
        <if test="deviceId != null">
          #{deviceId,jdbcType=INTEGER},
        </if>
        <if test="time != null">
          #{time,jdbcType=TIMESTAMP},
        </if>
        <if test="dosage != null">
          #{dosage,jdbcType=DOUBLE},
        </if>
        <if test="switchState != null">
          #{switchState,jdbcType=INTEGER},
        </if>
      </trim>
       on duplicate key update
        <if test="deptId != null">
          dept_id = #{deptId,jdbcType=INTEGER},
        </if>
        <if test="time != null">
          `time` = #{time,jdbcType=TIMESTAMP},
        </if>
        <if test="dosage != null">
          dosage = #{dosage,jdbcType=DOUBLE},
        </if>
        <if test="switchState != null">
          switch_state = #{switchState,jdbcType=INTEGER},
        </if>
      device_id = #{deviceId,jdbcType=INTEGER}
    </insert>
    <select id="selectByWaterId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from energy_water_meter_data
      where
      water_id = #{waterId,jdbcType=INTEGER}
    </select>
</mapper>
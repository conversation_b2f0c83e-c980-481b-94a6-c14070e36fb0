<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstReportAttachMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstReportAttach">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="attach" jdbcType="LONGVARCHAR" property="attach" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    guid,
    dept_id,
    `date`,
    attach,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from const_report_attach
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from const_report_attach
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.constlog.ConstReportAttach">
    insert into const_report_attach
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="attach != null">
        attach,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attach != null">
        #{attach,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstReportAttach">
    update const_report_attach
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attach != null">
        attach = #{attach,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptIdAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from const_report_attach
      where dept_id = #{deptId}
      and `date` = #{date}
      and del_flag = 0
     order by id desc limit 1
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from const_report_attach
    where guid = #{guid}
  </select>
</mapper>
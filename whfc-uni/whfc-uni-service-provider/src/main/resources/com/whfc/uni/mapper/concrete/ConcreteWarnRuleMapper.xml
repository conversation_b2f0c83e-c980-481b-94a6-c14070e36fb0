<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteWarnRuleMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteWarnRule">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="rule_param" jdbcType="LONGVARCHAR" property="ruleParam" />
    <result column="rule_express" jdbcType="LONGVARCHAR" property="ruleExpress" />
    <result column="enable_flag" jdbcType="INTEGER" property="enableFlag" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, rule_name, rule_type,rule_param, rule_express, enable_flag, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_warn_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_warn_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRule">
    insert into concrete_warn_rule (id, dept_id, rule_name, 
      rule_type, enable_flag, del_flag, 
      update_time, create_time, rule_param, 
      rule_express)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{ruleName,jdbcType=VARCHAR}, 
      #{ruleType,jdbcType=INTEGER}, #{enableFlag,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{ruleParam,jdbcType=LONGVARCHAR}, 
      #{ruleExpress,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRule">
    insert into concrete_warn_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="enableFlag != null">
        enable_flag,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="ruleParam != null">
        rule_param,
      </if>
      <if test="ruleExpress != null">
        rule_express,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="enableFlag != null">
        #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleParam != null">
        #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRule">
    update concrete_warn_rule
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="enableFlag != null">
        enable_flag = #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleParam != null">
        rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        rule_express = #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleParam != null">
        rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleExpress != null">
        rule_express = #{ruleExpress,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteWarnRule">
    update concrete_warn_rule
    set dept_id = #{deptId,jdbcType=INTEGER},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      rule_type = #{ruleType,jdbcType=INTEGER},
      enable_flag = #{enableFlag,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      rule_param = #{ruleParam,jdbcType=LONGVARCHAR},
      rule_express = #{ruleExpress,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptId" resultType="com.whfc.uni.dto.concrete.ConcreteWarnRuleDTO">
     select id as ruleId,
            rule_name,
            rule_type,
            rule_param,
            enable_flag
    from concrete_warn_rule
    where dept_id = #{deptId,jdbcType=INTEGER}
    and del_flag = 0
    <if test="keyword!=null and keyword!=''">
      and rule_name like concat('%',#{keyword},'%')
    </if>
    <if test="ruleType!=null">
      and rule_type = #{ruleType}
    </if>
    <if test="enableFlag!=null">
      and enable_flag = #{enableFlag}
    </if>
    order by id desc
  </select>

  <update id="updateEnableFlagByRuleId">
    update concrete_warn_rule
    set enable_flag = #{enableFlag}
    where id = #{ruleId}
  </update>

  <update id="logicDeleteById">
    update concrete_warn_rule
    set del_flag = 1
    where id = #{ruleId}
      and del_flag = 0
  </update>
</mapper>
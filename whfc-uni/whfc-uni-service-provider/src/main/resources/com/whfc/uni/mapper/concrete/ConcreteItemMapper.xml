<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.concrete.ConcreteItemMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.concrete.ConcreteItem">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="bind_state" jdbcType="INTEGER" property="bindState" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="pour_date" jdbcType="DATE" property="pourDate" />
    <result column="measure_start_date" jdbcType="DATE" property="measureStartDate" />
    <result column="measure_end_date" jdbcType="DATE" property="measureEndDate" />
    <result column="over_start_date" jdbcType="DATE" property="overStartDate" />
    <result column="over_end_date" jdbcType="DATE" property="overEndDate" />
    <result column="temp_diff_max" jdbcType="DOUBLE" property="tempDiffMax" />
    <result column="temp_diff_min" jdbcType="DOUBLE" property="tempDiffMin" />
    <result column="temp_diff_avg" jdbcType="DOUBLE" property="tempDiffAvg" />
    <result column="temp_measure_num" jdbcType="INTEGER" property="tempMeasureNum" />
    <result column="temp_over_num" jdbcType="INTEGER" property="tempOverNum" />
    <result column="temp_over_avg" jdbcType="DOUBLE" property="tempOverAvg" />
    <result column="temp_over_rate" jdbcType="DOUBLE" property="tempOverRate" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, group_id, name, platform, sn, bind_state, state, pour_date, measure_start_date, 
    measure_end_date, over_start_date, over_end_date, temp_diff_max, temp_diff_min, temp_diff_avg, 
    temp_measure_num, temp_over_num, temp_over_avg, temp_over_rate, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from concrete_item
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from concrete_item
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.uni.entity.concrete.ConcreteItem">
    insert into concrete_item (id, dept_id, group_id, 
      name, platform, sn, 
      bind_state, state, pour_date, 
      measure_start_date, measure_end_date, over_start_date, 
      over_end_date, temp_diff_max, temp_diff_min, 
      temp_diff_avg, temp_measure_num, temp_over_num, 
      temp_over_avg, temp_over_rate, del_flag, 
      update_time, create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, 
      #{name,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, 
      #{bindState,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{pourDate,jdbcType=DATE}, 
      #{measureStartDate,jdbcType=DATE}, #{measureEndDate,jdbcType=DATE}, #{overStartDate,jdbcType=DATE}, 
      #{overEndDate,jdbcType=DATE}, #{tempDiffMax,jdbcType=DOUBLE}, #{tempDiffMin,jdbcType=DOUBLE}, 
      #{tempDiffAvg,jdbcType=DOUBLE}, #{tempMeasureNum,jdbcType=INTEGER}, #{tempOverNum,jdbcType=INTEGER}, 
      #{tempOverAvg,jdbcType=DOUBLE}, #{tempOverRate,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItem">
    insert into concrete_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="bindState != null">
        bind_state,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="pourDate != null">
        pour_date,
      </if>
      <if test="measureStartDate != null">
        measure_start_date,
      </if>
      <if test="measureEndDate != null">
        measure_end_date,
      </if>
      <if test="overStartDate != null">
        over_start_date,
      </if>
      <if test="overEndDate != null">
        over_end_date,
      </if>
      <if test="tempDiffMax != null">
        temp_diff_max,
      </if>
      <if test="tempDiffMin != null">
        temp_diff_min,
      </if>
      <if test="tempDiffAvg != null">
        temp_diff_avg,
      </if>
      <if test="tempMeasureNum != null">
        temp_measure_num,
      </if>
      <if test="tempOverNum != null">
        temp_over_num,
      </if>
      <if test="tempOverAvg != null">
        temp_over_avg,
      </if>
      <if test="tempOverRate != null">
        temp_over_rate,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="bindState != null">
        #{bindState,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="pourDate != null">
        #{pourDate,jdbcType=DATE},
      </if>
      <if test="measureStartDate != null">
        #{measureStartDate,jdbcType=DATE},
      </if>
      <if test="measureEndDate != null">
        #{measureEndDate,jdbcType=DATE},
      </if>
      <if test="overStartDate != null">
        #{overStartDate,jdbcType=DATE},
      </if>
      <if test="overEndDate != null">
        #{overEndDate,jdbcType=DATE},
      </if>
      <if test="tempDiffMax != null">
        #{tempDiffMax,jdbcType=DOUBLE},
      </if>
      <if test="tempDiffMin != null">
        #{tempDiffMin,jdbcType=DOUBLE},
      </if>
      <if test="tempDiffAvg != null">
        #{tempDiffAvg,jdbcType=DOUBLE},
      </if>
      <if test="tempMeasureNum != null">
        #{tempMeasureNum,jdbcType=INTEGER},
      </if>
      <if test="tempOverNum != null">
        #{tempOverNum,jdbcType=INTEGER},
      </if>
      <if test="tempOverAvg != null">
        #{tempOverAvg,jdbcType=DOUBLE},
      </if>
      <if test="tempOverRate != null">
        #{tempOverRate,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.concrete.ConcreteItem">
    update concrete_item
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="bindState != null">
        bind_state = #{bindState,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="pourDate != null">
        pour_date = #{pourDate,jdbcType=DATE},
      </if>
      <if test="measureStartDate != null">
        measure_start_date = #{measureStartDate,jdbcType=DATE},
      </if>
      <if test="measureEndDate != null">
        measure_end_date = #{measureEndDate,jdbcType=DATE},
      </if>
      <if test="overStartDate != null">
        over_start_date = #{overStartDate,jdbcType=DATE},
      </if>
      <if test="overEndDate != null">
        over_end_date = #{overEndDate,jdbcType=DATE},
      </if>
      <if test="tempDiffMax != null">
        temp_diff_max = #{tempDiffMax,jdbcType=DOUBLE},
      </if>
      <if test="tempDiffMin != null">
        temp_diff_min = #{tempDiffMin,jdbcType=DOUBLE},
      </if>
      <if test="tempDiffAvg != null">
        temp_diff_avg = #{tempDiffAvg,jdbcType=DOUBLE},
      </if>
      <if test="tempMeasureNum != null">
        temp_measure_num = #{tempMeasureNum,jdbcType=INTEGER},
      </if>
      <if test="tempOverNum != null">
        temp_over_num = #{tempOverNum,jdbcType=INTEGER},
      </if>
      <if test="tempOverAvg != null">
        temp_over_avg = #{tempOverAvg,jdbcType=DOUBLE},
      </if>
      <if test="tempOverRate != null">
        temp_over_rate = #{tempOverRate,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.concrete.ConcreteItem">
    update concrete_item
    set dept_id = #{deptId,jdbcType=INTEGER},
      group_id = #{groupId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      bind_state = #{bindState,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      pour_date = #{pourDate,jdbcType=DATE},
      measure_start_date = #{measureStartDate,jdbcType=DATE},
      measure_end_date = #{measureEndDate,jdbcType=DATE},
      over_start_date = #{overStartDate,jdbcType=DATE},
      over_end_date = #{overEndDate,jdbcType=DATE},
      temp_diff_max = #{tempDiffMax,jdbcType=DOUBLE},
      temp_diff_min = #{tempDiffMin,jdbcType=DOUBLE},
      temp_diff_avg = #{tempDiffAvg,jdbcType=DOUBLE},
      temp_measure_num = #{tempMeasureNum,jdbcType=INTEGER},
      temp_over_num = #{tempOverNum,jdbcType=INTEGER},
      temp_over_avg = #{tempOverAvg,jdbcType=DOUBLE},
      temp_over_rate = #{tempOverRate,jdbcType=DOUBLE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectGroupStat" resultType="com.whfc.uni.dto.concrete.ConcreteGroupDTO">
    select group_id,count(*) as deviceNum
    from concrete_item
    where dept_id = #{deptId,jdbcType=INTEGER}
      and del_flag = 0
    group by group_id
  </select>

  <select id="selectItemList" resultType="com.whfc.uni.dto.concrete.ConcreteItemDTO">
    select  td.id as concreteId,
            td.name,
            td.platform,
            td.sn,
            td.bind_state,
            td.state,
            td.group_id,
            tg.name as groupName,
            td.pour_date,
            td.measure_start_date,
            td.measure_end_date,
            td.over_start_date,
            td.over_end_date,
            td.temp_diff_max,
            td.temp_diff_min,
            td.temp_diff_avg,
            td.temp_measure_num,
            td.temp_over_num,
            td.temp_over_avg,
            td.temp_over_rate
    from concrete_item td
    inner join concrete_group tg on td.group_id = tg.id
    where td.dept_id = #{deptId,jdbcType=INTEGER}
    and td.del_flag = 0
    <if test="groupId !=null">
      and td.group_id = #{groupId}
    </if>
    order by td.id desc
  </select>

  <select id="selectByDeptId" resultType="com.whfc.uni.dto.concrete.ConcreteItemDTO">
    select  td.id as concreteId,
            td.name,
            td.state,
            td.pour_date,
            td.measure_start_date,
            td.measure_end_date,
            td.over_start_date,
            td.over_end_date,
            td.temp_diff_max,
            td.temp_diff_min,
            td.temp_diff_avg,
            td.temp_measure_num,
            td.temp_over_num,
            td.temp_over_avg,
            round(td.temp_over_rate *100,1) as temp_over_rate
    from concrete_item td
    where td.dept_id = #{deptId,jdbcType=INTEGER}
      and td.del_flag = 0
    order by td.id desc
  </select>

  <select id="selectItemListByDeptId" resultType="com.whfc.uni.dto.concrete.ConcreteItemDTO">
    select  td.id as concreteId,td.name,td.state
    from concrete_item td
    where td.dept_id = #{deptId,jdbcType=INTEGER}
    and td.del_flag = 0
    order by td.id desc
  </select>

  <select id="selectByPlatformAndSn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from concrete_item
    where platform = #{platform}
    and sn = #{sn}
    and del_flag = 0
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from concrete_item
    where dept_id = #{deptId,jdbcType=INTEGER}
    and name = #{name,jdbcType=VARCHAR}
    and del_flag = 0
  </select>

  <update id="logicDeleteById">
    update concrete_item
    set del_flag = 1
    where id = #{id}
      and del_flag = 0
  </update>

  <select id="selectByWithinIdList" resultType="com.whfc.uni.dto.concrete.ConcreteItemDTO">
    select id as concreteId,name
    from concrete_item
    where id in (
    <foreach collection="idList" item="id" separator=",">
      #{id}
    </foreach>
    )
  </select>

  <select id="selectByWithoutIdList" resultType="com.whfc.uni.dto.concrete.ConcreteItemDTO">
    select id as concreteId,name
    from concrete_item
    where dept_id = #{deptId}
    and del_flag = 0
    <if test="idList.size()>0">
      and id not in (
      <foreach collection="idList" item="id" separator=",">
        #{id}
      </foreach>
      )
    </if>

  </select>
</mapper>
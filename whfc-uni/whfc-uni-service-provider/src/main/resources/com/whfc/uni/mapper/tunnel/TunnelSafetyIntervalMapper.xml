<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tunnel.TunnelSafetyIntervalMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tunnel.TunnelSafetyInterval">
        <!--@mbg.generated-->
        <!--@Table tunnel_safety_interval-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="tunnel_id" jdbcType="INTEGER" property="tunnelId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="distance" jdbcType="DOUBLE" property="distance"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, tunnel_id, `name`, distance, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tunnel_safety_interval
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from tunnel_safety_interval
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tunnel.TunnelSafetyInterval"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_safety_interval (dept_id, tunnel_id, `name`,
        distance, del_flag, update_time,
        create_time)
        values (#{deptId,jdbcType=INTEGER}, #{tunnelId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
        #{distance,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.uni.entity.tunnel.TunnelSafetyInterval" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into tunnel_safety_interval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="tunnelId != null">
                tunnel_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="distance != null">
                distance,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="tunnelId != null">
                #{tunnelId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="distance != null">
                #{distance,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tunnel.TunnelSafetyInterval">
        <!--@mbg.generated-->
        update tunnel_safety_interval
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="tunnelId != null">
                tunnel_id = #{tunnelId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="distance != null">
                distance = #{distance,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tunnel.TunnelSafetyInterval">
        <!--@mbg.generated-->
        update tunnel_safety_interval
        set dept_id = #{deptId,jdbcType=INTEGER},
        tunnel_id = #{tunnelId,jdbcType=INTEGER},
        `name` = #{name,jdbcType=VARCHAR},
        distance = #{distance,jdbcType=DOUBLE},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectSafetyIntervalByDeptIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tunnel_safety_interval
        WHERE
        `name` = #{name}
        and tunnel_id=#{tunnelId}
        AND dept_id = #{deptId}
    </select>
    <select id="selectByDeptId" resultType="com.whfc.uni.param.tunnel.TunnelSafetyIntervalParam">
        SELECT
        `name`,
        distance
        FROM
        tunnel_safety_interval
        where
        dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        <if test="tunnelId != null">
            and tunnel_id = #{tunnelId}
        </if>
        <![CDATA[ and `update_time` >  DATE_ADD(now(), INTERVAL -1 day)]]>
    </select>
    <select id="getSafetyWarnNum" resultType="java.lang.Integer">
        SELECT
	    COUNT(0)
        FROM
	    tunnel_safety_interval_log
	    where
	    dept_id in
	    <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
	    and distance >120
        <if test="startTime != null and endTime != null">
         and `time` between #{startTime} and #{endTime}
        </if>
    </select>
</mapper>
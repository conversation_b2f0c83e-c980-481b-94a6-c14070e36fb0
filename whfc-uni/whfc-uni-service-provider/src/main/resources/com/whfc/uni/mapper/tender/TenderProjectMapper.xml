<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.tender.TenderProjectMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.tender.TenderProject">
    <!--@mbg.generated-->
    <!--@Table tender_project-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, guid, dept_id, project_name, project_no, contract_name, contract_no, market, 
    country, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tender_project
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tender_project
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderProject" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_project (guid, dept_id, project_name, 
      project_no, contract_name, contract_no, 
      market, country, del_flag, 
      update_time, create_time)
    values (#{guid,jdbcType=VARCHAR}, #{deptId,jdbcType=INTEGER}, #{projectName,jdbcType=VARCHAR}, 
      #{projectNo,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR}, 
      #{market,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.tender.TenderProject" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectNo != null">
        project_no,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectNo != null">
        #{projectNo,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.tender.TenderProject">
    <!--@mbg.generated-->
    update tender_project
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectNo != null">
        project_no = #{projectNo,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        contract_no = #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.tender.TenderProject">
    <!--@mbg.generated-->
    update tender_project
    set guid = #{guid,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=INTEGER},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_no = #{projectNo,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      contract_no = #{contractNo,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tender_project
    (guid, dept_id, project_name, project_no, contract_name, contract_no, market, country, 
      del_flag, update_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.guid,jdbcType=VARCHAR}, #{item.deptId,jdbcType=INTEGER}, #{item.projectName,jdbcType=VARCHAR}, 
        #{item.projectNo,jdbcType=VARCHAR}, #{item.contractName,jdbcType=VARCHAR}, #{item.contractNo,jdbcType=VARCHAR}, 
        #{item.market,jdbcType=VARCHAR}, #{item.country,jdbcType=VARCHAR}, #{item.delFlag,jdbcType=INTEGER}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectList" resultType="com.whfc.uni.dto.tender.TenderProjectDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_project WHERE del_flag = 0
    AND dept_id = #{deptId}
    <if test="projectNo != null and projectNo != ''">
      AND project_no LIKE CONCAT('%', #{projectNo}, '%')
    </if>
    <if test="projectName != null and projectName != ''">
      AND project_name LIKE CONCAT('%', #{projectName}, '%')
    </if>
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_project
    WHERE del_flag = 0 AND guid = #{guid}
  </select>

  <delete id="logicDel">
    UPDATE tender_project SET del_flag = 1 WHERE guid = #{guid}
  </delete>

  <select id="selectByProjectNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM tender_project
    WHERE del_flag = 0 AND project_no = #{projectNo} AND dept_id = #{deptId}
  </select>

  <select id="selectMarketList" resultType="com.whfc.uni.dto.tender.TenderProjectDTO">
    SELECT
        guid,market
    FROM tender_project
    WHERE del_flag = 0 AND dept_id = #{deptId}
    GROUP BY market
  </select>

  <select id="selectCountryList" resultType="com.whfc.uni.dto.tender.TenderProjectDTO">
    SELECT
        guid,country
    FROM tender_project
    WHERE del_flag = 0 AND dept_id = #{deptId}
    GROUP BY country
  </select>
</mapper>
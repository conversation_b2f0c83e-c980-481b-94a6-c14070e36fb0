<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.permit.LiftingPermitItemMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.permit.LiftingPermitItem">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="permit_id" jdbcType="INTEGER" property="permitId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="INTEGER" property="value" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    permit_id,
    `name`,
    `value`,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lifting_permit_item
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lifting_permit_item
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.permit.LiftingPermitItem">
    insert into lifting_permit_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="permitId != null">
        permit_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="permitId != null">
        #{permitId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.permit.LiftingPermitItem">
    update lifting_permit_item
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="permitId != null">
        permit_id = #{permitId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByPermitId" resultType="com.whfc.uni.dto.permit.LiftingPermitItemDTO">
    select `name`,
           `value`
      from lifting_permit_item
     where permit_id = #{permitId}
       and del_flag = 0
  </select>

  <update id="logicDeleteByPermitId">
    update lifting_permit_item
       set del_flag = 1
     where permit_id = #{permitId}
  </update>

  <insert id="batchInsert">
    insert into lifting_permit_item
    (
        dept_id,
        permit_id,
        `name`,
        `value`
    )
    values
    <foreach collection="list" item="item" separator=",">
    (
        #{deptId},
        #{permitId},
        #{item.name},
        #{item.value}
    )
    </foreach>
  </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.trolley.TrolleyLiningDeviceDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.trolley.TrolleyLiningDeviceData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="left_level_1" jdbcType="INTEGER" property="leftLevel1" />
    <result column="left_level_2" jdbcType="INTEGER" property="leftLevel2" />
    <result column="left_level_3" jdbcType="INTEGER" property="leftLevel3" />
    <result column="left_level_4" jdbcType="INTEGER" property="leftLevel4" />
    <result column="left_level_5" jdbcType="INTEGER" property="leftLevel5" />
    <result column="left_level_6" jdbcType="INTEGER" property="leftLevel6" />
    <result column="left_level_7" jdbcType="INTEGER" property="leftLevel7" />
    <result column="left_level_8" jdbcType="INTEGER" property="leftLevel8" />
    <result column="right_level_1" jdbcType="INTEGER" property="rightLevel1" />
    <result column="right_level_2" jdbcType="INTEGER" property="rightLevel2" />
    <result column="right_level_3" jdbcType="INTEGER" property="rightLevel3" />
    <result column="right_level_4" jdbcType="INTEGER" property="rightLevel4" />
    <result column="right_level_5" jdbcType="INTEGER" property="rightLevel5" />
    <result column="right_level_6" jdbcType="INTEGER" property="rightLevel6" />
    <result column="right_level_7" jdbcType="INTEGER" property="rightLevel7" />
    <result column="right_level_8" jdbcType="INTEGER" property="rightLevel8" />
    <result column="vault_filling_1" jdbcType="INTEGER" property="vaultFilling1" />
    <result column="vault_filling_2" jdbcType="INTEGER" property="vaultFilling2" />
    <result column="vault_filling_3" jdbcType="INTEGER" property="vaultFilling3" />
    <result column="vault_filling_4" jdbcType="INTEGER" property="vaultFilling4" />
    <result column="pour_status" jdbcType="INTEGER" property="pourStatus" />
    <result column="left_tpl_status" jdbcType="INTEGER" property="leftTplStatus" />
    <result column="right_tpl_status" jdbcType="INTEGER" property="rightTplStatus" />
    <result column="vault_tpl_status" jdbcType="INTEGER" property="vaultTplStatus" />
    <result column="vault_pressure_1" jdbcType="DOUBLE" property="vaultPressure1" />
    <result column="vault_pressure_2" jdbcType="DOUBLE" property="vaultPressure2" />
    <result column="vault_pressure_3" jdbcType="DOUBLE" property="vaultPressure3" />
    <result column="vault_pressure_4" jdbcType="DOUBLE" property="vaultPressure4" />
    <result column="vault_pressure_5" jdbcType="DOUBLE" property="vaultPressure5" />
    <result column="vault_pressure_6" jdbcType="DOUBLE" property="vaultPressure6" />
    <result column="env_temp" jdbcType="DOUBLE" property="envTemp" />
    <result column="pour_plate_num" jdbcType="INTEGER" property="pourPlateNum" />
    <result column="pour_volume" jdbcType="DOUBLE" property="pourVolume" />
    <result column="total_pour_volume" jdbcType="DOUBLE" property="totalPourVolume" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    device_id,
    `time`,
    left_level_1,
    left_level_2,
    left_level_3,
    left_level_4,
    left_level_5,
    left_level_6,
    left_level_7,
    left_level_8,
    right_level_1,
    right_level_2,
    right_level_3,
    right_level_4,
    right_level_5,
    right_level_6,
    right_level_7,
    right_level_8,
    vault_filling_1,
    vault_filling_2,
    vault_filling_3,
    vault_filling_4,
    pour_status,
    left_tpl_status,
    right_tpl_status,
    vault_tpl_status,
    vault_pressure_1,
    vault_pressure_2,
    vault_pressure_3,
    vault_pressure_4,
    vault_pressure_5,
    vault_pressure_6,
    env_temp,
    pour_plate_num,
    pour_volume,
    total_pour_volume,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trolley_lining_device_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from trolley_lining_device_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.trolley.TrolleyLiningDeviceData">
    insert into trolley_lining_device_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="leftLevel1 != null">
        left_level_1,
      </if>
      <if test="leftLevel2 != null">
        left_level_2,
      </if>
      <if test="leftLevel3 != null">
        left_level_3,
      </if>
      <if test="leftLevel4 != null">
        left_level_4,
      </if>
      <if test="leftLevel5 != null">
        left_level_5,
      </if>
      <if test="leftLevel6 != null">
        left_level_6,
      </if>
      <if test="leftLevel7 != null">
        left_level_7,
      </if>
      <if test="leftLevel8 != null">
        left_level_8,
      </if>
      <if test="rightLevel1 != null">
        right_level_1,
      </if>
      <if test="rightLevel2 != null">
        right_level_2,
      </if>
      <if test="rightLevel3 != null">
        right_level_3,
      </if>
      <if test="rightLevel4 != null">
        right_level_4,
      </if>
      <if test="rightLevel5 != null">
        right_level_5,
      </if>
      <if test="rightLevel6 != null">
        right_level_6,
      </if>
      <if test="rightLevel7 != null">
        right_level_7,
      </if>
      <if test="rightLevel8 != null">
        right_level_8,
      </if>
      <if test="vaultFilling1 != null">
        vault_filling_1,
      </if>
      <if test="vaultFilling2 != null">
        vault_filling_2,
      </if>
      <if test="vaultFilling3 != null">
        vault_filling_3,
      </if>
      <if test="vaultFilling4 != null">
        vault_filling_4,
      </if>
      <if test="pourStatus != null">
        pour_status,
      </if>
      <if test="leftTplStatus != null">
        left_tpl_status,
      </if>
      <if test="rightTplStatus != null">
        right_tpl_status,
      </if>
      <if test="vaultTplStatus != null">
        vault_tpl_status,
      </if>
      <if test="vaultPressure1 != null">
        vault_pressure_1,
      </if>
      <if test="vaultPressure2 != null">
        vault_pressure_2,
      </if>
      <if test="vaultPressure3 != null">
        vault_pressure_3,
      </if>
      <if test="vaultPressure4 != null">
        vault_pressure_4,
      </if>
      <if test="vaultPressure5 != null">
        vault_pressure_5,
      </if>
      <if test="vaultPressure6 != null">
        vault_pressure_6,
      </if>
      <if test="envTemp != null">
        env_temp,
      </if>
      <if test="pourPlateNum != null">
        pour_plate_num,
      </if>
      <if test="pourVolume != null">
        pour_volume,
      </if>
      <if test="totalPourVolume != null">
        total_pour_volume,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="leftLevel1 != null">
        #{leftLevel1,jdbcType=INTEGER},
      </if>
      <if test="leftLevel2 != null">
        #{leftLevel2,jdbcType=INTEGER},
      </if>
      <if test="leftLevel3 != null">
        #{leftLevel3,jdbcType=INTEGER},
      </if>
      <if test="leftLevel4 != null">
        #{leftLevel4,jdbcType=INTEGER},
      </if>
      <if test="leftLevel5 != null">
        #{leftLevel5,jdbcType=INTEGER},
      </if>
      <if test="leftLevel6 != null">
        #{leftLevel6,jdbcType=INTEGER},
      </if>
      <if test="leftLevel7 != null">
        #{leftLevel7,jdbcType=INTEGER},
      </if>
      <if test="leftLevel8 != null">
        #{leftLevel8,jdbcType=INTEGER},
      </if>
      <if test="rightLevel1 != null">
        #{rightLevel1,jdbcType=INTEGER},
      </if>
      <if test="rightLevel2 != null">
        #{rightLevel2,jdbcType=INTEGER},
      </if>
      <if test="rightLevel3 != null">
        #{rightLevel3,jdbcType=INTEGER},
      </if>
      <if test="rightLevel4 != null">
        #{rightLevel4,jdbcType=INTEGER},
      </if>
      <if test="rightLevel5 != null">
        #{rightLevel5,jdbcType=INTEGER},
      </if>
      <if test="rightLevel6 != null">
        #{rightLevel6,jdbcType=INTEGER},
      </if>
      <if test="rightLevel7 != null">
        #{rightLevel7,jdbcType=INTEGER},
      </if>
      <if test="rightLevel8 != null">
        #{rightLevel8,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling1 != null">
        #{vaultFilling1,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling2 != null">
        #{vaultFilling2,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling3 != null">
        #{vaultFilling3,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling4 != null">
        #{vaultFilling4,jdbcType=INTEGER},
      </if>
      <if test="pourStatus != null">
        #{pourStatus,jdbcType=INTEGER},
      </if>
      <if test="leftTplStatus != null">
        #{leftTplStatus,jdbcType=INTEGER},
      </if>
      <if test="rightTplStatus != null">
        #{rightTplStatus,jdbcType=INTEGER},
      </if>
      <if test="vaultTplStatus != null">
        #{vaultTplStatus,jdbcType=INTEGER},
      </if>
      <if test="vaultPressure1 != null">
        #{vaultPressure1,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure2 != null">
        #{vaultPressure2,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure3 != null">
        #{vaultPressure3,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure4 != null">
        #{vaultPressure4,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure5 != null">
        #{vaultPressure5,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure6 != null">
        #{vaultPressure6,jdbcType=DOUBLE},
      </if>
      <if test="envTemp != null">
        #{envTemp,jdbcType=DOUBLE},
      </if>
      <if test="pourPlateNum != null">
        #{pourPlateNum,jdbcType=INTEGER},
      </if>
      <if test="pourVolume != null">
        #{pourVolume,jdbcType=DOUBLE},
      </if>
      <if test="totalPourVolume != null">
        #{totalPourVolume,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.trolley.TrolleyLiningDeviceData">
    update trolley_lining_device_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="leftLevel1 != null">
        left_level_1 = #{leftLevel1,jdbcType=INTEGER},
      </if>
      <if test="leftLevel2 != null">
        left_level_2 = #{leftLevel2,jdbcType=INTEGER},
      </if>
      <if test="leftLevel3 != null">
        left_level_3 = #{leftLevel3,jdbcType=INTEGER},
      </if>
      <if test="leftLevel4 != null">
        left_level_4 = #{leftLevel4,jdbcType=INTEGER},
      </if>
      <if test="leftLevel5 != null">
        left_level_5 = #{leftLevel5,jdbcType=INTEGER},
      </if>
      <if test="leftLevel6 != null">
        left_level_6 = #{leftLevel6,jdbcType=INTEGER},
      </if>
      <if test="leftLevel7 != null">
        left_level_7 = #{leftLevel7,jdbcType=INTEGER},
      </if>
      <if test="leftLevel8 != null">
        left_level_8 = #{leftLevel8,jdbcType=INTEGER},
      </if>
      <if test="rightLevel1 != null">
        right_level_1 = #{rightLevel1,jdbcType=INTEGER},
      </if>
      <if test="rightLevel2 != null">
        right_level_2 = #{rightLevel2,jdbcType=INTEGER},
      </if>
      <if test="rightLevel3 != null">
        right_level_3 = #{rightLevel3,jdbcType=INTEGER},
      </if>
      <if test="rightLevel4 != null">
        right_level_4 = #{rightLevel4,jdbcType=INTEGER},
      </if>
      <if test="rightLevel5 != null">
        right_level_5 = #{rightLevel5,jdbcType=INTEGER},
      </if>
      <if test="rightLevel6 != null">
        right_level_6 = #{rightLevel6,jdbcType=INTEGER},
      </if>
      <if test="rightLevel7 != null">
        right_level_7 = #{rightLevel7,jdbcType=INTEGER},
      </if>
      <if test="rightLevel8 != null">
        right_level_8 = #{rightLevel8,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling1 != null">
        vault_filling_1 = #{vaultFilling1,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling2 != null">
        vault_filling_2 = #{vaultFilling2,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling3 != null">
        vault_filling_3 = #{vaultFilling3,jdbcType=INTEGER},
      </if>
      <if test="vaultFilling4 != null">
        vault_filling_4 = #{vaultFilling4,jdbcType=INTEGER},
      </if>
      <if test="pourStatus != null">
        pour_status = #{pourStatus,jdbcType=INTEGER},
      </if>
      <if test="leftTplStatus != null">
        left_tpl_status = #{leftTplStatus,jdbcType=INTEGER},
      </if>
      <if test="rightTplStatus != null">
        right_tpl_status = #{rightTplStatus,jdbcType=INTEGER},
      </if>
      <if test="vaultTplStatus != null">
        vault_tpl_status = #{vaultTplStatus,jdbcType=INTEGER},
      </if>
      <if test="vaultPressure1 != null">
        vault_pressure_1 = #{vaultPressure1,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure2 != null">
        vault_pressure_2 = #{vaultPressure2,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure3 != null">
        vault_pressure_3 = #{vaultPressure3,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure4 != null">
        vault_pressure_4 = #{vaultPressure4,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure5 != null">
        vault_pressure_5 = #{vaultPressure5,jdbcType=DOUBLE},
      </if>
      <if test="vaultPressure6 != null">
        vault_pressure_6 = #{vaultPressure6,jdbcType=DOUBLE},
      </if>
      <if test="envTemp != null">
        env_temp = #{envTemp,jdbcType=DOUBLE},
      </if>
      <if test="pourPlateNum != null">
        pour_plate_num = #{pourPlateNum,jdbcType=INTEGER},
      </if>
      <if test="pourVolume != null">
        pour_volume = #{pourVolume,jdbcType=DOUBLE},
      </if>
      <if test="totalPourVolume != null">
        total_pour_volume = #{totalPourVolume,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDeviceDataByDeviceId" resultType="com.whfc.uni.dto.trolley.TrolleyLiningDeviceDataDTO">
    select
    <include refid="Base_Column_List">
    </include>
    from trolley_lining_device_data
    where device_id = #{deviceId,jdbcType=INTEGER}
  </select>

  <select id="selectByDeviceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from trolley_lining_device_data
     where device_id = #{deviceId,jdbcType=INTEGER}
  </select>

</mapper>
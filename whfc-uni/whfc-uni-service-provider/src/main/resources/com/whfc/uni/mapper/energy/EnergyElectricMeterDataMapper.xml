<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.energy.EnergyElectricMeterDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.energy.EnergyElectricMeterData">
    <!--@mbg.generated-->
    <!--@Table energy_electric_meter_data-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="rpe_id" jdbcType="INTEGER" property="rpeId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="positive_active" jdbcType="DOUBLE" property="positiveActive" />
    <result column="reverse_active" jdbcType="DOUBLE" property="reverseActive" />
    <result column="positive_reactive" jdbcType="DOUBLE" property="positiveReactive" />
    <result column="reverse_reactive" jdbcType="DOUBLE" property="reverseReactive" />
    <result column="abc_voltage" jdbcType="DOUBLE" property="abcVoltage" />
    <result column="abc_current" jdbcType="DOUBLE" property="abcCurrent" />
    <result column="abc_positive_active" jdbcType="DOUBLE" property="abcPositiveActive" />
    <result column="abc_reverse_active" jdbcType="DOUBLE" property="abcReverseActive" />
    <result column="a_current" jdbcType="DOUBLE" property="aCurrent" />
    <result column="b_current" jdbcType="DOUBLE" property="bCurrent" />
    <result column="c_current" jdbcType="DOUBLE" property="cCurrent" />
    <result column="a_voltage" jdbcType="DOUBLE" property="aVoltage" />
    <result column="b_voltage" jdbcType="DOUBLE" property="bVoltage" />
    <result column="c_cvoltage" jdbcType="DOUBLE" property="cCvoltage" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, rpe_id, device_id, `time`, positive_active, reverse_active, positive_reactive, 
    reverse_reactive, abc_voltage, abc_current, abc_positive_active, abc_reverse_active, 
    a_current, b_current, c_current, a_voltage, b_voltage, c_cvoltage, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from energy_electric_meter_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from energy_electric_meter_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.energy.EnergyElectricMeterData" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into energy_electric_meter_data (dept_id, rpe_id, device_id, 
      `time`, positive_active, reverse_active, 
      positive_reactive, reverse_reactive, abc_voltage, 
      abc_current, abc_positive_active, abc_reverse_active, 
      a_current, b_current, c_current, 
      a_voltage, b_voltage, c_cvoltage, 
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{rpeId,jdbcType=INTEGER}, #{deviceId,jdbcType=INTEGER}, 
      #{time,jdbcType=TIMESTAMP}, #{positiveActive,jdbcType=DOUBLE}, #{reverseActive,jdbcType=DOUBLE}, 
      #{positiveReactive,jdbcType=DOUBLE}, #{reverseReactive,jdbcType=DOUBLE}, #{abcVoltage,jdbcType=DOUBLE}, 
      #{abcCurrent,jdbcType=DOUBLE}, #{abcPositiveActive,jdbcType=DOUBLE}, #{abcReverseActive,jdbcType=DOUBLE}, 
      #{aCurrent,jdbcType=DOUBLE}, #{bCurrent,jdbcType=DOUBLE}, #{cCurrent,jdbcType=DOUBLE}, 
      #{aVoltage,jdbcType=DOUBLE}, #{bVoltage,jdbcType=DOUBLE}, #{cCvoltage,jdbcType=DOUBLE}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.energy.EnergyElectricMeterData" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into energy_electric_meter_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="rpeId != null">
        rpe_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="positiveActive != null">
        positive_active,
      </if>
      <if test="reverseActive != null">
        reverse_active,
      </if>
      <if test="positiveReactive != null">
        positive_reactive,
      </if>
      <if test="reverseReactive != null">
        reverse_reactive,
      </if>
      <if test="abcVoltage != null">
        abc_voltage,
      </if>
      <if test="abcCurrent != null">
        abc_current,
      </if>
      <if test="abcPositiveActive != null">
        abc_positive_active,
      </if>
      <if test="abcReverseActive != null">
        abc_reverse_active,
      </if>
      <if test="aCurrent != null">
        a_current,
      </if>
      <if test="bCurrent != null">
        b_current,
      </if>
      <if test="cCurrent != null">
        c_current,
      </if>
      <if test="aVoltage != null">
        a_voltage,
      </if>
      <if test="bVoltage != null">
        b_voltage,
      </if>
      <if test="cCvoltage != null">
        c_cvoltage,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="rpeId != null">
        #{rpeId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="positiveActive != null">
        #{positiveActive,jdbcType=DOUBLE},
      </if>
      <if test="reverseActive != null">
        #{reverseActive,jdbcType=DOUBLE},
      </if>
      <if test="positiveReactive != null">
        #{positiveReactive,jdbcType=DOUBLE},
      </if>
      <if test="reverseReactive != null">
        #{reverseReactive,jdbcType=DOUBLE},
      </if>
      <if test="abcVoltage != null">
        #{abcVoltage,jdbcType=DOUBLE},
      </if>
      <if test="abcCurrent != null">
        #{abcCurrent,jdbcType=DOUBLE},
      </if>
      <if test="abcPositiveActive != null">
        #{abcPositiveActive,jdbcType=DOUBLE},
      </if>
      <if test="abcReverseActive != null">
        #{abcReverseActive,jdbcType=DOUBLE},
      </if>
      <if test="aCurrent != null">
        #{aCurrent,jdbcType=DOUBLE},
      </if>
      <if test="bCurrent != null">
        #{bCurrent,jdbcType=DOUBLE},
      </if>
      <if test="cCurrent != null">
        #{cCurrent,jdbcType=DOUBLE},
      </if>
      <if test="aVoltage != null">
        #{aVoltage,jdbcType=DOUBLE},
      </if>
      <if test="bVoltage != null">
        #{bVoltage,jdbcType=DOUBLE},
      </if>
      <if test="cCvoltage != null">
        #{cCvoltage,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.energy.EnergyElectricMeterData">
    <!--@mbg.generated-->
    update energy_electric_meter_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="rpeId != null">
        rpe_id = #{rpeId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="positiveActive != null">
        positive_active = #{positiveActive,jdbcType=DOUBLE},
      </if>
      <if test="reverseActive != null">
        reverse_active = #{reverseActive,jdbcType=DOUBLE},
      </if>
      <if test="positiveReactive != null">
        positive_reactive = #{positiveReactive,jdbcType=DOUBLE},
      </if>
      <if test="reverseReactive != null">
        reverse_reactive = #{reverseReactive,jdbcType=DOUBLE},
      </if>
      <if test="abcVoltage != null">
        abc_voltage = #{abcVoltage,jdbcType=DOUBLE},
      </if>
      <if test="abcCurrent != null">
        abc_current = #{abcCurrent,jdbcType=DOUBLE},
      </if>
      <if test="abcPositiveActive != null">
        abc_positive_active = #{abcPositiveActive,jdbcType=DOUBLE},
      </if>
      <if test="abcReverseActive != null">
        abc_reverse_active = #{abcReverseActive,jdbcType=DOUBLE},
      </if>
      <if test="aCurrent != null">
        a_current = #{aCurrent,jdbcType=DOUBLE},
      </if>
      <if test="bCurrent != null">
        b_current = #{bCurrent,jdbcType=DOUBLE},
      </if>
      <if test="cCurrent != null">
        c_current = #{cCurrent,jdbcType=DOUBLE},
      </if>
      <if test="aVoltage != null">
        a_voltage = #{aVoltage,jdbcType=DOUBLE},
      </if>
      <if test="bVoltage != null">
        b_voltage = #{bVoltage,jdbcType=DOUBLE},
      </if>
      <if test="cCvoltage != null">
        c_cvoltage = #{cCvoltage,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.energy.EnergyElectricMeterData">
    <!--@mbg.generated-->
    update energy_electric_meter_data
    set dept_id = #{deptId,jdbcType=INTEGER},
      rpe_id = #{rpeId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=INTEGER},
      `time` = #{time,jdbcType=TIMESTAMP},
      positive_active = #{positiveActive,jdbcType=DOUBLE},
      reverse_active = #{reverseActive,jdbcType=DOUBLE},
      positive_reactive = #{positiveReactive,jdbcType=DOUBLE},
      reverse_reactive = #{reverseReactive,jdbcType=DOUBLE},
      abc_voltage = #{abcVoltage,jdbcType=DOUBLE},
      abc_current = #{abcCurrent,jdbcType=DOUBLE},
      abc_positive_active = #{abcPositiveActive,jdbcType=DOUBLE},
      abc_reverse_active = #{abcReverseActive,jdbcType=DOUBLE},
      a_current = #{aCurrent,jdbcType=DOUBLE},
      b_current = #{bCurrent,jdbcType=DOUBLE},
      c_current = #{cCurrent,jdbcType=DOUBLE},
      a_voltage = #{aVoltage,jdbcType=DOUBLE},
      b_voltage = #{bVoltage,jdbcType=DOUBLE},
      c_cvoltage = #{cCvoltage,jdbcType=DOUBLE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <insert id="insertOrUpdate">
      insert into energy_electric_meter_data
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="deptId != null">
          dept_id,
        </if>
        <if test="rpeId != null">
          rpe_id,
        </if>
        <if test="deviceId != null">
          device_id,
        </if>
        <if test="time != null">
          `time`,
        </if>
        <if test="positiveActive != null">
          positive_active,
        </if>
        <if test="reverseActive != null">
          reverse_active,
        </if>
        <if test="positiveReactive != null">
          positive_reactive,
        </if>
        <if test="reverseReactive != null">
          reverse_reactive,
        </if>
        <if test="abcVoltage != null">
          abc_voltage,
        </if>
        <if test="abcCurrent != null">
          abc_current,
        </if>
        <if test="abcPositiveActive != null">
          abc_positive_active,
        </if>
        <if test="abcReverseActive != null">
          abc_reverse_active,
        </if>
        <if test="aCurrent != null">
          a_current,
        </if>
        <if test="bCurrent != null">
          b_current,
        </if>
        <if test="cCurrent != null">
          c_current,
        </if>
        <if test="aVoltage != null">
          a_voltage,
        </if>
        <if test="bVoltage != null">
          b_voltage,
        </if>
        <if test="cCvoltage != null">
          c_cvoltage,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="deptId != null">
          #{deptId,jdbcType=INTEGER},
        </if>
        <if test="rpeId != null">
          #{rpeId,jdbcType=INTEGER},
        </if>
        <if test="deviceId != null">
          #{deviceId,jdbcType=INTEGER},
        </if>
        <if test="time != null">
          #{time,jdbcType=TIMESTAMP},
        </if>
        <if test="positiveActive != null">
          #{positiveActive,jdbcType=DOUBLE},
        </if>
        <if test="reverseActive != null">
          #{reverseActive,jdbcType=DOUBLE},
        </if>
        <if test="positiveReactive != null">
          #{positiveReactive,jdbcType=DOUBLE},
        </if>
        <if test="reverseReactive != null">
          #{reverseReactive,jdbcType=DOUBLE},
        </if>
        <if test="abcVoltage != null">
          #{abcVoltage,jdbcType=DOUBLE},
        </if>
        <if test="abcCurrent != null">
          #{abcCurrent,jdbcType=DOUBLE},
        </if>
        <if test="abcPositiveActive != null">
          #{abcPositiveActive,jdbcType=DOUBLE},
        </if>
        <if test="abcReverseActive != null">
          #{abcReverseActive,jdbcType=DOUBLE},
        </if>
        <if test="aCurrent != null">
          #{aCurrent,jdbcType=DOUBLE},
        </if>
        <if test="bCurrent != null">
          #{bCurrent,jdbcType=DOUBLE},
        </if>
        <if test="cCurrent != null">
          #{cCurrent,jdbcType=DOUBLE},
        </if>
        <if test="aVoltage != null">
          #{aVoltage,jdbcType=DOUBLE},
        </if>
        <if test="bVoltage != null">
          #{bVoltage,jdbcType=DOUBLE},
        </if>
        <if test="cCvoltage != null">
          #{cCvoltage,jdbcType=DOUBLE},
        </if>
      </trim>
      on duplicate key update
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="rpeId != null">
        rpe_id = #{rpeId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="positiveActive != null">
        positive_active = #{positiveActive,jdbcType=DOUBLE},
      </if>
      <if test="reverseActive != null">
        reverse_active = #{reverseActive,jdbcType=DOUBLE},
      </if>
      <if test="positiveReactive != null">
        positive_reactive = #{positiveReactive,jdbcType=DOUBLE},
      </if>
      <if test="reverseReactive != null">
        reverse_reactive = #{reverseReactive,jdbcType=DOUBLE},
      </if>
      <if test="abcVoltage != null">
        abc_voltage = #{abcVoltage,jdbcType=DOUBLE},
      </if>
      <if test="abcCurrent != null">
        abc_current = #{abcCurrent,jdbcType=DOUBLE},
      </if>
      <if test="abcPositiveActive != null">
        abc_positive_active = #{abcPositiveActive,jdbcType=DOUBLE},
      </if>
      <if test="abcReverseActive != null">
        abc_reverse_active = #{abcReverseActive,jdbcType=DOUBLE},
      </if>
      <if test="aCurrent != null">
        a_current = #{aCurrent,jdbcType=DOUBLE},
      </if>
      <if test="bCurrent != null">
        b_current = #{bCurrent,jdbcType=DOUBLE},
      </if>
      <if test="cCurrent != null">
        c_current = #{cCurrent,jdbcType=DOUBLE},
      </if>
      <if test="aVoltage != null">
        a_voltage = #{aVoltage,jdbcType=DOUBLE},
      </if>
      <if test="bVoltage != null">
        b_voltage = #{bVoltage,jdbcType=DOUBLE},
      </if>
      <if test="cCvoltage != null">
        c_cvoltage = #{cCvoltage,jdbcType=DOUBLE},
      </if>
      device_id = #{deviceId,jdbcType=INTEGER}
    </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.trolley.TrolleyMaintainDeviceDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.trolley.TrolleyMaintainDeviceData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="forward1" jdbcType="INTEGER" property="forward1" />
    <result column="backward1" jdbcType="INTEGER" property="backward1" />
    <result column="forward2" jdbcType="INTEGER" property="forward2" />
    <result column="backward2" jdbcType="INTEGER" property="backward2" />
    <result column="maintain" jdbcType="INTEGER" property="maintain" />
    <result column="env_rh" jdbcType="DOUBLE" property="envRh" />
    <result column="env_temp" jdbcType="DOUBLE" property="envTemp" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    device_id,
    `time`,
    forward1,
    backward1,
    forward2,
    backward2,
    maintain,
    env_rh,
    env_temp,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trolley_maintain_device_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from trolley_maintain_device_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.trolley.TrolleyMaintainDeviceData">
    insert into trolley_maintain_device_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="forward1 != null">
        forward1,
      </if>
      <if test="backward1 != null">
        backward1,
      </if>
      <if test="forward2 != null">
        forward2,
      </if>
      <if test="backward2 != null">
        backward2,
      </if>
      <if test="maintain != null">
        maintain,
      </if>
      <if test="envRh != null">
        env_rh,
      </if>
      <if test="envTemp != null">
        env_temp,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="forward1 != null">
        #{forward1,jdbcType=INTEGER},
      </if>
      <if test="backward1 != null">
        #{backward1,jdbcType=INTEGER},
      </if>
      <if test="forward2 != null">
        #{forward2,jdbcType=INTEGER},
      </if>
      <if test="backward2 != null">
        #{backward2,jdbcType=INTEGER},
      </if>
      <if test="maintain != null">
        #{maintain,jdbcType=INTEGER},
      </if>
      <if test="envRh != null">
        #{envRh,jdbcType=DOUBLE},
      </if>
      <if test="envTemp != null">
        #{envTemp,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.trolley.TrolleyMaintainDeviceData">
    update trolley_maintain_device_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="forward1 != null">
        forward1 = #{forward1,jdbcType=INTEGER},
      </if>
      <if test="backward1 != null">
        backward1 = #{backward1,jdbcType=INTEGER},
      </if>
      <if test="forward2 != null">
        forward2 = #{forward2,jdbcType=INTEGER},
      </if>
      <if test="backward2 != null">
        backward2 = #{backward2,jdbcType=INTEGER},
      </if>
      <if test="maintain != null">
        maintain = #{maintain,jdbcType=INTEGER},
      </if>
      <if test="envRh != null">
        env_rh = #{envRh,jdbcType=DOUBLE},
      </if>
      <if test="envTemp != null">
        env_temp = #{envTemp,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDeviceDataByDeviceId" resultType="com.whfc.uni.dto.trolley.TrolleyMaintainDeviceDataDTO">
    select
    <include refid="Base_Column_List">
    </include>
    from trolley_maintain_device_data
    where device_id = #{deviceId,jdbcType=INTEGER}
  </select>

  <select id="selectByDeviceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from trolley_maintain_device_data
    where device_id = #{deviceId,jdbcType=INTEGER}
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.permit.LiftingPermitMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.permit.LiftingPermit">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="other" jdbcType="VARCHAR" property="other" />
    <result column="enable_flag" jdbcType="INTEGER" property="enableFlag" />
    <result column="lifting_weight" jdbcType="DOUBLE" property="liftingWeight" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="sling_worker" jdbcType="VARCHAR" property="slingWorker" />
    <result column="card_1" jdbcType="VARCHAR" property="card1" />
    <result column="card_2" jdbcType="VARCHAR" property="card2" />
    <result column="danger_other" jdbcType="VARCHAR" property="dangerOther"/>
    <result column="commit_time" jdbcType="TIMESTAMP" property="commitTime" />
    <result column="commit_user_id" jdbcType="INTEGER" property="commitUserId" />
    <result column="commit_user_name" jdbcType="VARCHAR" property="commitUserName" />
    <result column="commit_sign" jdbcType="VARCHAR" property="commitSign" />
    <result column="commit_position" jdbcType="VARCHAR" property="commitPosition" />
    <result column="issue_time" jdbcType="TIMESTAMP" property="issueTime" />
    <result column="issue_user_id" jdbcType="INTEGER" property="issueUserId" />
    <result column="issue_user_name" jdbcType="VARCHAR" property="issueUserName" />
    <result column="issue_sign" jdbcType="VARCHAR" property="issueSign" />
    <result column="issue_position" jdbcType="VARCHAR" property="issuePosition" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    guid,
    `date`,
    location,
    specification,
    model,
    other,
    enable_flag,
    lifting_weight,
    operator,
    sling_worker,
    card_1,
    card_2,
    danger_other,
    commit_time,
    commit_user_id,
    commit_user_name,
    commit_sign,
    commit_position,
    issue_time,
    issue_user_id,
    issue_user_name,
    issue_sign,
    issue_position,
    `state`,
    detail,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lifting_permit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lifting_permit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.permit.LiftingPermit" useGeneratedKeys="true" keyProperty="id">
    insert into lifting_permit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="other != null">
        other,
      </if>
      <if test="enableFlag != null">
        enable_flag,
      </if>
      <if test="liftingWeight != null">
        lifting_weight,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="slingWorker != null">
        sling_worker,
      </if>
      <if test="card1 != null">
        card_1,
      </if>
      <if test="card2 != null">
        card_2,
      </if>
      <if test="dangerOther != null">
        danger_other,
      </if>
      <if test="commitTime != null">
        commit_time,
      </if>
      <if test="commitUserId != null">
        commit_user_id,
      </if>
      <if test="commitUserName != null">
        commit_user_name,
      </if>
      <if test="commitSign != null">
        commit_sign,
      </if>
      <if test="commitPosition != null">
        commit_position,
      </if>
      <if test="issueTime != null">
        issue_time,
      </if>
      <if test="issueUserId != null">
        issue_user_id,
      </if>
      <if test="issueUserName != null">
        issue_user_name,
      </if>
      <if test="issueSign != null">
        issue_sign,
      </if>
      <if test="issuePosition != null">
        issue_position,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="other != null">
        #{other,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="liftingWeight != null">
        #{liftingWeight,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="slingWorker != null">
        #{slingWorker,jdbcType=VARCHAR},
      </if>
      <if test="card1 != null">
        #{card1,jdbcType=VARCHAR},
      </if>
      <if test="card2 != null">
        #{card2,jdbcType=VARCHAR},
      </if>
      <if test="dangerOther != null">
        #{dangerOther,jdbcType=VARCHAR},
      </if>
      <if test="commitTime != null">
        #{commitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitUserId != null">
        #{commitUserId,jdbcType=INTEGER},
      </if>
      <if test="commitUserName != null">
        #{commitUserName,jdbcType=VARCHAR},
      </if>
      <if test="commitSign != null">
        #{commitSign,jdbcType=VARCHAR},
      </if>
      <if test="commitPosition != null">
        #{commitPosition,jdbcType=VARCHAR},
      </if>
      <if test="issueTime != null">
        #{issueTime,jdbcType=TIMESTAMP},
      </if>
      <if test="issueUserId != null">
        #{issueUserId,jdbcType=INTEGER},
      </if>
      <if test="issueUserName != null">
        #{issueUserName,jdbcType=VARCHAR},
      </if>
      <if test="issueSign != null">
        #{issueSign,jdbcType=VARCHAR},
      </if>
      <if test="issuePosition != null">
        #{issuePosition,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.permit.LiftingPermit">
    update lifting_permit
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="other != null">
        other = #{other,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        enable_flag = #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="liftingWeight != null">
        lifting_weight = #{liftingWeight,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="slingWorker != null">
        sling_worker = #{slingWorker,jdbcType=VARCHAR},
      </if>
      <if test="card1 != null">
        card_1 = #{card1,jdbcType=VARCHAR},
      </if>
      <if test="card2 != null">
        card_2 = #{card2,jdbcType=VARCHAR},
      </if>
      <if test="dangerOther != null">
        danger_other = #{dangerOther,jdbcType=VARCHAR},
      </if>
      <if test="commitTime != null">
        commit_time = #{commitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitUserId != null">
        commit_user_id = #{commitUserId,jdbcType=INTEGER},
      </if>
      <if test="commitUserName != null">
        commit_user_name = #{commitUserName,jdbcType=VARCHAR},
      </if>
      <if test="commitSign != null">
        commit_sign = #{commitSign,jdbcType=VARCHAR},
      </if>
      <if test="commitPosition != null">
        commit_position = #{commitPosition,jdbcType=VARCHAR},
      </if>
      <if test="issueTime != null">
        issue_time = #{issueTime,jdbcType=TIMESTAMP},
      </if>
      <if test="issueUserId != null">
        issue_user_id = #{issueUserId,jdbcType=INTEGER},
      </if>
      <if test="issueUserName != null">
        issue_user_name = #{issueUserName,jdbcType=VARCHAR},
      </if>
      <if test="issueSign != null">
        issue_sign = #{issueSign,jdbcType=VARCHAR},
      </if>
      <if test="issuePosition != null">
        issue_position = #{issuePosition,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectPermitList" resultType="com.whfc.uni.dto.permit.LiftingPermitDTO">
    select  id as permitId,
            guid,
            `date`,
            location,
            specification,
            model,
            other,
            enable_flag,
            lifting_weight,
            operator,
            sling_worker,
            card_1,
            card_2,
            danger_other,
            commit_time,
            commit_user_id,
            commit_user_name,
            commit_sign,
            commit_position,
            issue_time,
            issue_user_id,
            issue_user_name,
            issue_sign,
            issue_position,
            `state`,
            detail
      from lifting_permit
     where dept_id = #{deptId}
       and del_flag = 0
    <if test="keyword!=null and keyword.length()>0">
      and (operator like concat('%',#{keyword},'%')
          or sling_worker like concat('%',#{keyword},'%')
          or specification like concat('%',#{keyword},'%')
          or model like concat('%',#{keyword},'%')
          or location like concat('%',#{keyword},'%')
          )
    </if>
    <if test="startDate!=null and endDate!=null">
      and date between date(#{startDate}) and date(#{endDate})
    </if>
    order by id desc
  </select>

  <select id="selectPermitDetail" resultType="com.whfc.uni.dto.permit.LiftingPermitDTO">
    select  id as permitId,
            guid,
            `date`,
            location,
            specification,
            model,
            other,
            enable_flag,
            lifting_weight,
            operator,
            sling_worker,
            card_1,
            card_2,
            danger_other,
            commit_time,
            commit_user_id,
            commit_user_name,
            commit_sign,
            commit_position,
            issue_time,
            issue_user_id,
            issue_user_name,
            issue_sign,
            issue_position,
            `state`,
            detail
       from lifting_permit
      where id = #{id}
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from lifting_permit
    where guid = #{guid,jdbcType=VARCHAR}
  </select>

  <update id="logicDeleteByPrimaryKey">
    update lifting_permit
    set del_flag = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
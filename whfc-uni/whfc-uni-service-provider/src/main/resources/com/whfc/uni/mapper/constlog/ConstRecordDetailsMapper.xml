<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.constlog.ConstRecordDetailsMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.constlog.ConstRecordDetails">
        <!--@mbg.generated-->
        <!--@Table const_record_details-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="corp_id" jdbcType="INTEGER" property="corpId"/>
        <result column="corp_name" jdbcType="VARCHAR" property="corpName"/>
        <result column="obj_id" jdbcType="INTEGER" property="objId"/>
        <result column="obj_name" jdbcType="VARCHAR" property="objName"/>
        <result column="total_number" jdbcType="INTEGER" property="totalNumber"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, record_id, `type`, corp_id, corp_name, obj_id, obj_name, total_number,
        del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from const_record_details
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from const_record_details
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.whfc.uni.entity.constlog.ConstRecordDetails">
        <!--@mbg.generated-->
        insert into const_record_details (id, dept_id, record_id,
                                          `type`, corp_id, corp_name,
                                          obj_id, obj_name, total_number,
                                          del_flag, update_time, create_time)
        values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{recordId,jdbcType=INTEGER},
                #{type,jdbcType=INTEGER}, #{corpId,jdbcType=INTEGER}, #{corpName,jdbcType=VARCHAR},
                #{objId,jdbcType=INTEGER}, #{objName,jdbcType=VARCHAR}, #{totalNumber,jdbcType=INTEGER},
                #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.whfc.uni.entity.constlog.ConstRecordDetails">
        <!--@mbg.generated-->
        insert into const_record_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="recordId != null">
                record_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="corpName != null">
                corp_name,
            </if>
            <if test="objId != null">
                obj_id,
            </if>
            <if test="objName != null">
                obj_name,
            </if>
            <if test="totalNumber != null">
                total_number,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="recordId != null">
                #{recordId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="objId != null">
                #{objId,jdbcType=INTEGER},
            </if>
            <if test="objName != null">
                #{objName,jdbcType=VARCHAR},
            </if>
            <if test="totalNumber != null">
                #{totalNumber,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.constlog.ConstRecordDetails">
        <!--@mbg.generated-->
        update const_record_details
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="recordId != null">
                record_id = #{recordId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="objId != null">
                obj_id = #{objId,jdbcType=INTEGER},
            </if>
            <if test="objName != null">
                obj_name = #{objName,jdbcType=VARCHAR},
            </if>
            <if test="totalNumber != null">
                total_number = #{totalNumber,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.uni.entity.constlog.ConstRecordDetails">
        <!--@mbg.generated-->
        update const_record_details
        set dept_id      = #{deptId,jdbcType=INTEGER},
            record_id    = #{recordId,jdbcType=INTEGER},
            `type`       = #{type,jdbcType=INTEGER},
            corp_id      = #{corpId,jdbcType=INTEGER},
            corp_name    = #{corpName,jdbcType=VARCHAR},
            obj_id       = #{objId,jdbcType=INTEGER},
            obj_name     = #{objName,jdbcType=VARCHAR},
            total_number = #{totalNumber,jdbcType=INTEGER},
            del_flag     = #{delFlag,jdbcType=INTEGER},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            create_time  = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <insert id="batchInsert">
        insert into const_record_details
        (
            dept_id,
            record_id,
            `type`,
            corp_id,
            corp_name,
            obj_id,
            obj_name,
            total_number
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
            #{deptId,jdbcType=INTEGER},
            #{recordId,jdbcType=INTEGER},
            #{item.type,jdbcType=INTEGER},
            #{item.corpId,jdbcType=INTEGER},
            #{item.corpName,jdbcType=VARCHAR},
            #{item.objId,jdbcType=INTEGER},
            #{item.objName,jdbcType=VARCHAR},
            #{item.totalNumber,jdbcType=INTEGER}
        )
        </foreach>
    </insert>

    <update id="deleteByRecordId">
        update const_record_details
        set del_flag = 1
        where record_id = #{recordId}
          and del_flag = 0
    </update>

    <select id="selectSummaryDeptId" resultType="com.whfc.uni.dto.constlog.ConstRecordTypeSummaryDTO">
         select crd.record_id,
                crd.`type`,
                crd.corp_id,
                crd.corp_name,
                crd.obj_id,
                crd.obj_name,
                sum(crd.total_number) as total_number
        from const_record_details crd
        inner join const_record  cr on cr.id = crd.record_id and cr.del_flag = 0
        where crd.dept_id = #{deptId}
        <if test="type != null">
            and crd.`type` = #{type}
        </if>
        <if test="startTime != null and endTime != null">
            and cr.time BETWEEN #{startTime} and #{endTime}
        </if>
        and crd.del_flag = 0
        group by crd.corp_id, crd.obj_id
    </select>

    <select id="selectByRecordId" resultType="com.whfc.uni.dto.constlog.ConstRecordDetailsDTO">
        select `type`,
               corp_id,
               corp_name,
               obj_id,
               obj_name,
               total_number
        from const_record_details
        where record_id = #{deptId}
          and del_flag = 0
    </select>

    <select id="selectByRecordIdList" resultType="com.whfc.uni.dto.constlog.ConstRecordDetailsDTO">
        select record_id,
               `type`,
               corp_id,
               corp_name,
               obj_id,
               obj_name,
               total_number
        from const_record_details
        where del_flag = 0
         and record_id in (
        <foreach collection="recordIdList" item="recordId" separator=",">
            #{recordId}
        </foreach>
        )
    </select>
</mapper>
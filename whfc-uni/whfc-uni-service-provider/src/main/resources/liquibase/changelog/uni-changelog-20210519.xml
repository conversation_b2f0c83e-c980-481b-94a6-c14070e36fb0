<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>

    <changeSet id="1" author="hw">
        <comment>创建表</comment>
        <sql>
            CREATE TABLE `tunnel_part` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NOT NULL COMMENT '组织机构id',
            `part_name` varchar(32) NOT NULL COMMENT '名称',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道部位';

            CREATE TABLE `tunnel_vault_stake` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `part_id` int NOT NULL COMMENT '检查对象id',
            `stake_no` varchar(32) NULL DEFAULT NULL COMMENT '里程桩号',
            `instrument` varchar(32) NULL DEFAULT NULL COMMENT '仪器',
            `utility_method` varchar(32) NULL DEFAULT NULL COMMENT '工法',
            `benchmark_max` varchar(32) NULL DEFAULT NULL COMMENT '水准点高程',
            `point_time` datetime(0) NULL DEFAULT NULL COMMENT '测点埋设时间',
            `check_start_time` datetime(0) NULL DEFAULT NULL COMMENT '检测起始时间',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道部位-拱顶-桩号';

            CREATE TABLE `tunnel_clearance_stake` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `part_id` int NOT NULL COMMENT '检查对象id',
            `stake_no` varchar(32) NULL DEFAULT NULL COMMENT '里程桩号',
            `test_stake_no` varchar(32) NULL DEFAULT NULL COMMENT '测试断面桩号',
            `instrument` varchar(32) NULL DEFAULT NULL COMMENT '仪器',
            `temp_amend` varchar(32) NULL DEFAULT NULL COMMENT '温度修正值',
            `temp_amend_coefficient` varchar(32) NULL DEFAULT NULL COMMENT '温度修正系数',
            `setting_time` datetime(0) NULL DEFAULT NULL COMMENT '布设时间',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道部位-净空-桩号';


            CREATE TABLE `tunnel_stake_vault_day` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `stake_id` int NOT NULL COMMENT '里程桩id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
            `backsight_value` double NULL DEFAULT NULL COMMENT '后视读数',
            `forward_value` double NULL DEFAULT NULL COMMENT '前视读数',
            `height_diff` double NULL DEFAULT NULL COMMENT '高差',
            `single_sink_value` double NULL DEFAULT NULL COMMENT '单次下沉量',
            `single_sink_total` double NULL DEFAULT NULL COMMENT '累计下沉量',
            `rate_change` double NULL DEFAULT NULL COMMENT '变化速率',
            `mgmt_level` varchar(32) NULL DEFAULT NULL COMMENT '管理等级',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道部位-拱顶-桩号-每日数据';

            CREATE TABLE `tunnel_stake_clearance_day` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `stake_id` int NOT NULL COMMENT '里程桩id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
            `temp_correct_t` double NULL DEFAULT NULL COMMENT '温度修正T',
            `temp_correct_ditt` double NULL DEFAULT NULL COMMENT '温度修正t',
            `temp_correct_rt` double NULL DEFAULT NULL COMMENT '温度修正Rt',
            `ruler_hole_value` double NULL DEFAULT NULL COMMENT '钢尺孔位读数',
            `display_value1` double NULL DEFAULT NULL COMMENT '显示器读数1',
            `display_value2` double NULL DEFAULT NULL COMMENT '显示器读数2',
            `display_value3` double NULL DEFAULT NULL COMMENT '显示器读数3',
            `display_value_avg` double NULL DEFAULT NULL COMMENT '显示器读数平均',
            `modify_value` double NULL DEFAULT NULL COMMENT '修正后贯彻值',
            `convergence` double NULL DEFAULT NULL COMMENT '本次收敛',
            `convergence_total` double NULL DEFAULT NULL COMMENT '总收敛值',
            `convergence_rate` double NULL DEFAULT NULL COMMENT '收敛速率',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道部位-净空-桩号-每日数据';
        </sql>
    </changeSet>

    <changeSet id="2" author="hw">
        <comment>修改表名</comment>
        <sql>
            RENAME TABLE `tunnel_stake_vault_day` TO `tunnel_vault_stake_day`;
            RENAME TABLE `tunnel_stake_clearance_day` TO `tunnel_clearance_stake_day`;
        </sql>
    </changeSet>

    <changeSet id="3" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `tunnel_safety_interval` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NULL DEFAULT NULL COMMENT '组织机构id',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '隧道设备名称',
            `distance` double NULL DEFAULT NULL COMMENT '距离',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道安全布局';

            CREATE TABLE `tunnel_safety_interval_log` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NULL DEFAULT NULL COMMENT '组织机构id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
            `distance` double NULL DEFAULT NULL COMMENT '距离',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道安全布局历史记录';
        </sql>
    </changeSet>

    <changeSet id="4" author="hw">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tunnel_card_log` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NULL DEFAULT NULL COMMENT '组织机构id',
            `time` timestamp(0) NULL DEFAULT NULL COMMENT '时间',
            `sn` varchar(255) NULL DEFAULT NULL COMMENT '卡号',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '姓名',
            `group_name` varchar(32) NULL DEFAULT NULL COMMENT '部门',
            `work_name` varchar(32) NULL DEFAULT NULL COMMENT '职务',
            `hole` varchar(255) NULL DEFAULT NULL COMMENT '左右洞（1-左洞；2-右洞）',
            `distance` double NULL DEFAULT NULL COMMENT '距离',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '隧道人员历史定位' ;

            CREATE TABLE `tunnel_card_data` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NULL DEFAULT NULL COMMENT '组织机构id',
            `time` timestamp(0) NULL DEFAULT NULL COMMENT '时间',
            `sn` varchar(255) NULL DEFAULT NULL COMMENT '卡号',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '姓名',
            `group_name` varchar(32) NULL DEFAULT NULL COMMENT '部门',
            `work_name` varchar(32) NULL DEFAULT NULL COMMENT '职务',
            `hole` varchar(255) NULL DEFAULT NULL COMMENT '左右洞（1-左洞；2-右洞）',
            `distance` double NULL DEFAULT NULL COMMENT '距离',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '隧道人员定位';
        </sql>
    </changeSet>

    <changeSet id="3" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `lab_bd_info`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `bd_id` varchar(64) NOT NULL COMMENT '标段ID',
            `bd_name` varchar(64) NOT NULL COMMENT '标段名称',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '实验室-标段信息表';
        </sql>
    </changeSet>

    <changeSet id="5" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `tunnel_info`  (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `name` varchar(32)NULL DEFAULT NULL COMMENT '隧道名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '隧道信息';

            ALTER TABLE `tunnel_card_data`
            ADD COLUMN `tunnel_id` int NULL COMMENT '隧道id' AFTER `dept_id`;

            ALTER TABLE `tunnel_card_log`
            ADD COLUMN `tunnel_id` int NULL COMMENT '隧道id' AFTER `dept_id`;

            ALTER TABLE `tunnel_safety_interval`
            ADD COLUMN `tunnel_id` int NULL COMMENT '隧道id' AFTER `dept_id`;

            ALTER TABLE `tunnel_safety_interval_log`
            ADD COLUMN `tunnel_id` int NULL COMMENT '隧道id' AFTER `dept_id`;
        </sql>
    </changeSet>

    <changeSet id="6" author="hw">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `car_inout_record`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `name` varchar(20) NOT NULL COMMENT '车主名称',
            `license_plate` varchar(10) NOT NULL COMMENT '车牌号码',
            `device_name` varchar(20) NOT NULL COMMENT '设备名称',
            `direction` int(11) NOT NULL COMMENT '方向 (0-出,1-进)',
            `img_url` varchar(255) NULL COMMENT '车辆识别图片地址',
            `time` datetime(0) NOT NULL COMMENT '识别时间',
            `del_flag` int(255) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '车辆进出记录表';
        </sql>
    </changeSet>

    <changeSet id="7" author="qinzexing">
        <comment>新建管网表</comment>
        <sql>
            CREATE TABLE `pipe_info`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父节点',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `type` int(11) NOT NULL COMMENT '管道类型 1-雨水管  2-污水管',
            `name` varchar(30) NOT NULL COMMENT '管道名称',
            `start_wall_id` int(11) NOT NULL COMMENT '起始检查井ID',
            `end_wall_id` int(11) NOT NULL COMMENT '结束检查井ID',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0-未开始 1-已完成）',
            `pipe` multilinestring NOT NULL COMMENT '管道坐标信息',
            `length` int(11) NOT NULL DEFAULT 0 COMMENT '管道长度（单位：m）',
            `complete_length` int(11) NOT NULL DEFAULT 0 COMMENT '完成长度（单位：m）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '管网信息';


            CREATE TABLE `pipe_check_well`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `name` varchar(30) NOT NULL COMMENT '检查井名称',
            `lng` double NOT NULL COMMENT '经度',
            `lat` double NOT NULL COMMENT '纬度',
            `address` varchar(255) NULL COMMENT '位置信息',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '检查井';


            CREATE TABLE `pipe_work_record`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `pipe_id` int(11) NOT NULL COMMENT '管网ID',
            `time` datetime NOT NULL COMMENT '处理时间',
            `emp_group_id` int(11) NOT NULL COMMENT '班组ID',
            `emp_group_name` VARCHAR(30) NOT NULL COMMENT '班组名称',
            `desc` varchar(255) NULL COMMENT '描述',
            `user_id` int(11) NOT NULL COMMENT '操作人',
            `user_name` varchar(30) NOT NULL COMMENT '操作人姓名',
            `user_phone` varchar(15) NOT NULL COMMENT '操作人电话',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '管网施工记录';


            CREATE TABLE `pipe_work_record_img`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `record_id` int(11) NOT NULL COMMENT '施工记录ID',
            `img_url` varchar(255) NOT NULL COMMENT '图片地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '管网施工记录图片';


            CREATE TABLE `pipe_work_area`  (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `pipe_id` int NOT NULL COMMENT '管网ID',
            `work_area_id` int NOT NULL COMMENT '工区ID',
            `work_area_name` varchar(30) NOT NULL COMMENT '工区名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '管网工区绑定表';
        </sql>
    </changeSet>

    <changeSet id="8" author="hw">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `energy_water_meter`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `dept_id` int NULL COMMENT '组织机构id',
            `name` varchar(64) NULL DEFAULT NULL COMMENT '设备名称',
            `device_id` int NULL DEFAULT NULL COMMENT '设备id',
            `sn` varchar(32) NULL DEFAULT NULL COMMENT 'sn',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT '水表设备基本信息';

            CREATE TABLE `energy_water_meter_data`  (
            `id` int NOT NULL,
            `dept_id` int NULL COMMENT '组织机构id',
            `water_id` int NULL DEFAULT NULL COMMENT '水表id',
            `device_id` int NULL DEFAULT NULL COMMENT '设备id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
            `dosage` double NULL DEFAULT NULL COMMENT '用量',
            `switch_state` int NULL DEFAULT NULL COMMENT '阀门状态（1-开，2-关）',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_water_id`(`water_id`) USING BTREE
            )COMMENT '水表设备最新数据';

            CREATE TABLE `energy_water_meter_day` (
            `id` int NOT NULL,
            `dept_id` int NULL COMMENT '组织机构id',
            `water_id` int NULL DEFAULT NULL COMMENT '水表id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
            `total_dosage` double NULL DEFAULT NULL COMMENT '用量',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_water_id_time`(`water_id`,`time`) USING BTREE
            )COMMENT '水表设备每日数据统计';

            CREATE TABLE `energy_electric_meter`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `dept_id` int NULL COMMENT '组织机构id',
            `name` varchar(64) NULL DEFAULT NULL COMMENT '设备名称',
            `type` int  NULL DEFAULT NULL COMMENT '设备类型，1-单相电，2-三相电',
            `device_id` int NULL DEFAULT NULL COMMENT '设备id',
            `sn` varchar(32) NULL DEFAULT NULL COMMENT 'sn',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT '电表设备基本信息';

            CREATE TABLE `energy_electric_meter_data`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `dept_id` int NULL COMMENT '组织机构id',
            `rpe_id` int NULL DEFAULT NULL COMMENT '水表id',
            `device_id` int NULL DEFAULT NULL COMMENT '设备id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
            `positive_active` double NULL DEFAULT NULL COMMENT '正向有功电能',
            `reverse_active` double NULL DEFAULT NULL COMMENT '反向有功电能',
            `positive_reactive` double NULL DEFAULT NULL COMMENT '正向无功电能',
            `reverse_reactive` double NULL DEFAULT NULL COMMENT '反向无功电能',
            `abc_voltage` double NULL DEFAULT NULL COMMENT 'ABC三相电压',
            `abc_current` double NULL DEFAULT NULL COMMENT 'ABC三相电流',
            `abc_positive_active` double NULL DEFAULT NULL COMMENT 'ABC三相有功功率',
            `abc_reverse_active` double NULL DEFAULT NULL COMMENT 'ABC三相无功功率',
            `a_current` double NULL DEFAULT NULL COMMENT 'A相电流',
            `b_current` double NULL DEFAULT NULL COMMENT 'B相电流',
            `c_current` double NULL DEFAULT NULL COMMENT 'C相电流',
            `a_voltage` double NULL DEFAULT NULL COMMENT 'A相电压',
            `b_voltage` double NULL DEFAULT NULL COMMENT 'B相电压',
            `c_cvoltage` double NULL DEFAULT NULL COMMENT 'C相电压',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_rpe_id`(`rpe_id`) USING BTREE
            )COMMENT '电表设备最新数据';

            CREATE TABLE `energy_electric_meter_day`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `dept_id` int NULL COMMENT '组织机构id',
            `rpe_id` int NULL DEFAULT NULL COMMENT '水表id',
            `device_id` int NULL DEFAULT NULL COMMENT '设备id',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '上传时间',
            `total_positive_active` double NULL DEFAULT NULL COMMENT '正向有功总电能',
            `total_reverse_active` double NULL DEFAULT NULL COMMENT '反向有功总电能',
            `total_positive_reactive` double NULL DEFAULT NULL COMMENT '正向无功总电能',
            `total_reverse_reactive` double NULL DEFAULT NULL COMMENT '反向无功总电能',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_rpe_id_time`(`rpe_id`,`time`) USING BTREE
            )COMMENT '电表设备每日数据统计';
        </sql>
    </changeSet>

    <changeSet id="9" author="hw">
        <comment>添加表字段</comment>
        <sql>
            ALTER TABLE `energy_electric_meter`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `energy_water_meter_data`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `energy_electric_meter_day`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `energy_water_meter`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `energy_water_meter_data`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `energy_water_meter_day`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;

        </sql>
    </changeSet>

    <changeSet id="10" author="hw">
        <sql>
            ALTER TABLE `energy_electric_meter_day`
            MODIFY COLUMN `time` date NULL DEFAULT NULL COMMENT '上传时间' AFTER `device_id`;
            ALTER TABLE `energy_water_meter_day`
            MODIFY COLUMN `time` date NULL DEFAULT NULL COMMENT '上传时间' AFTER `water_id`;
        </sql>
    </changeSet>

    <changeSet id="11" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `settlement_point`  (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int NOT NULL COMMENT '组织机构ID',
            `pid` int NOT NULL DEFAULT 0 COMMENT '父节点ID',
            `pids` varchar(255) NOT NULL COMMENT '祖宗节点列表（以逗号分割）',
            `name` varchar(30) NOT NULL COMMENT '名称',
            `type` varchar(10) NOT NULL COMMENT '类型（PART-部位  POINT-监测点）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '沉降位移-点位';


            CREATE TABLE `settlement_point_log`  (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `point_id` int NOT NULL COMMENT '监测点ID',
            `no`varchar(30) NULL COMMENT '测点编号',
            `x` double NULL COMMENT 'x向变量(mm)',
            `y` double NULL COMMENT 'y向变量(mm)',
            `time` datetime NULL COMMENT '监测时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_index` (`point_id`, `no`, `x`, `y`, `time`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '沉降位移-点位-日志';
        </sql>
    </changeSet>

    <changeSet id="12" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `const_record_details`
            (
                `id`           int         NOT NULL COMMENT '主键',
                `dept_id`      int NULL DEFAULT NULL COMMENT '组织机构id',
                `record_id`    int NULL DEFAULT NULL COMMENT '施工记录id',
                `type`         int NULL DEFAULT NULL COMMENT '数据属性，1-劳动力信息，2-机械信息',
                `corp_id`      int NULL DEFAULT NULL COMMENT '合作单位id',
                `corp_name`    varchar(32) NOT NULL COMMENT '合作单位名称',
                `obj_id`       int NULL DEFAULT NULL COMMENT '工种/机械设备类型id',
                `obj_name`     varchar(255) NULL DEFAULT NULL COMMENT '工种/机械设备类型名称',
                `total_number` int NULL DEFAULT NULL COMMENT '数量',
                `del_flag`     int         NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '更新时间',
                `create_time`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '施工记录详情';

            CREATE TABLE `const_record_img`
            (
                `id`          int          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id`     int NULL DEFAULT NULL COMMENT '组织机构id',
                `record_id`   int          NOT NULL COMMENT '施工记录id',
                `img_url`     varchar(255) NOT NULL COMMENT '图片地址',
                `del_flag`    int          NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '施工记录图片';

            CREATE TABLE `const_record`
            (
                `id`          int NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`     int NULL DEFAULT NULL COMMENT '组织机构id',
                `part_name`   varchar(32) NULL DEFAULT NULL COMMENT '施工部位',
                `remark`      varchar(255) NULL DEFAULT NULL COMMENT '备注',
                `user_id`     int NULL DEFAULT NULL COMMENT '用户id',
                `user_name`   varchar(32) NULL DEFAULT NULL COMMENT '用户名称',
                `del_flag`    int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '施工记录信息';

            CREATE TABLE `const_outside_check`
            (
                `id`          int NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`     int NULL DEFAULT NULL COMMENT '组织机构id',
                `group_name`  varchar(32) NULL DEFAULT NULL COMMENT '检查部门',
                `check_type`  varchar(32) NULL DEFAULT NULL COMMENT '检查类型',
                `remark`      varchar(255) NULL DEFAULT NULL COMMENT '备注',
                `user_id`     int NULL DEFAULT NULL COMMENT '用户id',
                `user_name`   varchar(32) NULL DEFAULT NULL COMMENT '用户名称',
                `del_flag`    int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '外界检查信息';

            CREATE TABLE `const_outside_check_img`
            (
                `id`               int          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `deptId`           int NULL DEFAULT NULL COMMENT '组织机构id',
                `outside_check_id` int          NOT NULL COMMENT '施工记录id',
                `img_url`          varchar(255) NOT NULL COMMENT '图片地址',
                `del_flag`         int          NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
                `update_time`      datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '更新时间',
                `create_time`      datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '外界检查图片';
        </sql>
    </changeSet>

    <changeSet id="13" author="hw">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `const_record_details`
                MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST;
        </sql>
    </changeSet>

    <changeSet id="14" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `mon_info` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父ID',
            `name` varchar(64) NOT NULL COMMENT '名称',
            `point_num` int(11) NOT NULL DEFAULT '0' COMMENT '测点数量',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记:0-未删除,1-已删除',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线监测_监测信息';

            CREATE TABLE `mon_point` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `info_id` int(11) DEFAULT NULL COMMENT '监测信息ID',
            `data_type` int(11) DEFAULT NULL COMMENT '类型:1-基坑监测 2-高支模监测',
            `name` varchar(32) DEFAULT NULL COMMENT '监测项目名称',
            `platform` varchar(32) NOT NULL COMMENT '硬件数据平台:wonhere-华和',
            `sn` varchar(32) NOT NULL COMMENT '硬件SN',
            `dev_type` int(11) DEFAULT NULL COMMENT '监测类型',
            `mon_type` int(11) DEFAULT NULL COMMENT '传感器类型',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记:0-未删除,1-已删除',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线监测-测点';
        </sql>
    </changeSet>

    <changeSet id="15" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `pipe_info`
                MODIFY COLUMN `pipe` multilinestring NULL COMMENT '管道坐标信息' AFTER `state`,
                MODIFY COLUMN `length` double NOT NULL DEFAULT 0 COMMENT '管道长度（单位：m）' AFTER `pipe`,
                MODIFY COLUMN `complete_length` double NOT NULL DEFAULT 0 COMMENT '完成长度（单位：m）' AFTER `length`;
        </sql>
    </changeSet>

    <changeSet id="16" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `const_record`
                add COLUMN  `time` datetime(0) NOT NULL COMMENT '提交时间'  AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="17" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `pipe_check_well`
                ADD COLUMN `type` int(11) NOT NULL COMMENT '检查井类型 1-雨水井  2-污水井' AFTER `dept_id`,
                ADD COLUMN `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0-未开始 1-已完成）' AFTER `address`;

            ALTER TABLE `pipe_info`
                ADD COLUMN `well_num` int(11) NOT NULL DEFAULT 0 COMMENT '检查井数量' AFTER `complete_length`,
                ADD COLUMN `complete_well_num` int(11) NOT NULL DEFAULT 0 COMMENT '完成检查井数量' AFTER `well_num`,
                ADD COLUMN `start_date` date NULL COMMENT '开始时间' AFTER `complete_well_num`,
                ADD COLUMN `complete_date` date NULL COMMENT '竣工时间' AFTER `start_date`,
                ADD COLUMN `pipe_mode` int(11) NOT NULL DEFAULT 0 COMMENT '管网模式 0-线网模式  1-点图模式' AFTER `complete_date`;

            ALTER TABLE `pipe_work_record`
                ADD COLUMN `check_well_id` int(11) NOT NULL COMMENT '检查井ID' AFTER `id`,
                ADD COLUMN `work_area_id` int(11) NOT NULL COMMENT '工区ID' AFTER `time`,
                ADD COLUMN `work_area_name` varchar(30) NOT NULL COMMENT '工区名称' AFTER `emp_group_id`;
        </sql>
    </changeSet>

    <changeSet id="18" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `energy_electric_box` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `name` varchar(32) NOT NULL COMMENT '名称',
            `platform` varchar(32) NOT NULL COMMENT '平台',
            `sn` varchar(32) NOT NULL COMMENT '硬件SN',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电箱监测';
        </sql>
    </changeSet>

    <changeSet id="19" author="xuguocheng">
        <comment>水电表添加字段</comment>
        <sql>
            ALTER TABLE `energy_electric_meter`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `name`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称' AFTER `dept_id`,
            MODIFY COLUMN `sn`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN' AFTER `device_id`,
            ADD COLUMN `platform`  varchar(32) NULL COMMENT '硬件平台编号' AFTER `device_id`;

            ALTER TABLE `energy_water_meter`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `name`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称' AFTER `dept_id`,
            MODIFY COLUMN `sn`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN' AFTER `device_id`,
            ADD COLUMN `platform`  varchar(32) NULL COMMENT '硬件平台编码' AFTER `device_id`;

            ALTER TABLE `energy_electric_meter_data`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `rpe_id`  int(11) NOT NULL COMMENT '电表id' AFTER `dept_id`;

            ALTER TABLE `energy_electric_meter_day`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `rpe_id`  int(11) NOT NULL COMMENT '电表id' AFTER `dept_id`,
            MODIFY COLUMN `time`  date NOT NULL COMMENT '日期' AFTER `device_id`;

            ALTER TABLE `energy_water_meter_data`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `water_id`  int(11) NOT NULL COMMENT '水表id' AFTER `dept_id`;

            ALTER TABLE `energy_water_meter_day`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
            MODIFY COLUMN `water_id`  int(11) NOT NULL COMMENT '水表id' AFTER `dept_id`,
            MODIFY COLUMN `time`  date NOT NULL COMMENT '日期' AFTER `water_id`;
        </sql>
    </changeSet>

    <changeSet id="20" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `const_record_weather`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
             `date` date NOT NULL COMMENT '日期',
             `weather` varchar(50) NOT NULL COMMENT '天气',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '施工日报天气';
        </sql>
    </changeSet>

    <changeSet id="21" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `mon_info`
            ADD COLUMN `data_type`  int NULL COMMENT '类型:1-基坑监测 2-高支模监测' AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="22" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `party_member` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `name` varchar(20) NOT NULL COMMENT '姓名',
            `gender` int(11) NOT NULL DEFAULT '1' COMMENT '性别: 1-男 2-女',
            `id_card_no` varchar(20) DEFAULT NULL COMMENT '身份证',
            `birthday` date DEFAULT NULL COMMENT '出生日期',
            `nation` varchar(10) DEFAULT NULL COMMENT '名族',
            `address` varchar(128) DEFAULT NULL COMMENT '住址',
            `head_img` varchar(255) DEFAULT NULL COMMENT '头像',
            `education` int(11) DEFAULT NULL COMMENT '学历',
            `degree` int(11) DEFAULT NULL COMMENT '学位',
            `party_branch` varchar(64) DEFAULT NULL COMMENT '党支部',
            `party_time_1` date DEFAULT NULL COMMENT '加入党组织时间',
            `party_time_2` date DEFAULT NULL COMMENT '正式入党时间',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记:0-未删除 1-已删除',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='党建管理-党员';

            CREATE TABLE `party_activity` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) DEFAULT NULL COMMENT '项目ID',
            `title` varchar(64) DEFAULT NULL COMMENT '标题',
            `content` varchar(200) DEFAULT NULL COMMENT '内容',
            `act_type_id` int(11) DEFAULT NULL COMMENT '活动类型ID',
            `act_type_name` varchar(20) DEFAULT NULL COMMENT '活动类型名称',
            `act_time` date DEFAULT NULL COMMENT '活动时间',
            `cover` varchar(255) DEFAULT NULL COMMENT '封面',
            `ext` text COMMENT '扩展信息',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='党建管理-党建活动';
        </sql>
    </changeSet>

    <changeSet id="23" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `bim_model` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `name` varchar(64) NOT NULL COMMENT '名称',
            `status` int(11) NOT NULL COMMENT '状态',
            `reason` varchar(64) DEFAULT NULL COMMENT '原因',
            `cover` varchar(255) DEFAULT NULL COMMENT '封面缩略图',
            `file_id` varchar(64) NULL COMMENT '文件ID(bimface)',
            `file_url` varchar(255) NULL COMMENT '模型文件地址',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='bim管理-bim模型';
        </sql>
    </changeSet>

    <changeSet id="24" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `bim_model`
            ADD COLUMN `suffix`  varchar(10) NULL COMMENT '文件后缀' AFTER `file_url`;
        </sql>
    </changeSet>

    <changeSet id="25" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `party_member`
            ADD COLUMN `phone`  varchar(32) NULL COMMENT '手机号码' AFTER `degree`;
        </sql>
    </changeSet>

    <changeSet id="26" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `bim_model`
            ADD COLUMN `file_type`  varchar(32) NULL COMMENT '模型文件类型' AFTER `cover`;
        </sql>
    </changeSet>

    <changeSet id="27" author="xuguocheng">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `party_activity`
            MODIFY COLUMN `content`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容' AFTER `title`;
        </sql>
    </changeSet>

    <changeSet id="28" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `panorama_info`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
             `panorama_name` varchar(50) NOT NULL COMMENT '项目名称',
             `panorama_url` varchar(255) NOT NULL COMMENT '全景地址',
             `desc` varchar(255) NULL COMMENT '项目简介',
             `cover_url` varchar(255) NULL COMMENT '封面地址',
             `qr_code` varchar(255) NULL COMMENT '二维码地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '全景看板项目信息';
        </sql>
    </changeSet>

    <changeSet id="29" author="qinzexing">
        <comment>清理表</comment>
        <sql>
            DROP TABLE pipe_work_area;
        </sql>
    </changeSet>

    <changeSet id="30" author="qinzexing">
        <comment>增加索引</comment>
        <sql>
            ALTER TABLE `settlement_point_log`
                ADD INDEX `idx_pointId_time`(`point_id`, `time`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="31" author="xuguocheng">
        <comment>千里马-设备全生命周期数据包</comment>
        <sql>
            CREATE TABLE `qlm_equip`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机号',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
              `engine_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发动机编号',
              `engine_mode` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发动机型号',
              `performance` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性能参数',
              `environment` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '环保参数',
              `manufacture_date` date NULL DEFAULT NULL COMMENT '生产日期',
              `production_date` date NULL DEFAULT NULL COMMENT '出厂日期',
              `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
              `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源',
              `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE,
              INDEX `idx_code`(`code`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-整机制造';

            CREATE TABLE `qlm_equip_agent`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `equip_id` int(11) NOT NULL COMMENT '设备ID',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `agent` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商',
                `indate` date NULL DEFAULT NULL COMMENT '入库日期',
                `outdate` date NULL DEFAULT NULL COMMENT '出库日期',
                `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
                `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_equip_id`(`equip_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-代理商';

            CREATE TABLE `qlm_equip_location`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `equip_id` int(11) NOT NULL COMMENT '设备ID',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `sale_date` date NULL DEFAULT NULL COMMENT '销售日期',
               `customer` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户姓名',
               `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户电话',
               `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户地址',
               `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
               `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-所在地';

            CREATE TABLE `qlm_equip_maintain`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `equip_id` int(11) NOT NULL COMMENT '设备ID',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `sale_cust_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户名称',
               `serv_parts_date` datetime(0) NOT NULL COMMENT '维修订单时间',
               `serv_user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务人员',
               `time_money` double NOT NULL DEFAULT 0 COMMENT '服务费',
               `parts_money` double NOT NULL DEFAULT 0 COMMENT '配件费',
               `parts_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配件信息',
               `machine_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机器地址',
               `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据来源',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE,
               INDEX `idx_equip_id`(`equip_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-维修';

            CREATE TABLE `qlm_equip_remanufacture`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `equip_id` int(11) NOT NULL COMMENT '设备ID',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `enterprise` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业',
                `date` date NOT NULL COMMENT '日期',
                `content` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
                `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
                `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_equip_Id`(`equip_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-再制造';

            CREATE TABLE `qlm_equip_scrap`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `equip_id` int(11) NOT NULL COMMENT '设备ID',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `scrap_date` date NOT NULL COMMENT '报废日期',
                `scrap_reason` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报废原因',
                `scrap_person` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报废人',
                `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
                `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_equip_id`(`equip_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-报废';
        </sql>
    </changeSet>

    <changeSet id="32" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `qlm_equip`
            ADD COLUMN `model` varchar(32) NULL COMMENT '型号' AFTER `name`,
            ADD COLUMN `factory` varchar(32) NULL COMMENT '厂家' AFTER `model`;
        </sql>
    </changeSet>

    <changeSet id="33" author="xuguocheng">
        <comment>修改字段约束</comment>
        <sql>
            ALTER TABLE `qlm_equip_maintain`
                MODIFY COLUMN `sale_cust_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户名称' AFTER `dept_id`,
                MODIFY COLUMN `serv_parts_date` datetime(0) NULL COMMENT '维修订单时间' AFTER `sale_cust_name`,
                MODIFY COLUMN `serv_user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务人员' AFTER `serv_parts_date`;
        </sql>
    </changeSet>

    <changeSet id="34" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `qlm_equip`
            ADD COLUMN `sn` varchar(32) NULL COMMENT '硬件sn' AFTER `production_date`;
        </sql>
    </changeSet>

    <changeSet id="35" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `qlm_equip_datasource`  (
             `id` int(11) NOT NULL COMMENT '主键',
             `type` int(11) NOT NULL COMMENT '接口类型: 1-整机制造 2-代理商 3-所在地 4-维修 5-再制造 6-过程数据 7-报废',
             `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口编号',
             `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口名称',
             `provider` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口提供方',
             `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口请求地址',
             `format` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据格式',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '千里马-设备-数据来源';
        </sql>
    </changeSet>

    <changeSet id="36" author="xuguocheng">
        <comment>设置主键自增</comment>
        <sql>
            ALTER TABLE `qlm_equip_datasource`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST;
        </sql>
    </changeSet>

    <changeSet id="37" author="xuguocheng">
        <comment>新增表</comment>
        <sql>

        CREATE TABLE `lock_device`  (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `dept_id` int(11) NOT NULL COMMENT '项目ID',
        `group_id` int(11) NULL DEFAULT NULL COMMENT '分组ID',
        `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
        `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
        `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件SN',
        `model` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品型号',
        `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
        `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
        `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `idx_deptId`(`dept_id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能门锁-设备' ROW_FORMAT = Dynamic;

        CREATE TABLE `lock_device_data`  (
         `id` int(11) NOT NULL AUTO_INCREMENT,
         `dept_id` int(11) NOT NULL COMMENT '项目ID',
         `lock_id` int(11) NOT NULL COMMENT '智能锁设备ID',
         `time` datetime(0) NOT NULL COMMENT '时间',
         `lng` double NULL DEFAULT NULL COMMENT '经度',
         `lat` double NULL DEFAULT NULL COMMENT '纬度',
         `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
         `warn_flag` int(11) NULL DEFAULT NULL COMMENT '标记标志',
         `state` int(11) NULL DEFAULT NULL COMMENT '状态标志',
         `gps_state` int(11) NULL DEFAULT NULL COMMENT 'gps工作状态: 0-GPS休眠（静止）2-GPS开启（振动)',
         `gps_valid` int(11) NULL DEFAULT NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位 ',
         `seal_state` int(11) NULL DEFAULT NULL COMMENT '施封状态: 0-解封  1-施封',
         `lock_state` int(11) NULL DEFAULT NULL COMMENT '锁开关: 0-锁杆开  1-锁杆关',
         `battery_state` int(11) NULL DEFAULT 0 COMMENT '电池状态: 0-无充电 1-充电中 2-充满电',
         `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
         `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
         `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
         PRIMARY KEY (`id`) USING BTREE,
         INDEX `idx_deptId`(`dept_id`) USING BTREE,
         INDEX `idx_deviceId`(`lock_id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能门锁-设备最新数据' ROW_FORMAT = Dynamic;

        CREATE TABLE `lock_device_data_log`  (
         `id` int(11) NOT NULL AUTO_INCREMENT,
         `lock_id` int(11) NOT NULL COMMENT '智能锁设备ID',
         `time` datetime(0) NOT NULL COMMENT '时间',
         `lng` double NULL DEFAULT NULL COMMENT '经度',
         `lat` double NULL DEFAULT NULL COMMENT '纬度',
         `address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
         `warn_flag` int(11) NULL DEFAULT NULL COMMENT '标记标志',
         `state` int(11) NULL DEFAULT NULL COMMENT '状态标志',
         `gps_state` int(11) NULL DEFAULT NULL COMMENT 'gps工作状态: 0-GPS休眠（静止）2-GPS开启（振动)',
         `gps_valid` int(11) NULL DEFAULT NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位 ',
         `seal_state` int(11) NULL DEFAULT NULL COMMENT '施封状态: 0-解封  1-施封',
         `lock_state` int(11) NULL DEFAULT NULL COMMENT '锁开关: 0-锁杆开  1-锁杆关',
         `battery_state` int(11) NULL DEFAULT 0 COMMENT '电池状态: 0-无充电 1-充电中 2-充满电',
         `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
         `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
         `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
         PRIMARY KEY (`id`) USING BTREE,
         INDEX `idx_deviceId_time`(`lock_id`, `time`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能门锁-设备历史数据' ROW_FORMAT = Dynamic;

        CREATE TABLE `lock_group`  (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `dept_id` int(11) NOT NULL COMMENT '项目ID',
           `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
           `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
           `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
           `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能门锁-分组' ROW_FORMAT = Dynamic;

        </sql>
    </changeSet>

    <changeSet id="38" author="xuguocheng">
        <comment>新增数据表</comment>
        <sql>
            CREATE TABLE `temp_device`  (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `group_id` int(11) NULL DEFAULT NULL COMMENT '分组ID',
            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
            `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
            `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件SN',
            `model` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品型号',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `idx_deptId`(`dept_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-设备' ROW_FORMAT = Dynamic;

            CREATE TABLE `temp_device_data`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `temp_id` int(11) NOT NULL COMMENT '测温设备ID',
             `time` datetime(0) NOT NULL COMMENT '时间',
             `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
             `t1` double NULL DEFAULT NULL COMMENT '温度1',
             `t2` double NULL DEFAULT NULL COMMENT '温度2',
             `t3` double NULL DEFAULT NULL COMMENT '温度3',
             `t4` double NULL DEFAULT NULL COMMENT '温度4',
             `t5` double NULL DEFAULT NULL COMMENT '温度5',
             `t6` double NULL DEFAULT NULL COMMENT '温度6',
             `t7` double NULL DEFAULT NULL COMMENT '温度7',
             `t8` double NULL DEFAULT NULL COMMENT '温度8',
             `t9` double NULL DEFAULT NULL COMMENT '温度9',
             `t10` double NULL DEFAULT NULL COMMENT '温度10',
             `t11` double NULL DEFAULT NULL COMMENT '温度11',
             `t12` double NULL DEFAULT NULL COMMENT '温度12',
             `t13` double NULL DEFAULT NULL COMMENT '温度13',
             `t14` double NULL DEFAULT NULL COMMENT '温度14',
             `t15` double NULL DEFAULT NULL COMMENT '温度15',
             `t16` double NULL DEFAULT NULL COMMENT '温度16',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE,
             INDEX `idx_deptId`(`dept_id`) USING BTREE,
             INDEX `idx_deviceId`(`temp_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-设备最新数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `temp_device_data_log`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `temp_id` int(11) NOT NULL COMMENT '测温设备ID',
             `time` datetime(0) NOT NULL COMMENT '时间',
             `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
             `t1` double NULL DEFAULT NULL COMMENT '温度1',
             `t2` double NULL DEFAULT NULL COMMENT '温度2',
             `t3` double NULL DEFAULT NULL COMMENT '温度3',
             `t4` double NULL DEFAULT NULL COMMENT '温度4',
             `t5` double NULL DEFAULT NULL COMMENT '温度5',
             `t6` double NULL DEFAULT NULL COMMENT '温度6',
             `t7` double NULL DEFAULT NULL COMMENT '温度7',
             `t8` double NULL DEFAULT NULL COMMENT '温度8',
             `t9` double NULL DEFAULT NULL COMMENT '温度9',
             `t10` double NULL DEFAULT NULL COMMENT '温度10',
             `t11` double NULL DEFAULT NULL COMMENT '温度11',
             `t12` double NULL DEFAULT NULL COMMENT '温度12',
             `t13` double NULL DEFAULT NULL COMMENT '温度13',
             `t14` double NULL DEFAULT NULL COMMENT '温度14',
             `t15` double NULL DEFAULT NULL COMMENT '温度15',
             `t16` double NULL DEFAULT NULL COMMENT '温度16',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE,
             INDEX `idx_tempId_time`(`temp_id`, `time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-设备历史数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `temp_group`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-分组' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="39" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `temp_device_data`
                ADD COLUMN `battery` int NULL COMMENT '电量' AFTER `net_state`,
                ADD COLUMN `signal` int NULL COMMENT '信号强度' AFTER `battery`,
                ADD COLUMN `cycle` int NULL COMMENT '采集周期:分钟' AFTER `signal`;

            ALTER TABLE `temp_device_data_log`
                ADD COLUMN `battery` int NULL COMMENT '电量' AFTER `net_state`,
                ADD COLUMN `signal` int NULL COMMENT '信号强度' AFTER `battery`,
                ADD COLUMN `cycle` int NULL COMMENT '采集周期:分钟' AFTER `signal`;

            CREATE TABLE `temp_device_point`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `temp_id` int(11) NOT NULL COMMENT '测温设备ID',
              `point` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测点名称',
              `value` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测点字段',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="40" author="xuguocheng">
        <comment>新增数据表</comment>
        <sql>
            CREATE TABLE `lock_warn_record`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT,
                 `dept_id` int(11) NOT NULL COMMENT '项目ID',
                 `rule_id` int(11) NOT NULL COMMENT '规则ID',
                 `rule_type` int(11) NOT NULL COMMENT '规则类型',
                 `rule_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则参数',
                 `rule_express` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则表达式',
                 `trigger_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '触发参数',
                 `trigger_time` datetime(0) NOT NULL COMMENT '触发时间',
                 `trigger_object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发业务对象ID',
                 `state` int(11) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
                 `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
                 `handle_result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果',
                 `handle_remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果备注',
                 `handle_user_id` int(11) NULL DEFAULT NULL COMMENT '处理人',
                 `handle_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理人姓名',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                 PRIMARY KEY (`id`) USING BTREE,
                 INDEX `idx_dept_state_ruleType_time`(`dept_id`, `state`, `rule_type`, `trigger_time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 9139 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `lock_warn_rule`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `rule_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报警规则名称',
               `rule_type` int(11) NOT NULL COMMENT '报警规则类型',
               `rule_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则',
               `rule_express` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则',
               `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则' ROW_FORMAT = Dynamic;

            CREATE TABLE `lock_warn_rule_channel`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `rule_id` int(11) NOT NULL COMMENT '设备报警规则id',
               `msg_channel` int(11) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE,
               UNIQUE INDEX `uk_ruleId_msgChannel`(`rule_id`, `msg_channel`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警接收方式' ROW_FORMAT = Dynamic;

            CREATE TABLE `lock_warn_rule_object`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `rule_id` int(11) NOT NULL COMMENT '报警规则id',
              `rule_type` int(11) NOT NULL COMMENT '规则类型',
              `object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报警对象id',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE,
              UNIQUE INDEX `uk_ruleId_objectId`(`rule_id`, `object_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则和报警对象关联表' ROW_FORMAT = Dynamic;

            CREATE TABLE `lock_warn_rule_time`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `rule_id` int(11) NOT NULL COMMENT '报警规则ID',
                `start_time` time(0) NOT NULL COMMENT '开始时间',
                `end_time` time(0) NOT NULL COMMENT '结束时间',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则-生效时间' ROW_FORMAT = Dynamic;

            CREATE TABLE `lock_warn_rule_user`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `rule_id` int(11) NOT NULL COMMENT '设备报警规则id',
                `to_user_id` int(11) NULL DEFAULT NULL COMMENT '接收人(后台用户ID)',
                `to_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人姓名',
                `to_user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人手机号',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `uk_ruleId_userId`(`rule_id`, `to_user_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警-接收人' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="41" author="xuguocheng">
        <comment>新增数据表</comment>
        <sql>
            CREATE TABLE `concrete_group`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-分组' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_item`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `group_id` int(11) NULL DEFAULT NULL COMMENT '分组ID',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
              `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
              `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件SN',
              `bind_state` int(11) NULL DEFAULT NULL COMMENT '绑定状态:0-未绑定 1-已绑定',
              `state` int(11) NULL DEFAULT NULL COMMENT '状态:0-未浇筑 1-已浇筑 2-温控监测 10-完成',
              `pour_date` date NULL DEFAULT NULL COMMENT '浇筑日期',
              `measure_start_date` date NULL DEFAULT NULL COMMENT '测温开始日期',
              `measure_end_date` date NULL DEFAULT NULL COMMENT '测温结束日期',
              `over_start_date` date NULL DEFAULT NULL COMMENT '超温开始日期',
              `over_end_date` date NULL DEFAULT NULL COMMENT '超温结束日期',
              `temp_diff_max` double NULL DEFAULT NULL COMMENT '温差最大值',
              `temp_diff_min` double NULL DEFAULT NULL COMMENT '温差最小值',
              `temp_diff_avg` double NULL DEFAULT NULL COMMENT '温差平均值',
              `temp_measure_num` int(11) NULL DEFAULT NULL COMMENT '监控组数',
              `temp_over_num` int(11) NULL DEFAULT NULL COMMENT '超温组数',
              `temp_over_avg` double NULL DEFAULT NULL COMMENT '平均超温',
              `temp_over_rate` double NULL DEFAULT NULL COMMENT '超温率',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE,
              INDEX `idx_deptId`(`dept_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-监控项目' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_item_data`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `concrete_id` int(11) NOT NULL COMMENT '监控项目ID',
               `time` datetime(0) NOT NULL COMMENT '时间',
               `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
               `battery` int(11) NULL DEFAULT NULL COMMENT '电量',
               `signal` int(11) NULL DEFAULT NULL COMMENT '信号强度',
               `cycle` int(11) NULL DEFAULT NULL COMMENT '采集周期:分钟',
               `t1` double NULL DEFAULT NULL COMMENT '温度1',
               `t2` double NULL DEFAULT NULL COMMENT '温度2',
               `t3` double NULL DEFAULT NULL COMMENT '温度3',
               `t4` double NULL DEFAULT NULL COMMENT '温度4',
               `t5` double NULL DEFAULT NULL COMMENT '温度5',
               `t6` double NULL DEFAULT NULL COMMENT '温度6',
               `t7` double NULL DEFAULT NULL COMMENT '温度7',
               `t8` double NULL DEFAULT NULL COMMENT '温度8',
               `t9` double NULL DEFAULT NULL COMMENT '温度9',
               `t10` double NULL DEFAULT NULL COMMENT '温度10',
               `t11` double NULL DEFAULT NULL COMMENT '温度11',
               `t12` double NULL DEFAULT NULL COMMENT '温度12',
               `t13` double NULL DEFAULT NULL COMMENT '温度13',
               `t14` double NULL DEFAULT NULL COMMENT '温度14',
               `t15` double NULL DEFAULT NULL COMMENT '温度15',
               `t16` double NULL DEFAULT NULL COMMENT '温度16',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE,
               INDEX `idx_deptId`(`dept_id`) USING BTREE,
               INDEX `idx_deviceId`(`concrete_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-监控项目最新数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_item_data_log`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `concrete_id` int(11) NOT NULL COMMENT '测温监控项目ID',
               `time` datetime(0) NOT NULL COMMENT '时间',
               `t1` double NULL DEFAULT NULL COMMENT '温度1',
               `t2` double NULL DEFAULT NULL COMMENT '温度2',
               `t3` double NULL DEFAULT NULL COMMENT '温度3',
               `t4` double NULL DEFAULT NULL COMMENT '温度4',
               `t5` double NULL DEFAULT NULL COMMENT '温度5',
               `t6` double NULL DEFAULT NULL COMMENT '温度6',
               `t7` double NULL DEFAULT NULL COMMENT '温度7',
               `t8` double NULL DEFAULT NULL COMMENT '温度8',
               `t9` double NULL DEFAULT NULL COMMENT '温度9',
               `t10` double NULL DEFAULT NULL COMMENT '温度10',
               `t11` double NULL DEFAULT NULL COMMENT '温度11',
               `t12` double NULL DEFAULT NULL COMMENT '温度12',
               `t13` double NULL DEFAULT NULL COMMENT '温度13',
               `t14` double NULL DEFAULT NULL COMMENT '温度14',
               `t15` double NULL DEFAULT NULL COMMENT '温度15',
               `t16` double NULL DEFAULT NULL COMMENT '温度16',
               `temp_diff` double NULL DEFAULT NULL COMMENT '温差',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE,
               INDEX `idx_tempId_time`(`concrete_id`, `time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '混凝土测温-设备历史数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="42" author="xuguocheng">
        <comment>删除无效表</comment>
        <sql>
            DROP TABLE temp_device;
            DROP TABLE temp_device_data;
            DROP TABLE temp_device_data_log;
            DROP TABLE temp_device_point;
            DROP TABLE temp_group;
        </sql>
    </changeSet>

    <changeSet id="43" author="xuguocheng">
        <comment>新增数据表</comment>
        <sql>
            CREATE TABLE `concrete_warn_record`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `rule_id` int(11) NOT NULL COMMENT '规则ID',
             `rule_type` int(11) NOT NULL COMMENT '规则类型',
             `rule_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则参数',
             `rule_express` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则表达式',
             `trigger_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '触发参数',
             `trigger_time` datetime(0) NOT NULL COMMENT '触发时间',
             `trigger_object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发业务对象ID',
             `state` int(11) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
             `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
             `handle_result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果',
             `handle_remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理结果备注',
             `handle_user_id` int(11) NULL DEFAULT NULL COMMENT '处理人',
             `handle_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理人姓名',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
             PRIMARY KEY (`id`) USING BTREE,
             INDEX `idx_dept_state_ruleType_time`(`dept_id`, `state`, `rule_type`, `trigger_time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_warn_rule`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `rule_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报警规则名称',
               `rule_type` int(11) NOT NULL COMMENT '报警规则类型',
               `rule_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则',
               `rule_express` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报警检测规则',
               `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_warn_rule_channel`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `rule_id` int(11) NOT NULL COMMENT '设备报警规则id',
               `msg_channel` int(11) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE,
               UNIQUE INDEX `uk_ruleId_msgChannel`(`rule_id`, `msg_channel`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警接收方式' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_warn_rule_object`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `rule_id` int(11) NOT NULL COMMENT '报警规则id',
              `rule_type` int(11) NOT NULL COMMENT '规则类型',
              `object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报警对象id',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE,
              UNIQUE INDEX `uk_ruleId_objectId`(`rule_id`, `object_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则和报警对象关联表' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_warn_rule_time`  (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '报警规则ID',
            `start_time` time(0) NOT NULL COMMENT '开始时间',
            `end_time` time(0) NOT NULL COMMENT '结束时间',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警规则-生效时间' ROW_FORMAT = Dynamic;

            CREATE TABLE `concrete_warn_rule_user`  (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '设备报警规则id',
            `to_user_id` int(11) NULL DEFAULT NULL COMMENT '接收人(后台用户ID)',
            `to_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人姓名',
            `to_user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人手机号',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_ruleId_userId`(`rule_id`, `to_user_id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报警-接收人' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="44" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `const_record`
                MODIFY COLUMN `part_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '施工部位' AFTER `dept_id`;
            ALTER TABLE `lock_device_data`
                MODIFY COLUMN `lock_state` int(11) NULL DEFAULT NULL COMMENT '锁开关: 0-锁杆开  1-锁杆关' AFTER `time`,
                MODIFY COLUMN `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线' AFTER `lock_state`,
                MODIFY COLUMN `battery_state` int(11) NULL DEFAULT 0 COMMENT '电池状态: 0-无充电 1-充电中 2-充满电' AFTER `net_state`,
                MODIFY COLUMN `seal_state` int(11) NULL DEFAULT NULL COMMENT '施封状态: 0-解封  1-施封' AFTER `battery_state`,
                MODIFY COLUMN `gps_state` int(11) NULL DEFAULT NULL COMMENT 'gps工作状态: 0-GPS休眠（静止）2-GPS开启（振动)' AFTER `seal_state`,
                MODIFY COLUMN `gps_valid` int(11) NULL DEFAULT NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位 ' AFTER `gps_state`,
                MODIFY COLUMN `lng` double NULL DEFAULT NULL COMMENT '经度' AFTER `gps_valid`;
            ALTER TABLE `lock_device_data_log`
                MODIFY COLUMN `lock_state` int(11) NULL DEFAULT NULL COMMENT '锁开关: 0-锁杆开  1-锁杆关' AFTER `time`,
                MODIFY COLUMN `net_state` int(11) NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线' AFTER `lock_state`,
                MODIFY COLUMN `battery_state` int(11) NULL DEFAULT 0 COMMENT '电池状态: 0-无充电 1-充电中 2-充满电' AFTER `net_state`,
                MODIFY COLUMN `seal_state` int(11) NULL DEFAULT NULL COMMENT '施封状态: 0-解封  1-施封' AFTER `battery_state`,
                MODIFY COLUMN `gps_state` int(11) NULL DEFAULT NULL COMMENT 'gps工作状态: 0-GPS休眠（静止）2-GPS开启（振动)' AFTER `seal_state`,
                MODIFY COLUMN `gps_valid` int(11) NULL DEFAULT NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位 ' AFTER `gps_state`,
                MODIFY COLUMN `lng` double NULL DEFAULT NULL COMMENT '经度' AFTER `gps_valid`;
        </sql>
    </changeSet>

    <changeSet id="45" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `concrete_item_data`
                ADD COLUMN `data` varchar(512) NULL COMMENT '采集点数据' AFTER `cycle`;

            ALTER TABLE `concrete_item_data_log`
                ADD COLUMN `data` varchar(512) NULL COMMENT '采集点数据' AFTER `time`;
        </sql>
    </changeSet>

    <changeSet id="46" author="xuguocheng">
        <comment>删除无用字段</comment>
        <sql>
            ALTER TABLE `concrete_item_data`
            DROP COLUMN `t1`,
            DROP COLUMN `t2`,
            DROP COLUMN `t3`,
            DROP COLUMN `t4`,
            DROP COLUMN `t5`,
            DROP COLUMN `t6`,
            DROP COLUMN `t7`,
            DROP COLUMN `t8`,
            DROP COLUMN `t9`,
            DROP COLUMN `t10`,
            DROP COLUMN `t11`,
            DROP COLUMN `t12`,
            DROP COLUMN `t13`,
            DROP COLUMN `t14`,
            DROP COLUMN `t15`,
            DROP COLUMN `t16`;

            ALTER TABLE `concrete_item_data_log`
            DROP COLUMN `t1`,
            DROP COLUMN `t2`,
            DROP COLUMN `t3`,
            DROP COLUMN `t4`,
            DROP COLUMN `t5`,
            DROP COLUMN `t6`,
            DROP COLUMN `t7`,
            DROP COLUMN `t8`,
            DROP COLUMN `t9`,
            DROP COLUMN `t10`,
            DROP COLUMN `t11`,
            DROP COLUMN `t12`,
            DROP COLUMN `t13`,
            DROP COLUMN `t14`,
            DROP COLUMN `t15`,
            DROP COLUMN `t16`;
        </sql>
    </changeSet>

    <changeSet id="47" author="xuguocheng">
        <comment>添加字段</comment>
        <sql>
            ALTER TABLE `concrete_item_data_log`
                ADD COLUMN `temp_over` int NULL DEFAULT 0 COMMENT '是否超温:0-正常 1-超温' AFTER `temp_diff`;
        </sql>
    </changeSet>

    <changeSet id="48" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `concrete_item`
                MODIFY COLUMN `state` int(11) NULL DEFAULT 0 COMMENT '状态:0-待测温 1-测温中 2-已完成' AFTER `bind_state`;
        </sql>
    </changeSet>

    <changeSet id="49" author="xuguocheng">
        <comment>新增索引</comment>
        <sql>
            ALTER TABLE `car_inout_record`
            MODIFY COLUMN `time` datetime(0) NOT NULL COMMENT '识别时间' AFTER `dept_id`,
            MODIFY COLUMN `device_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称' AFTER `time`,
            ADD INDEX `idx_deptId_time`(`dept_id`, `time`);
        </sql>
    </changeSet>

    <changeSet id="50" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_sync`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL COMMENT '项目id',
             `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编号',
             `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台名称',
             `app_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台凭证',
             `app_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '秘钥',
             `host` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务地址',
             `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口token',
             `ext_1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
             `ext_2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用同步信息表' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="51" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_news`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
             `type` int(11) NOT NULL COMMENT '类型（1-新闻中心，2-应用案例）',
             `category` int(11) NULL DEFAULT NULL COMMENT '新闻种类（1-企业新闻 2-行业新闻）',
             `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
             `brief` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
             `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
             `cover` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面',
             `public_flag` int(11) NOT NULL DEFAULT 1 COMMENT '发布标记（0-未发布 1-已发布）',
             `public_time` date NULL DEFAULT NULL COMMENT '发布日期',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 259 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新闻中心' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="52" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `pressure_sensor`  (
                `id` int(11) NOT NULL,
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
                `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编号',
                `del_flag` int(32) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压力传感器-设备' ROW_FORMAT = Dynamic;

            CREATE TABLE `pressure_sensor_data`  (
                 `id` int(11) NOT NULL,
                 `sensor_id` int(11) NOT NULL COMMENT '设备ID',
                 `time` datetime(0) NOT NULL COMMENT '时间',
                 `pressure` double NULL DEFAULT NULL COMMENT '压力(kpa)',
                 `battery` double NULL DEFAULT NULL COMMENT '电压(v)',
                 `sig` int(11) NULL DEFAULT NULL COMMENT '信号',
                 `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE,
                 INDEX `idx_sensorid`(`sensor_id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压力传感器-设备实时数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `pressure_sensor_log`  (
                 `id` int(11) NOT NULL,
                 `sensor_id` int(11) NOT NULL COMMENT '设备ID',
                 `time` datetime(0) NOT NULL COMMENT '时间',
                 `pressure` double NULL DEFAULT NULL COMMENT '压力(kpa)',
                 `battery` double NULL DEFAULT NULL COMMENT '电压(v)',
                 `sig` int(11) NULL DEFAULT NULL COMMENT '信号',
                 `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE,
                 INDEX `idx_sensrid_time`(`sensor_id`, `time`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压力传感器-设备历史数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="53" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `pressure_sensor_config`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
               `value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置值',
               `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

            CREATE TABLE `pressure_sensor_warn`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `sensor_id` int(11) NOT NULL COMMENT '传感器ID',
             `time` datetime(0) NOT NULL COMMENT '时间',
             `content` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警内容',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '压力传感器-报警数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="54" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `pressure_sensor_warn`
                ADD COLUMN `pressure` double NULL COMMENT '压力' AFTER `content`,
                ADD COLUMN `battery` double NULL COMMENT '电量' AFTER `pressure`,
                ADD INDEX `idx_deptId_time`(`dept_id`, `time`) USING BTREE,
                ADD INDEX `idx_sensorId_time`(`sensor_id`, `time`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="55" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `pressure_sensor`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT FIRST;
        </sql>
    </changeSet>

    <changeSet id="56" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `pressure_sensor_log`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT FIRST;
            ALTER TABLE `pressure_sensor_data`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT FIRST;
        </sql>
    </changeSet>

    <changeSet id="57" author="xuguocheng">
        <comment>合并fsdm</comment>
        <sql>
            CREATE TABLE `fsdm_config`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `dept_id` int(11) NOT NULL COMMENT '项目id',
                `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
                `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '值',
                `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记0-禁用 1-启用',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `idx_deptId_code`(`dept_id`, `code`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 85 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '进度模块配置表' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_job`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL,
             `job_type` int(11) NOT NULL COMMENT '任务类型:	1-工作计划 2-任务分配',
             `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
             `content` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
             `finish_content` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完成内容',
             `plan_start` datetime(0) NULL DEFAULT NULL COMMENT '计划开始',
             `plan_finish` datetime(0) NULL DEFAULT NULL COMMENT '计划结束',
             `actual_start` datetime(0) NULL DEFAULT NULL COMMENT '实际开始',
             `actual_finish` datetime(0) NULL DEFAULT NULL COMMENT '实际结束',
             `running_state` int(11) NULL DEFAULT NULL COMMENT '执行状态:0-未开始 1-执行中 2-已完成',
             `state` int(11) NULL DEFAULT NULL COMMENT '完成状态1-提前完成 2-超期完成 3-正常完成',
             `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
             `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
             `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
             `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '计划管理-任务' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_job_duty`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `job_id` int(11) NOT NULL COMMENT '任务ID',
              `user_id` int(11) NOT NULL COMMENT '责任人ID',
              `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '责任人姓名',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '计划管理-任务-责任人' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_plan`  (
              `id` bigint(20) NOT NULL,
              `pid` bigint(20) NULL DEFAULT NULL,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `uid` bigint(20) NULL DEFAULT NULL,
              `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `full_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
              `type` int(11) NULL DEFAULT NULL COMMENT '计划类型:1-总计划 2-季度计划 3-月计划 4-周计划 5-其他计划',
              `version` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划版本号',
              `plan_start` date NULL DEFAULT NULL COMMENT '计划开始日期',
              `plan_finish` date NULL DEFAULT NULL COMMENT '计划完成日期',
              `plan_duration` int(11) NULL DEFAULT NULL COMMENT '计划工期(天)',
              `actual_start` date NULL DEFAULT NULL,
              `actual_finish` date NULL DEFAULT NULL,
              `actual_duration` int(11) NULL DEFAULT NULL COMMENT '实际工期(天)',
              `state` int(11) NOT NULL DEFAULT 0 COMMENT '计划状态,0-未发布 1-已发布',
              `running_state` int(11) NOT NULL DEFAULT 0 COMMENT '执行状态:0-未开始 1-未完成 2-已完成',
              `process` double NOT NULL DEFAULT 0 COMMENT '进度百分比',
              `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
              `update_user_id` int(11) NULL DEFAULT NULL,
              `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_user_id` int(11) NULL DEFAULT NULL,
              `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目计划' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_quantity`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型编码',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
              `unit_id` int(11) NOT NULL COMMENT '单位ID',
              `unit_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位名称',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目工程量类型' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_resource`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL,
              `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
              `type` int(11) NOT NULL COMMENT '资源类型 1-工时 2-材料 3-成本',
              `mat_unit_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料单位',
              `rate_unit` int(11) NULL DEFAULT NULL COMMENT '费率单位 1-分钟 2-小时 3-日 4-周 5-月 6-年',
              `price` int(11) NULL DEFAULT NULL COMMENT '价格',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目资源类型' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task`  (
              `id` bigint(20) NOT NULL,
              `dept_id` int(11) NOT NULL COMMENT '项目ID(冗余)',
              `uid` bigint(20) NOT NULL,
              `plan_id` bigint(20) NOT NULL COMMENT '计划iD',
              `parent_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '父任务节点ID',
              `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务编码',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
              `type` int(11) NULL DEFAULT NULL COMMENT '任务类型:0-里程碑 1-单位 2-分部 3-分项 4-工序',
              `level` int(11) NOT NULL DEFAULT 1 COMMENT '层级',
              `inner_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `plan_start` date NULL DEFAULT NULL COMMENT '计划开始日期',
              `plan_finish` date NULL DEFAULT NULL COMMENT '计划完成日期',
              `plan_duration` int(11) NULL DEFAULT NULL COMMENT '计划工期(天)',
              `actual_start` date NULL DEFAULT NULL COMMENT '实际开始日期',
              `actual_finish` date NULL DEFAULT NULL COMMENT '实际完成日期',
              `actual_duration` int(11) NULL DEFAULT NULL COMMENT '实际工期(天)',
              `running_state` int(11) NOT NULL DEFAULT 0 COMMENT '执行状态:0-未开始 1-未完成 2-已完成',
              `state` int(11) NULL DEFAULT NULL COMMENT '完成状态1-提前完成 2-超期完成 3-正常完成',
              `process` double NULL DEFAULT 0 COMMENT '进度百分比',
              `weight` int(11) NOT NULL DEFAULT 100 COMMENT '权重',
              `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
              `update_state` int(11) NOT NULL DEFAULT 1 COMMENT '变更状态 1-未变更 2-已变更 3-已删除',
              `update_user_id` int(11) NULL DEFAULT NULL COMMENT '变更人id',
              `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更人姓名',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_duty`  (
               `id` bigint(20) NOT NULL,
               `dept_id` int(11) NOT NULL COMMENT '项目ID(冗余)',
               `plan_id` bigint(20) NOT NULL COMMENT '计划ID(冗余)',
               `task_id` bigint(20) NOT NULL COMMENT '任务ID',
               `duty_user_id` int(11) NOT NULL COMMENT '责任用户ID',
               `duty_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '责任用户ID名称',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-责任人' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_process_log`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `task_id` bigint(20) NOT NULL COMMENT '任务ID',
              `process` double NOT NULL DEFAULT 0 COMMENT '进度百分比',
              `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
              `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名称',
              `time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 379 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务进度修改记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_quantity`  (
               `id` bigint(20) NOT NULL,
               `dept_id` int(11) NOT NULL,
               `plan_id` bigint(20) NOT NULL,
               `task_id` bigint(20) NOT NULL,
               `quantity_id` int(11) NULL DEFAULT NULL,
               `quantity_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
               `quantity_unit_id` int(11) NULL DEFAULT NULL,
               `quantity_unit_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
               `plan_amount` int(11) NULL DEFAULT NULL,
               `actual_amount` int(11) NULL DEFAULT NULL,
               `process` double NULL DEFAULT 0 COMMENT '进度百分比',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-工程量' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_quantity_log`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `task_id` bigint(20) NOT NULL COMMENT '任务ID',
               `actual_amount` int(11) NOT NULL DEFAULT 0 COMMENT '实际工程量',
               `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
               `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名称',
               `time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务工程量修改记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_relation`  (
               `id` bigint(20) NOT NULL,
               `dept_id` int(11) NOT NULL COMMENT '项目ID(冗余)',
               `plan_id` bigint(20) NOT NULL COMMENT '计划ID(冗余)',
               `pre_task_id` bigint(20) NOT NULL COMMENT '前置任务ID',
               `post_task_id` bigint(20) NOT NULL COMMENT '后置任务ID',
               `type` int(11) NOT NULL COMMENT '前置关系类型: 0-FF 1-FS 2-SF 3-SS',
               `interval` int(11) NOT NULL DEFAULT 0 COMMENT '时间间隔(天)',
               `del_flag` int(11) NOT NULL DEFAULT 0,
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-关联' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_resource`  (
               `id` bigint(20) NOT NULL,
               `dept_id` int(11) NOT NULL COMMENT '项目ID(冗余)',
               `plan_id` bigint(20) NOT NULL COMMENT '计划ID(冗余)',
               `task_id` bigint(20) NOT NULL COMMENT '任务ID',
               `resource_id` int(11) NOT NULL COMMENT '资源ID',
               `resource_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
               `type` int(11) NULL DEFAULT NULL COMMENT '资源类型 1-工时 2-材料 3-成本',
               `mat_unit_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '材料单位',
               `rate_unit` int(11) NULL DEFAULT NULL COMMENT '费率单位 1-分钟 2-小时 3-日 4-周 5-月 6-年',
               `price` int(11) NULL DEFAULT NULL COMMENT '价格',
               `plan_amount` int(11) NULL DEFAULT NULL COMMENT '计划总量',
               `plan_cost` int(11) NULL DEFAULT NULL COMMENT '计划成本',
               `actual_amount` int(11) NULL DEFAULT NULL COMMENT '实际总量',
               `actual_cost` int(11) NULL DEFAULT NULL COMMENT '实际成本',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-已删除 1-未删除',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-资源' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="58" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `bim_model`
                MODIFY COLUMN `reason` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原因' AFTER `status`;
        </sql>
    </changeSet>

    <changeSet id="59" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fsdm_stage_template`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '进度管理-工序模版' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_stage`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `template_id` int(11) NOT NULL COMMENT '模版ID',
               `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '进度管理-工序' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="60" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fsdm_stage`
                ADD COLUMN `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记' AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="61" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fsdm_task`
                ADD COLUMN `template_id` int NULL COMMENT '工序模版ID' AFTER `remark`,
                ADD COLUMN `template_name` varchar(32) NULL COMMENT '工序模版名称' AFTER `template_id`,
                ADD COLUMN `subcontractor_id` int NULL COMMENT '分包单位ID' AFTER `template_name`,
                ADD COLUMN `subcontractor_name` varchar(32) NULL COMMENT '分包单位名称' AFTER `subcontractor_id`;
        </sql>
    </changeSet>

    <changeSet id="62" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            CREATE TABLE `car_info`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `license_plate` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '车牌号',
             `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
             `group_id` int(11) NULL DEFAULT NULL COMMENT '班组ID',
             `group_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班组ID',
             `load` decimal(10, 2) NULL DEFAULT NULL COMMENT '载重量',
             `driver` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '司机',
             `enter_time` date NULL DEFAULT NULL COMMENT '进场日期',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车辆-基本信息' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="63" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            ALTER TABLE `fsdm_task`
                ADD COLUMN `stage_round` int NOT NULL COMMENT '工序轮数' AFTER `subcontractor_name`;

            CREATE TABLE `fsdm_task_stage`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `plan_id` bigint(20) NOT NULL COMMENT '计划ID',
                `task_id` bigint(20) NOT NULL COMMENT '任务ID',
                `stage_round` int(11) NULL DEFAULT NULL COMMENT '工序轮数',
                `stage_id` int(11) NULL DEFAULT NULL COMMENT '工序ID',
                `stage_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序名称',
                `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
                `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
                `duration` int(11) NULL DEFAULT NULL COMMENT '时长',
                `state` int(11) NOT NULL COMMENT '状态:0-未开始 1-执行中 2-已完成',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '进度管理-任务-工序执行' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_task_stage_ext`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `task_stage_id` int(11) NOT NULL COMMENT '工序执行ID',
                `hole_num` int(11) NULL DEFAULT NULL COMMENT '孔位数量',
                `blast_amount` double NULL DEFAULT NULL COMMENT '炸药用量',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '进度管理-任务-工序执行-扩展信息' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="64" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_task_resource`
                MODIFY COLUMN `actual_amount` int(11) NULL DEFAULT NULL COMMENT '实际总量' AFTER `plan_amount`;

            ALTER TABLE `fsdm_task_quantity`
                MODIFY COLUMN `id` bigint(20) NOT NULL COMMENT '主键' FIRST,
                MODIFY COLUMN `dept_id` int(11) NOT NULL COMMENT '项目ID' AFTER `id`,
                MODIFY COLUMN `plan_id` bigint(20) NOT NULL COMMENT '计划ID' AFTER `dept_id`,
                MODIFY COLUMN `task_id` bigint(20) NOT NULL COMMENT '任务ID' AFTER `plan_id`,
                MODIFY COLUMN `quantity_id` int(11) NULL DEFAULT NULL COMMENT '工程量类型ID' AFTER `task_id`,
                MODIFY COLUMN `quantity_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工程里类型名称' AFTER `quantity_id`,
                MODIFY COLUMN `quantity_unit_id` int(11) NULL DEFAULT NULL COMMENT '计量单位ID' AFTER `quantity_name`,
                MODIFY COLUMN `quantity_unit_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计量单位名称' AFTER `quantity_unit_id`,
                MODIFY COLUMN `plan_amount` int(11) NULL DEFAULT NULL COMMENT '计划量' AFTER `quantity_unit_name`,
                MODIFY COLUMN `actual_amount` int(11) NULL DEFAULT NULL COMMENT '实际量' AFTER `plan_amount`,
                MODIFY COLUMN `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `del_flag`,
                MODIFY COLUMN `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `update_time`;

            ALTER TABLE `fsdm_plan`
                MODIFY COLUMN `id` bigint(20) NOT NULL COMMENT '主键' FIRST,
                MODIFY COLUMN `pid` bigint(20) NULL DEFAULT NULL COMMENT '父级ID' AFTER `id`,
                MODIFY COLUMN `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称' AFTER `full_code`,
                MODIFY COLUMN `actual_start` date NULL DEFAULT NULL COMMENT '实际开始日期' AFTER `plan_duration`,
                MODIFY COLUMN `actual_finish` date NULL DEFAULT NULL COMMENT '实际完成日期' AFTER `actual_start`,
                MODIFY COLUMN `update_user_id` int(11) NULL DEFAULT NULL COMMENT '更新人ID' AFTER `remark`,
                MODIFY COLUMN `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人姓名' AFTER `update_user_id`,
                MODIFY COLUMN `create_user_id` int(11) NULL DEFAULT NULL COMMENT '创建人ID' AFTER `update_user_name`,
                MODIFY COLUMN `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人姓名' AFTER `create_user_id`,
                MODIFY COLUMN `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除' AFTER `create_user_name`,
                MODIFY COLUMN `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `del_flag`,
                MODIFY COLUMN `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `update_time`;
        </sql>
    </changeSet>

    <changeSet id="65" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `const_record`
                MODIFY COLUMN `dept_id` int(11) NOT NULL COMMENT '组织机构id' AFTER `id`,
                MODIFY COLUMN `time` datetime(0) NOT NULL COMMENT '提交时间' AFTER `dept_id`,
                MODIFY COLUMN `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存在问题' AFTER `part_name`,
                ADD COLUMN `plan` varchar(255) NULL COMMENT '明日计划' AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="66" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `car_info`
                ADD COLUMN `type` int NULL COMMENT '车辆类型:1-自有车辆 2-外部车辆' AFTER `group_name`;
        </sql>
    </changeSet>

    <changeSet id="67" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_task`
                MODIFY COLUMN `stage_round` int(11) NULL DEFAULT 1 COMMENT '工序轮数' AFTER `subcontractor_name`;
        </sql>
    </changeSet>

    <changeSet id="68" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fsdm_task`
                ADD COLUMN `family_type` varchar(32) NULL COMMENT '构件-familyType' AFTER `stage_round`,
                ADD COLUMN `family` varchar(32) NULL COMMENT '构件-family' AFTER `family_type`,
                ADD COLUMN `element` varchar(32) NULL COMMENT '构件-element' AFTER `family`,
                ADD COLUMN `volume` double NULL COMMENT '构件-体积' AFTER `element`,
                ADD COLUMN `depth` double NULL COMMENT '构件-进尺' AFTER `volume`,
                ADD COLUMN `hole_num` int NULL COMMENT '构件-炸药补孔数量' AFTER `depth`;
        </sql>
    </changeSet>

    <changeSet id="69" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_task_quantity`
                MODIFY COLUMN `plan_amount` double(11, 2) NULL DEFAULT NULL COMMENT '计划量' AFTER `quantity_unit_name`,
                MODIFY COLUMN `actual_amount` double(11, 2) NULL DEFAULT NULL COMMENT '实际量' AFTER `plan_amount`;

            ALTER TABLE `fsdm_task_quantity_log`
                MODIFY COLUMN `actual_amount` double(11, 2) NOT NULL DEFAULT 0 COMMENT '实际工程量' AFTER `task_id`;

            ALTER TABLE `fsdm_task_resource`
                MODIFY COLUMN `price` double(11, 2) NULL DEFAULT NULL COMMENT '价格' AFTER `rate_unit`,
                MODIFY COLUMN `plan_amount` double(11, 2) NULL DEFAULT NULL COMMENT '计划总量' AFTER `price`,
                MODIFY COLUMN `actual_amount` double(11, 2) NULL DEFAULT NULL COMMENT '实际总量' AFTER `plan_amount`,
                MODIFY COLUMN `plan_cost` double(11, 2) NULL DEFAULT NULL COMMENT '计划成本' AFTER `actual_amount`,
                MODIFY COLUMN `actual_cost` double(11, 2) NULL DEFAULT NULL COMMENT '实际成本' AFTER `plan_cost`;

            ALTER TABLE `fsdm_resource`
                MODIFY COLUMN `price` double(11, 2) NULL DEFAULT NULL COMMENT '价格' AFTER `rate_unit`;
        </sql>
    </changeSet>

    <changeSet id="70" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_task`
                MODIFY COLUMN `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称' AFTER `code`;
        </sql>
    </changeSet>

    <changeSet id="71" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tw_dict`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `pid` int(11) NOT NULL,
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段',
                `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记,0-未删除 1-已删除',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '临时工程-数据字典' ROW_FORMAT = Dynamic;

            CREATE TABLE `tw_base`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `cr` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '承包商代表',
                `em` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工程部经理',
                `twc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '临时工程-协调',
                `twd` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '临时工程-设计',
                `tws` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '临时工程-监理',
                `ice` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '临时工程-稽核',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

            CREATE TABLE `tw_t1`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `t1_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 't1编号',
                `design_package_id` int(11) NULL DEFAULT NULL COMMENT '工序ID',
                `design_package` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序名称',
                `risk_cat` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风险等级',
                `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="72" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `tw_t1`
                MODIFY COLUMN `design_package` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序名称' AFTER `design_package_id`;
        </sql>
    </changeSet>

    <changeSet id="73" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_t1`
                ADD COLUMN `t1_name` varchar(32) NULL COMMENT 't1名称' AFTER `t1_no`;
        </sql>
    </changeSet>

    <changeSet id="74" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tw_t2`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `t1_id` int(11) NOT NULL COMMENT 't1编号',
              `t2_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 't2编号',
              `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设计简介',
              `design_allocated_to` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设计者',
              `design_delivery_date` date NULL DEFAULT NULL COMMENT '交付日期',
              `design_notice_given` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知',
              `review_workshop_date` date NULL DEFAULT NULL COMMENT '评审日期',
              `review_workshop_option` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审结果',
              `review_conculsion` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审结论',
              `initiated` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '初始化',
              `issued` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交',
              `acknowledged` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '确认',
              `agreed` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布',
              `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="75" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tw_t3`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `t1_id` int(11) NOT NULL COMMENT 't1编号',
              `t3_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 't3编号',
              `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地点',
              `element` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要素',
              `change` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更',
              `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '理由',
              `checker` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人',
              `approval_date` date NULL DEFAULT NULL COMMENT '要求批准日期',
              `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="76" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tw_t4`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `t1_id` int(11) NOT NULL COMMENT 't1编号',
              `permit_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '许可类型',
              `permit_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '许可编号',
              `permit_valid_util` date NULL DEFAULT NULL COMMENT '许可有效期',
              `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地点',
              `element` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '要素',
              `operation` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '施工范围',
              `other_refer` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他参考',
              `removal` int(11) NULL DEFAULT NULL COMMENT '是否要拆除许可',
              `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="77" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `const_report_attach`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `date` date NULL DEFAULT NULL COMMENT '日期',
                `attach` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件信息',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '施工日报-附件' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="78" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_plan`
                ADD COLUMN `bim_model` varchar(32) NULL COMMENT '模型ID' AFTER `remark`;

            ALTER TABLE `fsdm_task_quantity`
                ADD COLUMN `over_amount` double(11, 2) NULL COMMENT '超挖量' AFTER `actual_amount`,
                ADD COLUMN `under_amount` double(11, 2) NULL COMMENT '欠挖量' AFTER `over_amount`,
                ADD COLUMN `slag_amount` double(11, 2) NULL COMMENT '出渣量' AFTER `under_amount`;
        </sql>
    </changeSet>

    <changeSet id="79" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `si_info`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `subcontract_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包合同编号',
                `subcontract_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包合同名称',
                `sis_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SI编号',
                `issue_date` date NULL DEFAULT NULL COMMENT '发布日期',
                `send_to` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包单位',
                `attention` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包老板',
                `ext_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
                `state` int(11) NULL DEFAULT NULL COMMENT '状态',
                `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
                `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人',
                `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作指令-信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `si_assessment`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `si_id` int(11) NOT NULL COMMENT 'SI的ID',
              `section_a` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SectionA',
              `section_b` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'SectionB',
              `state` int(11) NULL DEFAULT NULL COMMENT '状态',
              `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
              `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人',
              `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作指令-评估' ROW_FORMAT = Dynamic;

            CREATE TABLE `si_op_log`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `obj_type` int(11) NOT NULL COMMENT '业务对象类型',
              `obj_id` int(11) NOT NULL COMMENT '业务对象ID',
              `type` int(11) NOT NULL COMMENT '操作类型:1-提交 10-一审 20-二审 30-三审 40-四审 50-五审 ',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作名称',
              `result` int(11) NOT NULL COMMENT '操作结果:1-通过 2-不通过',
              `op_state` int(11) NOT NULL DEFAULT 0 COMMENT '操作状态:0-未操作 1-已操作',
              `op_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
              `op_user_id` int(11) NULL DEFAULT NULL COMMENT '操作用户',
              `op_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作用户名称',
              `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注',
              `sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名图片',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 419 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作指令-操作日志' ROW_FORMAT = Dynamic;

            CREATE TABLE `si_dict`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
                `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
                `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他信息',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工程指令-数据字典' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="80" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `energy_water_meter`
                ADD COLUMN `area_id` int NULL COMMENT '工区ID' AFTER `name`,
                ADD COLUMN `area_name` varchar(32) NULL COMMENT '工区名称' AFTER `area_id`;

            ALTER TABLE `energy_electric_meter`
                ADD COLUMN `area_id` int NULL COMMENT '工区ID' AFTER `type`,
                ADD COLUMN `area_name` varchar(32) NULL COMMENT '工区名称' AFTER `area_id`;
        </sql>
    </changeSet>

    <changeSet id="81" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            CREATE TABLE `tw_tz`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `t1_id` int(11) NOT NULL COMMENT 't1编号',
              `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '临时工程-图纸' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="82" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_tz`
                ADD COLUMN `check_flag` int NULL DEFAULT 0 COMMENT 'check标记' AFTER `t1_id`,
                ADD COLUMN `review_flag` int NULL DEFAULT 0 COMMENT 'review标记' AFTER `check_flag`,
                ADD COLUMN `release_flag` int NULL DEFAULT 0 COMMENT 'release标记' AFTER `review_flag`;
        </sql>
    </changeSet>

    <changeSet id="83" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `mail_config`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `email_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱类型',
                `host` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件服务器host',
                `port` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件服务器port',
                `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱用户名',
                `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱密码',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮件-配置' ROW_FORMAT = Dynamic;

            CREATE TABLE `mail_type`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类',
              `letter_reference` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
              `date` date NULL DEFAULT NULL COMMENT '日期',
              `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮件-分类' ROW_FORMAT = Dynamic;

            CREATE TABLE `mail_info`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `mail_type_id` int(11) NOT NULL COMMENT '邮件归类ID',
              `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题',
              `sent_date` datetime(0) NULL DEFAULT NULL COMMENT '发送时间',
              `received_date` datetime(0) NULL DEFAULT NULL COMMENT '接收时间',
              `from` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发件人',
              `message_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件消息ID',
              `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件原始数据',
              `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '邮箱解析详情',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮件-解析' ROW_FORMAT = Dynamic;

            CREATE TABLE `mail_attach`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `mail_info_id` int(11) NOT NULL COMMENT '邮件ID',
                `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮件服务器host',
                `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮件服务器port',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮件-附件' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="84" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `si_subcontract`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
               `subcontract_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
               `subcontract_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同名称',
               `corp_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包单位',
               `attention` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分包老板',
               `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SI-分包合同' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="85" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `si_mail`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `si_id` int(11) NOT NULL COMMENT 'SI的ID',
                `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件主题',
                `sent_date` datetime(0) NULL DEFAULT NULL COMMENT '发送时间',
                `from` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发件人',
                `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '邮件详情',
                `state` int(11) NULL DEFAULT 0 COMMENT '发送状态',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作指令-邮件发送' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="86" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `mail_attach`
                MODIFY COLUMN `name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件名称' AFTER `mail_info_id`,
                MODIFY COLUMN `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件地址' AFTER `name`;

            ALTER TABLE `mail_type`
                MODIFY COLUMN `subject` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题' AFTER `date`;

            ALTER TABLE `mail_info`
                MODIFY COLUMN `subject` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题' AFTER `mail_type_id`;
        </sql>
    </changeSet>

    <changeSet id="87" author="xuguocheng">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `bim_model`
                ADD COLUMN `idx` int NULL DEFAULT 0 COMMENT '排序' AFTER `status`;
        </sql>
    </changeSet>

    <changeSet id="88" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fire_work`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `work_type_id` int(11) NULL DEFAULT NULL COMMENT '作业类型ID',
              `work_type_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作业类型名称',
              `work_start_time` datetime(0) NULL DEFAULT NULL COMMENT '作业开始时间',
              `work_end_time` datetime(0) NULL DEFAULT NULL COMMENT '作业结束时间',
              `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作业位置描述',
              `state` int(11) NULL DEFAULT NULL COMMENT '状态',
              `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '申请时间',
              `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '申请人ID',
              `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人姓名',
              `commit_user_sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人签名',
              `approve_time` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
              `approve_user_id` int(11) NULL DEFAULT NULL COMMENT '审批人ID',
              `approve_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批人姓名',
              `approve_user_sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批人签名',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '动火作业-申请记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `fire_work_img`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `fire_work_id` int(11) NOT NULL COMMENT '作业ID',
              `img_url` int(11) NULL DEFAULT NULL COMMENT '图片URL',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '动火作业-现场图片' ROW_FORMAT = Dynamic;

            CREATE TABLE `fire_work_inspect`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `fire_work_id` int(11) NOT NULL COMMENT '作业ID',
              `idx` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '序号',
              `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
              `result` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '动火作业-自检信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `fire_work_user`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `fire_work_id` int(11) NOT NULL COMMENT '作业ID',
               `type` int(11) NULL DEFAULT NULL COMMENT '类型',
               `user_id` int(11) NULL DEFAULT NULL COMMENT '用户ID',
               `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '动火作业-审批用户' ROW_FORMAT = Dynamic;

            CREATE TABLE `app_dict`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT,
                 `dept_id` int(11) NOT NULL COMMENT '项目ID',
                 `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
                 `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                 `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                 `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
                 `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
                 `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他信息',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                 `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用数据字典' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="90" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fire_work_img`
                MODIFY COLUMN `img_url` varchar(255) NULL DEFAULT NULL COMMENT '图片URL' AFTER `fire_work_id`;
        </sql>
    </changeSet>

    <changeSet id="91" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `mail_info`
                ADD COLUMN `archive` int NULL DEFAULT 0 COMMENT '是否归档' AFTER `detail`;
        </sql>
    </changeSet>

    <changeSet id="92" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `si_info`
                MODIFY COLUMN `sis_no` varchar(220) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SI编号' AFTER `subcontract_title`;
        </sql>
    </changeSet>

    <changeSet id="93" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `si_op_log`
                ADD COLUMN `position` varchar(100) NULL COMMENT '职位' AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="94" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fsdm_config`
                MODIFY COLUMN `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称' AFTER `dept_id`,
                MODIFY COLUMN `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码' AFTER `name`,
                MODIFY COLUMN `value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值' AFTER `code`;
        </sql>
    </changeSet>

    <changeSet id="95" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_news`
                ADD COLUMN `dept_id` int NOT NULL DEFAULT 0 COMMENT '项目ID' AFTER `id`,
                ADD COLUMN `url` varchar(200) NULL COMMENT '地址' AFTER `cover`;
        </sql>
    </changeSet>

    <changeSet id="96" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_news`
                MODIFY COLUMN `type` int(11) NOT NULL DEFAULT 1 COMMENT '类型' AFTER `dept_id`,
                MODIFY COLUMN `category` int(11) NULL DEFAULT 1 COMMENT '种类' AFTER `type`,
                MODIFY COLUMN `brief` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介' AFTER `title`,
                MODIFY COLUMN `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容' AFTER `brief`,
                MODIFY COLUMN `cover` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '封面' AFTER `content`;


            ALTER TABLE `bim_model`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
                ADD COLUMN `guid` varchar(32) NULL COMMENT 'guid' AFTER `dept_id`,
                MODIFY COLUMN `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `del_flag`,
                MODIFY COLUMN `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `update_time`;
        </sql>
    </changeSet>

    <changeSet id="97" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_sign`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
             `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
             `biz_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务对象ID',
             `biz_log_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业主日志ID',
             `time` datetime(0) NULL DEFAULT NULL COMMENT '签名时间',
             `sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '流程签名' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="98" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_t3`
                ADD COLUMN `sign1` varchar(200) NULL COMMENT '签名1' AFTER `approval_date`,
                ADD COLUMN `sign2` varchar(200) NULL COMMENT '签名2' AFTER `sign1`,
                ADD COLUMN `sign3` varchar(200) NULL COMMENT '签名3' AFTER `sign2`,
                ADD COLUMN `sign4` varchar(200) NULL COMMENT '签名4' AFTER `sign3`;

            ALTER TABLE `tw_t4`
                ADD COLUMN `sign1` varchar(200) NULL COMMENT '签名1' AFTER `removal`,
                ADD COLUMN `sign2` varchar(200) NULL COMMENT '签名2' AFTER `sign1`,
                ADD COLUMN `sign3` varchar(200) NULL COMMENT '签名3' AFTER `sign2`,
                ADD COLUMN `sign4` varchar(200) NULL COMMENT '签名4' AFTER `sign3`;
        </sql>
    </changeSet>

    <changeSet id="99" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_t2`
                ADD COLUMN `sign1` varchar(200) NULL COMMENT '签名1' AFTER `agreed`,
                ADD COLUMN `sign2` varchar(200) NULL COMMENT '签名2' AFTER `sign1`,
                ADD COLUMN `sign3` varchar(200) NULL COMMENT '签名3' AFTER `sign2`,
                ADD COLUMN `sign4` varchar(200) NULL COMMENT '签名4' AFTER `sign3`,
                ADD COLUMN `time1` datetime NULL COMMENT '时间1' AFTER `sign4`,
                ADD COLUMN `time2` datetime NULL COMMENT '时间2' AFTER `time1`,
                ADD COLUMN `time3` datetime NULL COMMENT '时间3' AFTER `time2`,
                ADD COLUMN `time4` datetime NULL COMMENT '时间4' AFTER `time3`;
        </sql>
    </changeSet>

    <changeSet id="100" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `car_gate`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'GUID',
                `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台',
                `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SN',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `direction` int(11) NULL DEFAULT NULL COMMENT '方向',
                `lng` double(10, 6) NULL DEFAULT NULL COMMENT '经度',
                `lat` double(10, 6) NULL DEFAULT NULL COMMENT '纬度',
                `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
                `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
                `state` int(11) NULL DEFAULT NULL COMMENT '状态',
                `del_flag` int(11) NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车辆-车辆道闸' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="101" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `tw_t1`
                MODIFY COLUMN `t1_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 't1名称' AFTER `t1_no`;
        </sql>
    </changeSet>

    <changeSet id="102" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_base`
                ADD COLUMN `cr_user_id` int NULL COMMENT '承包商代表-用户ID' AFTER `cr`,
                ADD COLUMN `cr_user_name` varchar(32) NULL COMMENT '承包商代表-用户' AFTER `cr_user_id`,
                ADD COLUMN `em_user_id` int NULL COMMENT '工程部经理-用户ID' AFTER `em`,
                ADD COLUMN `em_user_name` varchar(32) NULL COMMENT '工程部经理-用户' AFTER `em_user_id`,
                ADD COLUMN `twc_user_id` int NULL COMMENT '临时工程-协调-用户ID' AFTER `twc`,
                ADD COLUMN `twc_user_name` varchar(32) NULL COMMENT '临时工程-协调-用户' AFTER `twc_user_id`,
                ADD COLUMN `twd_user_id` int NULL COMMENT '临时工程-设计-用户ID' AFTER `twd`,
                ADD COLUMN `twd_user_name` varchar(32) NULL COMMENT '临时工程-设计-用户' AFTER `twd_user_id`,
                ADD COLUMN `tws_user_id` int NULL COMMENT '临时工程-监理-用户ID' AFTER `tws`,
                ADD COLUMN `tws_user_name` varchar(32) NULL COMMENT '临时工程-监理-用户名称' AFTER `tws_user_id`,
                ADD COLUMN `ice_user_id` int NULL COMMENT '临时工程-稽核-用户ID' AFTER `ice`,
                ADD COLUMN `ice_user_name` varchar(32) NULL COMMENT '临时工程-稽核-用户' AFTER `ice_user_id`;
        </sql>
    </changeSet>

    <changeSet id="103" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tbm_info`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                 `dept_id` int(11) NOT NULL COMMENT '项目ID',
                 `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                 `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称',
                 `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件厂家',
                 `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN',
                 `time` datetime(0) NULL DEFAULT NULL COMMENT '通信时间',
                 `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '在线状态',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'tbm-设备信息' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="104" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_t3`
                ADD COLUMN `time1` datetime NULL COMMENT '时间1' AFTER `sign4`,
                ADD COLUMN `time2` datetime NULL COMMENT '时间2' AFTER `time1`,
                ADD COLUMN `time3` datetime NULL COMMENT '时间3' AFTER `time2`,
                ADD COLUMN `time4` datetime NULL COMMENT '时间4' AFTER `time3`;

            ALTER TABLE `tw_t4`
                ADD COLUMN `time1` datetime NULL COMMENT '时间1' AFTER `sign4`,
                ADD COLUMN `time2` datetime NULL COMMENT '时间2' AFTER `time1`,
                ADD COLUMN `time3` datetime NULL COMMENT '时间3' AFTER `time2`,
                ADD COLUMN `time4` datetime NULL COMMENT '时间4' AFTER `time3`;
        </sql>
    </changeSet>

    <changeSet id="105" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_t2`
                ADD COLUMN `state` int NULL DEFAULT 0 COMMENT '状态' AFTER `detail`;

            ALTER TABLE `tw_t3`
                ADD COLUMN `state` int NULL DEFAULT 0 COMMENT '状态' AFTER `detail`;

            ALTER TABLE `tw_t4`
                ADD COLUMN `state` int NULL DEFAULT 0 COMMENT '状态' AFTER `detail`;
        </sql>
    </changeSet>

    <changeSet id="106" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `bim_component`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `model_id` int(11) NOT NULL COMMENT '模型ID',
              `element` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '构件ID',
              `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '构件名称',
              `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
              `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
              `ext3` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段3',
              `ext4` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段4',
              `time` datetime(0) NULL DEFAULT NULL COMMENT '状态时间',
              `state` int(11) NOT NULL DEFAULT 0 COMMENT '构件状态',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'bim模型-构件状态' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="107" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `mail_type`
                ADD COLUMN `status` int NULL DEFAULT 0 COMMENT '回复状态' AFTER `subject`,
                ADD COLUMN `reply_date` date NULL COMMENT '回复日期' AFTER `status`,
                ADD COLUMN `overdue` int NULL DEFAULT 0 COMMENT '超期状态' AFTER `reply_date`;
            ALTER TABLE `mail_info`
                ADD COLUMN `reply` int NULL DEFAULT 0 COMMENT '是否回复' AFTER `archive`;
        </sql>
    </changeSet>

    <changeSet id="108" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fan_device`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
               `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
               `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件SN',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
               `time` datetime(0) NULL DEFAULT NULL COMMENT '通信时间',
               `net_state` int(11) NULL DEFAULT 0 COMMENT '网络状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风机-设备' ROW_FORMAT = Dynamic;

            CREATE TABLE `fan_device_data`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `fan_id` int(11) NOT NULL COMMENT '设备ID',
                `time` datetime(0) NOT NULL COMMENT '数据时间',
                `status` int(11) NULL DEFAULT NULL COMMENT '风机启停:0-关 1-开',
                `speed_preset` int(11) NULL DEFAULT NULL COMMENT '设定速度',
                `speed_actual` int(11) NULL DEFAULT NULL COMMENT '实际速度',
                `temperature` double(10, 2) NULL DEFAULT NULL COMMENT '温度',
                `current` double(10, 2) NULL DEFAULT NULL COMMENT '电流',
                `run_time` double(10, 2) NULL DEFAULT NULL COMMENT '运行时间',
                `consumption` double(10, 2) NULL DEFAULT NULL COMMENT '耗电量',
                `key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '激活码',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '风机-实时数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="109" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `bim_component`
                ADD COLUMN `model_guid` varchar(32) NOT NULL COMMENT '模型GUID' AFTER `model_id`;
        </sql>
    </changeSet>

    <changeSet id="110" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `si_info`
                ADD COLUMN `export_url` varchar(200) NULL COMMENT '导出地址' AFTER `commit_user_name`;

            ALTER TABLE `si_assessment`
                ADD COLUMN `export_url` varchar(200) NULL COMMENT '导出地址' AFTER `commit_user_name`;

            ALTER TABLE `tw_t2`
                ADD COLUMN `export_url` varchar(200) NULL COMMENT '导出地址' AFTER `state`;

            ALTER TABLE `tw_t3`
                ADD COLUMN `export_url` varchar(200) NULL COMMENT '导出地址' AFTER `state`;

            ALTER TABLE `tw_t4`
                ADD COLUMN `export_url` varchar(200) NULL COMMENT '导出地址' AFTER `state`;
        </sql>
    </changeSet>

    <changeSet id="111" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `lifting_permit`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
               `date` date NULL DEFAULT NULL COMMENT '日期',
               `location` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '位置',
               `specification` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工程说明',
               `model` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号',
               `enable_flag` int(11) NULL DEFAULT NULL COMMENT '能否起吊',
               `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作员',
               `sling_worker` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '吊索工',
               `card_1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执照编号',
               `card_2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平安卡号',
               `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细信息',
               `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '申请时间',
               `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '申请人',
               `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人姓名',
               `commit_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人签名',
               `commit_position` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人职位',
               `issue_time` datetime(0) NULL DEFAULT NULL COMMENT '签发时间',
               `issue_user_id` int(11) NULL DEFAULT NULL COMMENT '签发人',
               `issue_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签发人姓名',
               `issue_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签发人签名',
               `issue_position` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签发人职位',
               `state` int(11) NULL DEFAULT NULL COMMENT '状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '吊运许可证' ROW_FORMAT = Dynamic;

            CREATE TABLE `lifting_permit_item`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `permit_id` int(11) NOT NULL COMMENT '许可证ID',
                `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `value` int(11) NULL DEFAULT NULL COMMENT '值',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '吊运许可证-检查项' ROW_FORMAT = Dynamic;

            CREATE TABLE `lifting_permit_attach`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `permit_id` int(11) NOT NULL COMMENT '许可证ID',
              `type` int(11) NULL DEFAULT NULL COMMENT '附件类型',
              `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件名称',
              `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件地址',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '吊运许可证-附件信息' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="112" author="xuguocheng">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `fan_device_data`
                ADD COLUMN `diameter` double(10, 2) NULL COMMENT '风机直径' AFTER `key`,
                ADD COLUMN `run_power` double(10, 2) NULL COMMENT '运行功率' AFTER `diameter`,
                ADD COLUMN `air_supply` double(10, 2) NULL COMMENT '送风量' AFTER `run_power`,
                ADD COLUMN `wind_pressure` double(10, 2) NULL COMMENT '风压' AFTER `air_supply`,
                ADD COLUMN `fvs_device_id` int NULL COMMENT '视频设备ID' AFTER `wind_pressure`,
                ADD COLUMN `fvs_device_name` varchar(64) NULL COMMENT '视频设备' AFTER `fvs_device_id`,
                ADD COLUMN `env_device_id` int NULL COMMENT '环境设备ID' AFTER `fvs_device_name`,
                ADD COLUMN `env_device_name` varchar(64) NULL COMMENT '环境设备' AFTER `env_device_id`;
        </sql>
    </changeSet>

    <changeSet id="113" author="xuguocheng">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `tw_t4`
                ADD COLUMN `construction_cert` varchar(200) NULL COMMENT '施工许可证' AFTER `removal`,
                ADD COLUMN `construction_cert_path` varchar(200) NULL COMMENT '施工许可证地址' AFTER `construction_cert`;
        </sql>
    </changeSet>

    <changeSet id="114" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `tw_base`
                ADD COLUMN `t1_id` int NULL DEFAULT 0 COMMENT 'T1' AFTER `guid`;

            ALTER TABLE `tw_t1`
                ADD COLUMN `data_type` int NULL DEFAULT 0 COMMENT '数据类型:0-流程 1-补录' AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="115" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `tw_op_log`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `obj_type` int(11) NOT NULL COMMENT '业务对象类型',
              `obj_id` int(11) NOT NULL COMMENT '业务对象ID',
              `type` int(11) NOT NULL COMMENT '操作类型:1-提交 10-一审 20-二审 30-三审 40-四审 50-五审 ',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作名称',
              `result` int(11) NOT NULL COMMENT '操作结果:1-通过 2-不通过',
              `op_state` int(11) NOT NULL DEFAULT 0 COMMENT '操作状态:0-未操作 1-已操作',
              `op_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
              `op_user_id` int(11) NULL DEFAULT NULL COMMENT '操作用户',
              `op_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作用户名称',
              `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注',
              `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
              `sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名图片',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '临时工程-操作日志' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="116" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `led_device`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
               `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备名称',
               `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件平台',
               `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件SN',
               `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号',
               `data_type` int(11) NULL DEFAULT NULL COMMENT '数据来源',
               `obj_id` int(11) NULL DEFAULT NULL COMMENT '数据来源对象ID',
               `obj_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源对象名称',
               `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
               `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
               `ext3` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段3',
               `ext4` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段4',
               `time` datetime(0) NULL DEFAULT NULL COMMENT '通信时间',
               `net_state` int(11) NULL DEFAULT 0 COMMENT '网络状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'LED-设备' ROW_FORMAT = Dynamic;

            CREATE TABLE `led_device_area`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `led_id` int(11) NOT NULL COMMENT 'LED设备ID',
                `area` int(11) NULL DEFAULT NULL COMMENT '内码区域ID',
                `color` int(11) NULL DEFAULT NULL COMMENT '颜色',
                `tx` int(11) NULL DEFAULT NULL COMMENT '特效',
                `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'LED-设备显示区域' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="117" author="xuguocheng">
        <comment>新建字段</comment>
        <sql>
            ALTER TABLE `si_subcontract`
                ADD COLUMN `email` varchar(64) NULL COMMENT '电子邮件' AFTER `attention`,
                ADD COLUMN `ext1` varchar(64) NULL COMMENT '扩展字段1' AFTER `email`,
                ADD COLUMN `ext2` varchar(64) NULL COMMENT '扩展字段2' AFTER `ext1`,
                ADD COLUMN `ext3` varchar(64) NULL COMMENT '扩展字段3' AFTER `ext2`,
                ADD COLUMN `ext4` varchar(64) NULL COMMENT '扩展字段4' AFTER `ext3`;
        </sql>
    </changeSet>

    <changeSet id="118" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `tw_t2`
                MODIFY COLUMN `review_workshop_option` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审结果' AFTER `review_workshop_date`,
                MODIFY COLUMN `review_conculsion` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审结论' AFTER `review_workshop_option`;
        </sql>
    </changeSet>

    <changeSet id="119" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `energy_electric_box`
                ADD COLUMN `distribute_level` varchar(32) NULL COMMENT '配电级别' AFTER `sn`,
                ADD COLUMN `rated_voltage` varchar(32) NULL COMMENT '额定电压' AFTER `distribute_level`,
                ADD COLUMN `rated_power` varchar(32) NULL COMMENT '额定功率' AFTER `rated_voltage`;
        </sql>
    </changeSet>

    <changeSet id="120" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `lifting_permit`
                ADD COLUMN `other` varchar(32) NULL COMMENT '型号(其他)' AFTER `model`;
        </sql>
    </changeSet>

    <changeSet id="121" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `si_assessment`
                ADD COLUMN `sia_no` varchar(32) NULL COMMENT 'SIA编号' AFTER `si_id`;
        </sql>
    </changeSet>

    <changeSet id="122" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `car_inout_record`
                MODIFY COLUMN `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '车主名称' AFTER `device_name`;
        </sql>
    </changeSet>

    <changeSet id="123" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `lifting_permit`
                ADD COLUMN `lifting_weight` double(10, 2) NULL COMMENT '吊运物重量' AFTER `enable_flag`,
                ADD COLUMN `danger_other` varchar(100) NULL COMMENT '其他危险点' AFTER `detail`;
        </sql>
    </changeSet>

    <changeSet id="124" author="xuguocheng">
        <comment>LED字段修改</comment>
        <sql>
            ALTER TABLE `led_device`
            DROP COLUMN `data_type`,
            DROP COLUMN `obj_id`,
            DROP COLUMN `obj_name`,
            DROP COLUMN `ext1`,
            DROP COLUMN `ext2`,
            DROP COLUMN `ext3`,
            DROP COLUMN `ext4`,
            ADD COLUMN `width` int NULL COMMENT '宽度' AFTER `model`,
            ADD COLUMN `height` int NULL COMMENT '高度' AFTER `width`,
            ADD COLUMN `display` text NULL COMMENT '显示信息' AFTER `net_state`;

            DROP TABLE led_device_area;
        </sql>
    </changeSet>

    <changeSet id="125" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `trolley_lining_device`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
              `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
              `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件平台',
              `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN',
              `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
              `net_state` int(11) NULL DEFAULT NULL COMMENT '在线状态',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '二衬台车-设备信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `trolley_lining_device_data`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `device_id` int(11) NOT NULL COMMENT '设备ID',
               `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
               `left_level_1` int(11) NULL DEFAULT NULL COMMENT '左液位1',
               `left_level_2` int(11) NULL DEFAULT NULL COMMENT '左液位2',
               `left_level_3` int(11) NULL DEFAULT NULL COMMENT '左液位3',
               `left_level_4` int(11) NULL DEFAULT NULL COMMENT '左液位4',
               `left_level_5` int(11) NULL DEFAULT NULL COMMENT '左液位5',
               `left_level_6` int(11) NULL DEFAULT NULL COMMENT '左液位6',
               `left_level_7` int(11) NULL DEFAULT NULL COMMENT '左液位7',
               `left_level_8` int(11) NULL DEFAULT NULL COMMENT '左液位8',
               `right_level_1` int(11) NULL DEFAULT NULL COMMENT '右液位1',
               `right_level_2` int(11) NULL DEFAULT NULL COMMENT '右液位2',
               `right_level_3` int(11) NULL DEFAULT NULL COMMENT '右液位3',
               `right_level_4` int(11) NULL DEFAULT NULL COMMENT '右液位4',
               `right_level_5` int(11) NULL DEFAULT NULL COMMENT '右液位5',
               `right_level_6` int(11) NULL DEFAULT NULL COMMENT '右液位6',
               `right_level_7` int(11) NULL DEFAULT NULL COMMENT '右液位7',
               `right_level_8` int(11) NULL DEFAULT NULL COMMENT '右液位8',
               `vault_filling_1` int(11) NULL DEFAULT NULL COMMENT '拱顶灌满检测1#',
               `vault_filling_2` int(11) NULL DEFAULT NULL COMMENT '拱顶灌满检测2#',
               `vault_filling_3` int(11) NULL DEFAULT NULL COMMENT '拱顶灌满检测3#',
               `vault_filling_4` int(11) NULL DEFAULT NULL COMMENT '拱顶灌满检测4#',
               `pour_status` int(11) NULL DEFAULT NULL COMMENT '浇筑状态',
               `left_tpl_status` int(11) NULL DEFAULT NULL COMMENT '左模板到位状态',
               `right_tpl_status` int(11) NULL DEFAULT NULL COMMENT '右模板到位状态',
               `vault_tpl_status` int(11) NULL DEFAULT NULL COMMENT '拱顶模板到位状态',
               `vault_pressure_1` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力1',
               `vault_pressure_2` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力2',
               `vault_pressure_3` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力3',
               `vault_pressure_4` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力4',
               `vault_pressure_5` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力5',
               `vault_pressure_6` double(10, 2) NULL DEFAULT NULL COMMENT '拱顶压力6',
               `env_temp` double(10, 2) NULL DEFAULT NULL COMMENT '环境温度',
               `pour_plate_num` int(11) NULL DEFAULT NULL COMMENT '浇筑板数',
               `pour_volume` double(10, 2) NULL DEFAULT NULL COMMENT '本次浇筑量',
               `total_pour_volume` double(10, 2) NULL DEFAULT NULL COMMENT '总浇筑量',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '二衬台车-设备数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `trolley_maintain_device`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件平台',
                `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN',
                `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
                `net_state` int(11) NULL DEFAULT NULL COMMENT '在线状态',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '养护台车-设备信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `trolley_maintain_device_data`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                 `dept_id` int(11) NOT NULL COMMENT '项目ID',
                 `device_id` int(11) NOT NULL COMMENT '设备ID',
                 `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
                 `forward1` int(11) NULL DEFAULT NULL COMMENT '养护台车前进',
                 `backward1` int(11) NULL DEFAULT NULL COMMENT '养护台车后退',
                 `forward2` int(11) NULL DEFAULT NULL COMMENT '养护小车前进',
                 `backward2` int(11) NULL DEFAULT NULL COMMENT '养护小车后退',
                 `maintain` int(11) NULL DEFAULT NULL COMMENT '养护使能',
                 `env_rh` double(10, 2) NULL DEFAULT NULL COMMENT '环境温度',
                 `env_temp` double(10, 2) NULL DEFAULT NULL COMMENT '环境湿度',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '养护台车-设备数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="126" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `trolley_rockcut_device`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
               `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
               `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件平台',
               `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '硬件SN',
               `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
               `net_state` int(11) NULL DEFAULT NULL COMMENT '在线状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岩凿台车-设备信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `trolley_rockcut_device_data`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `device_id` int(11) NOT NULL COMMENT '设备ID',
                `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
                `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岩凿台车-设备数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="127" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `hot_permit`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GUID',
               `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司',
               `date` date NULL DEFAULT NULL COMMENT '日期',
               `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地点',
               `limit_date` date NULL DEFAULT NULL COMMENT '工作时限到',
               `nature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作性质',
               `equipment` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '装备编号',
               `work_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作类别',
               `check_flag` int(11) NULL DEFAULT NULL COMMENT '是否完成开工检查',
               `license_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书编号',
               `license_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证书附件',
               `welder_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '焊工姓名',
               `welder_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '焊工签名',
               `welder_time` date NULL DEFAULT NULL COMMENT '焊工签名日期',
               `foreman_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管工姓名',
               `foreman_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管工签名',
               `no_spark` int(11) NULL DEFAULT NULL COMMENT '是否留有火种',
               `no_gas` int(11) NULL DEFAULT NULL COMMENT '是否留有气体',
               `cancel_welder_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注销-焊工签名',
               `cancel_welder_time` date NULL DEFAULT NULL COMMENT '注销-焊工签名日期',
               `cancel_foreman_sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注销-管工签名',
               `cancel_foreman_time` date NULL DEFAULT NULL COMMENT '注销-管工签名日期',
               `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人',
               `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
               `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
               `state` int(11) NULL DEFAULT NULL COMMENT '状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '热工作许可-基本信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `hot_permit_measure`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL,
               `permit_id` int(11) NOT NULL,
               `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
               `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
               `result` int(11) NULL DEFAULT NULL,
               `del_flag` int(11) NOT NULL DEFAULT 0,
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '热工作许可-防火措施' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="128" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `dig_permit`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GUID',
               `permit_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '许可证编号',
               `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
               `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地点',
               `dimentsion` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '挖掘范围',
               `reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '挖掘原因',
               `applicant_date` date NULL DEFAULT NULL COMMENT '申请日期',
               `applicant_user_id` int(11) NULL DEFAULT NULL COMMENT '申请人',
               `applicant_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人姓名',
               `competent_user_id` int(11) NULL DEFAULT NULL COMMENT '合资格人',
               `competent_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合资格人姓名',
               `se_user_id` int(11) NULL DEFAULT NULL COMMENT 'S/E',
               `se_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'S/E姓名',
               `competent_sign_b` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合资格人签名(B)',
               `competent_time_b` datetime(0) NULL DEFAULT NULL COMMENT '合资格人签名时间(B)',
               `approval_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认可号码',
               `expire_date` date NULL DEFAULT NULL COMMENT '有效期',
               `se_sign_c1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'S/E签名(C1)',
               `se_time_c1` datetime(0) NULL DEFAULT NULL COMMENT 'S/E签名时间(C1)',
               `competent_sign_c1` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合资格人签名(C1)',
               `competent_time_c1` datetime(0) NULL DEFAULT NULL COMMENT '合资格人签名时间(C1)',
               `safety_measure` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加安全措施',
               `se_sign_c2` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'S/E签名(C2)',
               `se_time_c2` datetime(0) NULL DEFAULT NULL COMMENT 'S/E签名时间(C2)',
               `competent_sign_d` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合资格人签名(D)',
               `competent_time_d` datetime(0) NULL DEFAULT NULL COMMENT '合资格人签名时间(D)',
               `applicant_sign_e` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请人签名(E)',
               `applicant_time_e` datetime(0) NULL DEFAULT NULL COMMENT '申请人签名时间(E)',
               `se_sign_f` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'S/E签名(F)',
               `se_time_f` datetime(0) NULL DEFAULT NULL COMMENT 'S/E签名时间(F)',
               `state` int(11) NULL DEFAULT NULL COMMENT '状态',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '挖掘许可' ROW_FORMAT = Dynamic;

            CREATE TABLE `dig_permit_attach`  (
                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                  `dept_id` int(11) NOT NULL COMMENT '项目ID',
                  `permit_id` int(11) NOT NULL COMMENT '许可ID',
                  `type` int(11) NOT NULL COMMENT '文件类型',
                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名称',
                  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件地址',
                  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                  PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '挖掘许可-附件信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `dig_permit_attendee`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `permit_id` int(11) NOT NULL COMMENT '许可ID',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
                `position` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
                `sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '挖掘许可-讲解出席' ROW_FORMAT = Dynamic;

            CREATE TABLE `dig_permit_utility`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `permit_id` int(11) NOT NULL COMMENT '许可ID',
               `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
               `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称英文',
               `other` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他',
               `result` int(11) NULL DEFAULT NULL COMMENT '结果',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '挖掘许可-公共设施' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="129" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `dig_permit_utility`
                MODIFY COLUMN `name_en` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称英文' AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="130" author="qinzexing">
        <comment>更新BIM构件表</comment>
        <sql>
            ALTER TABLE `bim_component`
            DROP COLUMN `element`,
            DROP COLUMN `name`,
            DROP COLUMN `ext1`,
            DROP COLUMN `ext2`,
            DROP COLUMN `ext3`,
            DROP COLUMN `ext4`,
            DROP COLUMN `time`,
            DROP COLUMN `state`,
            ADD COLUMN `component_id` varchar(64) NULL COMMENT '构件ID' AFTER `model_guid`,
            ADD COLUMN `node_id` varchar(64) NOT NULL COMMENT '节点ID' AFTER `component_id`,
            ADD COLUMN `node_type` varchar(32) NULL COMMENT '节点类型' AFTER `node_id`,
            ADD COLUMN `p_node_id` varchar(64) NULL COMMENT '父节点ID' AFTER `node_type`,
            ADD COLUMN `p_node_names` varchar(255) NULL COMMENT '父节点名称 以 / 分割' AFTER `p_node_id`,
            ADD COLUMN `node_name` varchar(32) NULL COMMENT '节点名称' AFTER `p_node_names`,
            ADD COLUMN `node_actual_name` varchar(32) NULL COMMENT '节点实际名称' AFTER `node_name`,
            ADD COLUMN `node_element_count` int(11) NULL COMMENT '元素数量' AFTER `node_actual_name`;
        </sql>
    </changeSet>

    <changeSet id="131" author="qinzexing">
        <comment>新增进度任务与BIM构件关联表</comment>
        <sql>
            CREATE TABLE `fsdm_task_element`  (
              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `plan_id` bigint(20) NOT NULL COMMENT '计划ID',
              `model_id` int(11) NOT NULL COMMENT '模型ID',
              `task_id` bigint(20) NOT NULL COMMENT '任务ID',
              `element` varchar(32) NOT NULL COMMENT '构件-element',
              `family` varchar(32) NULL COMMENT '构件-family',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '任务构件表';
        </sql>
    </changeSet>

    <changeSet id="132" author="qinzexing">
        <comment>更新数据到BIM构件关联表</comment>
        <sql>
            INSERT INTO `fsdm_task_element`(`plan_id`,`model_id`,`task_id`,`element`,`family`)
            SELECT ft.plan_id, fp.bim_model, ft.id, ft.element, ft.family
            FROM fsdm_task ft LEFT JOIN fsdm_plan fp ON ft.plan_id = fp.id WHERE ft.element IS NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="133" author="qinzexing">
        <comment>修改构件字段长度</comment>
        <sql>
            ALTER TABLE `bim_component`
                MODIFY COLUMN `component_id` varchar(128) NULL COMMENT '构件ID' AFTER `model_guid`,
                MODIFY COLUMN `node_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ID' AFTER `component_id`,
                MODIFY COLUMN `p_node_id` varchar(128) NULL COMMENT '父节点ID' AFTER `node_type`,
                MODIFY COLUMN `p_node_names` varchar(1000) NULL COMMENT '父节点名称 以 / 分割' AFTER `p_node_id`,
                MODIFY COLUMN `node_name` varchar(128) NULL COMMENT '节点名称' AFTER `p_node_names`,
                MODIFY COLUMN `node_actual_name` varchar(128) NULL COMMENT '节点实际名称' AFTER `node_name`;
        </sql>
    </changeSet>

    <changeSet id="134" author="qzexing">
        <comment>新增投标表</comment>
        <sql>
            CREATE TABLE `tender_comparison_base`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `guid` varchar(32) NOT NULL COMMENT '唯一编码',
               `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构ID',
               `project_id` int(11) NULL DEFAULT NULL COMMENT '项目ID',
               `date` date NULL DEFAULT NULL COMMENT '日期',
               `subcontract_no` varchar(200) NULL DEFAULT NULL COMMENT '分包合同编号',
               `trade` varchar(200) NULL DEFAULT NULL COMMENT '分项工程名称',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '投标比较-基本信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `tender_comparison_item`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `guid` varchar(32) NOT NULL COMMENT '唯一编码',
               `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构ID',
               `tender_id` int(11) NULL DEFAULT NULL COMMENT '投标比价ID',
               `item` varchar(100) NULL DEFAULT NULL COMMENT '编号',
               `description` varchar(400) NULL DEFAULT NULL COMMENT '描述',
               `type` varchar(200) NULL DEFAULT NULL COMMENT '类型',
               `unit` varchar(200) NULL DEFAULT NULL COMMENT '单位',
               `qty` double(10, 2) NULL DEFAULT NULL COMMENT '数量',
               `currency` varchar(20) NULL DEFAULT NULL COMMENT '货币',
               `budget_rate` double(20, 2) NULL DEFAULT NULL COMMENT '我方出标-单价',
               `budget_value` double(20, 2) NULL DEFAULT NULL COMMENT '我方出标-总价',
               `tender_rate_1` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-单价-1',
               `tender_value_1` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-总价-1',
               `tender_rate_2` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-单价-2',
               `tender_value_2` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-总价-2',
               `tender_rate_3` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-单价-3',
               `tender_value_3` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-总价-3',
               `tender_rate_4` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-单价-4',
               `tender_value_4` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-总价-4',
               `tender_rate_5` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-单价-5',
               `tender_value_5` double(20, 2) NULL DEFAULT NULL COMMENT '分包商报价-总价-5',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '投标-报价-清单' ROW_FORMAT = Dynamic;

            CREATE TABLE `tender_comparison_subcontractor`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `guid` varchar(32) NOT NULL COMMENT '唯一编码',
                `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构ID',
                `tender_id` int(11) NULL DEFAULT NULL COMMENT '投标比价ID',
                `seq` int(11) NULL DEFAULT NULL COMMENT '序号',
                `subcontractor_id` int(11) NULL DEFAULT NULL COMMENT '分包商ID',
                `subcontractor_name` varchar(200) NULL DEFAULT NULL COMMENT '分包商名称',
                `total` double(20, 2) NULL DEFAULT NULL COMMENT '总价',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '投标-报价-分包商' ROW_FORMAT = Dynamic;

            CREATE TABLE `tender_project`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `guid` varchar(32) NOT NULL COMMENT '唯一编码',
               `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构ID',
               `project_name` varchar(400) NULL DEFAULT NULL COMMENT '项目名称',
               `project_no` varchar(100) NULL DEFAULT NULL COMMENT '项目编码',
               `contract_name` varchar(200) NULL DEFAULT NULL COMMENT '合同名称',
               `contract_no` varchar(100) NULL DEFAULT NULL COMMENT '合同编号',
               `market` varchar(200) NULL DEFAULT NULL COMMENT '市场',
               `country` varchar(200) NULL DEFAULT NULL COMMENT '国家/地区',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '投标-项目信息' ROW_FORMAT = Dynamic;

            CREATE TABLE `tender_subcontractor`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                 `guid` varchar(32) NOT NULL COMMENT '唯一编码',
                 `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构ID',
                 `name` varchar(200) NULL DEFAULT NULL COMMENT '分包商名称',
                 `contact_person` varchar(100) NULL DEFAULT NULL COMMENT '联系人',
                 `office` varchar(200) NULL DEFAULT NULL COMMENT '固定电话',
                 `mobile` varchar(100) NULL DEFAULT NULL COMMENT '手机号',
                 `email` varchar(200) NULL DEFAULT NULL COMMENT '邮件',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '投标-分包商' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="135" author="qzexing">
        <comment>修改投标比价字段</comment>
        <sql>
            ALTER TABLE `tender_comparison_item` DROP COLUMN `currency`;

            ALTER TABLE `tender_comparison_base` ADD COLUMN `currency` varchar(20) NULL COMMENT '货币' AFTER `trade`;
        </sql>
    </changeSet>

    <changeSet id="136" author="qzexing">
        <comment>修改投标比价维护id字段</comment>
        <sql>


            ALTER TABLE `tender_comparison_base`
                CHANGE COLUMN `project_id` `project_guid` varchar(32) NULL DEFAULT NULL COMMENT '项目GUID' AFTER `dept_id`,
                ADD COLUMN `project_name` varchar(400) NULL COMMENT '项目名称' AFTER `project_guid`;

            ALTER TABLE `tender_comparison_subcontractor`
                CHANGE COLUMN `tender_id` `tender_guid` varchar(32) NULL DEFAULT NULL COMMENT '投标比价GUID' AFTER `dept_id`,
                CHANGE COLUMN `subcontractor_id` `subcontractor_guid` varchar(32) NULL DEFAULT NULL COMMENT '分包商GUID' AFTER `seq`;

            ALTER TABLE `tender_comparison_item`
                CHANGE COLUMN `tender_id` `tender_guid` varchar(32) NULL DEFAULT NULL COMMENT '投标比价GUID' AFTER `dept_id`;



        </sql>
    </changeSet>

    <changeSet id="137" author="qzexing">
        <comment>修改投标比价描述字段长度</comment>
        <sql>
            ALTER TABLE `tender_comparison_item`
                MODIFY COLUMN `description` varbinary(1000) NULL DEFAULT NULL COMMENT '描述' AFTER `item`;
        </sql>
    </changeSet>

    <changeSet id="138" author="xuguocheng">
        <comment>新建表:SI外部审批</comment>
        <sql>
            CREATE TABLE `si_ext_log`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `obj_type` int(11) NOT NULL COMMENT '业务对象类型',
                `obj_id` int(11) NOT NULL COMMENT '业务对象ID',
                `type` int(11) NULL DEFAULT NULL COMMENT '操作类型',
                `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
                `op_user_id` int(11) NULL DEFAULT NULL COMMENT '操作用户',
                `op_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作用户名称',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作指令-外部审批' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="139" author="qzexing">
        <comment>新建表：智能门锁事件、操作记录表</comment>
        <sql>
            CREATE TABLE `lock_events`  (
                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                `lock_id` int(11) NOT NULL COMMENT '锁设备ID',
                `lock_name` varchar(32) NOT NULL COMMENT '锁设备名称',
                `time` datetime NOT NULL COMMENT '时间触发时间',
                `event_type` int(11) NOT NULL COMMENT '事件类型',
                `event_type_name` varchar(32) NOT NULL COMMENT '事件类型名称',
                `event_i18n_code` varchar(32) NOT NULL COMMENT '事件国际化编码',
                `event_name` varchar(100) NOT NULL COMMENT '事件名称',
                `event_msg` varchar(200) NULL COMMENT '事件信息',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '智能门锁事件' ROW_FORMAT = Dynamic;


            CREATE TABLE `lock_oper_record`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `lock_id` int(11) NOT NULL COMMENT '门锁ID',
                `lock_name` varchar(32) NOT NULL COMMENT '锁名称',
                `oper` int(11) NOT NULL COMMENT '锁操作 0-开锁 1-关锁 ',
                `reason` varchar(200) NULL COMMENT '操作原因',
                `dead_line` datetime NULL COMMENT '截止时间',
                `oper_user_id` int NULL COMMENT '操作人员ID',
                `oper_user_name` varchar(32) NULL COMMENT '操作人员名称',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '智能门锁操作记录' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="140" author="qzexing">
        <comment>电子锁增加guid字段</comment>
        <sql>
            ALTER TABLE `lock_device`
                ADD COLUMN `guid` varchar(32) NULL COMMENT 'GUID' AFTER `id`;

            UPDATE lock_device SET guid = REPLACE(UUID(),'-','') WHERE guid IS NULL;
        </sql>
    </changeSet>

    <changeSet id="141" author="qzexing">
        <comment>更新锁GUID</comment>
        <sql>
            UPDATE lock_device SET guid = MD5( CONCAT( `name`, id, sn, create_time,RAND()));
        </sql>
    </changeSet>

    <changeSet id="142" author="qzexing">
        <comment>投标比较增加描述查询字段</comment>
        <sql>
            ALTER TABLE `tender_comparison_item`
                MODIFY COLUMN `description` varchar(1000) NULL DEFAULT NULL COMMENT '描述' AFTER `item`,
                ADD COLUMN `description_lower` varchar(1000) NULL COMMENT '描述小写冗余字段' AFTER `description`;

            UPDATE tender_comparison_item SET description_lower = LOWER(description) WHERE description IS NOT NULL;
        </sql>
    </changeSet>

    <changeSet id="143" author="qzexing">
        <comment>修改投标比较描述字段长度</comment>
        <sql>
            ALTER TABLE `tender_comparison_item`
                MODIFY COLUMN `description` varchar(2000) NULL COMMENT '描述' AFTER `item`,
                MODIFY COLUMN `description_lower` varchar(2000) NULL COMMENT '描述小写冗余字段' AFTER `description`;
        </sql>
    </changeSet>

    <changeSet id="144" author="qzexing">
        <comment>创建里程碑表</comment>
        <sql>
            CREATE TABLE `fsdm_milestone`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `guid` varchar(32) NOT NULL COMMENT 'GUID',
               `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
               `index` int(11) NULL COMMENT '序号',
               `name` varchar(128) NOT NULL COMMENT '名称',
               `plan_finish` date NULL COMMENT '计划完成日期',
               `actual_finish` date NULL COMMENT '时间完成日期',
               `state` int(11) NULL COMMENT '完成状态 0-未开始 1-未完成 2-已完成',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '里程碑' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="145" author="qzexing">
        <comment>创建施工部位表</comment>
        <sql>
            CREATE TABLE `const_part`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `guid` varchar(32) NOT NULL COMMENT 'GUID',
                `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                `pid` int(11) NOT NULL COMMENT '父节点ID',
                `code` varchar(32) NOT NULL COMMENT '编码',
                `name` varchar(150) NOT NULL COMMENT '名称',
                `type` int(11) NOT NULL COMMENT '部位类型  1-单位工程 2-分部工程 3-子分部工程 4-分项工程',
                `quantity` double(11, 2) NULL COMMENT '工程量',
                `unit` varchar(10) NULL COMMENT '单位',
                `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态,0-禁用 1-启用',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '施工部位' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="146" author="qzexing">
        <comment>施工天气新增字段</comment>
        <sql>
            ALTER TABLE `const_record_weather`
                MODIFY COLUMN `weather` varchar(50) NULL COMMENT '天气' AFTER `date`,
                ADD COLUMN `am_weather` varchar(50) NULL COMMENT '上午天气' AFTER `weather`,
                ADD COLUMN `pm_weather` varchar(50) NULL COMMENT '下午天气' AFTER `am_weather`;
        </sql>
    </changeSet>

    <changeSet id="147" author="qzexing">
        <comment>修改施工记录字段</comment>
        <sql>
            ALTER TABLE `const_record`
                ADD COLUMN `part_id` int(11) NULL COMMENT '部位ID' AFTER `time`,
                ADD COLUMN `content` varchar(255) NULL COMMENT '施工内容' AFTER `part_name`,
                ADD COLUMN `material_arrival` varchar(255) NULL COMMENT '材料进场情况' AFTER `plan`,
                ADD COLUMN `const_acceptance` varchar(255) NULL COMMENT '施工验收情况' AFTER `material_arrival`,
                ADD COLUMN `safety_followup` varchar(255) NULL COMMENT '安全跟进情况' AFTER `const_acceptance`;
        </sql>
    </changeSet>

    <changeSet id="148" author="qzexing">
        <comment>新增施工进度表</comment>
        <sql>
            CREATE TABLE `const_progress`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `date` date NOT NULL COMMENT '日期',
                `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                `position_id` int(11) NULL COMMENT '位置ID',
                `position_name` varchar(150) NULL COMMENT '位置',
                `index` int(11) NULL COMMENT '序号',
                `part_id` int(11) NULL COMMENT '主要分项工程id',
                `part_name` varchar(150) NULL COMMENT '主要分项工程名称',
                `unit` varchar(10) NULL COMMENT '单位',
                `total_qty` double(11, 2) NULL COMMENT '暂估总量',
                `yesterday_done_qty` double(11, 2) NULL COMMENT '昨日已完工量',
                `today_done_qty` double(11, 2) NULL COMMENT '当日完工量',
                `total_done_qty` double(11, 2) NULL COMMENT '累计完成工程量',
                `total_done_rate` double(11, 2) NULL COMMENT '完成进度',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '施工进度表' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="149" author="qzexing">
        <comment>施工部位新增字段</comment>
        <sql>
            ALTER TABLE `const_part`
                ADD COLUMN `unit_part_id` int(11) NULL COMMENT '单位工程ID' AFTER `pid`,
                ADD COLUMN `unit_part_name` varchar(150) NULL COMMENT '单位工程名称' AFTER `unit_part_id`;
        </sql>
    </changeSet>

    <changeSet id="150" author="qzexing">
        <comment>施工内容字段可以为空</comment>
        <sql>
            ALTER TABLE `const_record_details`
                MODIFY COLUMN `corp_name` varchar(32) NULL COMMENT '合作单位名称' AFTER `corp_id`;
        </sql>
    </changeSet>

    <changeSet id="151" author="qzexing">
        <sql>
            ALTER TABLE `const_record_img`
                ADD COLUMN `img_name` varchar(50) NULL COMMENT '图片信息' AFTER `img_url`;
        </sql>
    </changeSet>

    <changeSet id="152" author="qzexing">
        <comment>施工进度新增操作人员字段</comment>
        <sql>
            ALTER TABLE `const_progress`
                ADD COLUMN `oper_user_id` int NULL COMMENT '操作用户ID' AFTER `total_done_rate`,
                ADD COLUMN `oper_username` varchar(30) NULL COMMENT '操作用户名称' AFTER `oper_user_id`;
        </sql>
    </changeSet>

    <changeSet id="153" author="qzexing">
        <comment>新增AI数据表</comment>
        <sql>
            CREATE TABLE `ai_agent`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一ID',
             `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '智能体名称',
             `host` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体Host',
             `app_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '智能体AppId',
             `api_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体ApiKey',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI智能体-应用' ROW_FORMAT = Dynamic;

            CREATE TABLE `ai_agent_chat`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `agent_id` int(11) NOT NULL COMMENT '智能体ID',
              `chat_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对话ID',
              `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '对话标题',
              `custom_title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义对话标题',
              `user_id` int(11) NOT NULL COMMENT '用户ID',
              `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI智能体-对话' ROW_FORMAT = Dynamic;

        </sql>
    </changeSet>

    <changeSet id="154" author="qzexing">
        <comment>增加电量字段</comment>
        <sql>
            ALTER TABLE `lock_device_data`
                ADD COLUMN `battery` double NULL COMMENT '电量' AFTER `net_state`;

            ALTER TABLE `lock_events`
                CHANGE COLUMN `event_i18n_code` `event_code` varchar(32) NULL COMMENT '事件编码' AFTER `event_type_name`;
        </sql>
    </changeSet>

    <changeSet id="155" author="qzexing">
        <comment>修改施工内容字段长度</comment>
        <sql>
            ALTER TABLE `const_record`
                MODIFY COLUMN `content` varchar(1000) NULL COMMENT '施工内容' AFTER `part_name`,
                MODIFY COLUMN `remark` varchar(1000) NULL COMMENT '存在问题' AFTER `content`,
                MODIFY COLUMN `plan` varchar(1000) NULL COMMENT '明日计划' AFTER `remark`;
        </sql>
    </changeSet>

    <changeSet id="156" author="qzexing">
        <comment>修改智能体字段</comment>
        <sql>
        ALTER TABLE `ai_agent`
            ADD COLUMN  `support_image` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否支持图片(0-否 1-是)' AFTER `api_key`,
            ADD COLUMN  `support_file`  TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否支持文件(0-否 1-是)' AFTER `support_image`;
        </sql>
    </changeSet>

    <changeSet id="157" author="qzexing">
        <comment>修改智能体字段</comment>
        <sql>
            ALTER TABLE `ai_agent`
                ADD COLUMN `platform` varchar(32) NULL COMMENT '平台' AFTER `dept_id`;

            UPDATE `ai_agent` SET `platform` = 'FastGPT' WHERE `platform` IS NULL;
        </sql>
    </changeSet>

    <changeSet id="158" author="qzexing">
        <comment>里程碑新增开始时间字段</comment>
        <sql>
            ALTER TABLE `fsdm_milestone`
                ADD COLUMN `begin_date` date NULL COMMENT '开始时间' AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="159" author="xuguocheng">
        <comment>新建tbm_data表</comment>
        <sql>
            CREATE TABLE `tbm_data`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `tbm_id` int(32) NOT NULL COMMENT '盾构机ID',
             `time` datetime(0) NULL DEFAULT NULL COMMENT '通信时间',
             `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据json',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'tbm-设备数据' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="160" author="xuguocheng">
        <comment>tbm新增字段</comment>
        <sql>
            ALTER TABLE `tbm_info`
                ADD COLUMN `model` varchar(32) NULL COMMENT '设备型号' AFTER `name`,
                ADD COLUMN `code` varchar(32) NULL COMMENT '设备编号' AFTER `model`,
                ADD COLUMN `factory_num` varchar(32) NULL COMMENT '出厂编号' AFTER `code`,
                ADD COLUMN `manufacture_date` date NULL COMMENT '生产日期' AFTER `factory_num`;

            ALTER TABLE `tbm_data`
                ADD COLUMN `total_ring` int NULL COMMENT '总环号' AFTER `time`,
                ADD COLUMN `total_mile` double(10, 2) NULL COMMENT '总里程' AFTER `total_ring`,
                ADD COLUMN `ring` int NULL COMMENT '当前环号' AFTER `total_mile`,
                ADD COLUMN `mile` double(10, 2) NULL COMMENT '当前里程' AFTER `ring`;
        </sql>
    </changeSet>

    <changeSet id="161" author="xuguocheng">
        <comment>新建tbm_fvs表</comment>
        <sql>
            CREATE TABLE `tbm_fvs`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `tbm_id` int(11) NOT NULL COMMENT '盾构机ID',
                `fvs_device_guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频设备guid',
                `fvs_device_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频设备名称',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'tbm-设备数据' ROW_FORMAT = Dynamic;

            ALTER TABLE `tbm_data`
                MODIFY COLUMN `tbm_id` int NOT NULL COMMENT '盾构机ID' AFTER `dept_id`;
        </sql>
    </changeSet>

    <changeSet id="162" author="xuguocheng">
        <comment>风机新增字段</comment>
        <sql>
            ALTER TABLE `fan_device`
                ADD COLUMN `pass` varchar(32) NULL COMMENT '硬件密码' AFTER `sn`;
        </sql>
    </changeSet>

    <changeSet id="163" author="xuguocheng">
        <comment>风机新增字段</comment>
        <sql>
            ALTER TABLE `fan_device_data`
                ADD COLUMN `fault` int NULL COMMENT '故障指示:0-正常 1-故障' AFTER `status`,
                ADD COLUMN `voltage` double(10, 2) NULL COMMENT '电压' AFTER `current`,
                ADD COLUMN `motor_voltage` double(10, 2) NULL COMMENT '电机电压' AFTER `voltage`;
        </sql>
    </changeSet>

    <changeSet id="164" author="xuguocheng">
        <comment>tbm新增字段</comment>
        <sql>
            ALTER TABLE `tbm_info`
                ADD COLUMN `host` varchar(200) NULL COMMENT '硬件API地址' AFTER `sn`,
                ADD COLUMN `user` varchar(32) NULL COMMENT '硬件API用户' AFTER `host`,
                ADD COLUMN `pass` varchar(32) NULL COMMENT '硬件API密码' AFTER `user`;
        </sql>
    </changeSet>

    <changeSet id="165" author="qzexing">
        <comment>报警规则新增字段</comment>
        <sql>
            ALTER TABLE `lock_warn_rule`
                ADD COLUMN `warn_type` int(11) NOT NULL DEFAULT 2 COMMENT '报警类型  1-硬件报警  2-系统报警' AFTER `rule_name`;
        </sql>
    </changeSet>

    <changeSet id="166" author="qzexing">
        <comment>新建车辆盲区相关表</comment>
        <sql>
            CREATE TABLE `blind_device` (
            `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `guid` VARCHAR ( 32 ) NOT NULL COMMENT 'guid',
            `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
            `device_type` INT ( 11 ) NOT NULL COMMENT '设备类型(字典)',
            `device_type_name` VARCHAR ( 64 ) NOT NULL COMMENT '类型名称',
            `name` VARCHAR ( 64 ) NOT NULL COMMENT '设备名称',
            `code` VARCHAR ( 64 ) NOT NULL COMMENT '设备编号',
            `model` VARCHAR ( 64 ) NULL COMMENT '设备型号',
            `platform` VARCHAR ( 32 ) NOT NULL COMMENT '硬件平台',
            `sn` VARCHAR ( 32 ) NOT NULL COMMENT '硬件SN',
            `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '更新时间',
            `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) COMMENT '创建时间',
            PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '车辆盲区设备' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_device_data` (
             `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
             `blind_id` INT ( 11 ) NOT NULL COMMENT '盲区设备ID',
             `time` DATETIME NULL COMMENT '时间',
             `net_state` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
             `gps_valid` INT ( 11 ) NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位',
             `lng` DOUBLE NULL COMMENT '经度',
             `lat` DOUBLE NULL COMMENT '纬度',
             `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '更新时间',
             `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) COMMENT '创建时间',
             PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '车辆盲区设备-数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_device_data_log` (
             `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `blind_id` INT ( 11 ) NOT NULL COMMENT '盲区设备ID',
             `time` DATETIME NOT NULL COMMENT '时间',
             `net_state` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '网络状态: 0-离线 1-在线',
             `gps_valid` INT ( 11 ) NULL COMMENT 'gps有效标志: 0-无效定位 1-有效定位',
             `lng` DOUBLE NULL COMMENT '经度',
             `lat` DOUBLE NULL COMMENT '纬度',
             `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ) COMMENT '更新时间',
             `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) COMMENT '创建时间',
             PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '车辆盲区设备-历史数据' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_record` (
             `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
             `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
             `rule_id` INT ( 11 ) NOT NULL COMMENT '规则ID',
             `rule_type` INT ( 11 ) NOT NULL COMMENT '规则类型',
             `rule_param` TEXT NULL COMMENT '报警检测规则参数',
             `rule_express` TEXT NULL COMMENT '报警检测规则表达式',
             `trigger_param` TEXT NULL COMMENT '触发参数',
             `trigger_time` DATETIME ( 0 ) NOT NULL COMMENT '触发时间',
             `trigger_object_id` VARCHAR ( 32 ) NOT NULL COMMENT '触发业务对象ID',
             `state` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
             `handle_time` DATETIME ( 0 ) NULL DEFAULT NULL COMMENT '处理时间',
             `handle_result` VARCHAR ( 100 ) NULL DEFAULT NULL COMMENT '处理结果',
             `handle_remark` VARCHAR ( 100 ) NULL DEFAULT NULL COMMENT '处理结果备注',
             `handle_user_id` INT ( 11 ) NULL DEFAULT NULL COMMENT '处理人',
             `handle_user_name` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '处理人姓名',
             `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
             `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
             `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
             PRIMARY KEY ( `id` ) USING BTREE,
             INDEX `idx_dept_state_ruleType_time` ( `dept_id`, `state`, `rule_type`, `trigger_time` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_rule` (
               `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
               `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
               `rule_name` VARCHAR ( 32 ) NOT NULL COMMENT '报警规则名称',
               `rule_type` INT ( 11 ) NOT NULL COMMENT '报警规则类型',
               `rule_param` TEXT NULL COMMENT '报警检测规则',
               `rule_express` TEXT NULL COMMENT '报警检测规则',
               `enable_flag` INT ( 11 ) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
               `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
               `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
               PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警规则' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_rule_channel` (
               `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
               `rule_id` INT ( 11 ) NOT NULL COMMENT '设备报警规则id',
               `msg_channel` INT ( 11 ) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信',
               `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
               `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
               PRIMARY KEY ( `id` ) USING BTREE,
               UNIQUE INDEX `uk_ruleId_msgChannel` ( `rule_id`, `msg_channel` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警接收方式' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_rule_object` (
              `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
              `rule_id` INT ( 11 ) NOT NULL COMMENT '报警规则id',
              `rule_type` INT ( 11 ) NOT NULL COMMENT '规则类型',
              `object_id` VARCHAR ( 32 ) NOT NULL COMMENT '报警对象id',
              `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
              `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
              PRIMARY KEY ( `id` ) USING BTREE,
              UNIQUE INDEX `uk_ruleId_objectId` ( `rule_id`, `object_id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警规则和报警对象关联表' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_rule_time` (
            `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
            `rule_id` INT ( 11 ) NOT NULL COMMENT '报警规则ID',
            `start_time` TIME ( 0 ) NOT NULL COMMENT '开始时间',
            `end_time` TIME ( 0 ) NOT NULL COMMENT '结束时间',
            `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
            `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
            PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警规则-生效时间' ROW_FORMAT = Dynamic;

            CREATE TABLE `blind_warn_rule_user` (
            `id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
            `rule_id` INT ( 11 ) NOT NULL COMMENT '设备报警规则id',
            `to_user_id` INT ( 11 ) NULL DEFAULT NULL COMMENT '接收人(后台用户ID)',
            `to_user_name` VARCHAR ( 32 ) NULL DEFAULT NULL COMMENT '接收人姓名',
            `to_user_phone` VARCHAR ( 20 ) NULL DEFAULT NULL COMMENT '接收人手机号',
            `update_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ) ON UPDATE CURRENT_TIMESTAMP ( 0 ),
            `create_time` DATETIME ( 0 ) NOT NULL DEFAULT CURRENT_TIMESTAMP ( 0 ),
            PRIMARY KEY ( `id` ) USING BTREE,
            UNIQUE INDEX `uk_ruleId_userId` ( `rule_id`, `to_user_id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警-接收人' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="167" author="qzexing">
        <comment>新增设备报警记录附件</comment>
        <sql>
            CREATE TABLE `blind_warn_record_attach`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `record_id` int(11) NOT NULL COMMENT '报警记录ID',
            `type` int(11) NOT NULL COMMENT '附件类型 1-图片 2-视频',
            `url` varchar(255) NULL COMMENT '附件地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '车辆盲区报警记录附件' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="168" author="qzexing">
        <comment>新增施工日报模板表并初始化数据</comment>
        <sql>
            CREATE TABLE `const_report_template`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                `template` varchar(50) NOT NULL COMMENT '模板名称',
                `language` varchar(10) NOT NULL COMMENT '语言',
                `date_format` varchar(10) NOT NULL COMMENT '日期格式',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '施工日报模板表' ROW_FORMAT = Dynamic;

            INSERT INTO `const_report_template` VALUES (1, 0, 'const-report-template.xlsx','zh_CN','yyyy-MM-dd');
            INSERT INTO `const_report_template` VALUES (2, 613, 'const-report-template.xlsx','zh_HK','yyyy-MM-dd');
            INSERT INTO `const_report_template` VALUES (3, 623, 'const-report-template-lhx.xlsx','zh_HK','dd.MM.yyyy');
        </sql>
    </changeSet>

    <changeSet id="169" author="qzexing">
        <comment>更新日报导出模板数据</comment>
        <sql>
            UPDATE const_report_template SET template = REPLACE(template,'.xlsx','');
        </sql>
    </changeSet>

    <changeSet id="170" author="qzexing">
        <comment>新建数字化追踪相关表</comment>
        <sql>
            CREATE TABLE `track_mach_info`
            (
                `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `guid`            varchar(32) NULL COMMENT 'GUID',
                `dept_id`         int(11) NOT NULL COMMENT '机构ID',
                `mach_code`       varchar(64) NOT NULL COMMENT '设备编号',
                `mach_name`       varchar(128) NULL COMMENT '设备编号',
                `mach_type`       int(11) NOT NULL COMMENT '设备类型(字典)',
                `mach_type_name`  varchar(64) NULL COMMENT '类型名称',
                `mach_model_name` varchar(64) NULL COMMENT '品牌型号',
                `corp_id`         int(11) NULL DEFAULT NULL COMMENT '合作单位id',
                `corp_name`       varchar(32) NULL COMMENT '合作单位名称',
                `enter_time`      date NULL DEFAULT NULL COMMENT '进场时间',
                `outer_time`      date NULL DEFAULT NULL COMMENT '离场时间',
                `state`           int(11) NULL DEFAULT 1 COMMENT '设备在场状态 0-不在场 1-在场',
                `delivery_time`   date NULL DEFAULT NULL COMMENT '出厂时间',
                `delivery_code`   varchar(32) NULL COMMENT '出厂编号',
                `manufacturer`    varchar(32) NULL COMMENT '生产厂家',
                `cert_state`      int(1) NULL DEFAULT 1 COMMENT '证书状态 0-失效  1-正常',
                `del_flag`        int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '数字化追踪设备表' ROW_FORMAT = Dynamic;


            CREATE TABLE `track_mach_cert`
            (
                `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id`         int(11) NOT NULL COMMENT '组织机构ID',
                `mach_id`         int(11) NOT NULL COMMENT '设备ID',
                `cert_name`       varchar(32) NULL COMMENT '证书名称',
                `cert_code`       varchar(32) NULL COMMENT '证书编码',
                `level`           varchar(16) NULL COMMENT '技能等级',
                `cert_start_date` date NULL DEFAULT NULL COMMENT '发证日期',
                `cert_end_date`   date NULL DEFAULT NULL COMMENT '截止日期',
                `cert_grant_org`  varchar(32) NULL COMMENT '发证单位',
                `state`           int(1) NULL DEFAULT 1 COMMENT '证书状态 0-失效  1-正常',
                `del_flag`        int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time`     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_time`     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '数字化追踪设备证书表' ROW_FORMAT = DYNAMIC;


            CREATE TABLE `track_mach_check_record`
            (
                `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id`         int(11) NOT NULL COMMENT '组织机构ID',
                `mach_id`         int(11) NOT NULL COMMENT '设备ID',
                `mach_code`       varchar(64) NULL COMMENT '设备编号',
                `check_time`      datetime NULL DEFAULT NULL COMMENT '检查时间',
                `check_part`      varchar(50) NULL COMMENT '检查部位',
                `check_result`    int(11)  NULL COMMENT '检查结果 0-正常  1-异常',
                `check_remark`    varchar(255) NULL COMMENT '检查说明',
                `check_user_id`   int(11) NULL DEFAULT NULL COMMENT '检查人ID',
                `check_user_name` varchar(32) NULL COMMENT '检查人名称',
                `del_flag`        int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time`     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_time`     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '数字化追踪设备检查记录表' ROW_FORMAT = DYNAMIC;


            CREATE TABLE `track_mach_attach`
            (
                `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`     int(11) NULL DEFAULT NULL COMMENT '项目ID',
                `attach_type` int(11) NOT NULL COMMENT '附件类型 1-设备附件 2-证书附件 3-检查记录附件',
                `obj_id`      int(11) NOT NULL COMMENT '对象ID',
                `name`        varchar(64) NULL COMMENT '名称',
                `url`         varchar(255) NOT NULL COMMENT '附件地址',
                `del_flag`    int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
                `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '数字化追踪设备附件' ROW_FORMAT = DYNAMIC;

        </sql>
    </changeSet>

    <changeSet id="171" author="qzexing">
        <comment>数字化追踪设备新增字段</comment>
        <sql>
            ALTER TABLE `track_mach_info`
                ADD COLUMN `part_name` varchar(32) NULL COMMENT '使用位置' AFTER `delivery_code`;

            ALTER TABLE `track_mach_cert`
                ADD COLUMN `cert_type` int(11) NULL COMMENT '证书类型' AFTER `mach_id`,
                ADD COLUMN `cert_type_name` varchar(32) NULL COMMENT '证书名称' AFTER `cert_type`;
        </sql>
    </changeSet>

    <changeSet id="172" author="qzexing">
        <comment>数字化追踪设备修改字段</comment>
        <sql>
            ALTER TABLE `track_mach_info`
                MODIFY COLUMN `cert_state` int(1) NULL DEFAULT -1 COMMENT '证书状态 -1-暂无证书 0-失效  1-正常' AFTER `manufacturer`;
        </sql>
    </changeSet>

    <changeSet id="173" author="qzexing">
        <comment>TBM新增导向数据字段</comment>
        <sql>
            ALTER TABLE `tbm_data`
                ADD COLUMN `tg_data` text NULL COMMENT '导向数据json' AFTER `data`;
        </sql>
    </changeSet>

    <changeSet id="174" author="xuguocheng">
        <comment>迁移数字洞挖的数据表</comment>
        <sql>
            CREATE TABLE `fsdm_cavern_report`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `date` date NOT NULL COMMENT '日期',
               `part_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部位ID',
               `part_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部位名称',
               `stage_tpl_id` int(11) NULL DEFAULT NULL COMMENT '工序分类ID',
               `stage_tpl_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序模版名称',
               `start` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '起始',
               `start_direction` int(11) NULL DEFAULT NULL COMMENT '起始方向',
               `start_stake` double(10, 2) NULL DEFAULT NULL COMMENT '起始桩号',
               `end` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '截止',
               `end_direction` int(11) NULL DEFAULT NULL COMMENT '截止方向',
               `end_stake` double(10, 2) NULL DEFAULT NULL COMMENT '截止桩号',
               `depth` double(10, 2) NULL DEFAULT NULL COMMENT '进尺-今日完成',
               `plan_depth` double(10, 2) NULL DEFAULT NULL COMMENT '进尺-明日计划',
               `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存在问题',
               `plan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明日计划',
               `imgs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '现场图片',
               `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
               `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人ID',
               `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字洞挖-施工日报' ROW_FORMAT = Dynamic;

            CREATE TABLE `fsdm_cavern_report_detail`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `report_id` int(11) NOT NULL COMMENT '记录iD',
              `type` int(11) NOT NULL COMMENT '人机材类型',
              `obj_id` int(11) NULL DEFAULT NULL COMMENT '对象ID',
              `obj_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对象名称',
              `total_number` int(11) NULL DEFAULT 0 COMMENT '数量',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数字洞挖-施工日报-详情' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>
</databaseChangeLog>
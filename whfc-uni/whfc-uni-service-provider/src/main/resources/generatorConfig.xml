<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <!--导入属性配置-->
    <properties resource="generator.properties"></properties>

    <classPathEntry location="${jdbc.driverLocation}"/>
    <!--指定特定数据库的jdbc驱动jar包的位置-->
    <context id="default" targetRuntime="MyBatis3">

        <!-- optional，旨在创建class时，对注释进行控制 -->
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--jdbc的数据库连接 -->
        <jdbcConnection
                driverClass="${jdbc.driverClass}"
                connectionURL="${jdbc.connectionURL}"
                userId="${jdbc.userId}"
                password="${jdbc.password}">
        </jdbcConnection>

        <!-- 非必需，类型处理器，在数据库类型和java类型之间的转换控制-->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>


        <!-- Model模型生成器,用来生成含有主键key的类，记录类 以及查询Example类
            targetPackage     指定生成的model生成所在的包名
            targetProject     指定在该项目下所在的路径
        -->
        <javaModelGenerator targetPackage="com.whfc.uni.entity.fsdm" targetProject="src/main/java">

            <!-- 是否允许子包，即targetPackage.schemaName.tableName -->
            <property name="enableSubPackages" value="false"/>
            <!-- 是否对model添加 构造函数 -->
            <property name="constructorBased" value="false"/>
            <!-- 是否对类CHAR类型的列的数据进行trim操作 -->
            <property name="trimStrings" value="true"/>
            <!-- 建立的Model对象是否 不可改变  即生成的Model对象不会有 setter方法，只有构造方法 -->
            <property name="immutable" value="false"/>
        </javaModelGenerator>

        <!--Mapper映射文件生成所在的目录 为每一个数据库的表生成对应的SqlMap文件 -->
        <sqlMapGenerator targetPackage="com.whfc.uni.mapper.fsdm" targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- 客户端代码，生成易于使用的针对Model对象和XML配置文件 的代码
                type="ANNOTATEDMAPPER",生成Java Model 和基于注解的Mapper对象
                type="MIXEDMAPPER",生成基于注解的Java Model 和相应的Mapper对象
                type="XMLMAPPER",生成SQLMap XML文件和独立的Mapper接口
        -->
        <javaClientGenerator targetPackage="com.whfc.uni.dao.fsdm" targetProject="src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

<!--        <table tableName="concrete_item"             domainObjectName="ConcreteItem"        enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_item_data"        domainObjectName="ConcreteItemData"    enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_item_data_log"    domainObjectName="ConcreteItemDataLog" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_group"            domainObjectName="ConcreteGroup"       enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->

<!--        <table tableName="concrete_warn_record"          domainObjectName="ConcreteWarnRecord"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_warn_rule"            domainObjectName="ConcreteWarnRule"              enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_warn_rule_channel"    domainObjectName="ConcreteWarnRuleChannel"       enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_warn_rule_object"     domainObjectName="ConcreteWarnRuleObject"        enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_warn_rule_user"       domainObjectName="ConcreteWarnRuleUser"          enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="concrete_warn_rule_time"       domainObjectName="ConcreteWarnRuleTime"          enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="pressure_sensor"               domainObjectName="PressureSensor"                enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="pressure_sensor_data"          domainObjectName="PressureSensorData"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="pressure_sensor_log"           domainObjectName="PressureSensorLog"             enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="pressure_sensor_config"        domainObjectName="PressureSensorConfig"          enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="pressure_sensor_warn"          domainObjectName="PressureSensorWarn"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="car_info"                      domainObjectName="CarInfo"                       enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="car_gate"                      domainObjectName="CarGate"                       enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_stage_template"           domainObjectName="FsdmStageTemplate"             enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_stage"                    domainObjectName="FsdmStage"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_task_stage"               domainObjectName="FsdmTaskStage"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_task_stage_ext"           domainObjectName="FsdmTaskStageExt"              enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_blast_hole"               domainObjectName="FsdmBlastHole"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_base"                  domainObjectName="TwBase"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_dict"                  domainObjectName="TwDict"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_t1"                    domainObjectName="TwT1"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_t2"                    domainObjectName="TwT2"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_t3"                    domainObjectName="TwT3"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_t4"                    domainObjectName="TwT4"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_tz"                    domainObjectName="TwTz"                     enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tw_op_log"                domainObjectName="TwOpLog"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="const_report_attach"      domainObjectName="ConstReportAttach"        enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_info"                  domainObjectName="SiInfo"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_assessment"            domainObjectName="SiAssessment"             enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_op_log"                domainObjectName="SiOpLog"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_ext_log"               domainObjectName="SiExtLog"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_dict"                  domainObjectName="SiDict"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_subcontract"           domainObjectName="SiSubcontract"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="si_mail"                  domainObjectName="SiMail"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="mail_config"              domainObjectName="MailConfig"               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="mail_type"                domainObjectName="MailType"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="mail_info"                domainObjectName="MailInfo"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="mail_attach"              domainObjectName="MailAttach"               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="app_dict"                 domainObjectName="AppDict"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="app_sign"                 domainObjectName="AppSign"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fire_work"                domainObjectName="FireWork"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fire_work_img"            domainObjectName="FireWorkImg"              enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fire_work_user"           domainObjectName="FireWorkUser"             enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fire_work_inspect"        domainObjectName="FireWorkInspect"          enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tbm_info"                 domainObjectName="TbmInfo"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tbm_data"                 domainObjectName="TbmData"                  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="tbm_fvs"                  domainObjectName="TbmFvs"                   enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="bim_model"                domainObjectName="BimModel"                 enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="bim_component"            domainObjectName="BimComponent"             enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fan_device"               domainObjectName="FanDevice"                enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fan_device_data"          domainObjectName="FanDeviceData"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="lifting_permit"           domainObjectName="LiftingPermit"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="lifting_permit_item"      domainObjectName="LiftingPermitItem"        enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="lifting_permit_attach"    domainObjectName="LiftingPermitAttach"      enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="led_device"               domainObjectName="LedDevice"                enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="led_device_area"          domainObjectName="LedDeviceArea"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_lining_device"          domainObjectName="TrolleyLiningDevice"            enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_lining_device_data"     domainObjectName="TrolleyLiningDeviceData"        enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_maintain_device"        domainObjectName="TrolleyMaintainDevice"          enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_maintain_device_data"   domainObjectName="TrolleyMaintainDeviceData"      enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_rockcut_device"         domainObjectName="TrolleyRockcutDevice"           enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="trolley_rockcut_device_data"    domainObjectName="TrolleyRockcutDeviceData"       enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="hot_permit"                     domainObjectName="HotPermit"                      enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="hot_permit_measure"             domainObjectName="HotPermitMeasure"               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="dig_permit"                     domainObjectName="DigPermit"                      enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="dig_permit_attach"              domainObjectName="DigPermitAttach"                enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="dig_permit_attendee"            domainObjectName="DigPermitAttendee"              enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="dig_permit_utility"             domainObjectName="DigPermitUtility"               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_cavern_report"             domainObjectName="FsdmCavernReport"               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
<!--        <table tableName="fsdm_cavern_report_detail"      domainObjectName="FsdmCavernReportDetail"         enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false" />-->
    </context>
</generatorConfiguration>
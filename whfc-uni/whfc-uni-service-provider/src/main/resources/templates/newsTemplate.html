<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="apple-mobile-web-app-title" content="智慧工地平台_工程机械管理系统_塔吊监控系统-武汉风潮物联科技有限公司">
    <meta name="keywords" content="智慧工地平台,工程机械管理系统,塔吊监控系统,智慧工地系统,机械油耗管理">
    <meta name="description" content="风潮物联风潮物联坚持以项目需求为导向，以技术创新为切入点，自主可控研发了智慧工地平台,工程机械管理系统,塔吊监控系统,智慧工地系统系列产品。是一家致力于智慧工地核心技术及相关产品的研发、应用和服务的高新技术企业。">
    <title>智慧工地_BIM+智慧工地_智慧工地解决方案-武汉风潮物联科技有限公司</title>
    <link rel="stylesheet" href="https://file.whfciot.com/ms/pro/news/assets/css/news-details.css">
    <link rel="stylesheet" href="https://file.whfciot.com/ms/pro/news/assets/css/bass.css">
    <link rel="stylesheet" href="https://file.whfciot.com/ms/pro/news/assets/css/comm.css">
    <!-- <link rel="stylesheet" href="https://file.whfciot.com/ms/pro/news/assets/css/fullPage.css"> -->
    <script src="https://file.whfciot.com/ms/pro/news/assets/js/jqury.js"></script>
    <!-- <script src="https://file.whfciot.com/ms/pro/news/assets/js/fullPage.js"></script> -->
    <script src="https://file.whfciot.com/ms/pro/news/assets/js/common.js"></script>
    <script>
        var _hmt = _hmt || [];
            (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?8e1115033bfccee7d861d5fb89528d5f";
            var s = document.getElementsByTagName("script")[0]; 
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body id="body">
    <header>
        <h1 class="logo">
            <a href="https://www.whfciot.com/">
                <img src="https://file.whfciot.com/ms/pro/news/assets/img/product/logo-active.png" alt="智慧工地">
            </a>
        </h1>
        <ul class="nav">
            <li class="li "><a href="https://www.whfciot.com/">首页</a></li>
            <li class="li "><a href="https://www.whfciot.com/product-center.html">产品中心</a></li>
            <li class="li "><a href="https://www.whfciot.com/smart-solutions.html">智慧方案</a></li>
            <li class="li "><a href="https://www.whfciot.com/case.html">应用案例</a></li>
            <li class="li active"><a href="https://www.whfciot.com/news.html">新闻中心</a></li>
            <li class="li "><a href="https://www.whfciot.com/league.html" rel='nofollow'>招商加盟</a></li>
            <li class="li "><a href="https://www.whfciot.com/about-us.html" rel='nofollow'>关于我们</a></li>
            <li class="li "><a href="https://www.whfciot.com/talent-recruitment.html" rel='nofollow'>人才招聘</a></li>
            <li class="li "><a href="https://www.whfciot.com/contant-us.html" rel='nofollow'>联系我们</a></li>
        </ul>
        <div class="search">
            <img src="" alt="" class="iocn">
            <input type="text" style="display: none;">
        </div>
    </header>
    <div class="navigation">
        <img src="https://file.whfciot.com/ms/pro/news/assets/img/news/icon.png" alt="" class="icon">
        <span class="text"><a href="https://www.whfciot.com/">首页</a></span>
        <span class="text"> - </span>
        <span class="text"><a href="https://www.whfciot.com/news.html">新闻资讯</a></span>
        <span class="text"> - </span>
        <span class="text">公司动态</span>
        <span class="text"> - </span>
        <span class="blod text">公司动态详情</span>
    </div>
    <div class="content">
        <div class="info">
            <div class="top">
                <div class="bc"></div>
                <h2 class="h2" th:utext="${title}"></h2>
            </div>
            <ul>
                <li>
                    <div class="name">发布日期：</div>
                    <div class="sub time" th:utext="${date}"></div>
                </li>
                <li>
                    <div class="name">新闻来源：</div>
                    <div class="sub">风潮物联</div>
                </li>
                <li>
                    <div class="name">字体显示：</div>
                    <div style="cursor: pointer;" class="sub big">【大】</div>
                    <div style="cursor: pointer;" class="sub middle">【中】</div>
                    <div style="cursor: pointer;" class="sub small">【小】</div>
                </li> 
            </ul>
            <div class="html" th:utext="${content}"></div>
            <div class="lineBc"></div>
            <div class="item">
                <div class="btn btn1">上一篇：</div>
                <p class="prev"></p>
            </div>
            <div class="item">
                <div class="btn btn2">下一篇：</div>
                <p class="next"></p>
            </div>
        </div>
    </div>
    <footer>
        <div class="top">
            <ul class="nav">
                <li class="li active"><a href="https://www.whfciot.com/">首页</a></li>
                <li class="li "><a href="https://www.whfciot.com/product-center.html">产品中心</a></li>
                <li class="li "><a href="https://www.whfciot.com/smart-solutions.html">智慧方案</a></li>
                <li class="li "><a href="https://www.whfciot.com/case.html">应用案例</a></li>
                <li class="li "><a href="https://www.whfciot.com/news.html">新闻中心</a></li>
                <li class="li "><a href="https://www.whfciot.com/league.html" rel='nofollow'>招商加盟</a></li>
                <li class="li "><a href="https://www.whfciot.com/about-us.html" rel='nofollow'>关于我们</a></li>
                <li class="li "><a href="https://www.whfciot.com/talent-recruitment.html" rel='nofollow'>人才招聘</a></li>
                <li class="li "><a href="https://www.whfciot.com/contant-us.html" rel='nofollow'>联系我们</a></li>
            </ul>
        </div>
        <div class="bottom">
            <ul>
                <li>
                    <div class="name">
                        <img src="https://file.whfciot.com/ms/pro/news/assets/img/index/address.png" alt="" class="icon">
                        <div class="span">地址</div>
                    </div>
                    <div class="sub">武汉市光谷汇金中心1号楼</div>
                </li>
                <li>
                    <div class="name">
                        <img src="https://file.whfciot.com/ms/pro/news/assets/img/index/phone.png" alt="" class="icon" style="width: 0.2rem;height: 0.16rem;">
                        <div class="span">电话</div>
                    </div>
                    <div class="sub">027-86952032</div>
                </li>
                <li>
                    <div class="name">
                        <img src="https://file.whfciot.com/ms/pro/news/assets/img/index/email.png" alt="" class="icon" style="width: 0.18rem;height: 0.14rem;">
                        <div class="span">邮箱</div>
                    </div>
                    <div class="sub"><EMAIL></div>
                </li>
            </ul>
        </div>
        <div class="line"></div>
        <div class="copright">
            版权所有:武汉风潮物联科技有限公司 备案号:Copright©2009-2018 武汉风潮物联 鄂ICP备19001717号-
        </div>
        <div class="bg"></div>
        <div class="code">
            <div class="left"><img src="https://file.whfciot.com/ms/pro/news/assets/img/index/code.png" alt=""></div>
            <div class="right">
                <p>扫一扫</p>
                <p>关注官方微信</p>
            </div>
        </div>
    </footer>
    <script>
        $(function () {
            getNewsListFn();
            // 文字内容大小设置
            $('.small').click(function(){
                $('.html').css({'font-size':'0.12rem'})
            })
            $('.middle').click(function(){
                $('.html').css({'font-size':'0.16rem'})
            })
            $('.big').click(function(){
                $('.html').css({'font-size':'0.2rem'})
            })
            
        })
        // 数据列表请求
        function getNewsListFn(params) {
            let searchObj = getAllUrlParms(location.href);
            let id = searchObj.id;
            $.ajax({
                url: 'https://file.whfciot.com/ms/pro/news/file/news_index_all.json',
                type: 'GET',
                async: false,
                success: function (data) {
                    data.forEach((v,i)=>{
                        if(id == v.id){
                            let prevObj = data[i-1] == undefined ? data[data.length -1] : data[i-1];
                            let nextObj = data[i+1] == undefined ? data[0] : data[i+1];
                            $('.next').html(nextObj.title)
                            $('.prev').html(prevObj.title)
                            $('.next').click(function(){
                                location.href = `https://file.whfciot.com/ms/pro/news/file/news_${nextObj.id}.html?id=${nextObj.id}`;
                            })
                            $('.prev').click(function(){
                                location.href = `https://file.whfciot.com/ms/pro/news/file/news_${prevObj.id}.html?id=${prevObj.id}`;
                            })
                        }
                    })
                },
                error: function (error) {
                    console.log(error)
                }
            })
        }
        // 路由地址处理
        function getAllUrlParms(url)    {
            let theRequest = new Object();
            if (!url)
                url = location.href;
            if (url.indexOf("?") !== -1)
            {
                let str = url.substr(url.indexOf("?") + 1) + "&";
                let strs = str.split("&");
                for (let i = 0; i < strs.length - 1; i++)
                {
                    let key = strs[i].substring(0, strs[i].indexOf("="));
                    let val = strs[i].substring(strs[i].indexOf("=") + 1);
                    theRequest[key] = val;
                }
            }
            return theRequest;
        }
    </script>
</body>
</html>
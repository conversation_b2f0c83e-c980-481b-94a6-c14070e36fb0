package com.whfc.fse.influx.impl;

import com.whfc.common.enums.DelFlag;
import com.whfc.fse.constant.BridgeMeasurement;
import com.whfc.fse.dto.FseBridgeDataLogDTO;
import com.whfc.fse.entity.FseBridgeDataLog;
import com.whfc.fse.influx.FseBridgeDataLogDao;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.impl.InfluxDBMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.*;

/**
 * <AUTHOR>
 * @date 2021-05-31
 */
@Repository
public class FseBridgeDataLogDaoImpl implements FseBridgeDataLogDao {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 数据库名称
     */
    private static final String database = BridgeMeasurement.DATABASE;

    /**
     * 表名
     */
    private static final String measurement = BridgeMeasurement.MEASUREMENT;

    /**
     * 保留策略
     */
    private static final String retentionPolicy = BridgeMeasurement.RETENTION_POLICY;

    /**
     * 时间单位:秒
     */
    private static final TimeUnit timeUnit = TimeUnit.SECONDS;

    @Autowired
    private InfluxDB influxDB;

    @Autowired
    private InfluxDBMapper influxDBMapper;

    @Override
    public void insert(FseBridgeDataLog record) {
        logger.debug("influxdb的insert方法");
        influxDB.setDatabase(database);
        influxDB.setRetentionPolicy(retentionPolicy);
        influxDB.write(this.buildDataPoint(record));
    }

    @Override
    public void batchInsert(List<FseBridgeDataLog> recordList) {
        logger.debug("influxdb的batchInsert方法");
        BatchPoints.Builder batchBuiler = BatchPoints.builder();
        for (FseBridgeDataLog record : recordList) {
            Point point = this.buildDataPoint(record);
            batchBuiler.point(point);
        }
        influxDB.setDatabase(database);
        influxDB.setRetentionPolicy(retentionPolicy);
        influxDB.write(batchBuiler.build());
    }

    @Override
    public List<FseBridgeDataLogDTO> selectBridgeDataLogListByBridgeId(Integer bridgeId, Date startTime, Date endTime) {
        logger.debug("influxdb的selectBridgeDataLogListByBridgeId方法");
        Query query = select().from(database, measurement)
                .where(eq("bridgeId", String.valueOf(bridgeId)))
                .and(eq("delFlag", DelFlag.UNDELETE.getValue()))
                .andNested()
                .and(gte("time", startTime.toInstant().toString()))
                .and(lte("time", endTime.toInstant().toString()))
                .close()
                .orderBy(asc());
        logger.debug(query.getCommand());
        List<FseBridgeDataLogDTO> list = influxDBMapper.query(query, FseBridgeDataLogDTO.class);
        this.transformTime(list);
        return list;
    }

    /**
     * 构建数据点
     *
     * @param record
     * @return
     */
    private Point buildDataPoint(FseBridgeDataLog record) {
        logger.debug("influxdb的buildDataPoint方法");
        Point.Builder builder = Point
                .measurement(measurement)
                .time(record.getTime().toInstant().getEpochSecond(), timeUnit);
        if (!ObjectUtils.isEmpty(record.getBridgeId())) {
            builder.tag("bridgeId", String.valueOf(record.getBridgeId()));
        }
        if (!ObjectUtils.isEmpty(record.getFrontLiftHeight())) {
            builder.addField("frontLiftHeight", record.getFrontLiftHeight());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLiftWeight())) {
            builder.addField("frontLiftWeight", record.getFrontLiftWeight());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLngPosition())) {
            builder.addField("frontLngPosition", record.getFrontLngPosition());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLatPosition())) {
            builder.addField("frontLatPosition", record.getFrontLatPosition());
        }
        if (!ObjectUtils.isEmpty(record.getBackLiftHeight())) {
            builder.addField("backLiftHeight", record.getBackLiftHeight());
        }
        if (!ObjectUtils.isEmpty(record.getBackLiftWeight())) {
            builder.addField("backLiftWeight", record.getBackLiftWeight());
        }
        if (!ObjectUtils.isEmpty(record.getBackLngPosition())) {
            builder.addField("backLngPosition", record.getBackLngPosition());
        }
        if (!ObjectUtils.isEmpty(record.getBackLatPosition())) {
            builder.addField("backLatPosition", record.getBackLatPosition());
        }
        if (!ObjectUtils.isEmpty(record.getLngPosition())) {
            builder.addField("lngPosition", record.getLngPosition());
        }
        if (!ObjectUtils.isEmpty(record.getLatPosition())) {
            builder.addField("latPosition", record.getLatPosition());
        }
        if (!ObjectUtils.isEmpty(record.getWindSpeed())) {
            builder.addField("windSpeed", record.getWindSpeed());
        }
        if (!ObjectUtils.isEmpty(record.getLevelAngle())) {
            builder.addField("levelAngle", record.getLevelAngle());
        }
        if (!ObjectUtils.isEmpty(record.getVerticalAngle())) {
            builder.addField("verticalAngle", record.getVerticalAngle());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLiftWarn())) {
            builder.addField("frontLiftWarn", record.getFrontLiftWarn());
        }
        if (!ObjectUtils.isEmpty(record.getBackLiftWarn())) {
            builder.addField("backLiftWarn", record.getBackLiftWarn());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLiftWarnNum())) {
            builder.addField("frontLiftWarnNum", record.getFrontLiftWarnNum());
        }
        if (!ObjectUtils.isEmpty(record.getBackLiftWarnNum())) {
            builder.addField("backLiftWarnNum", record.getBackLiftWarnNum());
        }
        if (!ObjectUtils.isEmpty(record.getWindWarnNum())) {
            builder.addField("windWarnNum", record.getWindWarnNum());
        }
        if (!ObjectUtils.isEmpty(record.getDelFlag())) {
            builder.addField("delFlag", record.getDelFlag());
        }
        if (!ObjectUtils.isEmpty(record.getBackLiftTimeWarnNum())) {
            builder.addField("backLiftTimeWarnNum", record.getBackLiftTimeWarnNum());
        }
        if (!ObjectUtils.isEmpty(record.getWindTimeWarnNum())) {
            builder.addField("windTimeWarnNum", record.getWindTimeWarnNum());
        }
        if (!ObjectUtils.isEmpty(record.getFrontLiftTimeWarnNum())) {
            builder.addField("frontLiftTimeWarnNum", record.getFrontLiftTimeWarnNum());
        }
        builder.addField("serverTime", System.currentTimeMillis() / 1000);
        return builder.build();
    }

    /**
     * 时间类型转换
     *
     * @param list
     */
    private void transformTime(List<FseBridgeDataLogDTO> list) {
        for (FseBridgeDataLogDTO dto : list) {
            dto.setTime(Date.from(dto.getDeviceTime()));
            if (!ObjectUtils.isEmpty(dto.getServerTime())) {
                dto.setCreateTime(new Date(dto.getServerTime() * 1000));
            }
        }
    }
}

package com.whfc.fse.influx;

import com.whfc.fse.dto.FseCraneDataLogDTO;
import com.whfc.fse.entity.FseDeviceCraneDataLog;

import java.util.Date;
import java.util.List;

/**
 * 塔机历史数据
 */
public interface FseCraneDataLogDao {

    /**
     * 插入数据
     *
     * @param record
     */
    void insert(FseDeviceCraneDataLog record);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    void batchInsert(List<FseDeviceCraneDataLog> logList);

    /**
     * 查询塔机数据
     *
     * @param craneId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseCraneDataLogDTO> selectByCraneIdAndTime(Integer craneId, Date startTime, Date endTime);
}

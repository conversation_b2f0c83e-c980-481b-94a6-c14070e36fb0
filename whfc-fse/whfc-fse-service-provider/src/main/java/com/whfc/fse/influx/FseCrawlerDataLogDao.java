package com.whfc.fse.influx;

import com.whfc.fse.dto.FseCrawlerDataLogDTO;
import com.whfc.fse.entity.FseCrawlerDataLog;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/1 19:02
 */
public interface FseCrawlerDataLogDao {

    /**
     * 插入数据
     *
     * @param record 数据
     */
    void insert(FseCrawlerDataLog record);

    /**
     * 批量插入
     *
     * @param logList 数据列表
     */
    void batchInsert(List<FseCrawlerDataLog> logList);

    /**
     * 查询设备在时间段内的数据记录
     *
     * @param crawlerId 履带吊ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数据记录
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataLogListByCrawlerId(Integer crawlerId, Date startTime, Date endTime);

    /**
     * 查询设备在时间段内的数据记录
     *
     * @param crawlerId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataLogListByCrawlerId(String crawlerId, Date startTime, Date endTime);

    /**
     * 查询设备在时间段内的数据采样
     *
     * @param crawlerId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataSamplingByCrawlerId(Integer crawlerId, Date startTime, Date endTime);

    /**
     * 查询设备在时间段内的数据采样
     *
     * @param crawlerId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataSamplingByCrawlerId(String crawlerId, Date startTime, Date endTime);

    /**
     * 查询设备在时间段内的数据采样
     *
     * @param crawlerId
     * @param startTime
     * @param endTime
     * @param columns
     * @return
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataSamplingByCrawlerId(Integer crawlerId, Date startTime, Date endTime, String[] columns);

    /**
     * 查询设备在时间段内的数据采样
     *
     * @param crawlerId
     * @param startTime
     * @param endTime
     * @param columns
     * @return
     */
    List<FseCrawlerDataLogDTO> selectCrawlerDataSamplingByCrawlerId(String crawlerId, Date startTime, Date endTime, String[] columns);

}

package com.whfc.fse.influx;

import com.whfc.fse.dto.FseBridgeDataLogDTO;
import com.whfc.fse.entity.FseBridgeDataLog;

import java.util.Date;
import java.util.List;

/**
 * 架桥机历史数据
 */
public interface FseBridgeDataLogDao {

    /**
     * 插入数据
     *
     * @param record
     */
    void insert(FseBridgeDataLog record);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    void batchInsert(List<FseBridgeDataLog> logList);

    /**
     * 查询设备在时间段内的数据记录
     *
     * @param bridgeId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseBridgeDataLogDTO> selectBridgeDataLogListByBridgeId(Integer bridgeId, Date startTime, Date endTime);

}

package com.whfc.fse.influx;

import com.whfc.fse.dto.FseGantryDataLogDTO;
import com.whfc.fse.entity.FseGantryDataLog;

import java.util.Date;
import java.util.List;

/**
 * 龙门吊历史数据
 */
public interface FseGantryDataLogDao {
    /**
     * 插入数据
     *
     * @param record
     */
    void insert(FseGantryDataLog record);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    void batchInsert(List<FseGantryDataLog> logList);

    /**
     * 查询设备在时间段内的数据记录
     *
     * @param gantryId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseGantryDataLogDTO> selectGantryDataLogListByGantryId(Integer gantryId, Date startTime, Date endTime);
}

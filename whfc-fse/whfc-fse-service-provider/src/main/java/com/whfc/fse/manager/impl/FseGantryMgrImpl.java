package com.whfc.fse.manager.impl;

import com.whfc.common.enums.DelFlag;
import com.whfc.common.enums.NetState;
import com.whfc.fse.dao.FseGantryDataMapper;
import com.whfc.fse.dao.FseGantryMapper;
import com.whfc.fse.dto.FseGantryDataDTO;
import com.whfc.fse.dto.FseWarnDTO;
import com.whfc.fse.entity.FseGantry;
import com.whfc.fse.entity.FseGantryData;
import com.whfc.fse.entity.FseGantryDataLog;
import com.whfc.fse.enums.FseType;
import com.whfc.fse.enums.FseWarnRuleType;
import com.whfc.fse.manager.FseGantryMgr;
import com.whfc.fse.warn.fse.FseWarnMgr;
import com.whfc.fse.influx.FseGantryDataLogDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/17 18:10
 */
@Component
public class FseGantryMgrImpl implements FseGantryMgr {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FseGantryMapper fseGantryMapper;

    @Autowired
    private FseGantryDataMapper fseGantryDataMapper;

    @Autowired
    private FseGantryDataLogDao fseGantryDataLogDao;

    @Autowired
    private FseWarnMgr fseWarnMgr;

    @Override
    public void addData(FseGantryDataDTO fseGantryData) {
        String sn = fseGantryData.getSn();

        FseGantryData newFseGantryData = new FseGantryData();
        BeanUtils.copyProperties(fseGantryData, newFseGantryData);

        FseGantry fseGantry = fseGantryMapper.selectByPlatformAndSn(fseGantryData.getPlatform(), sn);
        if (fseGantry == null) {
            logger.info("未找到设备sn:{}", sn);
            return;
        }
        Integer mainHookWarnNum1 = newFseGantryData.getMainHookWarnNum() != null ? newFseGantryData.getMainHookWarnNum() : 0;
        Integer subHookWarnNum1 = newFseGantryData.getSubHookWarnNum() != null ? newFseGantryData.getSubHookWarnNum() : 0;
        Integer windWarnNum1 = newFseGantryData.getWindWarnNum() != null ? newFseGantryData.getWindWarnNum() : 0;

        FseGantryDataLog record = new FseGantryDataLog();
        BeanUtils.copyProperties(newFseGantryData, record);
        record.setDelFlag(DelFlag.UNDELETE.getValue());
        record.setSubHookTimeWarnNum(subHookWarnNum1);
        record.setWindTimeWarnNum(windWarnNum1);
        record.setMainHookTimeWarnNum(mainHookWarnNum1);


        FseGantryData gantryData = fseGantryDataMapper.selectByGantryId(fseGantry.getId());
        if (gantryData == null) {
            gantryData = new FseGantryData();
            gantryData.setGantryId(fseGantry.getId());
            fseGantryDataMapper.insertSelective(gantryData);
        }
        Integer mainHookWarnNum2 = gantryData.getMainHookWarnNum() != null ? gantryData.getMainHookWarnNum() : 0;
        Integer mainHookWarnNum = mainHookWarnNum1 - mainHookWarnNum2;
        newFseGantryData.setMainHookTimeWarnNum(mainHookWarnNum);
        record.setMainHookTimeWarnNum(mainHookWarnNum);

        Integer subHookWarnNum2 = gantryData.getSubHookWarnNum() != null ? gantryData.getSubHookWarnNum() : 0;
        Integer subHookWarnNum = subHookWarnNum1 - subHookWarnNum2;
        newFseGantryData.setSubHookTimeWarnNum(subHookWarnNum);
        record.setSubHookTimeWarnNum(subHookWarnNum);

        Integer windWarnNum2 = gantryData.getWindWarnNum() != null ? gantryData.getWindWarnNum() : 0;
        Integer windWarnNum = windWarnNum1 - windWarnNum2;
        newFseGantryData.setWindTimeWarnNum(windWarnNum);
        record.setWindTimeWarnNum(windWarnNum);
        // 插入或更新及时数据
        newFseGantryData.setId(gantryData.getId());
        fseGantryDataMapper.updateByPrimaryKeySelective(newFseGantryData);

        // 插入历史数据
        record.setGantryId(fseGantry.getId());
        fseGantryDataLogDao.insert(record);

        fseGantry.setNetState(NetState.ONLINE.getValue());
        fseGantryMapper.updateByPrimaryKeySelective(fseGantry);

        //报警数据处理
        if (fseGantryData.getWindWarnNum() != null && fseGantryData.getWindWarnNum() > 0) {
            FseWarnRuleType fseWarnRuleType = FseWarnRuleType.GANTRY_WIND_SPEED;
            String triggerValue = fseGantryData.getWindSpeed().toString();
            this.handleWarn(fseGantry, fseWarnRuleType, triggerValue, fseGantryData.getTime());
        }
        if (fseGantryData.getMainHookWarn() != null && fseGantryData.getMainHookWarn() > 0) {
            FseWarnRuleType fseWarnRuleType = FseWarnRuleType.GANTRY_MAIN_OVERLOAD;
            String triggerValue = fseGantryData.getMainHookLiftWeight().toString();
            this.handleWarn(fseGantry, fseWarnRuleType, triggerValue, fseGantryData.getTime());
        }
        if (fseGantryData.getSubHookWarn() != null && fseGantryData.getSubHookWarn() > 0) {
            FseWarnRuleType fseWarnRuleType = FseWarnRuleType.GANTRY_AUX_OVERLOAD;
            String triggerValue = fseGantryData.getSubHookLiftWeight().toString();
            this.handleWarn(fseGantry, fseWarnRuleType, triggerValue, fseGantryData.getTime());
        }
    }

    /**
     * 处理报警信息
     *
     * @param fseGantry
     * @param fseWarnRuleType
     * @param triggerValue
     * @param triggerTime
     */
    private void handleWarn(FseGantry fseGantry, FseWarnRuleType fseWarnRuleType, String triggerValue, Date triggerTime) {
        FseWarnDTO fseWarnDTO = new FseWarnDTO();
        fseWarnDTO.setFseType(FseType.GANTRY);
        fseWarnDTO.setFseWarnRuleType(fseWarnRuleType);
        fseWarnDTO.setDeptId(fseGantry.getDeptId());
        fseWarnDTO.setCode(fseGantry.getCode());
        fseWarnDTO.setTriggerObjectId(fseGantry.getId());
        fseWarnDTO.setTriggerTime(triggerTime);
        fseWarnDTO.setTriggerValue(triggerValue);
        fseWarnMgr.handleFseWarn(fseWarnDTO);
    }
}

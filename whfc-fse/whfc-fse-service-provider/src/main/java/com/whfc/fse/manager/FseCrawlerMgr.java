package com.whfc.fse.manager;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.DateUtil;
import com.whfc.fse.dao.FseCrawlerDayMapper;
import com.whfc.fse.dao.FseCrawlerMapper;
import com.whfc.fse.dto.FseCrawlerDataLogDTO;
import com.whfc.fse.dto.FseCrawlerDayDTO;
import com.whfc.fse.entity.FseCrawler;
import com.whfc.fse.entity.FseCrawlerDay;
import com.whfc.fse.influx.FseCrawlerDataLogDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/5/9 9:08
 */
@Component
public class FseCrawlerMgr {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private Date SPLIT_DATE = DateUtil.parseDate("2024-03-22", "yyyy-MM-dd");

    @Autowired
    private FseCrawlerMapper fseCrawlerMapper;

    @Autowired
    private FseCrawlerDataLogDao fseCrawlerDataLogDao;

    @Autowired
    private FseCrawlerDayMapper fseCrawlerDayMapper;

    /**
     * 履带吊每日统计数据生成
     *
     * @param crawlerId
     * @param date
     * @throws BizException
     */
    public void crawlerDayGenerate(Integer crawlerId, Date date) throws BizException {
        FseCrawler fseCrawler = fseCrawlerMapper.selectByPrimaryKey(crawlerId);
        if (fseCrawler == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "该履带吊不存在");
        }

        Date start = null;
        Date end = null;
        int num = 0;

        List<FseCrawlerDataLogDTO> list = this.getCrawlerLog(crawlerId, date);
        if (list.size() > 0) {
            start = list.get(0).getTime();
            end = list.get(list.size() - 1).getTime();
            num = list.size();
        }

        FseCrawlerDay record = fseCrawlerDayMapper.selectByCrawlerIdAndDate(crawlerId, date);
        if (record == null) {
            record = new FseCrawlerDay();
            record.setDeptId(fseCrawler.getDeptId());
            record.setCrawlerId(crawlerId);
            record.setDate(date);
            record.setStartTime(start);
            record.setEndTime(end);
            record.setNum(num);
            fseCrawlerDayMapper.insertSelective(record);
        } else {
            record.setStartTime(start);
            record.setEndTime(end);
            record.setNum(num);
            fseCrawlerDayMapper.updateByPrimaryKeySelective(record);
        }
    }

    public FseCrawlerDayDTO crawlerDay(Date date, List<FseCrawlerDataLogDTO> list) {
        Date start = null;
        Date end = null;
        int num = 0;
        if (list.size() > 0) {
            start = list.get(0).getTime();
            end = list.get(list.size() - 1).getTime();
            num = list.size();
        }
        FseCrawlerDayDTO record = new FseCrawlerDayDTO();
        record.setDate(date);
        record.setStartTime(start);
        record.setEndTime(end);
        record.setNum(num);
        return record;
    }

    public List<FseCrawlerDataLogDTO> getCrawlerLog(Integer crawlerId, Date date) {

        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);

        //2024.03.22之后,tag-crawlerId为字符串
        if (date.after(SPLIT_DATE)) {
            return fseCrawlerDataLogDao.selectCrawlerDataLogListByCrawlerId(String.valueOf(crawlerId), startTime, endTime);
        }
        //2024.03.22之前,tag-crawlerId为Integer
        else {
            return fseCrawlerDataLogDao.selectCrawlerDataLogListByCrawlerId(crawlerId, startTime, endTime);
        }
    }

    public List<FseCrawlerDataLogDTO> getCrawlerSampleLog(Integer crawlerId, Date date) {

        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);

        //2024.03.22之后,tag-crawlerId为字符串
        if (date.after(SPLIT_DATE)) {
            return fseCrawlerDataLogDao.selectCrawlerDataSamplingByCrawlerId(String.valueOf(crawlerId), startTime, endTime);
        }
        //2024.03.22之前,tag-crawlerId为Integer
        else {
            return fseCrawlerDataLogDao.selectCrawlerDataSamplingByCrawlerId(crawlerId, startTime, endTime);
        }
    }

    public List<FseCrawlerDataLogDTO> getCrawlerSampleLog(Integer crawlerId, Date date, String[] columns) {

        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);

        //2024.03.22之后,tag-crawlerId为字符串
        if (date.after(SPLIT_DATE)) {
            return fseCrawlerDataLogDao.selectCrawlerDataSamplingByCrawlerId(String.valueOf(crawlerId), startTime, endTime, columns);
        }
        //2024.03.22之前,tag-crawlerId为Integer
        else {
            return fseCrawlerDataLogDao.selectCrawlerDataSamplingByCrawlerId(crawlerId, startTime, endTime, columns);
        }
    }
}

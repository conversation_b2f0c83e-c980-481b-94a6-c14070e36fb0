package com.whfc.fse.influx;

import com.whfc.fse.dto.FseLiftDataLogDTO;
import com.whfc.fse.entity.FseDeviceLiftDataLog;

import java.util.Date;
import java.util.List;

/**
 * 塔机历史数据
 */
public interface FseLiftDataLogDao {

    /**
     * 插入数据
     *
     * @param record
     */
    void insert(FseDeviceLiftDataLog record);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    void batchInsert(List<FseDeviceLiftDataLog> logList);

    /**
     * 查询塔机数据
     *
     * @param liftId
     * @param startTime
     * @param endTime
     * @return
     */
    List<FseLiftDataLogDTO> selectByLiftIdAndTime(Integer liftId, Date startTime, Date endTime);

    /**
     * 查询塔机数据
     *
     * @param liftId
     * @param startTime
     * @param endTime
     * @param liftSide
     * @return
     */
    List<FseLiftDataLogDTO> selectByLiftIdAndTime(Integer liftId, Date startTime, Date endTime, Integer liftSide);

}

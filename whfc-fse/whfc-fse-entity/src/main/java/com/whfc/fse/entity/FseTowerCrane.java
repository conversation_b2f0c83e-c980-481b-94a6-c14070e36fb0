package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 4S天秤吊表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Schema(description = "4S天秤吊表")
@Data
public class FseTowerCrane {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * GUID
     */
    @Schema(description = "GUID")
    private String guid;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String code;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String model;

    /**
     * 制造单位
     */
    @Schema(description = "制造单位")
    private String manufacturer;

    /**
     * 出厂编号
     */
    @Schema(description = "出厂编号")
    private String deliveryCode;

    /**
     * 出厂日期
     */
    @Schema(description = "出厂日期")
    private Date deliveryDate;

    /**
     * 位置信息
     */
    @Schema(description = "位置信息")
    private String address;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @Schema(description = "删除标记 0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
package com.whfc.fse.entity;

import java.util.Date;

public class FseDeviceLiftDataLog {
    private Integer id;

    private Integer liftId;

    private Integer liftSide;

    private Integer deviceId;

    private Date time;

    private Double height;

    private Double windSpeed;

    private Double speed;

    private Integer floor;

    private Double weight;

    private Integer personNo;

    private Double dipAngle;

    private Integer frontDoorState;

    private Integer backDoorState;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLiftId() {
        return liftId;
    }

    public void setLiftId(Integer liftId) {
        this.liftId = liftId;
    }

    public Integer getLiftSide() {
        return liftSide;
    }

    public void setLiftSide(Integer liftSide) {
        this.liftSide = liftSide;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public Double getSpeed() {
        return speed;
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public Integer getFloor() {
        return floor;
    }

    public void setFloor(Integer floor) {
        this.floor = floor;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Integer getPersonNo() {
        return personNo;
    }

    public void setPersonNo(Integer personNo) {
        this.personNo = personNo;
    }

    public Double getDipAngle() {
        return dipAngle;
    }

    public void setDipAngle(Double dipAngle) {
        this.dipAngle = dipAngle;
    }

    public Integer getFrontDoorState() {
        return frontDoorState;
    }

    public void setFrontDoorState(Integer frontDoorState) {
        this.frontDoorState = frontDoorState;
    }

    public Integer getBackDoorState() {
        return backDoorState;
    }

    public void setBackDoorState(Integer backDoorState) {
        this.backDoorState = backDoorState;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.whfc.fse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 升降机实时数据公开接口
 * <AUTHOR>
 * @Date 2021-09-30 14:49
 * @Version 1.0
 */
@Data
public class FseLiftDataAddParam implements Serializable {

    /**
     * 硬件编码
     */
    private String sn;

    /**
     * 时间
     */
    @JsonFormat(pattern = DateUtil.DATE_TIME_FORMAT)
    private Date time;

    /**
     * 轿厢方位（1-左 2-右）
     */
    private Integer liftSide;

    /**
     * 最大高度
     */
    private Double maxHeight;
    /**
     * 最大重量
     */
    private Double maxWeight;
    /**
     * 最大人数
     */
    private Integer maxPersonNo;
    /**
     * 最大倾角
     */
    private Double maxDipAngle;
    /**
     * 最大速度
     */
    private Double maxSpeed;
}

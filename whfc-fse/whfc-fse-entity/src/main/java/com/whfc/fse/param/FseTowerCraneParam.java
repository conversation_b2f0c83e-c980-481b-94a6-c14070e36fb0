package com.whfc.fse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Data
@Schema(description = "4S天秤吊设备DTO")
public class FseTowerCraneParam implements Serializable {
    /**
     * GUID
     */
    @Schema(description = "GUID-修改时传递")
    private String guid;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String code;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String model;

    /**
     * 制造单位
     */
    @Schema(description = "制造单位")
    private String manufacturer;

    /**
     * 出厂编号
     */
    @Schema(description = "出厂编号")
    private String deliveryCode;

    /**
     * 出厂日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "出厂日期")
    private Date deliveryDate;

    /**
     * 位置信息
     */
    @Schema(description = "位置信息")
    private String address;


    @Schema(description = "设备列表")
    private List<FseTowerCraneDeviceParam> deviceList;

}

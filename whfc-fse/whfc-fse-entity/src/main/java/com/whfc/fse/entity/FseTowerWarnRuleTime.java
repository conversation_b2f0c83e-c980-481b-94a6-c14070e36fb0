package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 天秤吊报警-报警时间
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/23
 */
@Schema(description = "天秤吊报警-报警时间")
@Data
public class FseTowerWarnRuleTime {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 报警规则ID
     */
    @Schema(description = "报警规则ID")
    private Integer ruleId;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date endTime;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    @Schema(description = "删除标记(0-未删除 1-已删除)")
    private Integer delFlag;

    @Schema(description = "")
    private Date updateTime;

    @Schema(description = "")
    private Date createTime;
}
package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 天秤吊报警-接收人
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Schema(description = "天秤吊报警-接收人")
@Data
public class FseTowerWarnRuleUser {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 天秤吊报警规则id
     */
    @Schema(description = "天秤吊报警规则id")
    private Integer warnRuleId;

    /**
     * 接收人
     */
    @Schema(description = "接收人")
    private Integer toUserId;

    /**
     * 接收人姓名
     */
    @Schema(description = "接收人姓名")
    private String toUserName;

    /**
     * 接收人手机号
     */
    @Schema(description = "接收人手机号")
    private String toUserPhone;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
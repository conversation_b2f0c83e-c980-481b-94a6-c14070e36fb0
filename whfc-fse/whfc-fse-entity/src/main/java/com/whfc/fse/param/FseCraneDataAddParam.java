package com.whfc.fse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 塔吊实时数据公开接口
 * <AUTHOR>
 * @Date 2021-09-30 10:27
 * @Version 1.0
 */
@Data
public class FseCraneDataAddParam implements Serializable {

    /**
     * 硬件编号
     */
    private String sn;

    /**
     *
     */
    @JsonFormat(pattern = DateUtil.DATE_TIME_FORMAT)
    private Date time;

    /**
     * 起重臂长（米）
     */
    private Double foreArmLength;

    /**
     * 平衡臂长（米）
     */
    private Double rearArmLength;

    /**
     * 塔臂高（米）
     */
    private Double towerArmHeight;

    /**
     * 最大载重（吨）
     */
    private Double maxWeight;

    /**
     * 最大高度（米）
     */
    private Double maxHeight;

    /**
     * 最大幅度（米）
     */
    private Double maxRange;

    /**
     * 最大风速（m/s）
     */
    private Double maxWindSpeed;

    /**
     * 最大倾角（度）
     */
    private Double maxDipAngle;

    /**
     * 最大转角（度）
     */
    private Double maxTurnAngle;

    /**
     * 最大力矩比（%）
     */
    private Double maxMomentRatio;
}

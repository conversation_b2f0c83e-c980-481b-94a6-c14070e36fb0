package com.whfc.fse.entity;

import lombok.Data;

import java.util.Date;

/**
    * 特种设备履历附件
    */
@Data
public class FseResumeAttach {
    /**
    * 主键
    */
    private Integer id;

    /**
    * 履历id
    */
    private Integer resumeId;

    /**
    * 文件地址
    */
    private String url;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;
}
package com.whfc.fse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.fse.dto.FseOperatorDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 架桥机修改
 * <AUTHOR>
 * @Date 2021-06-01 10:11
 * @Version 1.0
 */
@Data
public class FseBridgeEditParam implements Serializable {

    /**
     * 组织机构id
     */
    @NotNull
    private Integer bridgeId;
    /**
     * 塔机编码
     */
    @NotEmpty
    private String code;
    /**
     * 塔机型号名称
     */
    private String modelName;
    /**
     * 制造商
     */
    private String manufacturer;
    /**
     * 出厂编码
     */
    private String deliveryCode;
    /**
     * 产权单位
     */
    private String propertyUnit;
    /**
     * 监察编号
     */
    private String monitorNo;
    /**
     * 备案编号
     */
    private String filingNo;
    /**
     * 备案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date filingDate;
    /**
     * 出厂日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    /**
     * 安拆单位
     */
    private String installUnit;

    /**
     * 操作人员
     */
    private List<FseOperatorDTO> operatorList;
}

package com.whfc.fse.param;

import com.whfc.common.validator.IntValueBetween;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-26 15:18
 */
@Data
public class FseLiftUnbindParam implements Serializable {

    /**
     * 升降机ID
     */
    @NotNull
    private Integer liftId;

    /**
     * 升降机方位
     */
    @NotNull
    @IntValueBetween(between = {1, 2})
    private Integer liftSide;
}

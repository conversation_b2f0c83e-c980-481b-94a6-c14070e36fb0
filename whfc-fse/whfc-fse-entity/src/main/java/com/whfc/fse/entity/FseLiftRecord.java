package com.whfc.fse.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-17 17:41
 */

/**
 * 升降机运行记录
 */
public class FseLiftRecord implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 升降机ID
     */
    private Integer liftId;

    /**
     * 升降机轿厢方位 1-左  2-右
     */
    private Integer liftSide;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 载重（吨）
     */
    private Double weight;

    /**
     * 人数
     */
    private Integer personNo;

    /**
     * 开始高度 (米)
     */
    private Double startHeight;

    /**
     * 结束高度 (米)
     */
    private Double endHeight;

    /**
     * 行程高度 (米)
     */
    private Double strokeHeight;

    /**
     * 平均速度 (米/秒)
     */
    private Double avgSpeed;

    /**
     * 方向 1-下 2-上
     */
    private Integer direction;

    /**
     * X倾向角度 (度)
     */
    private Double dipAngleX;

    /**
     * Y倾向角度 (度)
     */
    private Double dipAngleY;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLiftId() {
        return liftId;
    }

    public void setLiftId(Integer liftId) {
        this.liftId = liftId;
    }

    public Integer getLiftSide() {
        return liftSide;
    }

    public void setLiftSide(Integer liftSide) {
        this.liftSide = liftSide;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Integer getPersonNo() {
        return personNo;
    }

    public void setPersonNo(Integer personNo) {
        this.personNo = personNo;
    }

    public Double getStartHeight() {
        return startHeight;
    }

    public void setStartHeight(Double startHeight) {
        this.startHeight = startHeight;
    }

    public Double getEndHeight() {
        return endHeight;
    }

    public void setEndHeight(Double endHeight) {
        this.endHeight = endHeight;
    }

    public Double getStrokeHeight() {
        return strokeHeight;
    }

    public void setStrokeHeight(Double strokeHeight) {
        this.strokeHeight = strokeHeight;
    }

    public Double getAvgSpeed() {
        return avgSpeed;
    }

    public void setAvgSpeed(Double avgSpeed) {
        this.avgSpeed = avgSpeed;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Double getDipAngleX() {
        return dipAngleX;
    }

    public void setDipAngleX(Double dipAngleX) {
        this.dipAngleX = dipAngleX;
    }

    public Double getDipAngleY() {
        return dipAngleY;
    }

    public void setDipAngleY(Double dipAngleY) {
        this.dipAngleY = dipAngleY;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
package com.whfc.fse.param;

import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-04
 */
@Data
public class FseWarnSetUserParam implements Serializable {
    @NotEmpty
    private List<Integer> ruleIdList;

    private List<AppMsgToUserDTO> userList;

    private List<Integer> msgChannelList;
}

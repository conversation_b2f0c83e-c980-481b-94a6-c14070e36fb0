package com.whfc.fse.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 塔机监控绑定请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-23 09:56
 */
@Data
public class FseCraneFvsParam implements Serializable {

    /**
     * 塔机ID
     */
    @NotNull
    private Integer craneId;

    /**
     * 视频监控列表
     */
    private List<FseFvsDeviceParam> fvsList;


}

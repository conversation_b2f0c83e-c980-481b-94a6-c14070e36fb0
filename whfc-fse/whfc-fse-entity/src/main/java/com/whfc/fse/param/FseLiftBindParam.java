package com.whfc.fse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @DESCRIPTION
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/4/8
 */
@Data
public class FseLiftBindParam implements Serializable {
    /**
     * 升降机ID
     */
    @NotNull
    private Integer liftId;
    /**
     * 升降机轿厢方位  1-左  2-右
     */
    @NotNull
    private Integer liftSide;
    /**
     * SN
     */
    @NotEmpty
    private String sn;

    /**
     * 平台
     */
    private String platform;
}

package com.whfc.fse.entity;

import java.io.Serializable;
import java.util.Date;

public class FseCraneRecord implements Serializable {
    private Integer id;

    /**
     * 起重机id
     */
    private Integer craneId;

    /**
     * 数据上报时间
     */
    private Date time;

    /**
     * 起吊时间
     */
    private Date startTime;

    /**
     * 卸吊时间
     */
    private Date endTime;

    /**
     * 开始高度（米）
     */
    private Double startHeight;

    /**
     * 结束高度（米）
     */
    private Double endHeight;

    /**
     * 开始角度（度）
     */
    private Double startAngle;

    /**
     * 结束角度（度）
     */
    private Double endAngle;

    /**
     * 开始幅度（米）
     */
    private Double startRange;

    /**
     * 结束幅度（米）
     */
    private Double endRange;

    /**
     * 最大吊重（顿）
     */
    private Double maxWeight;

    /**
     * 最大风速（m/s）
     */
    private Double maxWindSpeed;

    /**
     * 最大倾角（度）
     */
    private Double maxDipAngle;

    /**
     * 最大转角（度）
     */
    private Double maxTurnAngle;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCraneId() {
        return craneId;
    }

    public void setCraneId(Integer craneId) {
        this.craneId = craneId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Double getStartHeight() {
        return startHeight;
    }

    public void setStartHeight(Double startHeight) {
        this.startHeight = startHeight;
    }

    public Double getEndHeight() {
        return endHeight;
    }

    public void setEndHeight(Double endHeight) {
        this.endHeight = endHeight;
    }

    public Double getStartAngle() {
        return startAngle;
    }

    public void setStartAngle(Double startAngle) {
        this.startAngle = startAngle;
    }

    public Double getEndAngle() {
        return endAngle;
    }

    public void setEndAngle(Double endAngle) {
        this.endAngle = endAngle;
    }

    public Double getStartRange() {
        return startRange;
    }

    public void setStartRange(Double startRange) {
        this.startRange = startRange;
    }

    public Double getEndRange() {
        return endRange;
    }

    public void setEndRange(Double endRange) {
        this.endRange = endRange;
    }

    public Double getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(Double maxWeight) {
        this.maxWeight = maxWeight;
    }

    public Double getMaxWindSpeed() {
        return maxWindSpeed;
    }

    public void setMaxWindSpeed(Double maxWindSpeed) {
        this.maxWindSpeed = maxWindSpeed;
    }

    public Double getMaxDipAngle() {
        return maxDipAngle;
    }

    public void setMaxDipAngle(Double maxDipAngle) {
        this.maxDipAngle = maxDipAngle;
    }

    public Double getMaxTurnAngle() {
        return maxTurnAngle;
    }

    public void setMaxTurnAngle(Double maxTurnAngle) {
        this.maxTurnAngle = maxTurnAngle;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
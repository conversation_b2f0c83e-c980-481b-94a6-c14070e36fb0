package com.whfc.fse.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 特种设备视频监控绑定参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-23 09:59
 */
@Data
public class FseFvsDeviceParam implements Serializable {

    /**
     * 特种设备类型 {@link com.whfc.fse.enums.FseType}
     */
    private Integer fseType;
    /**
     * 塔机部位
     *
     * @see com.whfc.fse.enums.FseCranePart
     */
    private Integer cranePart;

    /**
     * 升降机轿厢
     *
     * @see com.whfc.fse.enums.FseLiftSide
     */
    private Integer liftSide;

    /**
     * 特种设备监控部位 {@link com.whfc.fse.enums.FsePart}
     */
    private Integer part;

    /**
     * 视频监控设备ID
     */
    private Integer fvsDeviceId;

    /**
     * 视频监控设备名称
     */
    private String fvsDeviceName;

}

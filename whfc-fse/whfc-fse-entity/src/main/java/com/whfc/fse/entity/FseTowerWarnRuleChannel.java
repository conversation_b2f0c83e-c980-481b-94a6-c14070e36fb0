package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 天秤吊报警接收方式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Schema(description = "天秤吊报警接收方式")
@Data
public class FseTowerWarnRuleChannel {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 特种设备报警规则id
     */
    @Schema(description = "特种设备报警规则id")
    private Integer warnRuleId;

    /**
     * 接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件
     */
    @Schema(description = "接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件")
    private Integer msgChannel;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 天秤吊报警规则和报警对象关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Schema(description = "天秤吊报警规则和报警对象关联表")
@Data
public class FseTowerWarnRuleObject {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 报警规则id
     */
    @Schema(description = "报警规则id")
    private Integer warnRuleId;

    /**
     * 报警对象id
     */
    @Schema(description = "报警对象id")
    private String objectId;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
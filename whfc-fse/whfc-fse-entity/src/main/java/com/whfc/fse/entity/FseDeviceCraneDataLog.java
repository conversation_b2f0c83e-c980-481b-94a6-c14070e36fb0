package com.whfc.fse.entity;

import java.util.Date;

public class FseDeviceCraneDataLog {
    private Integer id;

    private Integer craneId;

    private Date time;

    private Double foreArmLength;

    private Double rearArmLength;

    private Double towerArmHeight;

    private Double range;

    private Double momentRatio;

    private Double maxWeight;

    private Double armLength;

    private Double height;

    private Double weight;

    private Double windSpeed;

    private Double liftSpeed;

    private Double derrickSpeed;

    private Double dipAngle;

    private Double turnAngle;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCraneId() {
        return craneId;
    }

    public void setCraneId(Integer craneId) {
        this.craneId = craneId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getForeArmLength() {
        return foreArmLength;
    }

    public void setForeArmLength(Double foreArmLength) {
        this.foreArmLength = foreArmLength;
    }

    public Double getRearArmLength() {
        return rearArmLength;
    }

    public void setRearArmLength(Double rearArmLength) {
        this.rearArmLength = rearArmLength;
    }

    public Double getTowerArmHeight() {
        return towerArmHeight;
    }

    public void setTowerArmHeight(Double towerArmHeight) {
        this.towerArmHeight = towerArmHeight;
    }

    public Double getRange() {
        return range;
    }

    public Double getMomentRatio() {
        return momentRatio;
    }

    public void setMomentRatio(Double momentRatio) {
        this.momentRatio = momentRatio;
    }

    public Double getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(Double maxWeight) {
        this.maxWeight = maxWeight;
    }

    public Double getArmLength() {
        return armLength;
    }

    public void setArmLength(Double armLength) {
        this.armLength = armLength;
    }

    public void setRange(Double range) {
        this.range = range;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public Double getLiftSpeed() {
        return liftSpeed;
    }

    public void setLiftSpeed(Double liftSpeed) {
        this.liftSpeed = liftSpeed;
    }

    public Double getDerrickSpeed() {
        return derrickSpeed;
    }

    public void setDerrickSpeed(Double derrickSpeed) {
        this.derrickSpeed = derrickSpeed;
    }

    public Double getDipAngle() {
        return dipAngle;
    }

    public void setDipAngle(Double dipAngle) {
        this.dipAngle = dipAngle;
    }

    public Double getTurnAngle() {
        return turnAngle;
    }

    public void setTurnAngle(Double turnAngle) {
        this.turnAngle = turnAngle;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
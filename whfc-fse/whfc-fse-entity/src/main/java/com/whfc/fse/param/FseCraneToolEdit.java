package com.whfc.fse.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-28 17:06
 */
@Data
public class FseCraneToolEdit implements Serializable {

    @NotNull
    private Integer toolId;

    @Length(max = 64)
    private String hook;

    @Length(max = 64)
    private String wire;

    @Length(max = 64)
    private String bolt;

    @Length(max = 64)
    private String other;
}

package com.whfc.fse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @DESCRIPTION 架桥机
 * @AUTHOR hw
 * @DATE 2021/6/1
 */
@Data
public class FseBridgeBindParam implements Serializable {
    /**
     * 塔机ID
     */
    @NotNull
    private Integer bridgeId;
    /**
     * 设备SN
     */
    @NotEmpty
    private String sn;

    /**
     * 平台
     */
    private String platform;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;
}

package com.whfc.fse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @DESCRIPTION
 * @AUTHOR hw
 * @DATE 2021/6/1
 */
@Data
public class FseGantryBindParam implements Serializable {
    /**
     * 塔机ID
     */
    @NotNull
    private Integer gantryId;
    /**
     * 设备SN
     */
    @NotEmpty
    private String sn;

    /**
     * 平台
     */
    private String platform;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;
}

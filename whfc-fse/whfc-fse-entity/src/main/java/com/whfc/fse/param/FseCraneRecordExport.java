package com.whfc.fse.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/12 21:21
 */
@Data
public class FseCraneRecordExport implements Serializable {

    @NotNull
    private Integer deptId;

    private Date startTime;

    private Date endTime;

    @Length(max = 32)
    private String keyword;
}

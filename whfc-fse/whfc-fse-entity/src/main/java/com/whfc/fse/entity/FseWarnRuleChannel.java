package com.whfc.fse.entity;

import java.util.Date;

public class FseWarnRuleChannel {
    private Integer id;

    private Integer warnRuleId;

    private Integer msgChannel;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getWarnRuleId() {
        return warnRuleId;
    }

    public void setWarnRuleId(Integer warnRuleId) {
        this.warnRuleId = warnRuleId;
    }

    public Integer getMsgChannel() {
        return msgChannel;
    }

    public void setMsgChannel(Integer msgChannel) {
        this.msgChannel = msgChannel;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
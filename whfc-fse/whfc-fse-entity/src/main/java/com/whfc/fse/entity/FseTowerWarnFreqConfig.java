package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 报警频率配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/27
 */
@Schema(description = "报警频率配置")
@Data
public class FseTowerWarnFreqConfig {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Integer deptId;

    /**
     * 每天最大次数
     */
    @Schema(description = "每天最大次数")
    private Integer frequencyLimitDay;

    /**
     * 每小时最大次数
     */
    @Schema(description = "每小时最大次数")
    private Integer frequencyLimitHour;

    /**
     * 报警时间间隔（分钟）
     */
    @Schema(description = "报警时间间隔（分钟）")
    private Integer frequencyLimitInterval;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @Schema(description = "删除标记 0-未删除 1-已删除")
    private Integer delFlag;

    @Schema(description = "")
    private Date updateTime;

    @Schema(description = "")
    private Date createTime;
}
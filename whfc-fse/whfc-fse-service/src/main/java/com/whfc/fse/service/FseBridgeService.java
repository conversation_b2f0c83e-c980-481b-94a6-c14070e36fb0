package com.whfc.fse.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fse.dto.FseBridgeDTO;
import com.whfc.fse.dto.FseBridgeDataDTO;
import com.whfc.fse.dto.FseBridgeDataLogDTO;
import com.whfc.fse.param.FseBridgeAddParam;
import com.whfc.fse.param.FseBridgeBindParam;
import com.whfc.fse.param.FseBridgeEditParam;
import com.whfc.fse.param.FseBridgeUnbindParam;

import java.util.Date;
import java.util.List;

/**
 * @Description 架桥机
 * <AUTHOR>
 * @Date 2021-05-31 16:31
 * @Version 1.0
 */
public interface FseBridgeService {

    /**
     * 新增架桥机设备
     *
     * @param param
     * @throws BizException
     */
    void add(FseBridgeAddParam param) throws BizException;


    /**
     * 修改架桥机设备
     *
     * @param param
     * @throws BizException
     */
    void edit(FseBridgeEditParam param) throws BizException;

    /**
     * 删除架桥机
     *
     * @param bridgeId
     * @throws BizException
     */
    void del(Integer bridgeId) throws BizException;

    /**
     * 架桥机列表(分页)
     *
     * @param deptId
     * @param keyword
     * @param bindFlag
     * @param netState
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FseBridgeDTO> list(Integer deptId, String keyword, Integer bindFlag, Integer netState, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 桥架机详情
     *
     * @param bridgeId
     * @return
     * @throws BizException
     */
    FseBridgeDTO details(Integer bridgeId) throws BizException;

    /**
     * 架桥机列表(不分页)
     *
     * @param deptId
     * @param keyword
     * @param bindFlag
     * @param netState
     * @return
     * @throws BizException
     */
    List<FseBridgeDTO> list(Integer deptId, String keyword, Integer bindFlag, Integer netState) throws BizException;


    /**
     * 绑定架桥机硬件
     *
     * @param param
     * @throws BizException
     */
    void bind(FseBridgeBindParam param) throws BizException;


    /**
     * 解绑架桥机硬件
     *
     * @param param
     * @throws BizException
     */
    void unbind(FseBridgeUnbindParam param) throws BizException;

    /**
     * 架桥机监测
     *
     * @param bridgeId
     * @return
     * @throws BizException
     */
    FseBridgeDataDTO monitor(Integer bridgeId) throws BizException;

    /**
     * 桥架机历史记录
     * @param bridgeId
     * @param time
     * @return
     */
    List<FseBridgeDataLogDTO> bridgeLog(Integer bridgeId, Date time);
}

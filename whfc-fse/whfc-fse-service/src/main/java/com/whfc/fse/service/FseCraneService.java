package com.whfc.fse.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.fse.dto.*;
import com.whfc.fse.param.*;

import java.util.Date;
import java.util.List;

/**
 * @DESCRIPTION 塔机
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/4/8
 */

public interface FseCraneService {
    /**
     * 塔机列表
     *
     * @param pageNum
     * @param pageSize
     * @param deptId
     * @param keyword
     * @param bindFlag
     * @param netState
     * @return
     * @throws BizException
     */
    PageData<FseCraneDTO> list(Integer pageNum, Integer pageSize, Integer deptId, String keyword, Integer bindFlag, Integer netState) throws BizException;

    /**
     * 塔机列表
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    ListData<OpenApiFseCraneDTO> list(Integer deptId) throws BizException;

    /**
     * 塔机列表
     *
     * @param deptId
     * @param bindFlag
     * @return
     * @throws BizException
     */
    ListData<FseCraneDTO> list(Integer deptId, Integer bindFlag) throws BizException;

    /**
     * 添加塔机
     *
     * @param request
     * @throws BizException
     */
    void add(FseCraneAddParam request) throws BizException;

    /**
     * 编辑塔机
     *
     * @param request
     * @throws BizException
     */
    void edit(FseCraneEditParam request) throws BizException;

    /**
     * 删除塔机
     *
     * @param id
     * @throws BizException
     */
    void del(Integer id) throws BizException;

    /**
     * 塔机绑定硬件
     *
     * @param request
     * @return
     * @throws BizException
     */
    void bind(FseCraneBindParam request) throws BizException;

    /**
     * 塔机解绑硬件
     *
     * @param craneId
     * @throws BizException
     */
    void unbind(Integer craneId) throws BizException;

    /**
     * 塔机详情
     *
     * @param craneId id
     * @return 详情
     * @throws BizException
     */
    FseCraneDTO detail(Integer craneId) throws BizException;

    /**
     * 获取视频监控设备ID列表
     *
     * @param craneId 塔机ID
     * @return 视频监控设备ID列表
     * @throws BizException 业务异常
     */
    List<FseCraneFvsDeviceDTO> getFvsDeviceList(Integer craneId) throws BizException;

    /**
     * 绑定视频监控设备
     *
     * @param fseCraneFvsParam 请求参数
     * @throws BizException 业务异常
     */
    void bindFvs(FseCraneFvsParam fseCraneFvsParam) throws BizException;

    /**
     * 吊索具类表
     *
     * @param deptId
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FseCraneToolDTO> getCraneToolList(Integer deptId, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加吊索具
     *
     * @param param
     * @throws BizException
     */
    void addCraneTool(FseCraneToolAdd param) throws BizException;

    /**
     * 编辑吊索具
     *
     * @param param
     * @throws BizException
     */
    void editCraneTool(FseCraneToolEdit param) throws BizException;

    /**
     * 删除吊索具
     *
     * @param toolId
     * @throws BizException
     */
    void delCraneTool(Integer toolId) throws BizException;

    /**
     * 获取塔机及时数据
     *
     * @param craneId
     * @return
     * @throws BizException
     */
    FseCraneDataDTO latest(Integer craneId) throws BizException;

    /**
     * 获取塔机数据
     *
     * @param sn
     * @return
     * @throws BizException
     */
    FseCraneDataDTO latest(Integer deptId, String sn) throws BizException;

    /**
     * 获取塔机监控的历史数据
     *
     * @param craneId
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    ListData<FseCraneDataLogDTO> log(Integer craneId, Date startTime, Date endTime) throws BizException;

    /**
     * 获取塔机监控的历史数据
     *
     * @param sn
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    ListData<FseCraneDataLogDTO> log(Integer deptId, String sn, Date startTime, Date endTime) throws BizException;

    /**
     * 吊装记录列表
     *
     * @param deptId
     * @param pageNum
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param keyword
     * @return
     * @throws BizException
     */
    PageData<FseCraneRecordDTO> craneRecordList(Integer deptId, Integer pageNum, Integer pageSize, Date startTime, Date endTime, String keyword) throws BizException;

    /**
     * 吊装记录列表
     *
     * @param sn
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    ListData<FseCraneRecordDTO> craneRecordList(Integer deptId, String sn, Date startTime, Date endTime) throws BizException;

    /**
     * 吊装记录导出
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param keyword
     * @return
     * @throws BizException
     */
    OssPathDTO exportCraneRecord(Integer deptId, Date startTime, Date endTime, String keyword) throws BizException;

    /**
     * 大屏-特种设备-塔机运行详情
     *
     * @param craneId 塔机ID
     * @return 塔机详情
     * @throws BizException 业务异常
     */
    FseCraneDataDetailDTO getCraneDataDetail(Integer craneId) throws BizException;
}

package com.whfc.base.manager;

import java.util.Collection;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/27 17:16
 */
public interface MailMgr {

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param content
     */
    void sendMail(String to, String subject, String content);

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param content
     */
    void sendMail(Collection<String> to, String subject, String content);

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param html
     */
    void sendHtml(String to, String subject, String html);

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param html
     */
    void sendHtml(Collection<String> to, String subject, String html);
}

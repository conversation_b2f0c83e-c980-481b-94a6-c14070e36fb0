package com.whfc.base.queue;

import com.whfc.base.manager.MailMgr;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.util.JSONUtil;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import com.whfc.entity.dto.msg.PushMsgDTO;
import com.whfc.fuum.service.SysUserService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.springframework.amqp.core.ExchangeTypes.FANOUT;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-22
 */
@Component
@RabbitListener(bindings = @QueueBinding(value = @Queue(QueueConst.PUSH_MAIL),
        exchange = @Exchange(name = QueueConst.PUSH_MSG_EXCHANGE, type = FANOUT)), concurrency = "1-2")
public class MailDataQueueMessageListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = SysUserService.class, version = "1.0.0")
    private SysUserService sysUserService;

    @Autowired
    private MailMgr mailMgr;

    @RabbitHandler
    public void process(String textMsg) {
        PushMsgDTO pushMsgDTO = JSONUtil.parseObject(textMsg, PushMsgDTO.class);
        if (pushMsgDTO == null) {
            return;
        }
        Date time = pushMsgDTO.getTime();
        if (time == null) {
            return;
        }
        // 超过当前时间24小时的消息不发送
        if (time.getTime() < new Date().getTime() - 1000 * 60 * 60 * 24) {
            return;
        }

        List<AppMsgToUserDTO> toUserList = pushMsgDTO.getToUserList();
        if (CollectionUtils.isEmpty(toUserList)) {
            return;
        }
        // 查询用户邮件
        List<Integer> toUserIdList = toUserList.stream().map(AppMsgToUserDTO::getUserId).collect(Collectors.toList());
        Map<Integer, String> emails = sysUserService.getUserEmails(toUserIdList);
        if (emails.isEmpty()) {
            return;
        }
        // 发送邮件
        String title = pushMsgDTO.getTitle();
        String content = pushMsgDTO.getContent();
        String html = pushMsgDTO.getHtml();
        logger.info("发送邮件,title:{},content:{},emails:{}", title, content, emails);

        // 分开消息推送
        if (pushMsgDTO.isSingle()) {
            for (AppMsgToUserDTO toUser : toUserList) {
                if (emails.containsKey(toUser.getUserId())) {
                    String mail = emails.get(toUser.getUserId());
                    mailMgr.sendHtml(mail, title, toUser.getHtml());
                }
            }
        }
        // 统一消息推送
        else {
            if (StringUtils.isEmpty(html)) {
                mailMgr.sendMail(emails.values(), title, content);
            } else {
                mailMgr.sendHtml(emails.values(), title, html);
            }
        }
    }
}

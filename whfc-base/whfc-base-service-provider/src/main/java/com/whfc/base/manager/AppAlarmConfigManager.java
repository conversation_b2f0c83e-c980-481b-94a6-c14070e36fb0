package com.whfc.base.manager;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 报警频率配置
 * @date 2021-01-08
 */
public interface AppAlarmConfigManager {

    /**
     * 检测报警频率
     *
     * @param deptId
     * @param moduleType
     * @param ruleType
     * @param objectId
     * @param triggerTime
     * @return
     */
    boolean checkAlarmFrequency(Integer deptId, Integer moduleType, Integer ruleType, String objectId, Date triggerTime);
}

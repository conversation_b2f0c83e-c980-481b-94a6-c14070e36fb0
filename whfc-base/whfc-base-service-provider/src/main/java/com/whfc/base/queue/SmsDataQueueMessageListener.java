package com.whfc.base.queue;

import com.whfc.base.entity.AppSmsRecord;
import com.whfc.base.manager.SmsMgr;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.third.sms.SmsApi;
import com.whfc.common.third.sms.SmsResult;
import com.whfc.common.util.JSONUtil;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import com.whfc.entity.dto.msg.PushMsgDTO;
import com.whfc.entity.dto.msg.PushSmsDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.TreeMap;

import static org.springframework.amqp.core.ExchangeTypes.FANOUT;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-22
 */
@Component
@RabbitListener(bindings = @QueueBinding(value = @Queue(QueueConst.PUSH_SMS), exchange = @Exchange(name = QueueConst.PUSH_MSG_EXCHANGE, type = FANOUT)), concurrency = "1-2")
public class SmsDataQueueMessageListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SmsMgr smsMgr;

    @RabbitHandler
    public void process(String textMsg) {
        PushMsgDTO pushMsg = JSONUtil.parseObject(textMsg, PushMsgDTO.class);
        if (pushMsg == null) {
            return;
        }
        Date time = pushMsg.getTime();
        if (time == null) {
            return;
        }
        // 超过当前时间24小时的消息不发送
        if (time.getTime() < new Date().getTime() - 1000 * 60 * 60 * 24) {
            return;
        }
        List<AppMsgToUserDTO> toUserList = pushMsg.getToUserList();
        if (CollectionUtils.isEmpty(toUserList)) {
            return;
        }
        PushSmsDTO sms = pushMsg.getSms();
        if (sms == null) {
            return;
        }
        // 获取短信API调用接口
        Integer deptId = pushMsg.getDeptId();
        SmsApi smsApi = smsMgr.smsApi();
        if (smsApi == null) {
            return;
        }
        String tplCode = sms.getTplCode();
        TreeMap<String, String> tplParam = sms.getTplParam();
        Date now = new Date();
        for (AppMsgToUserDTO toUser : toUserList) {
            String username = toUser.getNickName();
            String phone = toUser.getPhone();
            logger.info("push_sms,推送消息,username:{},phone:{},tplCode:{},tplParam:{}", username, phone, tplCode, tplParam);
            try {
                tplParam.put("username", username);
                SmsResult result = smsApi.sendSms(phone, tplCode, tplParam);
                AppSmsRecord record = new AppSmsRecord();
                record.setDeptId(deptId);
                record.setTime(now);
                record.setPhone(phone);
                record.setTplCode(tplCode);
                record.setStatus(result.getStatus());
                record.setMessage(result.getMessage());
                smsMgr.saveSmsRecord(record);
            } catch (Exception ex) {
                logger.error("push_sms,发送短信异常", ex);
            }
        }
    }
}

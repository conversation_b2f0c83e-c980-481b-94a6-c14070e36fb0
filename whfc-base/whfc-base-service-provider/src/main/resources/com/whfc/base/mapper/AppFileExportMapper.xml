<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.base.dao.AppFileExportMapper">
    <resultMap id="BaseResultMap" type="com.whfc.base.entity.AppFileExport">
        <!--@mbg.generated-->
        <!--@Table app_file_export-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_format" jdbcType="VARCHAR" property="fileFormat"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="file_state" jdbcType="INTEGER" property="fileState"/>
        <result column="file_desc" jdbcType="VARCHAR" property="fileDesc"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, file_name, file_format, file_url, file_state, file_desc, del_flag, update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_file_export
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_file_export
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.base.entity.AppFileExport"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_file_export (dept_id, file_name, file_format,
        file_url, file_state, file_desc,
        del_flag, update_time, create_time
        )
        values (#{deptId,jdbcType=INTEGER}, #{fileName,jdbcType=VARCHAR}, #{fileFormat,jdbcType=VARCHAR},
        #{fileUrl,jdbcType=VARCHAR}, #{fileState,jdbcType=INTEGER}, #{fileDesc,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.base.entity.AppFileExport"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_file_export
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileFormat != null">
                file_format,
            </if>
            <if test="fileUrl != null">
                file_url,
            </if>
            <if test="fileState != null">
                file_state,
            </if>
            <if test="fileDesc != null">
                file_desc,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileFormat != null">
                #{fileFormat,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileState != null">
                #{fileState,jdbcType=INTEGER},
            </if>
            <if test="fileDesc != null">
                #{fileDesc,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.base.entity.AppFileExport">
        <!--@mbg.generated-->
        update app_file_export
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileFormat != null">
                file_format = #{fileFormat,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileState != null">
                file_state = #{fileState,jdbcType=INTEGER},
            </if>
            <if test="fileDesc != null">
                file_desc = #{fileDesc,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.base.entity.AppFileExport">
        <!--@mbg.generated-->
        update app_file_export
        set dept_id = #{deptId,jdbcType=INTEGER},
        file_name = #{fileName,jdbcType=VARCHAR},
        file_format = #{fileFormat,jdbcType=VARCHAR},
        file_url = #{fileUrl,jdbcType=VARCHAR},
        file_state = #{fileState,jdbcType=INTEGER},
        file_desc = #{fileDesc,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptId" resultType="com.whfc.base.dto.AppExportDTO">
      select
      file_name,
      file_format,
      file_url,
      file_state,
      file_desc,
      create_time
      from app_file_export
      where del_flag= 0
      and dept_id =#{deptId}
      and (file_state = 0 or file_state = 1)
      order by id desc
    </select>

    <select id="selectByOverdue" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        app_file_export
        WHERE
        file_state = 1
        AND DATE_ADD( now(), INTERVAL - 3 DAY )>= create_time
    </select>

    <update id="updateFailureState">
        UPDATE app_file_export
        SET file_state = -1
        WHERE
                DATE_ADD( now(), INTERVAL - 30 MINUTE )>= create_time
          AND file_state = 0
    </update>

    <update id="updateDelState">
        update app_file_export
        set file_state = 2
        where
        id in
        <foreach collection="fileIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateFileState">
        update app_file_export
        set file_state = #{state}
        where
        id in
        <foreach collection="fileIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
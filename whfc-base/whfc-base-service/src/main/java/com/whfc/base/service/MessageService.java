package com.whfc.base.service;

import com.whfc.base.dto.AppMsgDTO;
import com.whfc.base.dto.AppMsgStatDTO;
import com.whfc.base.dto.AppMsgStatItemDTO;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.entity.param.msg.AppMsgDelAllParam;

import java.util.Date;
import java.util.List;

/**
 * @Description: 消息服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-11-25 14:18
 */
public interface MessageService {

    /**
     * 消息统计
     *
     * @param userId
     * @param deptId
     * @return
     * @throws BizException
     */
    AppMsgStatDTO getMsgStat(Integer userId, Integer deptId) throws BizException;

    /**
     * 消息统计
     *
     * @param userId
     * @param deptId
     * @param msgType
     * @return
     * @throws BizException
     */
    AppMsgStatDTO getMsgStat(Integer userId, Integer deptId, Integer msgType) throws BizException;

    /**
     * 消息列表
     *
     * @param userId
     * @param deptId
     * @param msgType
     * @param moduleType
     * @param readState
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<AppMsgDTO> getMsgList(Integer userId, Integer deptId, Integer msgType, Integer moduleType, Integer readState, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 消息详情
     *
     * @param userId
     * @param id
     * @return
     * @throws BizException
     */
    AppMsgDTO getMsgDetail(Integer userId, Long id) throws BizException;

    /**
     * 消息详情
     *
     * @param userId
     * @param guid
     * @return
     * @throws BizException
     */
    AppMsgDTO getMsgDetail(Integer userId, String guid) throws BizException;

    /**
     * 删除消息
     *
     * @param userId
     * @param idList
     */
    void delMsg(Integer userId, List<Long> idList) throws BizException;

    /**
     * 删除消息
     *
     * @param userId
     * @param deptId
     * @param moduleType
     * @param readState
     */
    void delAllMsg(Integer userId, Integer deptId, Integer moduleType, Integer readState) throws BizException;


    /**
     * 根据消息类型删除用户消息
     *
     * @param userId
     * @param param
     */
    void delByModelType(Integer userId, AppMsgDelAllParam param) throws BizException;

    /**
     * 将消息设置为已读
     *
     * @param userId
     * @param idList
     */
    void readMsg(Integer userId, List<Long> idList) throws BizException;

    /**
     * 标记读取所有消息
     *
     * @param userId
     * @param deptId
     * @param moduleType
     */
    void readAllMsg(Integer userId, Integer deptId, Integer moduleType) throws BizException;

    /**
     * 统计消息模块类型
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppMsgStatItemDTO> statMsgByModuleType(Integer deptId, Date startTime, Date endTime) throws BizException;
}

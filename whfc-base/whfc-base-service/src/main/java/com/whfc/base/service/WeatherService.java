package com.whfc.base.service;

import com.whfc.base.dto.AppWeatherDTO;
import com.whfc.base.dto.AppWeatherDayDTO;
import com.whfc.base.dto.AppWeatherLogDTO;
import com.whfc.base.dto.WeatherDTO;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;

import java.util.Date;
import java.util.List;

/**
 * 天气预报
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年10月25日
 */
public interface WeatherService {

    /**
     * 获取项目的天气预报
     *
     * @param deptId
     * @param lng
     * @param lat
     */
    WeatherDTO getWeather(Integer deptId, Double lng, Double lat) throws BizException;

    /**
     * 获取历史天气预报
     *
     * @param deptId
     * @param date
     * @return
     */
    ListData<AppWeatherLogDTO> getWeatherHistory(Integer deptId, Date date) throws BizException;

    /**
     * 获取历史天气预报
     *
     * @param deptId
     * @param month
     * @return
     * @throws
     */
    ListData<AppWeatherLogDTO> getWeatherHistory(Integer deptId, String month) throws BizException;

    /**
     * 查询当天温度
     *
     * @param deptId
     * @param day
     * @return
     * @throws BizException
     */
    AppWeatherDayDTO getDatWeather(Integer deptId, Date day) throws BizException;

    /**
     * 获取一周天气
     *
     * @param deptId 组织机构ID
     * @return 天级天气
     * @throws BizException
     */
    List<AppWeatherDTO> getWeekWeatherList(Integer deptId) throws BizException;
}

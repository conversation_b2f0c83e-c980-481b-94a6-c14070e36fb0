package com.whfc.base.service;

import com.whfc.common.exception.BizException;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/27 17:30
 */
public interface MailService {

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param content
     */
    void sendMail(String to, String subject, String content) throws BizException;

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param content
     */
    void sendMail(List<String> to, String subject, String content) throws BizException;

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param html
     */
    void sendHtml(String to, String subject, String html) throws BizException;

    /**
     * 发送邮件
     *
     * @param to
     * @param subject
     * @param html
     */
    void sendHtml(List<String> to, String subject, String html) throws BizException;
}

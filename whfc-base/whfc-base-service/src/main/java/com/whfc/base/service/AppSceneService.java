package com.whfc.base.service;

import com.whfc.base.dto.SceneDTO;
import com.whfc.base.param.SceneAdd;
import com.whfc.base.param.SceneEdit;
import com.whfc.common.exception.BizException;

import java.util.List;

/**
 * @Description 现场模拟
 * <AUTHOR>
 * @Date 2021/3/24 17:31
 * @Version 1.0
 */
public interface AppSceneService {

    /**
     * 现场模拟列表
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<SceneDTO> sceneList(Integer deptId) throws BizException;

    /**
     * 现场模拟详情
     *
     * @param guid
     * @return
     * @throws BizException
     */
    SceneDTO sceneDetail(String guid) throws BizException;

    /**
     * 添加现场模拟
     *
     * @param param
     * @throws BizException
     */
    void sceneAdd(SceneAdd param) throws BizException;

    /**
     * 修改现场模拟
     *
     * @param param
     * @throws BizException
     */
    void sceneEdit(SceneEdit param) throws BizException;

    /**
     * 修改现场模拟
     *
     * @param param
     * @throws BizException
     */
    void sceneEditDetail(SceneEdit param) throws BizException;

    /**
     * 删除现场模拟
     *
     * @param guid
     * @throws BizException
     */
    void sceneDel(String guid) throws BizException;


    /**
     * 添加现场模拟
     *
     * @param param
     * @throws BizException
     */
    @Deprecated
    void add(SceneDTO param) throws BizException;

    /**
     * 修改现场模拟
     *
     * @param param
     * @throws BizException
     */
    @Deprecated
    void edit(SceneDTO param) throws BizException;

    /**
     * 删除现场模拟
     *
     * @param deptId
     * @throws BizException
     */
    @Deprecated
    void del(Integer deptId) throws BizException;

    /**
     * 查询现场模拟详情
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    @Deprecated
    SceneDTO details(Integer deptId) throws BizException;
}

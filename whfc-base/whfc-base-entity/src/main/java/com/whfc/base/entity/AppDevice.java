package com.whfc.base.entity;

import java.util.Date;

/**
 * 硬件信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-14 14:37
 */
public class AppDevice {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 设备类型:1-机械设备 2-安全帽 3-定位工牌 4-安全帽（视频版）
     */
    private Integer type;

    /**
     * 设备唯一SN
     */
    private String sn;

    /**
     * 设备编号(外部设备的编号)
     */
    private String code;

    /**
     * 扩展信息
     */
    private String ext1;

    /**
     * 扩展信息
     */
    private String ext2;

    /**
     * 颜色
     */
    private String color;

    /**
     * 激活标记:0-未激活 1-已激活
     */
    private Integer activeFlag;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 删除标记（0-未删除 1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Integer getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(Integer activeFlag) {
        this.activeFlag = activeFlag;
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
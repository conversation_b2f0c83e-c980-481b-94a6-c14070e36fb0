package com.whfc.common;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.HttpUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import org.junit.Test;

import java.nio.charset.Charset;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/16 15:44
 */
public class HttpUtilTest {

    @Test
    public void test() {
        String url = "http://127.0.0.1:8080/test/test";

        JSONObject json = new JSONObject();
        json.put("name", "张三");
        json.put("age", 18);

        String data = json.toString();

        String res = HttpUtil.doPost(url, data, null, null, null, "GBK");

        System.out.println(data);
        System.out.println(res);
    }

    @Test
    public void test1() throws Exception {
        ByteBuf buf = Unpooled.buffer();
        buf.writeCharSequence("男", Charset.forName("UTF8"));
        System.out.println(ByteBufUtil.prettyHexDump(buf));
    }

    @Test
    public void test2() throws Exception {
        ByteBuf buf = Unpooled.buffer();
        buf.writeCharSequence("操作成功", Charset.forName("GBK"));
        System.out.println(ByteBufUtil.prettyHexDump(buf));
    }
}

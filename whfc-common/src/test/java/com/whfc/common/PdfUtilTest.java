package com.whfc.common;

import com.whfc.common.pdf.ImageInfo;
import com.whfc.common.pdf.PdfUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/26 15:21
 */
public class PdfUtilTest {

    private static final String pdf = "C:\\Users\\<USER>\\Desktop\\test\\test.pdf";
    private static final String marker = "C:\\Users\\<USER>\\Desktop\\test\\marker.png";
    private static final String dst1 = "C:\\Users\\<USER>\\Desktop\\test\\dst1.pdf";
    private static final String dst2 = "C:\\Users\\<USER>\\Desktop\\test\\dst2.pdf";
    private static final String dst3 = "C:\\Users\\<USER>\\Desktop\\test\\dst3.pdf";
    private static final String dst4 = "C:\\Users\\<USER>\\Desktop\\test\\dst4.pdf";

    @Test
    public void testAddImageWaterMarker() {
        String url = "https://file.whfciot.com/ms/pro/fmam/zthk/purchase/961271984912596992/invoice/IN-2023-00868-China Civil_ HP Probook 450 (K)_1712999227.pdf";
        PdfUtil.addImageWaterMarker(url, dst1, marker);
    }

    @Test
    public void testAddTextWaterMater() {
        PdfUtil.addTextWaterMarker(pdf, dst2, "测试水印");
    }

    @Test
    public void testMergePdf() {
        List<String> pdfList = new ArrayList<>();
        pdfList.add("C:\\Users\\<USER>\\Desktop\\test\\1.pdf");
        pdfList.add("C:\\Users\\<USER>\\Desktop\\test\\2.pdf");
        pdfList.add("C:\\Users\\<USER>\\Desktop\\test\\dst4.pdf");
        PdfUtil.mergePdf(pdfList, dst3);
    }

    @Test
    public void testCreatePdf() {
        List<ImageInfo> imageInfos = new ArrayList<>();
        imageInfos.add(new ImageInfo("AAAAA", "C:\\Users\\<USER>\\Desktop\\test\\img1.jpg"));
        imageInfos.add(new ImageInfo("BBBBB", "C:\\Users\\<USER>\\Desktop\\test\\img2.jpg"));
        PdfUtil.createPdf(imageInfos, dst4);
    }

    @Test
    public void testPdfToImage() {
        String pdf = "C:\\Users\\<USER>\\Desktop\\test\\t1.pdf";
        List<String> images = PdfUtil.pdfToImage(pdf);
        System.out.println(images);
    }

}

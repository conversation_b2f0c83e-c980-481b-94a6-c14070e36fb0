package com.whfc.common;

import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.vs.hk.ArtemisApi;
import com.whfc.common.vs.hk.entity.*;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/9 15:50
 */
public class ArtemisApiTest {

    private Logger logger = LoggerFactory.getLogger(ArtemisApiTest.class);
    private String host = "*************:7443";
    private String appKey = "22298331";
    private String appSecret = "rtFh0tjrQ9GhfCQsb4J4";
    private ArtemisApi artemisApi = new ArtemisApi(host, appKey, appSecret);
    private String cameraIndexCode = "84eeab9238c54a868850c77bea9f51eb";

    @Test
    public void testGetRegionsRoot() {
        Region data = artemisApi.getRegionsRoot();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetSubRegions() {
        Regions data = artemisApi.getSubRegions("root00000000", "camera", 1, 100, 0);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetRegions() {
        Regions data = artemisApi.getRegions("camera", 1, 100);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testSearchEncodeDevice() {
        List<EncodeDevice> data = artemisApi.searchAllEncodeDevice();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testSearchCamera() {
        List<Camera> data = artemisApi.searchAllCamera().stream().collect(Collectors.toList());
        for (Camera camera : data) {
            System.out.println(String.format("%s-%s-%s-%s", camera.getParentIndexCode(), camera.getRegionPathName(), camera.getIndexCode(), camera.getName()));
        }
    }

    @Test
    public void testGetEncodeDeviceOnlines() {
        List<DeviceOnline> data = artemisApi.getAllEncodeDeviceOnlines();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetCameraOnlines() {
        List<DeviceOnline> data = artemisApi.getAllCameraOnlines();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetCameraInfo() {
        CameraInfo data = artemisApi.getCameraInfo(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetHlsStreamUrl() {
        String hls = artemisApi.getHlsStreamUrl(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(hls));
    }

    @Test
    public void testGetWssStreamUrl() {
        String data = artemisApi.getWssStreamUrl(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetHikStreamUrl() {
        String data = artemisApi.getHikStreamUrl(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetRtmpStreamUrl() {
        String data = artemisApi.getRtmpStreamUrl(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetDeviceRecordUrl() {
        Date now = new Date();
        Date beginTime = DateUtil.getDateBegin(now);
        Date endTime = DateUtil.getDateEnd(now);
        Records data = artemisApi.getDeviceRecordUrl(cameraIndexCode, beginTime, endTime);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetCenterRecordUrl() {
        Date now = new Date();
        Date beginTime = DateUtil.getDateBegin(now);
        Date endTime = DateUtil.getDateEnd(now);
        Records data = artemisApi.getCenterRecordUrl(cameraIndexCode, beginTime, endTime);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testCapture() {
        String data = artemisApi.capture(cameraIndexCode);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testFormat() {
        String format = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
        System.out.println(DateUtil.formatDate(new Date(), format));
    }

    @Test
    public void testPtz() {
        artemisApi.startPtz(cameraIndexCode, "LEFT");
    }

    @Test
    public void testStopPtz() {
        artemisApi.stopPtz(cameraIndexCode, "LEFT");
    }
}

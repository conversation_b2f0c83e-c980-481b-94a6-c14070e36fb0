package com.whfc.common;

import com.whfc.common.util.ZipUtil;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/9 10:06
 */
public class ZipUtilTest {

    @Test
    public void test1() throws Exception {
        String dir = "C:\\Users\\<USER>\\Desktop\\test";
        String dstFile = "C:\\Users\\<USER>\\Desktop\\test.zip";

        ZipUtil.zipDir(dir, dstFile);
    }

    @Test
    public void test2() throws Exception {
        String src1 = "C:\\Users\\<USER>\\Desktop\\test\\test1.pdf";
        String src2 = "C:\\Users\\<USER>\\Desktop\\test\\test2.pdf";
        String src3 = "C:\\Users\\<USER>\\Desktop\\test\\test3.pdf";
        String dstFile = "C:\\Users\\<USER>\\Desktop\\test1.zip";
        List<InputStream> iss = new ArrayList<>();
        iss.add(new FileInputStream(src1));
        iss.add(new FileInputStream(src2));
        iss.add(new FileInputStream(src3));
        InputStream inputStream = ZipUtil.zipPdfFile(iss);
        File file = new File(dstFile);
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream fileOutputStream = new FileOutputStream(file);
        int count;
        while ((count = inputStream.read()) != -1) {
            fileOutputStream.write(count);
        }
        fileOutputStream.flush();
        fileOutputStream.close();
    }
}

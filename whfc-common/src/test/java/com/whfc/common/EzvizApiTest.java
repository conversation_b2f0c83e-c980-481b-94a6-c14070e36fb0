package com.whfc.common;

import com.whfc.common.util.DateUtil;
import com.whfc.common.util.FileUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.RandomUtil;
import com.whfc.common.vs.ezviz.EzvizApi;
import com.whfc.common.vs.ezviz.entity.EzvizGbInfo;
import com.whfc.common.vs.ezviz.entity.EzvizRecordInfo;
import com.whfc.common.vs.ezviz.entity.EzvizStreamInfo;
import org.junit.Test;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-07-06 11:38
 */
public class EzvizApiTest {

    private String accessToken = "at.4roe73fi6jiuwmgi682ij0mi1g5xzflq-67xqel6k1l-13vbkek-ztxa3d9uo";

    private String productKey = "33010730992217952267";

    private String ipc = "GB0845614";


    @Test
    public void testGetToken() {
        String token = EzvizApi.getAccessToken("0eb4db7f781b4d1caa096c39fb8f95ff", "de0348e1878079c67bb42688f6ae0ef2");
        System.out.println(token);
    }

    @Test
    public void testGetStream() {
        EzvizStreamInfo streamInfo = EzvizApi.getStreamUrl(accessToken, ipc, 1);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetStreamRtmp() {
        EzvizStreamInfo streamInfo = EzvizApi.getRtmpUrl(accessToken, ipc, 1);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetStreamHls() {
        EzvizStreamInfo streamInfo = EzvizApi.getHlsUrl(accessToken, ipc, 1);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetStreamEzopen() {
        EzvizStreamInfo streamInfo = EzvizApi.getEzopenUrl(accessToken, ipc, 1);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetBackRtmp() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date stopTime = DateUtil.getDateEnd(now);
        EzvizStreamInfo streamInfo = EzvizApi.getBackRtmpUrl(accessToken, ipc, 1, startTime, stopTime);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetBackFlv() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date stopTime = DateUtil.getDateEnd(now);
        EzvizStreamInfo streamInfo = EzvizApi.getBackFlvUrl(accessToken, ipc, 1, startTime, stopTime);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetBackHls() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date stopTime = DateUtil.getDateEnd(now);
        EzvizStreamInfo streamInfo = EzvizApi.getBackHlsUrl(accessToken, ipc, 1, startTime, stopTime);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetBackEzopen() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date stopTime = DateUtil.getDateEnd(now);
        EzvizStreamInfo streamInfo = EzvizApi.getBackEzopenUrl(accessToken, ipc, 1, startTime, stopTime);
        System.out.println(JSONUtil.toPrettyString(streamInfo));
    }

    @Test
    public void testGetRecord() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date endTime = DateUtil.getDateEnd(now);
        List<EzvizRecordInfo> list = EzvizApi.getRecordList(accessToken, ipc, 1, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetGbLicense() {

        List<EzvizGbInfo> list = EzvizApi.getGbLicenseList(accessToken, productKey);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testAddDevice() {
        EzvizApi.createDevice("at.06b2rp0q34mhre7n8l2e55h6axw2vth9-8oift4glyy-17t2rij-kykavvzb3", "*********", "PZCJQM");
    }

    @Test
    public void deviceDecrypt() {
        EzvizApi.deviceDecrypt(accessToken, "*********", "`FCWLAP");
    }

    @Test
    public void testSendVoice1() {
        String voicePath = "C:\\Users\\<USER>\\Desktop\\test\\test.mp3";
        String deviceSerial = "*********";
        Integer channelNo = 1;
        File voiceFile = new File(voicePath);
        EzvizApi.sendVoice(accessToken, deviceSerial, channelNo, voiceFile);
    }

    @Test
    public void testSendVoice2() {
        String voiceUrl = "https://file.whfciot.com/ms/pro/mach/voice/2023030412323990232.mp3";
        String deviceSerial = "*********";
        Integer channelNo = 1;
        EzvizApi.sendVoice(accessToken, deviceSerial, channelNo, voiceUrl);
    }

    @Test
    public void test() throws Exception {
        String fileUrl = "https://file.whfciot.com/ms/pro/mach/voice/2023030412323990232.mp3";
        //wav文件
        String random = RandomUtil.getRandomFileName();
        String wavFileName = random + FileUtil.getSuffix(fileUrl);
        File wavFile = File.createTempFile("fvs/voice", wavFileName);
        System.out.println(wavFile.getPath());
        System.out.println(wavFile.getAbsolutePath());
    }

    @Test
    public void testGetSnapshot() {
        String deviceSerial = "AH8175304";
        Integer channel = 9;
        String url = EzvizApi.getSnapshotUrl(accessToken, deviceSerial, channel);
    }

    @Test
    public void testWorksiteQuery() {
        String worksiteList = EzvizApi.getWorksiteList(accessToken, 0, 10);
        System.out.println(worksiteList);
    }

    @Test
    public void testWorksiteAdd() {
        EzvizApi.addWorksite(accessToken, "测试工地");
    }

    @Test
    public void testBodyCardQuery() {
        String bodyCameraList = EzvizApi.getBodyCameraList(accessToken, "GB0845614");
        System.out.println(bodyCameraList);
    }

    @Test
    public void testBodyCardAdd() {
        EzvizApi.addDodyCamera(accessToken, "GB0845614", "88184042543d403f81c7ca0c255e69e7");
    }

    @Test
    public void testGetInspectList() {
        Date start = DateUtil.parseDateTime("2025-06-19 00:00:00");
        Date end = DateUtil.parseDateTime("2025-06-21 00:00:00");
        Integer inspectRecordId = null;
        Integer size = 10;
        String inspectList = EzvizApi.getInspectList(accessToken, "GB0845614", start, end, inspectRecordId, size);
        System.out.println(inspectList);
    }
}

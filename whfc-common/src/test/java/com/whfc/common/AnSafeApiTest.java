package com.whfc.common;

import com.whfc.common.third.ansafe.AnSafeApi;
import com.whfc.common.third.ansafe.entity.*;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/17 15:39
 */
public class AnSafeApiTest {

    private static final String ACCESS_KEY = "d40fad1d7d6d73d41ec89b9c725c2af7";

    private AnSafeApi api = new AnSafeApi(ACCESS_KEY);

    @Test
    public void test1() {

        List<GantryDevice> list = api.getGantryDevice();
        System.out.println("获取龙门吊设备信息 ==> " + JSONUtil.toPrettyString(list));
    }

    @Test
    public void test2() {

        GantryWork gantryWork = api.getGantryWork("610012");
        System.out.println(JSONUtil.toPrettyString("获取龙门吊设备实时数据信息 ==> " + gantryWork));
    }

    @Test
    public void test3() {

        List<CraneDevice> craneDeviceList = api.getCraneDevice();
        System.out.println(JSONUtil.toPrettyString("获取塔吊设备信息 ==> " + craneDeviceList));

    }

    @Test
    public void test4() {

        CraneWork craneWork = api.getCraneWork("400156");
        System.out.println(JSONUtil.toPrettyString("获取塔吊设备实时数据信息 ==> " + craneWork));
    }

    @Test
    public void test5() {

        CraneWorkLoop craneWorkLoop = api.getCraneWorkLoop("400156");
        System.out.println(JSONUtil.toPrettyString("获取塔吊实时运行记录数据 ==> " + craneWorkLoop));
    }
}

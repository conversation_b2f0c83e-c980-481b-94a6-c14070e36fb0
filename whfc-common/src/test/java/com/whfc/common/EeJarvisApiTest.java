package com.whfc.common;

import com.whfc.common.third.jarvis.AccessToken;
import com.whfc.common.third.jarvis.EeJarvisApi;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/11 9:42
 */
public class EeJarvisApiTest {

    @Test
    public void test() {
        String host = "http://*************:8000/api/core/v1.0/auth/pwd/sign-in";
        String username = "<EMAIL>";
        String password = "+zhongtu123";
        EeJarvisApi api = new EeJarvis<PERSON><PERSON>(host, username, password);
        AccessToken token = api.getAccessToken();
        System.out.println(JSONUtil.toPrettyString(token));
    }
}

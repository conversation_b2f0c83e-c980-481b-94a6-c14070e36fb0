package com.whfc.common;

import com.whfc.common.util.RegexUtil;
import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-22 9:50
 */
public class RegexUtilTest {

    @Test
    public void test() {
        System.out.println(RegexUtil.isRtmp("rtmp://xugc/live/test"));
        System.out.println(RegexUtil.isRtmp("http://xugc/live/test"));
        System.out.println(RegexUtil.isHls("https://xugc/live/test.m3u8"));
        System.out.println(RegexUtil.isHls("https://xugc/live/test.mp4"));
        System.out.println(RegexUtil.isMp4("https://xugc/live/test.m3u8"));
        System.out.println(RegexUtil.isMp4("https://xugc/live/test.mp4"));
    }

    @Test
    public void test1() {
        //Pattern pattern = Pattern.compile("^((QS|SSK520)[-|_][a-zA-Z0-9]+[-|_][0-9]+)-[a-zA-Z0-9\\s]+");
        Pattern pattern = Pattern.compile("((QS|SSK520)[-|_][a-zA-Z0-9]+[-|_][0-9]+)");
        String str1 = "QS-S-098-Confirmation of the CLP101 Revision";
        String str2 = "QS-BS-098-Confirmation of the CLP101 Revision";
        String str3 = "RE: SSK520-Redevelopment of Yuen Long Stadium -- Query Pending Answer-QS-A-034(Request TD HYD Consent for Run in Out)";
        String str4 = "SSK520-E3-099-Confirmation of the CLP101 Revision";
        String str5 = "SSK520_E7_098-Confirmation of the CLP101 Revision";
        String str6 = "SSK520_J3_098-Confirmation of the CLP101 Revision";

        this.getKey(pattern, str1);
        this.getKey(pattern, str2);
        this.getKey(pattern, str3);
        this.getKey(pattern, str4);
        this.getKey(pattern, str5);
        this.getKey(pattern, str6);
    }

    @Test
    public void test2(){
        String str1 = "YuanKong/PLC/315005012408A564/data";
        String[] arr=  str1.split("/");
        for (int i = 0; i < arr.length; i++) {
            System.out.println(arr[i]);
        }
    }

    public void getKey(Pattern pattern, String str) {
        String key = null;
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            key = matcher.group();
            break;
        }
        System.out.println(key);
    }
}

package com.whfc.common;

import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FileHandlerStrategy;
import com.whfc.common.file.properties.FileProperties;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/19 15:05
 */
public class MinioFileHandlerTest {

    private String type = "minio";
    private String name = "yhbh";
    private String keyId = "dtwM96VBbaug3mmKg1uq";
    private String keySecret = "7spM5vM0dij5QqmErdvCLNFhQAhTzWh8ZF1HjUg6";
    private String bucket = "whfciot";
    private String endpoint = "http://*************:8130";
    private String saveUrl = "http://*************:8130";
    private FileProperties properties = new FileProperties(type, name, keyId, keySecret, bucket, endpoint, saveUrl);
    private FileHandler fileHandler = FileHandlerStrategy.FileHandlerStrategy(properties);
    private String fileUrl = "http://**************:9000/whfciot/test.png";
    private String path = "test.png";

    @Test
    public void testUpload() throws Exception {
        String filepath = "C:\\Users\\<USER>\\Desktop\\test\\test.png";
        InputStream fis = new FileInputStream(filepath);
        String key = fileHandler.upload("test.png", fis);
        System.out.println(key);
    }

    @Test
    public void testGetAuthFileUrl() {
        String key = fileHandler.getAuthFileUrl(path, true);
        System.out.println(key);
    }

    @Test
    public void testGetPolicy() {
        Map<String, String> map = fileHandler.getPolicy("/ms/dev/sign.png", "tt");
        System.out.println(JSONUtil.toPrettyString(map));
    }

    @Test
    public void testGetSize() {
        Long size = fileHandler.getFileSize("test.png");
        System.out.println(size);
    }

    @Test
    public void testParseUrl() {
        String url = fileUrl;
        String path = fileHandler.getPath(url);
        String endPointUrl = fileHandler.getEndpointUrl(path);
        System.out.println(url);
        System.out.println(path);
        System.out.println(endPointUrl);
    }

    @Test
    public void testGetDownloadUrl(){
        System.out.println(fileHandler.getDownloadUrl("https://file.whfciot.com/test.png"));
        System.out.println(fileHandler.getDownloadUrl("http://127.0.0.1:9000/whfciot/test.png"));
        System.out.println(fileHandler.getDownloadUrl("http://**************:9000/whfciot/test.png"));
        System.out.println(fileHandler.getDownloadUrl("http://127.0.0.1:9000/yjbh/test.png"));
        System.out.println(fileHandler.getDownloadUrl("http://**************:9000/yjbh/test.png"));
    }

    @Test
    public void testDeleteFile(){
        fileHandler.delete(fileUrl);
    }

    @Test
    public void testDeleteFiles(){
        List<String> fileUrls = new ArrayList<>();
        fileUrls.add(fileUrl);
        fileHandler.delete(fileUrls);
    }

    @Test
    public void deleteFile() throws Exception {

        List<String> list = FileUtils.readLines(new File("C:\\Users\\<USER>\\Desktop\\data.txt"), "utf8");
        int length = list.size();
        int step = 1000;
        int pos = 0;
        System.out.println(length);
        while (pos < length) {
            int end = pos + step >= length ? length : pos + step;
            System.out.println("pos:" + pos + ",end:" + end);
            List<String> subList = list.subList(pos, end).stream()
                    .map(str-> str.replace("http://192.168.1.30:8130", "http://*************:8130"))
                    .collect(Collectors.toList());
            System.out.println(subList);
            pos = end + 1;
            try {
                fileHandler.delete(subList);
            } catch (Exception ex) {
            }
        }

    }
}

package com.whfc.common;

import com.whfc.common.third.yuke.YukeApi;
import com.whfc.common.third.yuke.result.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-18 9:27
 */
public class YukeApiTest {

    private static final String HOST = "https://api4.ykzt-hjjc.com";

    private static final String USERNAME = "数智建造（广东横琴）科技有限公司";

    private static final String PASSWORD = "123456";

    private static final YukeApi api = new YukeApi(HOST, USERNAME, PASSWORD);

    private static final String token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjU5ZjdjZmQwLWJmZjYtNDJhZC1iYzdiLTdlNTRhMzAwM2RlOCJ9.rTnsiwpMxZyG9F1FSPpGIEaQzWBumgZPZkvHVTrKY3dEz_3YdJXbJ78MlyZBmig0Kiseh1eAENV2uZhurSrYAQ";

    @Test
    public void testAccessToken() {
        String accessToken = api.getAccessToken();
        System.out.println(accessToken);
    }

    @Test
    public void testCraneInfo() {
        List<String> list = new ArrayList<>();
        list.add("6390229");
        list.add("6390224");
        list.add("5390135");
        List<CraneInfo> info = api.getCraneInfo(token, list);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testCraneRealData() {
        List<String> list = new ArrayList<>();
        list.add("6390229");
        list.add("6390224");
        list.add("5390135");
        List<CraneRealData> info = api.getCraneRealData(token, list, 3);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testCraneRecordData() {
        List<String> list = new ArrayList<>();
        list.add("6390229");
        list.add("6390224");
        list.add("5390135");
        Date startTime = DateUtil.parseDateTime("2025-06-26 00:00:00");
        Date endTime = DateUtil.parseDateTime("2025-06-27 23:59:59");
        List<CraneRecordData> info = api.getCraneRecordData(token, list, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testLiftInfo() {
        List<String> list = new ArrayList<>();
        list.add("3230015");
        list.add("3230016");
        List<LiftInfo> info = api.getLiftInfo(token, list);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testLiftRealData() {
        List<String> list = new ArrayList<>();
        list.add("3230015");
        list.add("3230016");
        List<LiftRealData> info = api.getLiftRealData(token, list, 3);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testLiftRecordData() {
        List<String> list = new ArrayList<>();
        list.add("3230015");
        list.add("3230016");
        Date startTime = DateUtil.parseDateTime("2025-06-26 00:00:00");
        Date endTime = DateUtil.parseDateTime("2025-06-27 23:59:59");
        List<LiftRecordData> info = api.getLiftRecordData(token, list, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(info));
    }
}

package com.whfc.common;

import com.whfc.common.third.exlive.ExliveApi;
import com.whfc.common.third.exlive.entity.*;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.*;

public class ExliveApiTest {

    private String host = "http://*************:8083";

    private String id = "NTk0LjI1NjUuMTAw";

    private String secret = "14e5a8b5451ae226185935d73e05d33e";

    private ExliveApi api = new ExliveApi(host, id, secret);

    private String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRpZCI6MTEwMTYsInRraSI6OTAxMDcxNjE0NDcxNDQyNDMyLCJjcnQiOjE2OTQ5OTg2Nzk2MjIsImNsaWVudE5hbWUiOiLh3uHiLcuutefB+b7WIiwicm9sZWlkIjo4NTksInVzZXJ0eXBlIjowLCJsdHAiOiJhcGkiLCJ0eXBlaWQiOjE1MDEsImV4cCI6MTY5NTA4NTA3OTYyMiwidXNlcmlkIjoyNTY1LCJzZXJ2ZXJpZCI6MTAzLCJ1c2VybmFtZSI6IsuutefB+b7WIn0=.ODk0NThmNGI5YzhhMjU5M2EyZmQ2ZGNhODQzODA3OWM=";

    @Test
    public void testLogin() {
        Token token = api.login();
        System.out.println(JSONUtil.toPrettyString(token));
    }

    @Test
    public void testGetDeviceList() {
        VehicleQueryData data = api.queryVehicleList(token, 1, 40);
        //System.out.println(JSONUtil.toPrettyString(data));
        List<Vehicle> pageList = data.getPageList();
        for (Vehicle v : pageList) {
            System.out.println(v.getDevicecoding() + "," + v.getVehicle_id() + "," + v.getVehicle_name() + "," + v.getVehicle_type() + "," + v.getInstalldate());
        }
        Map<String, String> vehicleMap = CollectionUtil.list2map(pageList, Vehicle::getVehicle_id, Vehicle::getDevicecoding);
        for (String key : vehicleMap.keySet()) {
            System.out.println(key + "," + vehicleMap.get(key));
        }
    }

    @Test
    public void testGetLocationData() {
        List<String> carIds = new ArrayList<>();
        carIds.add("39897");
        carIds.add("39905");
        carIds.add("39739");
        carIds.add("39901");
        carIds.add("39745");
        carIds.add("39195");
        carIds.add("39737");
        carIds.add("39740");
        carIds.add("39909");
        carIds.add("39904");
        carIds.add("39746");
        carIds.add("39738");
        carIds.add("39741");
        carIds.add("39742");
        carIds.add("39744");
        carIds.add("39743");
        carIds.add("39902");
        carIds.add("39903");
        carIds.add("39196");
        carIds.add("39900");
        carIds.add("39889");
        carIds.add("39895");
        carIds.add("39898");
        carIds.add("39899");
        carIds.add("39896");
        carIds.add("39894");
        carIds.add("39908");
        carIds.add("39892");
        carIds.add("39893");
        carIds.add("39200");
        carIds.add("39890");
        carIds.add("39891");
        LocationData data = api.queryLocationData(token, carIds);
        //System.out.println(JSONUtil.toPrettyString(data));
        List<Location> list = data.getData();
        Collections.sort(list, Comparator.comparing(Location::getGpstime));
        for (Location l : list) {
            System.out.println(l.getVehicle_id() + "," + l.getVehicle_name() + "," + l.getGpstime() + "," + l.getLng() + "," + l.getLat());
        }
    }

    @Test
    public void testGetTrajectoryData() {
        String car_id = "39902";
        Date startTime = DateUtil.parseDateTime("2023-09-01 00:00:00");
        Date endTime = DateUtil.parseDateTime("2023-09-01 23:55:59");
        List<Location> list = api.queryTrajectoryData(token, car_id, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }
}

package com.whfc.common;

import com.whfc.common.third.ship.Ships66Api;
import com.whfc.common.third.ship.entity.Ships66Info;
import com.whfc.common.third.ship.entity.Ships66Track;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;

public class Ships66ApiTest {

    private String host = "http://saas.ships66.com";
    private String key = "2ce4b6032b683ce8f2b382d43846cc07";
    private Ships66Api api = new Ships66Api(host, key);

    @Test
    public void testGetShipsInfo() {
        String mmsi = "701200221";
        Ships66Info info = api.getAisInfo(mmsi);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testGetTrack() {
        String mmsi = "352919000";
        Date start = new Date(1645604136000L);
        Date end = new Date(1645604496000L);
        List<Ships66Track> tracks = api.getTrack(mmsi, start, end);
        System.out.println(JSONUtil.toPrettyString(tracks));
    }
}

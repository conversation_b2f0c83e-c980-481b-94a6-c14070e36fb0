package com.whfc.common;

import com.whfc.common.util.WebUtils;
import org.junit.Test;

public class WebUtilsTest {

    @Test
    public void test() {
        System.out.println(WebUtils.isInnerHost(null, null));
        System.out.println(WebUtils.isInnerHost(null, "a"));
        System.out.println(WebUtils.isInnerHost("abc", null));
        System.out.println(WebUtils.isInnerHost("abc", ""));
        System.out.println(WebUtils.isInnerHost("abc", "a"));
        System.out.println(WebUtils.isInnerHost("abc", "d"));
    }
}

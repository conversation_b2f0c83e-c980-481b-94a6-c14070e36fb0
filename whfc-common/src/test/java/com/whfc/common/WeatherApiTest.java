package com.whfc.common;

import com.whfc.common.third.weather.WeatherApi;
import com.whfc.common.third.weather.WeatherInfo;
import com.whfc.common.third.weather.impl.CaiyunWeatherApi;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-13 16:20
 */
public class WeatherApiTest {

    /**
     * 测试彩云天气
     */
    @Test
    public void testCaiyunWeatherApi() {
        Double lng = 114.02659;
        Double lat = 22.439084;
        String token = "RMqUwk8szvFyMg5n";
        WeatherApi weatherApi = new CaiyunWeatherApi(token);
        WeatherInfo weatherInfo = weatherApi.getWeather(lng, lat);
        System.out.println(JSONUtil.toPrettyString(weatherInfo));
    }
}

package com.whfc.common;

import com.whfc.common.third.wuhu.WhHdApi;
import com.whfc.common.util.Base64Util;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/29 10:00
 */
public class WhHdApiTest {

    private String host = "http://zj.ruixin.net/hdxtsj";

    private String appCode = "FC_25c05989-8ee0-48b0-ad2a-2d01d2bca6cd";

    private String appSecret = "a37e69b7-fab2-4fc2-bf8d-0d27a2ec624f";

    private String idCard = "340223198304231116";

    private String userName = "张三";

    private String idCardImgUrl = "https://file.whfciot.com/ms/pro/emp/image/2021102910244061508.png";

    private String faceImgUrl = "https://file.whfciot.com/ms/pro/emp/image/2021102910254780075.png";

    private String equNo = "435";

    private String xmbm = "BD2017176901";

    @Test
    public void testRyrz() {

        String idCardImg = Base64Util.getUrlImageToBase64(idCardImgUrl);
        String faceImg = Base64Util.getUrlImageToBase64(faceImgUrl);

        WhHdApi api = new WhHdApi(host, appCode, appSecret, xmbm, equNo);
        api.ryrz(idCard, userName, idCardImg, faceImg);
    }

}

package com.whfc.common;

import com.whfc.common.third.sinoverse.SinoApi;
import com.whfc.common.third.sinoverse.entity.Device;
import com.whfc.common.third.sinoverse.entity.RealData;
import com.whfc.common.third.sinoverse.entity.Token;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/8/13 10:07
 */
public class SinoApiTest {

    private String username = "18682262103";
    private String password = "yjbh123456";
    private SinoApi api = new SinoApi(username, password);
    private String token = "M0T5c6yfN6z7I40cNcj9Yf36Mzk4OSw0ZWIwYzUyMmIwMWU0NDk2OTU5NTdiM2NkMzRhZDY3NSw0ZWIwYzUyMmIwMWU0NDk2OTU5NTdiM2NkMzRhZDY3NSwxNzI3MjQ2NjczOTg5";
    private String did = "863482068243803";
    @Test
    public void testGetToken(){
        Token token = api.getToken();
        System.out.println(JSONUtil.toPrettyString(token));
    }

    @Test
    public void testGetDeviceList(){
        List<Device> data = api.getDeviceList(token);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetDeviceData(){
        List<RealData> data = api.getDeviceData(token,did);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

package com.whfc.common;

import com.whfc.common.third.gzglgcjt.GzglgcjtApi;
import com.whfc.common.third.gzglgcjt.entity.AssetRecord;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/10 16:34
 */
public class GzglgcjtApiTest {

    private String systemid = "GLD";
    private String password = "C00404E91D944067950EFE99D296933C";
    private String host = "http://www.gzglgcjt.cn";

    private GzglgcjtApi api = new GzglgcjtApi(host, systemid, password);

    @Test
    public void test() {

        AssetRecord data = new AssetRecord();
        data.setGdzc("test");
        data.setBm("bm0001");
        data.setFl("fl");
        data.setFlbm("flbm");
        data.setDjrq("2023-05-10");
        data.setGzxs("22");
        data.setGzdd("深圳市");
        data.setGznr("挖土石方");
        data.setGzlc("300");

        String response = api.updateAssetRecord(data);
        System.out.println(response);
    }
}

package com.whfc.common;

import com.whfc.common.face.wheatSunshine.FaceResult;
import com.whfc.common.face.wheatSunshine.WheatSunshineConst;
import com.whfc.common.face.wheatSunshine.WheatSunshineFaceApi;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.FileUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/6/8 11:52
 */
public class WheatSunshineFaceApiTest {

    private String appId = "Wc7Ovxkc";
    private String appSecret = "4d135851dfc0d51107e2e46ecf8aa5a67767e8f5";
    private String server = "http://sg.4000750222.com/testHardware";
    private WheatSunshineFaceApi faceApi = new WheatSunshineFaceApi(appId, appSecret, server);
    private String deviceKey = "dd32fcebf1d68b1f";
    private String guid = "YG325433750870278144";

    @Test
    public void testDeviceAdd() {
        String name = "风潮物联";
        FaceResult<String> result = faceApi.deviceAdd(deviceKey, name);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceDel() {
        FaceResult<String> result = faceApi.deviceDel(deviceKey);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceQuery() {
        FaceResult<String> result = faceApi.deviceQuery(deviceKey);
        System.out.println(JSONUtil.toPrettyString(result));
    }


    @Test
    public void testDeviceAuthorization() {
        List<String> personGuids = new ArrayList<>();
        personGuids.add(guid);
        FaceResult<String> result = faceApi.deviceAuthorization(deviceKey, personGuids);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceAuthorizationQuery() {
        WheatSunshineFaceApi faceApi = new WheatSunshineFaceApi(WheatSunshineConst.appId, WheatSunshineConst.appSecret, WheatSunshineConst.server);
        FaceResult<String> result = faceApi.deviceAuthorizationQuery("QYdd32fcebf1d68b1f");
        System.out.println(JSONUtil.toPrettyString(result.getData()));
    }

    @Test
    public void testDeviceAuthorizationCancel() {
        FaceResult<String> result = faceApi.deviceAuthorizationCancel(deviceKey, guid);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceAuthorizationCancelBatch() {
        List<String> personGuids = new ArrayList<>();
        personGuids.add(guid);
        FaceResult<String> result = faceApi.deviceAuthorizationCancel(deviceKey, personGuids);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceMode() {

        Integer type = 2;
        FaceResult<String> result = faceApi.deviceMode(deviceKey, guid, type);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testDeviceOpenDoor() {
        FaceResult<String> result = faceApi.deviceOpenDoor(deviceKey);
        System.out.println(JSONUtil.toPrettyString(result));
    }


    @Test
    public void testPersonAdd() {

        String name = "张三";
        FaceResult<String> result = faceApi.personAdd(name);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testPersonUpdate() {

        String name = "李四";
        FaceResult<String> result = faceApi.personUpdate(guid, name);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testPersonDel() {

        FaceResult<String> result = faceApi.personDel(guid);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testPersonQuery() {
        FaceResult<String> result = faceApi.personQuery("YG333117481173012480");
        System.out.println(JSONUtil.toPrettyString(result));
    }


    @Test
    public void testPersonFaceQuery() {
        FaceResult<String> result = faceApi.personFaceQuery("YG333117481173012480");
        System.out.println(JSONUtil.toPrettyString(result));

    }

    @Test
    public void testPersonFaceAdd() throws Exception {
        String imgPath = "F:\\素材\\me.png";
        byte[] bytes = FileUtil.readData(new FileInputStream(imgPath));
        String imgBase64 = Base64Util.encodeToString(bytes);
        FaceResult<String> result = faceApi.personFaceAdd(guid, imgBase64);
        System.out.println(JSONUtil.toPrettyString(result));

    }

    @Test
    public void testCallbackAdd() {

        //拍照回调
        Integer callbackType = 1;
        String callbackUrl = "https://test.whfciot.com/open/api/faceGate/imgRegCallBack";
        FaceResult<String> result = faceApi.callbackAdd(callbackType, callbackUrl);
        System.out.println(JSONUtil.toPrettyString(result));

        //读卡回调
        callbackType = 2;
        callbackUrl = "https://test.whfciot.com/open/api/faceGate/identifyCallBack";
        result = faceApi.callbackAdd(callbackType, callbackUrl);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testCallbackUpdate() {
        WheatSunshineFaceApi faceApi = new WheatSunshineFaceApi(WheatSunshineConst.appId, WheatSunshineConst.appSecret, WheatSunshineConst.server);
        Integer callbackType;
        String callbackUrl;
        FaceResult<String> result;

        //读卡回调
        callbackType = 1;
        callbackUrl = "https://cms.whfciot.com/open/api/faceGate/cardCallBack";
        result = faceApi.callbackUpdate(callbackType, callbackUrl);
        System.out.println(JSONUtil.toPrettyString(result));

        //拍照回调
        callbackType = 2;
        callbackUrl = "https://cms.whfciot.com/open/api/faceGate/imgRegCallBack";
        result = faceApi.callbackUpdate(callbackType, callbackUrl);
        System.out.println(JSONUtil.toPrettyString(result));


        //考勤回调
        callbackType = 3;
        callbackUrl = "https://cms.whfciot.com/open/api/faceGate/recCallBack";
        result = faceApi.callbackUpdate(callbackType, callbackUrl);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testCallbackQuery() {
        Integer callbackType = 3;
        FaceResult<String> result = faceApi.callbackQuery(callbackType);
        System.out.println(JSONUtil.toPrettyString(result));
    }


    @Test
    public void test() {
        String json = "{\"code\":\"GS_SUS301\",\"data\":\"[{\\\"deviceKey\\\":\\\"84E0F421A70306B2\\\",\\\"name\\\":\\\"84E0F421A70306B2\\\",\\\"state\\\":6,\\\"status\\\":1,\\\"expired\\\":\\\"false\\\"}]\",\"msg\":\"success\"}";
        FaceResult<?> result = JSONUtil.parseObject(json, FaceResult.class);
        System.out.println(JSONUtil.toPrettyString(result));
    }


}

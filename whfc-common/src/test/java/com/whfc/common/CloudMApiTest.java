package com.whfc.common;

import com.whfc.common.result.PageData;
import com.whfc.common.third.cloudm.CloudMApi;
import com.whfc.common.third.cloudm.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/8 14:01
 */
public class CloudMApiTest {

    private static String host = "http://api.cloudm.com";
    private static String accessKey = "Odnaq9duaRJDaddHHKQPPldmand84UDHAdNahdqPad";
    private static String secretKey = "HDoqnxna81jasnd7qTahdyqUYTG8d8qbdnavDTAiqO";
    private static CloudMApi api = new CloudMApi(host, accessKey, secretKey);
    private static String token = "03F3DE3FC5E1C813BE2509C814FB5372";
    private static String deviceId = "30629";
    private static String sn = "04201809B0004667";

    @Test
    public void testGetToken() {
        String token = api.getToken();
        System.out.println(token);
    }

    @Test
    public void testDeviceList() {
        PageData<DeviceInfo> list = api.deviceList(token, 1, 100);
        System.out.println(JSONUtil.toPrettyString(list));
        list.getList().stream().map(DeviceInfo::getSnId).collect(Collectors.toList()).forEach(System.out::println);
        list.getList().stream().map(DeviceInfo::getId).collect(Collectors.toList()).forEach(System.out::println);
    }

    @Test
    public void testDeviceData() {
        DeviceInfo info = api.deviceData(token, deviceId);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testWorkTimeList() {
        Date startDay = DateUtil.parseDate("2022-03-01", "yyyy-MM-dd");
        Date endDay = DateUtil.parseDate("2022-03-08", "yyyy-MM-dd");
        List<WorkTime> list = api.workTimeList(token, deviceId, startDay, endDay);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testWorkTimeDetail() {
        Date date = DateUtil.parseDate("2022-03-01", "yyyy-MM-dd");
        List<WorkTime> list = api.workTimeDetail(token, deviceId, date);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testWorkTimeDetailStatus() {
        Date date = DateUtil.parseDate("2022-03-01", "yyyy-MM-dd");
        List<WorkTime> list = api.workTimeDetailStatus(token, deviceId, date);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testLocation() {
        Date startTime = DateUtil.parseDate("2024-04-19", "yyyy-MM-dd");
        Date endTime = DateUtil.parseDate("2024-04-20", "yyyy-MM-dd");
        List<LocInfo> list = api.location(token, deviceId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testOilLevel() {
        Date startTime = DateUtil.parseDate("2022-03-07 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate("2022-03-08 12:00:00", "yyyy-MM-dd HH:mm:ss");
        List<OilInfo> list = api.oilLevel(token, deviceId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testOilLevelMerged() {
        Date startTime = DateUtil.parseDate("2022-03-07 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate("2022-03-08 12:00:00", "yyyy-MM-dd HH:mm:ss");
        List<OilInfo> list = api.oilLevelMerged(token, deviceId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testOilAddData() {
        Date startTime = DateUtil.parseDate("2022-03-07 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate("2022-03-08 12:00:00", "yyyy-MM-dd HH:mm:ss");
        OilAddData data = api.oilAddData(token, deviceId, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

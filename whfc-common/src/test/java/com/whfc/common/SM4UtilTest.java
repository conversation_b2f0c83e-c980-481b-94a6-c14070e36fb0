package com.whfc.common;

import com.whfc.common.util.SM4Util;
import com.whfc.common.util.Sm4Utils;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/29 10:49
 */
public class SM4UtilTest {

    @Test
    public void test1() throws Exception {
        String str = "340223198304231116";
        String secret = "a37e69b7-fab2-4fc2-bf8d-0d27a2ec624f";
        String data1 = SM4Util.encrypt(str, secret);
        String data2 = Sm4Utils.encryptEcb(secret, str);
        System.out.println(data1);
        System.out.println(data2);
    }

    @Test
    public void test2() throws Exception {
        String str = "安徽瑞信软件有限公司SMZQYRYADD9f019618b7358b1d52c8f";
        String secret = "a258f21c2d8a7ae955c893870d22b3c9";
        String data1 = SM4Util.encrypt(str, secret);
        String data2 = Sm4Utils.encryptEcb(secret, str);
        System.out.println(data1);
        System.out.println(data2);
    }

    @Test
    public void test3(){
        String str = "6baceb841dd74388d238f6fcf66de52552223414900cd50634e3bfc3237e1c00d5f1e22216c8b1280c810c476724033306543c28712cd750463ce56677d403e7e25b8c5d90c40d380e95912ffc1151511b6d3121275181615703680fff3d640c11c6f925e522df1f075aa58f562eeb8b7481cb4adaccb70744d2fa05ead947f190f7827690115a8ae622c6c9d9b1dd07cbca0557d590748e3682ad1e4537eac8c75d9e14fdc143e9be40a48a6e4c70ea18a7996d5eeb1067296762f45647dc054b8fe59bd06a7f65da848fa96f67ca2110cd76265c93eb35b173e61fb9bfb6e2bb5431e4739268b12f730ffc6f8d830d86b035d9362c10fe0b647b268ac96591eb715c8b89c41d1c80f0ba40f0e8661eb1e9096503e308db4d0ba20a36aed626cc4d23a66ed3cf277b32ecd49fa2f4a73f065e530704082f5fe0fc02d0a7a65f23f3caed7692bb9d9db25a9b7f0d97e2d41b8d2f49794e31a18ad9d7ee2c86aac9538622ae574dc49c9ae9558828d0747f60dc8154f6f9201e5c37d7fd21d1b31489a21645fd0da6d528dc9eb4181e43b16e93a3de86b81559b766ee6f66b2f4ccbc06d298c7780b9b64ab2ca5df1fd52b9743d1021029af6a3e43d20639d10ee9ee934b5675ecd9415b08cc9ff774252ccb4a5168750568d2af2b3c6b58c7c5679903024691f058f509e2c4613d7f0c1d122bf367df3191b22ede2280f296f433765a0d9c1ab137b48ef10f26def3cb78da16a267de19f63822a996322ca3faef4a90ccd0034b5d3a5bd00a559a3c9844c9da93f2b0cf4a2f8328c95c76fb79b3c78f822888cb46b83b5ef0048c1dbcef6a5cff89dd228d6e219ebe11e35dcdcfc76ddd57dbd696b885809644a3036b19b774eda9a630cc70793279f4cec249d7e4c8f0de156a1b9f17c76f4d8bd9c09a7c0d5d527cfb97d8aa2886d3943215be1d9f0de196fc43ae754c6cc4c8887d8b4ffc2ceccb9df3200a6efe8701e4e9a1d389bbf7c1cf77102e64cfea6a6805";
        String secret = "9ABE2F738D6C1F50E4950BCD637A3104";
        String data1 = SM4Util.decrypt(str,secret);
        System.out.println(data1);
    }

    @Test
    public void test4(){
        String str = "10";
        String secret = "9ABE2F738D6C1F50E4950BCD637A3104";
        String data1 = SM4Util.encrypt(str,secret);
        System.out.println(data1);
    }
}

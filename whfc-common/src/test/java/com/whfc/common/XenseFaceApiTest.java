package com.whfc.common;

import com.whfc.common.face.xense.XenseFaceApi;
import com.whfc.common.face.xense.entity.DailyResult;
import com.whfc.common.face.xense.entity.WorkerInfo;
import com.whfc.common.face.xense.entity.WorkerProfile;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class XenseFaceApiTest {

    private String appKey = "111740370715397";
    private String appSecret = "306558b6fbfa41a78a7ad599133c3056";
    private XenseFaceApi api = new XenseFaceApi(appKey, appSecret);

    @Test
    public void testDailyCount() {
        Date date = new Date();
        DailyResult data = api.dailyCount(date);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testWorkerProfile() {
        int pageNum = 1;
        int pageSize = 100;
        int pages = 0;
        List<WorkerInfo> workerInfos = null;
        do {
            WorkerProfile workerProfile = api.workerProfile(pageNum, pageSize);

            if (workerInfos == null) {
                int total = workerProfile.getTotalResult();
                pages = total / pageSize + ((total % pageSize) > 0 ? 1 : 0);
                workerInfos = new ArrayList<>(total);
            }
            workerInfos.addAll(workerProfile.getData());
            pageNum++;
        } while (pageNum <= pages);

        System.out.println("人员数量:" + workerInfos.size());

        for (WorkerInfo workerInfo : workerInfos) {
            String nameChi = workerInfo.getNameChi();
            String nameEng = workerInfo.getNameEng();
            System.out.println(String.format("%s,%s,%s", workerInfo.getEmployeeId(), nameChi, nameEng));
        }
    }
}

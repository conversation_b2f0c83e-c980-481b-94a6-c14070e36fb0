package com.whfc.common;

import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/9/27 9:50
 */
public class DateUtilTest {

    @Test
    public void test() {
        String timeStr = "2023/11/14 07:23:55.851";
        Date time = DateUtil.parseDate(timeStr, "yyyy/MM/dd HH:mm:ss.SSS");
        String str = DateUtil.formatDateTime(time);
        System.out.println(str);
        System.out.println(timeStr);
        System.out.println(timeStr.substring(0, 19));
    }

    @Test
    public void test1() {
        Date now = new Date();
        System.out.println(DateUtil.formatDate(now, "dd-MMM-yy",Locale.ENGLISH));
    }

    @Test
    public void test3() {
        // 日期时间字符串
        String dateTimeString = "2024-09-19T15:25:22+08:00";
        String format = "yyyy-MM-dd'T'HH:mm:ssX";
        Date date = DateUtil.parseDate(dateTimeString, format);
        System.out.println(date);
        System.out.println(DateUtil.formatDateTime(date));
        System.out.println(DateUtil.formatDate(date, format));
    }

    @Test
    public void test4() {
        Date start = DateUtil.getDateBegin(new Date());
        Date end = DateUtil.getDateEnd(new Date());
        List<Date> dateList = DateUtil.getHourListBetween(start, end);
        for (Date date : dateList) {
            System.out.println(DateUtil.formatDateTime(date));
        }
    }

    @Test
    public void test5() {
        Date now = new Date();
        Date date = DateUtil.getDate(now, null, null, 59);
        System.out.println(DateUtil.formatDateTime(date));
    }

    @Test
    public void test6() {
        Date now = new Date();
        Date start = DateUtil.getDateBegin(now);
        List<Date> list = DateUtil.getHourListBetween(start, now);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void test7() {
        Instant instant = Instant.now();
        System.out.println(instant);
        System.out.println(instant.getEpochSecond());

        ZonedDateTime utcTime = instant.atZone(ZoneOffset.UTC);

        // 用户的时区
        ZoneId userZoneId = ZoneId.of("Asia/Shanghai");
        ZonedDateTime localTime = utcTime.withZoneSameInstant(userZoneId);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.JAPAN).withZone(userZoneId);
        String formattedTime = localTime.format(formatter);
        System.out.println(formattedTime);


        /************/

        Locale locale = Locale.getDefault();
        System.out.println(locale);

        ZoneId zoneId = ZoneId.systemDefault();
        System.out.println(zoneId);

        ZoneOffset zoneOffset = ZoneOffset.of("+8");
        System.out.println(zoneOffset);
        System.out.println(zoneOffset.getRules());

        LocalDateTime localDateTime = LocalDateTime.now();
        System.out.println(localDateTime);
        System.out.println(localDateTime.atZone(ZoneOffset.of("Z")));


        /////////////////////

        Date now = new Date();
        System.out.println(now);
        ZonedDateTime nowUtc = now.toInstant().atZone(ZoneOffset.UTC);
        System.out.println(nowUtc.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.JAPAN).withZone(ZoneOffset.UTC)));
        ZonedDateTime nowUtc_6 = nowUtc.withZoneSameInstant(ZoneOffset.of("+6"));
        System.out.println(nowUtc_6.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.JAPAN).withZone(ZoneOffset.of("+6"))));
    }
}

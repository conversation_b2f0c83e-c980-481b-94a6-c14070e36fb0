package com.whfc.common;

import com.whfc.common.third.xiaoma.XiaoMaApi;
import com.whfc.common.third.xiaoma.entity.DeviceData;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2022/5/30 17:24
 */
public class XiaoMaApiTest {

    @Test
    public void test() {
        XiaoMaApi api = new XiaoMaApi("http://**************:8888");
        DeviceData data = api.maintainInfo("CEBCA-020116");
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

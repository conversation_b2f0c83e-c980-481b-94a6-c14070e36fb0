package com.whfc.common;

import com.whfc.common.util.ImageUtil;
import net.coobird.thumbnailator.Thumbnails;
import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-22 10:46
 */
public class ImageUtilTest {

    @Test
    public void test1() throws Exception {
        String imgUrl = "https://file.whfciot.com/ms/pro/fvs/snapshot/image/2761.png?1640139722599";
        InputStream imgIs = ImageUtil.getFileStream(imgUrl);
        InputStream cis = ImageUtil.compressPicForScale(imgIs, 20);
        FileOutputStream fos = new FileOutputStream("d:\\test.png");

        byte[] bytes = new byte[1024];
        int b = -1;
        while ((b = cis.read(bytes)) != -1) {
            fos.write(bytes, 0, b);
        }
        fos.flush();
        fos.close();
    }

    @Test
    public void test2() {
        String srcImage = "https://file.whfciot.com/ms/dev/tw/attach/a2073f6ff43f4bb896861e75599bb386/seal_1713334692.png";
        String dstImage = "C:\\Users\\<USER>\\Desktop\\test\\xxx.png";
        ImageUtil.transparent(srcImage, dstImage);
    }

    @Test
    public void test3() throws Exception {
        Thumbnails.of("C:\\Users\\<USER>\\Desktop\\test1.jpg")
                .scale(1.0) // 设置图片尺寸
                .rotate(-90) // 顺时针旋转90度
                .outputFormat("png")
                .toFile("C:\\Users\\<USER>\\Desktop\\test2.jpg");
        System.out.println("Image rotated successfully.");
    }

    @Test
    public void test4() {
        File srcFile = new File("C:\\Users\\<USER>\\Desktop\\test\\img1.jpg");
        File dstFile = new File("C:\\Users\\<USER>\\Desktop\\test\\img1-compress.jpg");
        ImageUtil.compressForHeight(srcFile, dstFile, 300);
    }

    @Test
    public void test5() {
        File srcFile = new File("C:\\Users\\<USER>\\Desktop\\test\\test2.png");
        File dstFile = new File("C:\\Users\\<USER>\\Desktop\\test\\test2-compress1.png");
        ImageUtil.compress(srcFile, dstFile, 650, 840);
    }

}

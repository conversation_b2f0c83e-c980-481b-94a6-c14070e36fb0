package com.whfc.common;

import com.whfc.common.result.PageData;
import com.whfc.common.third.baibutonget.BaibutongetApi;
import com.whfc.common.third.baibutonget.entity.Group;
import com.whfc.common.third.baibutonget.entity.Image;
import com.whfc.common.third.baibutonget.entity.Path;
import com.whfc.common.third.baibutonget.entity.Token;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/8 11:50
 */
public class BaibutongetApiTest {

    private String host = "https://caps.runde.pro";

    //private String user = "NJBBT8038";
    //private String pwd = "Wyb043819.";

    private String user = "武汉风潮物联科技";
    private String pwd = "Whfcwl123.";
    private String adminId = "13830";
    private String token = "74ba0d4ef668b8b1e616c6836fe57807";

    BaibutongetApi api = new BaibutongetApi(host, user, pwd, adminId);

    @Test
    public void testGetToken() {
        String pkey = api.getPkey();
        Token token = api.getToken(pkey);
        System.out.println(JSONUtil.toPrettyString(token));
    }

    @Test
    public void testGetGroupMembers() {
        List<Group> groups = api.getGroupMembers(token);
        System.out.println(JSONUtil.toPrettyString(groups));
    }

    @Test
    public void testGetPath() {
        Date start = DateUtil.parseDateTime("2024-08-26 00:00:00");
        Date end = DateUtil.parseDateTime("2024-08-27 00:00:00");
        List<Path> paths = api.getPath(token, "97418", start, end);
        System.out.println(JSONUtil.toPrettyString(paths));
    }

    @Test
    public void testGetImage() {
        Date date = DateUtil.parseDate("2024-09-08", "yyyy-MM-dd");
        PageData<Image> pageData = api.getImage(token, "97419", date, 1);
        System.out.println(JSONUtil.toPrettyString(pageData));
    }
}

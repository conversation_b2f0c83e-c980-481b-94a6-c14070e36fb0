package com.whfc.common;

import com.whfc.common.file.FileHandler;
import com.whfc.common.file.impl.AliyunOssFileHandler;
import com.whfc.common.file.properties.FileProperties;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.ImageUtil;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-06-30 15:24
 */
public class AliyunOssFileHandlerTest {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static FileHandler fileHandler;

    static {
        FileProperties properties = new FileProperties();
        properties.setType("aliyun");
        properties.setName("ms");
        properties.setKey_id("LTAI9vFcwYOpIVXu");
        properties.setKey_secret("RUjj2jZorR5utY0l16IINQ7HnrE7fw");
        properties.setBucket("whfciot");
        properties.setEndpoint("https://oss-cn-hangzhou.aliyuncs.com");
        properties.setSave_url("https://file.whfciot.com");
        fileHandler = new AliyunOssFileHandler(properties);
    }


    @Test
    public void testUpLoad() throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-oss-object-acl", "private");
        String fileUrl = fileHandler.upload("ms/dev/test.png", new FileInputStream("D:\\test.png"), headers);
        System.out.println(fileUrl);
        String path = fileHandler.getPath(fileUrl);
        System.out.println(fileHandler.checkFile(path));
    }

    @Test
    public void testGetAuthUrl() {
        String path = "ms/dev/test.png";
        String url = fileHandler.getAuthFileUrl(path, true);
        System.out.println(url);
    }

    @Test
    public void testDelete1() {
        fileHandler.delete("https://file.whfciot.com/ms/dev/test.png");
    }

    @Test
    public void testDelete2() {
        fileHandler.delete("ms/dev/test.png");
    }

    @Test
    public void testGetPolicy() {
        Map map = fileHandler.getPolicy("ms/dev/", "test.png");
        System.out.println(JSONUtil.toPrettyString(map));
    }

    @Test
    public void testParseUrl() {
        String url = fileHandler.parseUrl("ms/dev/test.png");
        System.out.print(url);
    }

    @Test
    public void testGetPath() {
        String path = fileHandler.getPath("https://file.whfciot.com/ms/dev/test.png");
        System.out.print(path);
    }

    @Test
    public void deleteFile() throws Exception {

        List<String> list = FileUtils.readLines(new File("C:\\Users\\<USER>\\Desktop\\data.txt"), "utf8");
        int length = list.size();
        int step = 1000;
        int pos = 0;
        System.out.println(length);
        while (pos < length) {
            int end = pos + step >= length ? length : pos + step;
            System.out.println("pos:" + pos + ",end:" + end);
            List<String> subList = list.subList(pos, end);
            System.out.println(subList);
            pos = end + 1;
            try {
                fileHandler.delete(subList);
            } catch (Exception ex) {
            }
        }

    }

    @Test
    public void test() {
        byte[] src = Base64Util.getUrlImageData("https://uniubi-aiot.oss-cn-hangzhou.aliyuncs.com/device/E03C1CB096641608/20230706084656_790_rgb.jpg");

        byte[] dst = ImageUtil.compressPicForScale(src, 50);

        String url = fileHandler.upload("common/idcard/dst.png", new ByteArrayInputStream(dst));

        System.out.println(url);
    }

    @Test
    public void test2() {
        String base64 = getImageBase64("https://uniubi-aiot.oss-cn-hangzhou.aliyuncs.com/device/E03C1CB096641608/20230706084656_790_rgb.jpg");
        System.out.println(base64);
    }

    private String getImageBase64(String url) {
        logger.info("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        logger.info("获取图片base64,{},压缩前:{}", url, imageData.length);
        byte[] compressed = ImageUtil.compressPicForScale(imageData, 45);
        logger.info("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(compressed);
    }
}

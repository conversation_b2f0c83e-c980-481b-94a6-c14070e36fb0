package com.whfc.common;

import com.whfc.common.iot.forthink.entity.*;
import com.whfc.common.iot.forthink.util.ForthinkMsgUtil;
import com.whfc.common.util.JSONUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ForthinkMsgUtilTest {

    private static final int stationId = 0x051582;
    ;

    @Test
    public void testStation() {
        ByteBuf buf = Unpooled.buffer();
        buf.writeIntLE(stationId);
        System.out.println(ByteBufUtil.prettyHexDump(buf));
    }

    @Test
    public void testTime() {

        Date date = new Date();

        ForthinkTimeMsg msg = new ForthinkTimeMsg();
        msg.setStation(stationId);
        msg.setTime(date);
        msg.setTimestamp(date.getTime() / 1000);
        msg.setVersion(0x01);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testHeart() {
        ForthinkHeartMsg msg = new ForthinkHeartMsg();
        msg.setAddr(0x011);
        msg.setType(0x01);
        msg.setCommunityId(0x02);
        msg.setVersion(0x01);
        msg.setSoftVersion("1.2.3.4");
        msg.setSv1(1);
        msg.setSv2(2);
        msg.setSv3(3);
        msg.setSv4(4);
        msg.setSn("0000051582");
        msg.setFirmwareCode(0x01);
        msg.setLogLevel(0x01);
        msg.setCirMode(0x01);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");

        ForthinkHeartMsg msg2 = (ForthinkHeartMsg) msg1;

        System.out.println(Integer.toBinaryString(msg2.getDeviceType()));
    }

    @Test
    public void testDistanceConfig() {
        ForthinkDistanceConfigMsg msg = new ForthinkDistanceConfigMsg();
        msg.setSeq(0x11);
        msg.setStation(51582);
        msg.setCommunityId(0x02);
        msg.setPeriod(1000);
        msg.setDelay(1000);
        msg.setMax(8);
        msg.setVersion(0x01);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testDistanceConfigResp() {
        ForthinkDistanceConfigRespMsg msg = new ForthinkDistanceConfigRespMsg();
        msg.setSeq(0x11);
        msg.setStation(51582);
        msg.setCommunityId(0x02);
        msg.setPeriod(1000);
        msg.setDelay(1000);
        msg.setMax(8);
        msg.setVersion(0x01);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testQuery() {
        ForthinkQueryMsg msg = new ForthinkQueryMsg();
        msg.setOrder(0x01);
        msg.setVersion(0x02);
        msg.setAddr(0x11);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testTofData() {

        int[] bytes = new int[]{0xA3, 0x52, 0x33, 0x01, 0x1F, 0x3A, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x82, 0x15, 0x05, 0x00, 0x01, 0x08, 0x80, 0x1C, 0x32, 0x0A, 0x00, 0x7C, 0x7C, 0x01, 0x07, 0x82, 0x15, 0x05, 0x00, 0x6D, 0x02, 0xB5, 0xD5};
        ByteBuf buf = Unpooled.buffer();
        for (int b : bytes) {
            buf.writeByte(b);
        }
        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf1);

        System.out.println(JSONUtil.toPrettyString(msg1));
    }

    @Test
    public void testTofDataAck() {
        int[] bytes = new int[]{0xA3, 0x52, 0x33, 0x01, 0xFE, 0x3A, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x44, 0xCA, 0x01, 0x00, 0x01, 0x04, 0x1F, 0x3A, 0x0, 0x00, 0xD8};
        ByteBuf buf = Unpooled.buffer();
        for (int b : bytes) {
            buf.writeByte(b);
        }
        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf1);

        System.out.println(JSONUtil.toPrettyString(msg1));
    }

    @Test
    public void testWarnDataQuery() {
        ForthinkWarnDataQueryMsg msg = new ForthinkWarnDataQueryMsg();
        msg.setStation(51582);
        msg.setSeq(1);
        msg.setVersion(1);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testWarnDataQueryResp() {

        ForthinkWarnData w1 = new ForthinkWarnData();
        w1.setWarnDataLen(14);
        w1.setWarnId(100);
        w1.setWarnType(0);
        w1.setStatus(0);
        w1.setStart((int) (System.currentTimeMillis() / 1000));
        w1.setDuration(30);
        w1.setMinDistance(10);

        List<ForthinkWarnData> warnDataList = new ArrayList<>();
        warnDataList.add(w1);

        ForthinkWarnDataQueryRespMsg msg = new ForthinkWarnDataQueryRespMsg();
        msg.setStation(51582);
        msg.setSeq(1);
        msg.setVersion(1);
        msg.setFixLen(1);
        msg.setWarnNum(1);
        msg.setWarnDataList(warnDataList);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testWarnRecordQuery() {
        ForthinkWarnRecordQueryMsg msg = new ForthinkWarnRecordQueryMsg();
        msg.setVersion(1);
        msg.setId(0XFFFFFFFF);
        msg.setSeq(1);

        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testWarnRecordQueryResp() {


        ForthinkWarnRecord warn1 = new ForthinkWarnRecord();
        warn1.setTag(222);
        warn1.setStart((int) (System.currentTimeMillis() / 1000));
        warn1.setDuration(30);
        warn1.setMinDistance(15);

        ForthinkWarnRecord warn2 = new ForthinkWarnRecord();
        warn2.setTag(224);
        warn2.setStart((int) (System.currentTimeMillis() / 1000));
        warn2.setDuration(34);
        warn2.setMinDistance(124);

        List<ForthinkWarnRecord> warnList = new ArrayList<>();
        warnList.add(warn1);
        warnList.add(warn2);

        ForthinkWarnRecordQueryRespMsg msg = new ForthinkWarnRecordQueryRespMsg();
        msg.setVersion(1);
        msg.setStation(51582);
        msg.setSeq(12);
        msg.setFlag(0);
        msg.setNum(warnList.size());
        msg.setWarnList(warnList);


        System.out.println(JSONUtil.toPrettyString(msg));

        ByteBuf buf = ForthinkMsgUtil.encode(msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        ForthinkMsg msg1 = ForthinkMsgUtil.decode(buf);

        System.out.println(JSONUtil.toPrettyString(msg1));

        ByteBuf buf1 = ForthinkMsgUtil.encode(msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println("---------------------------");
    }

    @Test
    public void testDecodeMsgList() {
        int[] bytes = new int[]{0xa3, 0x52, 0x33, 0x01, 0x12, 0x2b, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x01, 0x82, 0x15, 0x05, 0x00, 0x76, 0x00, 0x01, 0x06, 0x1c, 0x32, 0x0a, 0x00, 0x43, 0xc3, 0x70, 0x68, 0x08, 0x00, 0x31, 0x01, 0x1c, 0x32, 0x0a, 0x00, 0x43, 0xc3, 0x70, 0x68, 0x08, 0x00, 0x31, 0x01, 0xe9, 0x44, 0x0a, 0x00, 0x1b, 0xc3, 0x70, 0x68, 0x09, 0x04, 0xbe, 0x00, 0xe9, 0x44, 0x0a, 0x00, 0x1b, 0xc3, 0x70, 0x68, 0x09, 0x04, 0xbe, 0x00, 0xe9, 0x44, 0x0a, 0x00, 0x25, 0xc7, 0x70, 0x68, 0xf7, 0x02, 0xbe, 0x00, 0xe9, 0x44, 0x0a, 0x00, 0x25, 0xc7, 0x70, 0x68, 0xf7, 0x02, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8b};
        ByteBuf buf = Unpooled.buffer(bytes.length);
        for (int aByte : bytes) {
            buf.writeByte(aByte);
        }
        List<ForthinkMsg> msgList = ForthinkMsgUtil.decodeMsgList(buf);
        System.out.println(JSONUtil.toPrettyString(msgList));
    }
}

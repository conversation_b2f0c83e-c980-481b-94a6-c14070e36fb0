package com.whfc.common;

import com.whfc.common.util.FileUtil;
import com.whfc.common.util.PinyinUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/26 14:52
 */
public class FileRenameTest {

    @Test
    public void test() throws Exception{
        String src = "C:\\Users\\<USER>\\Desktop\\jpg";
        File dir = new File(src);
        File[] files = dir.listFiles();
        System.out.println(files.length);
        for (File file : files){
            String filename = file.getName();
            int index = filename.lastIndexOf(".");
            String name = filename.substring(0,index);
            String pinyin = PinyinUtil.toPinyin(name).toLowerCase();
            String ext  = filename.substring(index);
            System.out.println(name + " " + pinyin + " " + ext);
            String dstName = pinyin + ext;
            System.out.println(dstName);
            File dstFile = new File("C:\\Users\\<USER>\\Desktop\\dst\\"+dstName);
            FileUtils.copyFile(file,dstFile);
        }
    }
}

package com.whfc.common;

import com.whfc.common.tcp.NettyClient;
import org.junit.Test;

import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/6 11:17
 */
public class NettyClientTest {

    String host = "127.0.0.1";

    Integer port = 9045;

    String data = "{\"deviceType\":\"2001000060\",\"SN\":\"DSC1010000000YJB001\",\"dataType\":\"200300025\",\"bidCode\":\"YJBHGC-TJSGCK\",\"workAreaCode\":\"YJBHGC-TJSGCK-01\",\"deviceName\":\"有毒有害气体监测设备名称\",\"values\":[{\"reportTs\":1572702827618,\"profile\":{\"appType\":\"environment\",\"modelId\":\"2055\",\"poiCode\":\"w0907001\",\"name\":\"智科场地环境监测\",\"model\":\"测试型号\",\"manufacture\":\"智科\",\"owner\":\"智科\",\"makeDate\":\"2020-05-22\",\"validYear\":\"2050-05-22\",\"state\":\"01\",\"installPosition\":\"珠三角南沙黄阁水厂\",\"x\":112.002224,\"y\":112.002224,\"z\":1.2},\"properties\":{\"monitorTime\":\"2020-05-22 13:41:05\",\"co\":\"2.5\",\"co2\":\"10.0\",\"so2\":\"10.0\",\"so\":\"10.0\",\"ch4\":\"2.5\",\"location\":\"1\",\"x\":112.002224,\"y\":112.002224,\"z\":1.2},\"events\":{},\"services\":{}}]}";

    @Test
    public void test1() throws Exception {

        NettyClient client = new NettyClient(host, port, 10 * 1000);

        client.connect().thenAccept(channelFuture -> {
            for (int i = 0; i < 1; i++) {
                client.sendMessage(data);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        });
        TimeUnit.MINUTES.sleep(1);
        client.close();
    }
}

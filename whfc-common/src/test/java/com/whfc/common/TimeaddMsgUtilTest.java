package com.whfc.common;

import com.whfc.common.iot.mach.timeadd.TimeaddMsg;
import com.whfc.common.iot.mach.timeadd.TimeaddMsgUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

public class TimeaddMsgUtilTest {

    @Test
    public void test(){
        String data = "wTIyMDkuMDM1NDZOMTEzMzIuNTUzODVFhwE=";
        TimeaddMsg msg = TimeaddMsgUtil.decode(data);
        System.out.println(JSONUtil.toPrettyString(msg));
    }

    @Test
    public void test2(){
        String data = "AACAPwAAgD8AAIA/";
        TimeaddMsg msg = TimeaddMsgUtil.decode2(data);
        System.out.println(JSONUtil.toPrettyString(msg));
    }
}

package com.whfc.common;

import com.whfc.common.geometry.GeometryUtil;
import com.whfc.common.geometry.Point;
import com.whfc.common.geometry.Polygon;
import com.whfc.common.third.map.MapApi;
import com.whfc.common.third.map.impl.AMapApi;
import com.whfc.common.util.Gps;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/10/12 15:29
 */
public class GeometryUtilTest {

    @Test
    public void toPolygonWkt() {
        String gpsStr = "115.02037048,43.51510239,0 115.02036285,43.51504898,0 115.01311493,43.51538086,0 115.01299286,43.51539230,0 115.01287079,43.51542663,0 115.01276398,43.51547241,0 115.01267242,43.51553345,0 115.01260376,43.51560211,0 115.01254272,43.51568603,0 115.01252033,43.51574763,0 115.01259464,43.51574698,0 115.01261139,43.51570511,0 115.01266480,43.51562881,0 115.01272583,43.51556778,0 115.01280975,43.51551437,0 115.01290131,43.51547623,0 115.01301575,43.51544571,0 115.01312256,43.51543426,0 115.02037048,43.51510239,0";
//        String gpsStr = "115.03577352,43.51792323,0 115.03572219,43.51790757,0 115.03017102,43.52754287,0 115.03017509,43.52810248,0 115.03075989,43.53024041,0 115.03136665,43.53487310,0 115.03348154,43.54187492,0 115.03353592,43.54186622,0 115.03142174,43.53486681,0 115.03081504,43.53023452,0 115.03023072,43.52809839,0 115.03022674,43.52755092,0 115.03577352,43.51792323,0";
//        String gpsStr = "115.01416183,43.51573308,0 115.01400901,43.51539370,0 115.01395163,43.51539633,0 115.01410350,43.51573360,0 115.01416183,43.51573308,0";
//        String gpsStr = "114.98735393,43.51930195,0 114.98798974,43.51920699,0 114.98900532,43.51930674,0 114.99027839,43.51943177,0 114.99029573,43.51943347,0 114.99327100,43.51972562,0 115.00132682,43.52051624,0 115.00134022,43.52051755,0 115.00411488,43.52078973,0 115.00304279,43.52142044,0 115.00307778,43.52145194,0 115.00419076,43.52079717,0 115.00562428,43.52093776,0 115.00520307,43.52125020,0 115.00505523,43.52166883,0 115.00487980,43.52234883,0 115.00481100,43.52267808,0 115.00494294,43.52304073,0 115.00484451,43.52369975,0 115.00420155,43.52520838,0 115.00277231,43.52650201,0 115.00228741,43.52680069,0 115.00227760,43.52681507,0 115.00200882,43.53048623,0 115.00207509,43.53609545,0 115.00204722,43.53645693,0 115.00333482,43.54286642,0 115.00338991,43.54286057,0 115.00210311,43.53645512,0 115.00213077,43.53609641,0 115.00207336,43.53123704,0 115.01705066,43.53733069,0 115.01747746,43.53809747,0 115.01713737,43.53893431,0 115.01570632,43.54443912,0 115.01576102,43.54444664,0 115.01719155,43.53894384,0 115.01753636,43.53809538,0 115.01709491,43.53730229,0 115.00207281,43.53119041,0 115.00206450,43.53048713,0 115.00233254,43.52682595,0 115.00281234,43.52653041,0 115.00425157,43.52522774,0 115.00489926,43.52370802,0 115.00499939,43.52303764,0 115.00486777,43.52267585,0 115.00493468,43.52235561,0 115.00510959,43.52167762,0 115.00525325,43.52127081,0 115.00569306,43.52094457,0 115.00902553,43.52128754,0 115.01061019,43.52135531,0 115.01190682,43.52131704,0 115.01322007,43.52141530,0 115.01432160,43.52164318,0 115.01582341,43.52180633,0 115.01628901,43.52168843,0 115.01639489,43.52151337,0 115.01640251,43.52117739,0 115.01600953,43.51983610,0 115.01429293,43.51602423,0 115.01429519,43.51615930,0 115.01595572,43.51984666,0 115.01634676,43.52118132,0 115.01633943,43.52150453,0 115.01624783,43.52165598,0 115.01581807,43.52176480,0 115.01433337,43.52160351,0 115.01323061,43.52137538,0 115.01190855,43.52127646,0 115.01061069,43.52131476,0 115.00903107,43.52124721,0 115.00569007,43.52090336,0 115.00568711,43.52090306,0 115.00568675,43.52090302,0 115.00567293,43.52090167,0 115.00567139,43.52090152,0 115.00134180,43.52047684,0 115.00132840,43.52047553,0 114.99092027,43.51945394,0 114.98847610,43.51921389,0 114.98808009,43.51917499,0 114.98799262,43.51916640,0 114.96364250,43.51668891,0 114.95827119,43.51643377,0 114.94938885,43.51347470,0 114.94887207,43.51327222,0 114.94805553,43.50898464,0 114.94800041,43.50899020,0 114.94881136,43.51324844,0 114.94271919,43.51086120,0 114.93543563,43.50840478,0 114.92205591,43.50611382,0 114.91918753,43.50618546,0 114.91429642,43.50643784,0 114.91193705,43.50532190,0 114.91190673,43.50535586,0 114.91420512,43.50644295,0 114.91002948,43.50667892,0 114.89214059,43.50490167,0 114.89213306,43.50494180,0 114.91002786,43.50671964,0 114.91429115,43.50647871,0 114.91919045,43.50622591,0 114.92205039,43.50615448,0 114.92463746,43.50659759,0 114.91760426,43.51376899,0 114.91764955,43.51379252,0 114.92469599,43.50660761,0 114.93541737,43.50844326,0 114.94121069,43.51039714,0 114.93789355,43.51274068,0 114.93201064,43.51873812,0 114.92539450,43.52339606,0 114.92506371,43.52366435,0 114.91580796,43.53076372,0 114.91584835,43.53079160,0 114.92510463,43.52369181,0 114.92543461,43.52342417,0 114.93205299,43.51876466,0 114.93793592,43.51276719,0 114.94126477,43.51041538,0 114.94269428,43.51089743,0 114.94936405,43.51351098,0 114.95822194,43.51646190,0 114.94907246,43.52572755,0 114.93968717,43.53067944,0 114.93971985,43.53071223,0 114.94911241,43.52575649,0 114.95827786,43.51647467,0 114.96363683,43.51672922,0 114.98782440,43.51919081,0 114.98733413,43.51926356,0 114.98359782,43.52080641,0 114.98120108,43.52182561,0 114.98085277,43.52197373,0 114.98083075,43.52198194,0 114.98084086,43.52199851,0 114.98326504,43.52656415,0 114.98496151,43.53306918,0 114.98931446,43.54572348,0 114.98937288,43.55293800,0 114.98826862,43.56448609,0 114.98913494,43.56519442,0 115.00507878,43.56848073,0 115.00509397,43.56844176,0 114.98916577,43.56515867,0 114.98832581,43.56447191,0 114.98942857,43.55293929,0 114.98937010,43.54571837,0 114.98501591,43.53306049,0 114.98331878,43.52655299,0 114.98090131,43.52199999,0 114.98120184,43.52187220,0 114.98370540,43.52080756,0 114.98735393,43.51930195,0";
//        String gpsStr = "114.70828833,43.49488764,0 114.70777768,43.49372707,0 114.70921428,43.49326431,0 114.71215073,43.49203607,0 114.73593051,43.48325753,0 114.77614143,43.47767229,0 114.78094834,43.47277888,0 114.78129221,43.47224388,0 114.78154382,43.46935976,0 114.78200063,43.46831342,0 114.78217779,43.46806025,0 114.78370880,43.46735211,0 114.78540845,43.46667244,0 114.78726447,43.46593564,0 114.78926821,43.46505417,0 114.79181712,43.46394683,0 114.79372054,43.46285639,0 114.79616690,43.46154117,0 114.79876148,43.46008094,0 114.80150365,43.45847604,0 114.80439321,43.45672655,0 114.80439985,43.45672192,0 114.80444674,43.45673066,0 114.80593813,43.45723255,0 114.84129717,43.47324943,0 114.84499335,43.47398698,0 114.85819595,43.47621395,0 114.85853669,43.47627015,0 114.85179350,43.47937250,0 114.85182320,43.47940674,0 114.85861334,43.47628280,0 114.88315113,43.48032697,0 114.88316246,43.48032712,0 114.88878525,43.47954816,0 114.89276413,43.47944883,0 114.89486372,43.47953500,0 114.89592900,43.47972851,0 114.89644297,43.48000059,0 114.89843983,43.48050731,0 114.89916063,43.48082126,0 114.89975180,43.48112556,0 114.90028219,43.48130627,0 114.90060417,43.48136372,0 114.89658886,43.48481246,0 114.89663128,43.48483865,0 114.90066455,43.48137449,0 114.90091579,43.48141931,0 114.90303094,43.48204778,0 114.90362943,43.48217703,0 114.90568668,43.48279608,0 114.90661853,43.48311669,0 114.90739410,43.48323750,0 114.90779868,43.48323738,0 114.90855228,43.48316672,0 114.90878695,43.48311559,0 114.90910100,43.48299387,0 114.90955104,43.48275353,0 114.90988793,43.48261458,0 114.91210084,43.48195225,0 114.91263243,43.48151032,0 114.91290712,43.48137269,0 114.91544152,43.48057237,0 114.91656019,43.48028170,0 114.91770749,43.47993138,0 114.91856251,43.47978150,0 114.91895960,43.47975139,0 114.92181932,43.47962914,0 114.92233592,43.47948673,0 114.92259060,43.47935383,0 114.92324380,43.47886018,0 114.92357400,43.47844904,0 114.92388130,43.47817070,0 114.92533885,43.47708940,0 114.92623714,43.47631865,0 114.92716508,43.47567801,0 114.92781466,43.47517671,0 114.92877291,43.47440563,0 114.93004129,43.47344348,0 114.93044081,43.47307276,0 114.93120900,43.47246204,0 114.93224631,43.47157116,0 114.93333436,43.47081946,0 114.93383354,43.47036858,0 114.93429257,43.47003788,0 114.93438502,43.46995544,0 114.93460450,43.46965514,0 114.93476081,43.46951785,0 114.93543632,43.46909870,0 114.93576694,43.46893747,0 114.93590951,43.46882502,0 114.93632038,43.46845281,0 114.93693901,43.46771179,0 114.93720065,43.46747939,0 114.93739105,43.46702714,0 114.93761865,43.46671926,0 114.93791687,43.46639005,0 114.93821350,43.46610215,0 114.93879042,43.46577216,0 114.93947161,43.46528901,0 114.93956445,43.46519577,0 114.93974351,43.46490625,0 114.93985873,43.46480982,0 114.94003347,43.46471217,0 114.94052318,43.46455099,0 114.94127132,43.46406965,0 114.94218595,43.46376900,0 114.94263054,43.46359973,0 114.94327722,43.46355866,0 114.94374713,43.46342677,0 114.94515196,43.46318424,0 114.94607196,43.46300195,0 114.94641299,43.46289060,0 114.94639025,43.46285364,0 114.94605318,43.46296370,0 114.94513824,43.46314498,0 114.94373065,43.46338799,0 114.94326456,43.46351881,0 114.94261464,43.46356009,0 114.94216161,43.46373256,0 114.94124080,43.46403525,0 114.94049265,43.46451659,0 114.94000472,43.46467719,0 114.93982045,43.46478016,0 114.93969618,43.46488417,0 114.93951610,43.46517535,0 114.93942940,43.46526241,0 114.93875375,43.46574164,0 114.93817341,43.46607359,0 114.93787138,43.46636672,0 114.93757086,43.46669846,0 114.93733950,43.46701146,0 114.93715070,43.46745989,0 114.93689353,43.46768831,0 114.93627476,43.46842950,0 114.93586736,43.46879856,0 114.93573054,43.46890648,0 114.93540271,43.46906635,0 114.93472108,43.46948930,0 114.93455808,43.46963247,0 114.93433850,43.46993290,0 114.93425137,43.47001060,0 114.93379221,43.47034139,0 114.93329340,43.47079194,0 114.93220585,43.47154329,0 114.93116728,43.47243525,0 114.93039837,43.47304654,0 114.92999925,43.47341689,0 114.92873223,43.47437802,0 114.92777381,43.47514922,0 114.92712569,43.47564940,0 114.92619669,43.47629076,0 114.92529776,43.47706207,0 114.92383968,43.47814376,0 114.92352807,43.47842600,0 114.92319914,43.47883556,0 114.92255407,43.47932306,0 114.92230943,43.47945073,0 114.92180758,43.47958907,0 114.91895508,43.47971101,0 114.91855308,43.47974149,0 114.91769011,43.47989277,0 114.91654007,43.48024392,0 114.91542109,43.48053468,0 114.91289421,43.48133263,0 114.90654243,43.47909611,0 114.90480307,43.47862759,0 114.90363563,43.47112222,0 114.90490821,43.46888118,0 114.91158992,43.46343111,0 114.91277798,43.46286051,0 114.91274737,43.46282670,0 114.91155326,43.46340020,0 114.90486044,43.46885933,0 114.90357876,43.47111639,0 114.90475166,43.47865692,0 114.90652062,43.47913342,0 114.91283840,43.48135796,0 114.91259518,43.48147982,0 114.91206761,43.48191841,0 114.90986355,43.48257808,0 114.90952078,43.48271947,0 114.90907133,43.48295949,0 114.90876570,43.48307795,0 114.90854068,43.48312698,0 114.90779510,43.48319688,0 114.90739998,43.48319700,0 114.90663647,43.48307807,0 114.90570920,43.48275904,0 114.90364801,43.48213880,0 114.90304940,43.48200952,0 114.90093300,43.48138068,0 114.90030078,43.48126789,0 114.89977986,43.48109040,0 114.89919100,43.48078730,0 114.89846350,43.48047043,0 114.89646903,43.47996431,0 114.89595296,43.47969112,0 114.89487209,43.47949478,0 114.89276475,43.47940829,0 114.88877906,43.47950779,0 114.88315775,43.48028654,0 114.86990346,43.47810277,0 114.86668220,43.47308650,0 114.86663182,43.47310365,0 114.86983479,43.47809145,0 114.85820837,43.47617448,0 114.85416654,43.47549289,0 114.85267718,43.47330792,0 114.85245466,43.47146791,0 114.85369190,43.46994867,0 114.85571131,43.46891913,0 114.86782175,43.46475977,0 114.86803038,43.46473656,0 114.86855789,43.46468690,0 114.88769316,43.46538821,0 114.88944336,43.46539278,0 114.89037844,43.46483490,0 114.89948876,43.45727268,0 114.89944697,43.45724597,0 114.89033967,43.46480568,0 114.88942359,43.46535223,0 114.88769466,43.46534771,0 114.86855571,43.46464626,0 114.86802262,43.46469646,0 114.86780535,43.46472062,0 114.85568330,43.46888397,0 114.85365019,43.46992050,0 114.85239769,43.47145847,0 114.85262270,43.47331909,0 114.85409638,43.47548106,0 114.84500698,43.47394770,0 114.84131967,43.47321193,0 114.80596457,43.45719684,0 114.80446545,43.45669235,0 114.80444356,43.45668826,0 114.80460012,43.45660082,0 114.80471397,43.45653723,0 114.80528737,43.45621699,0 114.80535463,43.45617942,0 114.80682923,43.45535584,0 114.81062172,43.45323747,0 114.81290365,43.45196272,0 114.81596960,43.45045510,0 114.82356065,43.44672168,0 114.83152743,43.44176495,0 114.83495951,43.43960242,0 114.83551493,43.43929287,0 114.83614144,43.43910228,0 114.83713129,43.43891221,0 114.84222784,43.43793347,0 114.84237924,43.43790439,0 114.84237212,43.43789805,0 114.85677213,43.43512449,0 114.85676503,43.43510492,0 114.85675792,43.43508534,0 114.84227432,43.43787499,0 114.84228141,43.43788129,0 114.84221367,43.43789430,0 114.83612356,43.43906383,0 114.83548688,43.43925751,0 114.83492442,43.43957099,0 114.83149120,43.44173423,0 114.82352695,43.44668939,0 114.81287115,43.45192984,0 114.80455160,43.45657688,0 114.80437508,43.45667547,0 114.80402288,43.45660968,0 114.75175685,43.44683456,0 114.75167340,43.44517831,0 114.75287434,43.44366086,0 114.75673451,43.44029008,0 114.75669183,43.44026414,0 114.75282859,43.44363759,0 114.75161726,43.44516816,0 114.75170069,43.44682405,0 114.74749987,43.44603720,0 114.74702051,43.44596999,0 114.74655423,43.44594619,0 114.74176743,43.44610903,0 114.74174535,43.44610205,0 114.74174066,43.44610994,0 114.74043947,43.44615416,0 114.73764019,43.44032492,0 114.73758772,43.44033830,0 114.74038152,43.44615613,0 114.72903384,43.44654122,0 114.71292358,43.44892418,0 114.70861225,43.44778785,0 114.69724485,43.44913482,0 114.69725377,43.44917480,0 114.70860692,43.44782952,0 114.71285239,43.44894849,0 114.70937739,43.45026460,0 114.70863354,43.45052340,0 114.69620578,43.45709293,0 114.69546874,43.45778666,0 114.68709328,43.46981898,0 114.67761824,43.47672404,0 114.67891338,43.47947774,0 114.67896600,43.47946461,0 114.67768209,43.47673481,0 114.68713888,43.46984304,0 114.69551594,43.45780840,0 114.69624487,43.45712230,0 114.70866208,43.45055835,0 114.70940219,43.45030086,0 114.71293081,43.44896444,0 114.72753870,43.44680381,0 114.72273156,43.45446951,0 114.72278213,43.45448634,0 114.72760606,43.44679384,0 114.72904070,43.44658153,0 114.74171604,43.44615132,0 114.74134066,43.44678221,0 114.74134056,43.44678238,0 114.74092525,43.44749785,0 114.74092517,43.44749801,0 114.74049924,43.44824879,0 114.74049916,43.44824893,0 114.74006261,43.44903503,0 114.74006253,43.44903516,0 114.73961536,43.44985657,0 114.73961530,43.44985669,0 114.73915750,43.45071341,0 114.73915744,43.45071352,0 114.73868902,43.45160556,0 114.73868896,43.45160566,0 114.73820991,43.45253301,0 114.73826194,43.45254727,0 114.73874097,43.45161998,0 114.73920933,43.45072805,0 114.73966707,43.44987144,0 114.74011417,43.44905016,0 114.74055065,43.44826419,0 114.74097650,43.44751355,0 114.74139171,43.44679824,0 114.74177787,43.44614922,0 114.74655359,43.44598676,0 114.74701329,43.44601022,0 114.74748769,43.44607674,0 114.80400907,43.45664892,0 114.80433417,43.45670964,0 114.80146846,43.45844468,0 114.79872707,43.46004913,0 114.79613339,43.46150884,0 114.79368683,43.46282418,0 114.79178556,43.46391339,0 114.78923959,43.46501945,0 114.78723677,43.46590050,0 114.78538177,43.46663691,0 114.78368048,43.46731723,0 114.78213514,43.46803200,0 114.78194902,43.46829799,0 114.78148875,43.46935223,0 114.78123738,43.47223366,0 114.78090014,43.47275834,0 114.77610914,43.47763555,0 114.73591237,43.48321881,0 114.71867091,43.48958441,0 114.71591437,43.48699166,0 114.71201953,43.48650173,0 114.71201006,43.48654164,0 114.71588361,43.48702889,0 114.71862039,43.48960306,0 114.71212428,43.49200042,0 114.70918910,43.49322813,0 114.70566824,43.49436223,0 114.69665655,43.48994973,0 114.69662551,43.48998334,0 114.70560810,43.49438160,0 114.69595179,43.49749121,0 114.69597430,43.49752825,0 114.70772652,43.49374355,0 114.70823720,43.49490419,0 114.71349193,43.50148198,0 114.71354003,43.50146162,0 114.70828833,43.49488764,0 ";
        String wkt = GeometryUtil.toPolygonWkt(gpsStr);
        System.out.println(wkt);

        Polygon polygon = GeometryUtil.decodePolygon(wkt);
        System.out.println(JSONUtil.toPrettyString(polygon));
    }

    @Test
    public void test() {
        String wkt = "multipolygon(((115.02037048 43.51510239,115.02036285 43.51504898,115.01311493 43.51538086,115.01299286 43.5153923,115.01287079 43.51542663,115.01276398 43.51547241,115.01267242 43.51553345,115.01260376 43.51560211,115.01254272 43.51568603,115.01252033 43.51574763,115.01259464 43.51574698,115.01261139 43.51570511,115.0126648 43.51562881,115.01272583 43.51556778,115.01280975 43.51551437,115.01290131 43.51547623,115.01301575 43.51544571,115.01312256 43.51543426,115.02037048 43.51510239)),((115.03577352 43.51792323,115.03572219 43.51790757,115.03017102 43.52754287,115.03017509 43.52810248,115.03075989 43.53024041,115.03136665 43.5348731,115.03348154 43.54187492,115.03353592 43.54186622,115.03142174 43.53486681,115.03081504 43.53023452,115.03023072 43.52809839,115.03022674 43.52755092,115.03577352 43.51792323)),((115.01416183 43.51573308,115.01400901 43.5153937,115.01395163 43.51539633,115.0141035 43.5157336,115.01416183 43.51573308)),((114.98735393 43.51930195,114.98798974 43.51920699,114.98900532 43.51930674,114.99027839 43.51943177,114.99029573 43.51943347,114.993271 43.51972562,115.00132682 43.52051624,115.00134022 43.52051755,115.00411488 43.52078973,115.00304279 43.52142044,115.00307778 43.52145194,115.00419076 43.52079717,115.00562428 43.52093776,115.00520307 43.5212502,115.00505523 43.52166883,115.0048798 43.52234883,115.004811 43.52267808,115.00494294 43.52304073,115.00484451 43.52369975,115.00420155 43.52520838,115.00277231 43.52650201,115.00228741 43.52680069,115.0022776 43.52681507,115.00200882 43.53048623,115.00207509 43.53609545,115.00204722 43.53645693,115.00333482 43.54286642,115.00338991 43.54286057,115.00210311 43.53645512,115.00213077 43.53609641,115.00207336 43.53123704,115.01705066 43.53733069,115.01747746 43.53809747,115.01713737 43.53893431,115.01570632 43.54443912,115.01576102 43.54444664,115.01719155 43.53894384,115.01753636 43.53809538,115.01709491 43.53730229,115.00207281 43.53119041,115.0020645 43.53048713,115.00233254 43.52682595,115.00281234 43.52653041,115.00425157 43.52522774,115.00489926 43.52370802,115.00499939 43.52303764,115.00486777 43.52267585,115.00493468 43.52235561,115.00510959 43.52167762,115.00525325 43.52127081,115.00569306 43.52094457,115.00902553 43.52128754,115.01061019 43.52135531,115.01190682 43.52131704,115.01322007 43.5214153,115.0143216 43.52164318,115.01582341 43.52180633,115.01628901 43.52168843,115.01639489 43.52151337,115.01640251 43.52117739,115.01600953 43.5198361,115.01429293 43.51602423,115.01429519 43.5161593,115.01595572 43.51984666,115.01634676 43.52118132,115.01633943 43.52150453,115.01624783 43.52165598,115.01581807 43.5217648,115.01433337 43.52160351,115.01323061 43.52137538,115.01190855 43.52127646,115.01061069 43.52131476,115.00903107 43.52124721,115.00569007 43.52090336,115.00568711 43.52090306,115.00568675 43.52090302,115.00567293 43.52090167,115.00567139 43.52090152,115.0013418 43.52047684,115.0013284 43.52047553,114.99092027 43.51945394,114.9884761 43.51921389,114.98808009 43.51917499,114.98799262 43.5191664,114.9636425 43.51668891,114.95827119 43.51643377,114.94938885 43.5134747,114.94887207 43.51327222,114.94805553 43.50898464,114.94800041 43.5089902,114.94881136 43.51324844,114.94271919 43.5108612,114.93543563 43.50840478,114.92205591 43.50611382,114.91918753 43.50618546,114.91429642 43.50643784,114.91193705 43.5053219,114.91190673 43.50535586,114.91420512 43.50644295,114.91002948 43.50667892,114.89214059 43.50490167,114.89213306 43.5049418,114.91002786 43.50671964,114.91429115 43.50647871,114.91919045 43.50622591,114.92205039 43.50615448,114.92463746 43.50659759,114.91760426 43.51376899,114.91764955 43.51379252,114.92469599 43.50660761,114.93541737 43.50844326,114.94121069 43.51039714,114.93789355 43.51274068,114.93201064 43.51873812,114.9253945 43.52339606,114.92506371 43.52366435,114.91580796 43.53076372,114.91584835 43.5307916,114.92510463 43.52369181,114.92543461 43.52342417,114.93205299 43.51876466,114.93793592 43.51276719,114.94126477 43.51041538,114.94269428 43.51089743,114.94936405 43.51351098,114.95822194 43.5164619,114.94907246 43.52572755,114.93968717 43.53067944,114.93971985 43.53071223,114.94911241 43.52575649,114.95827786 43.51647467,114.96363683 43.51672922,114.9878244 43.51919081,114.98733413 43.51926356,114.98359782 43.52080641,114.98120108 43.52182561,114.98085277 43.52197373,114.98083075 43.52198194,114.98084086 43.52199851,114.98326504 43.52656415,114.98496151 43.53306918,114.98931446 43.54572348,114.98937288 43.552938,114.98826862 43.56448609,114.98913494 43.56519442,115.00507878 43.56848073,115.00509397 43.56844176,114.98916577 43.56515867,114.98832581 43.56447191,114.98942857 43.55293929,114.9893701 43.54571837,114.98501591 43.53306049,114.98331878 43.52655299,114.98090131 43.52199999,114.98120184 43.5218722,114.9837054 43.52080756,114.98735393 43.51930195)),((114.70828833 43.49488764,114.70777768 43.49372707,114.70921428 43.49326431,114.71215073 43.49203607,114.73593051 43.48325753,114.77614143 43.47767229,114.78094834 43.47277888,114.78129221 43.47224388,114.78154382 43.46935976,114.78200063 43.46831342,114.78217779 43.46806025,114.7837088 43.46735211,114.78540845 43.46667244,114.78726447 43.46593564,114.78926821 43.46505417,114.79181712 43.46394683,114.79372054 43.46285639,114.7961669 43.46154117,114.79876148 43.46008094,114.80150365 43.45847604,114.80439321 43.45672655,114.80439985 43.45672192,114.80444674 43.45673066,114.80593813 43.45723255,114.84129717 43.47324943,114.84499335 43.47398698,114.85819595 43.47621395,114.85853669 43.47627015,114.8517935 43.4793725,114.8518232 43.47940674,114.85861334 43.4762828,114.88315113 43.48032697,114.88316246 43.48032712,114.88878525 43.47954816,114.89276413 43.47944883,114.89486372 43.479535,114.895929 43.47972851,114.89644297 43.48000059,114.89843983 43.48050731,114.89916063 43.48082126,114.8997518 43.48112556,114.90028219 43.48130627,114.90060417 43.48136372,114.89658886 43.48481246,114.89663128 43.48483865,114.90066455 43.48137449,114.90091579 43.48141931,114.90303094 43.48204778,114.90362943 43.48217703,114.90568668 43.48279608,114.90661853 43.48311669,114.9073941 43.4832375,114.90779868 43.48323738,114.90855228 43.48316672,114.90878695 43.48311559,114.909101 43.48299387,114.90955104 43.48275353,114.90988793 43.48261458,114.91210084 43.48195225,114.91263243 43.48151032,114.91290712 43.48137269,114.91544152 43.48057237,114.91656019 43.4802817,114.91770749 43.47993138,114.91856251 43.4797815,114.9189596 43.47975139,114.92181932 43.47962914,114.92233592 43.47948673,114.9225906 43.47935383,114.9232438 43.47886018,114.923574 43.47844904,114.9238813 43.4781707,114.92533885 43.4770894,114.92623714 43.47631865,114.92716508 43.47567801,114.92781466 43.47517671,114.92877291 43.47440563,114.93004129 43.47344348,114.93044081 43.47307276,114.931209 43.47246204,114.93224631 43.47157116,114.93333436 43.47081946,114.93383354 43.47036858,114.93429257 43.47003788,114.93438502 43.46995544,114.9346045 43.46965514,114.93476081 43.46951785,114.93543632 43.4690987,114.93576694 43.46893747,114.93590951 43.46882502,114.93632038 43.46845281,114.93693901 43.46771179,114.93720065 43.46747939,114.93739105 43.46702714,114.93761865 43.46671926,114.93791687 43.46639005,114.9382135 43.46610215,114.93879042 43.46577216,114.93947161 43.46528901,114.93956445 43.46519577,114.93974351 43.46490625,114.93985873 43.46480982,114.94003347 43.46471217,114.94052318 43.46455099,114.94127132 43.46406965,114.94218595 43.463769,114.94263054 43.46359973,114.94327722 43.46355866,114.94374713 43.46342677,114.94515196 43.46318424,114.94607196 43.46300195,114.94641299 43.4628906,114.94639025 43.46285364,114.94605318 43.4629637,114.94513824 43.46314498,114.94373065 43.46338799,114.94326456 43.46351881,114.94261464 43.46356009,114.94216161 43.46373256,114.9412408 43.46403525,114.94049265 43.46451659,114.94000472 43.46467719,114.93982045 43.46478016,114.93969618 43.46488417,114.9395161 43.46517535,114.9394294 43.46526241,114.93875375 43.46574164,114.93817341 43.46607359,114.93787138 43.46636672,114.93757086 43.46669846,114.9373395 43.46701146,114.9371507 43.46745989,114.93689353 43.46768831,114.93627476 43.4684295,114.93586736 43.46879856,114.93573054 43.46890648,114.93540271 43.46906635,114.93472108 43.4694893,114.93455808 43.46963247,114.9343385 43.4699329,114.93425137 43.4700106,114.93379221 43.47034139,114.9332934 43.47079194,114.93220585 43.47154329,114.93116728 43.47243525,114.93039837 43.47304654,114.92999925 43.47341689,114.92873223 43.47437802,114.92777381 43.47514922,114.92712569 43.4756494,114.92619669 43.47629076,114.92529776 43.47706207,114.92383968 43.47814376,114.92352807 43.478426,114.92319914 43.47883556,114.92255407 43.47932306,114.92230943 43.47945073,114.92180758 43.47958907,114.91895508 43.47971101,114.91855308 43.47974149,114.91769011 43.47989277,114.91654007 43.48024392,114.91542109 43.48053468,114.91289421 43.48133263,114.90654243 43.47909611,114.90480307 43.47862759,114.90363563 43.47112222,114.90490821 43.46888118,114.91158992 43.46343111,114.91277798 43.46286051,114.91274737 43.4628267,114.91155326 43.4634002,114.90486044 43.46885933,114.90357876 43.47111639,114.90475166 43.47865692,114.90652062 43.47913342,114.9128384 43.48135796,114.91259518 43.48147982,114.91206761 43.48191841,114.90986355 43.48257808,114.90952078 43.48271947,114.90907133 43.48295949,114.9087657 43.48307795,114.90854068 43.48312698,114.9077951 43.48319688,114.90739998 43.483197,114.90663647 43.48307807,114.9057092 43.48275904,114.90364801 43.4821388,114.9030494 43.48200952,114.900933 43.48138068,114.90030078 43.48126789,114.89977986 43.4810904,114.899191 43.4807873,114.8984635 43.48047043,114.89646903 43.47996431,114.89595296 43.47969112,114.89487209 43.47949478,114.89276475 43.47940829,114.88877906 43.47950779,114.88315775 43.48028654,114.86990346 43.47810277,114.8666822 43.4730865,114.86663182 43.47310365,114.86983479 43.47809145,114.85820837 43.47617448,114.85416654 43.47549289,114.85267718 43.47330792,114.85245466 43.47146791,114.8536919 43.46994867,114.85571131 43.46891913,114.86782175 43.46475977,114.86803038 43.46473656,114.86855789 43.4646869,114.88769316 43.46538821,114.88944336 43.46539278,114.89037844 43.4648349,114.89948876 43.45727268,114.89944697 43.45724597,114.89033967 43.46480568,114.88942359 43.46535223,114.88769466 43.46534771,114.86855571 43.46464626,114.86802262 43.46469646,114.86780535 43.46472062,114.8556833 43.46888397,114.85365019 43.4699205,114.85239769 43.47145847,114.8526227 43.47331909,114.85409638 43.47548106,114.84500698 43.4739477,114.84131967 43.47321193,114.80596457 43.45719684,114.80446545 43.45669235,114.80444356 43.45668826,114.80460012 43.45660082,114.80471397 43.45653723,114.80528737 43.45621699,114.80535463 43.45617942,114.80682923 43.45535584,114.81062172 43.45323747,114.81290365 43.45196272,114.8159696 43.4504551,114.82356065 43.44672168,114.83152743 43.44176495,114.83495951 43.43960242,114.83551493 43.43929287,114.83614144 43.43910228,114.83713129 43.43891221,114.84222784 43.43793347,114.84237924 43.43790439,114.84237212 43.43789805,114.85677213 43.43512449,114.85676503 43.43510492,114.85675792 43.43508534,114.84227432 43.43787499,114.84228141 43.43788129,114.84221367 43.4378943,114.83612356 43.43906383,114.83548688 43.43925751,114.83492442 43.43957099,114.8314912 43.44173423,114.82352695 43.44668939,114.81287115 43.45192984,114.8045516 43.45657688,114.80437508 43.45667547,114.80402288 43.45660968,114.75175685 43.44683456,114.7516734 43.44517831,114.75287434 43.44366086,114.75673451 43.44029008,114.75669183 43.44026414,114.75282859 43.44363759,114.75161726 43.44516816,114.75170069 43.44682405,114.74749987 43.4460372,114.74702051 43.44596999,114.74655423 43.44594619,114.74176743 43.44610903,114.74174535 43.44610205,114.74174066 43.44610994,114.74043947 43.44615416,114.73764019 43.44032492,114.73758772 43.4403383,114.74038152 43.44615613,114.72903384 43.44654122,114.71292358 43.44892418,114.70861225 43.44778785,114.69724485 43.44913482,114.69725377 43.4491748,114.70860692 43.44782952,114.71285239 43.44894849,114.70937739 43.4502646,114.70863354 43.4505234,114.69620578 43.45709293,114.69546874 43.45778666,114.68709328 43.46981898,114.67761824 43.47672404,114.67891338 43.47947774,114.678966 43.47946461,114.67768209 43.47673481,114.68713888 43.46984304,114.69551594 43.4578084,114.69624487 43.4571223,114.70866208 43.45055835,114.70940219 43.45030086,114.71293081 43.44896444,114.7275387 43.44680381,114.72273156 43.45446951,114.72278213 43.45448634,114.72760606 43.44679384,114.7290407 43.44658153,114.74171604 43.44615132,114.74134066 43.44678221,114.74134056 43.44678238,114.74092525 43.44749785,114.74092517 43.44749801,114.74049924 43.44824879,114.74049916 43.44824893,114.74006261 43.44903503,114.74006253 43.44903516,114.73961536 43.44985657,114.7396153 43.44985669,114.7391575 43.45071341,114.73915744 43.45071352,114.73868902 43.45160556,114.73868896 43.45160566,114.73820991 43.45253301,114.73826194 43.45254727,114.73874097 43.45161998,114.73920933 43.45072805,114.73966707 43.44987144,114.74011417 43.44905016,114.74055065 43.44826419,114.7409765 43.44751355,114.74139171 43.44679824,114.74177787 43.44614922,114.74655359 43.44598676,114.74701329 43.44601022,114.74748769 43.44607674,114.80400907 43.45664892,114.80433417 43.45670964,114.80146846 43.45844468,114.79872707 43.46004913,114.79613339 43.46150884,114.79368683 43.46282418,114.79178556 43.46391339,114.78923959 43.46501945,114.78723677 43.4659005,114.78538177 43.46663691,114.78368048 43.46731723,114.78213514 43.468032,114.78194902 43.46829799,114.78148875 43.46935223,114.78123738 43.47223366,114.78090014 43.47275834,114.77610914 43.47763555,114.73591237 43.48321881,114.71867091 43.48958441,114.71591437 43.48699166,114.71201953 43.48650173,114.71201006 43.48654164,114.71588361 43.48702889,114.71862039 43.48960306,114.71212428 43.49200042,114.7091891 43.49322813,114.70566824 43.49436223,114.69665655 43.48994973,114.69662551 43.48998334,114.7056081 43.4943816,114.69595179 43.49749121,114.6959743 43.49752825,114.70772652 43.49374355,114.7082372 43.49490419,114.71349193 43.50148198,114.71354003 43.50146162,114.70828833 43.49488764)))";

        List<List<Point>> list = GeometryUtil.decodeMultiPolygon(wkt);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testWkt() {

        String key = "0b549aabcbb0528ba72b48ef0210b6b3";
        MapApi mapApi = new AMapApi(key);

        List<Gps> list = new ArrayList<>();
        list.add(new Gps(33.383019860, 118.943683339));
        list.add(new Gps(33.383062475, 118.943657666));
        list.add(new Gps(33.386073549, 118.949627901));
        list.add(new Gps(33.386112627, 118.949606099));
        list.add(new Gps(33.386079358, 118.949631507));
        list.add(new Gps(33.386118070, 118.949599402));
        list.add(new Gps(33.388392452, 118.954395708));
        list.add(new Gps(33.388432706, 118.954369895));
        list.add(new Gps(33.390993163, 118.959998779));
        list.add(new Gps(33.391030764, 118.959975783));
        list.add(new Gps(33.391647910, 118.961445192));
        list.add(new Gps(33.391694103, 118.961415818));
        list.add(new Gps(33.391730164, 118.961614875));
        list.add(new Gps(33.391773649, 118.961580815));
        list.add(new Gps(33.392570281, 118.963422942));
        list.add(new Gps(33.392613039, 118.963386924));
        list.add(new Gps(33.392647045, 118.963584364));
        list.add(new Gps(33.392681820, 118.963542444));
        list.add(new Gps(33.393583861, 118.965591255));
        list.add(new Gps(33.393623715, 118.965555068));
        list.add(new Gps(33.393653252, 118.965731531));
        list.add(new Gps(33.393694835, 118.965708113));
        list.add(new Gps(33.394800975, 118.968178856));
        list.add(new Gps(33.394845840, 118.968167341));
        list.add(new Gps(33.394919886, 118.968440690));
        list.add(new Gps(33.394960108, 118.968417322));
        list.add(new Gps(33.395029517, 118.968689211));
        list.add(new Gps(33.395070451, 118.968650615));
        list.add(new Gps(33.395791686, 118.970351064));
        list.add(new Gps(33.395825343, 118.970324087));
        list.add(new Gps(33.395817919, 118.970448562));
        list.add(new Gps(33.395865403, 118.970415737));
        list.add(new Gps(33.396202806, 118.971299781));
        list.add(new Gps(33.396239760, 118.971244293));
        list.add(new Gps(33.396592061, 118.972125116));
        list.add(new Gps(33.396621668, 118.972046790));
        list.add(new Gps(33.396804711, 118.972586517));
        list.add(new Gps(33.396878873, 118.972614783));
        list.add(new Gps(33.396981853, 118.972969230));
        list.add(new Gps(33.397221523, 118.973350361));
        list.add(new Gps(33.397284034, 118.973619064));
        list.add(new Gps(33.397431680, 118.973924009));
        list.add(new Gps(33.397449518, 118.973835277));
        list.add(new Gps(33.397662525, 118.974387484));
        list.add(new Gps(33.397710463, 118.974371357));
        list.add(new Gps(33.398029271, 118.975082497));
        list.add(new Gps(33.398064122, 118.975021972));
        list.add(new Gps(33.398448106, 118.975921665));
        list.add(new Gps(33.398489234, 118.975893172));
        list.add(new Gps(33.398449688, 118.975918895));
        list.add(new Gps(33.398486960, 118.975892930));
        list.add(new Gps(33.399200391, 118.977617793));
        list.add(new Gps(33.399244939, 118.977587315));
        list.add(new Gps(33.399283693, 118.977827607));
        list.add(new Gps(33.399324855, 118.977800963));
        list.add(new Gps(33.400034912, 118.979521093));
        list.add(new Gps(33.400077990, 118.979500371));
        list.add(new Gps(33.400112167, 118.979719556));
        list.add(new Gps(33.400157231, 118.979683817));
        list.add(new Gps(33.400125571, 118.979675405));
        list.add(new Gps(33.400169148, 118.979658343));
        list.add(new Gps(33.401884443, 118.983504960));
        list.add(new Gps(33.401923873, 118.983478026));
        list.add(new Gps(33.401896274, 118.983525159));
        list.add(new Gps(33.401932430, 118.983495813));
        list.add(new Gps(33.402588967, 118.985031136));
        list.add(new Gps(33.402626607, 118.985009040));
        list.add(new Gps(33.402865621, 118.985626488));
        list.add(new Gps(33.402903529, 118.985599669));
        list.add(new Gps(33.403636808, 118.987288525));
        list.add(new Gps(33.403669057, 118.987263105));
        list.add(new Gps(33.403644255, 118.987322814));
        list.add(new Gps(33.403686079, 118.987292983));
        list.add(new Gps(33.407890007, 118.996642066));
        list.add(new Gps(33.407938814, 118.996617124));
        System.out.println(list.size());
        List<String> distList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Gps gps84 = list.get(i);
            //坐标转换
            Gps gps = mapApi.translateToGcj02(gps84.getLng(), gps84.getLat());
            distList.add(String.format("%s %s", gps.getLng(), gps.getLat()));
        }
        System.out.print(StringUtils.joinWith(",", distList));
    }
}

package com.whfc.common;

import org.junit.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

public class TimeZoneTest {

    @Test
    public void test() {
        for (int i = -12; i <= 0; i++) {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX");
            String dateStr = dtf.format(Instant.ofEpochSecond(1647658144).atZone(ZoneId.of("GMT-" + Math.abs(i))));
            System.out.println(dateStr);
        }
        for (int i = 0; i <= 12; i++) {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX");
            String dateStr = dtf.format(Instant.ofEpochSecond(1647658144).atZone(ZoneId.of("GMT+" + i)));
            System.out.println(dateStr);
        }
    }

    @Test
    public void testTimeZone() {
        String[] ids = TimeZone.getAvailableIDs();
        for (String id : ids) {
            TimeZone tz = TimeZone.getTimeZone(id);
            System.out.println(tz);
        }
    }

    @Test
    public void testZoneDateTime(){
        ZonedDateTime zonedDateTime = ZonedDateTime.now();
        System.out.println(zonedDateTime);
        for (int i = 0; i <= 12; i++) {
            ZonedDateTime zonedDateTime1 = zonedDateTime.withZoneSameInstant(ZoneId.of("GMT+" + Math.abs(i)));
            System.out.println(zonedDateTime1);
            System.out.println(zonedDateTime1.isEqual(zonedDateTime));
        }
    }

    @Test
    public void testFormater(){
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        System.out.println(formatter.format(ZonedDateTime.now()));
        System.out.println(new Date());
    }
}

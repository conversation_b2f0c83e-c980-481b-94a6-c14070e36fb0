package com.whfc.common;

import com.whfc.common.third.juch.*;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/3/6 9:42
 */
public class SpraySwitchTest {

    @Test
    public void test() {

        String sn = "JCSW01000001";
        String status = "off";
        String support = "[\"CH1\"]";
        status = Status.on.name().equals(status) ? Status.on.getValue() : Status.off.getValue();
        String ch1 = support.contains(JuchConst.CH1) ? status : null;
        String ch2 = support.contains(JuchConst.CH2) ? status : null;

        Switch data = new Switch();
        data.setCmd(JuchConst.CMD_SWCONTROL);
        data.setCH1(StringUtils.upperCase(ch1));
        data.setCH2(StringUtils.upperCase(ch2));

        SwitchMsg msg = new SwitchMsg();
        msg.setSn(sn);
        msg.setDataType(JuchConst.CMD);
        msg.setProductId(JuchConst.PRODUCT_ID_SW);
        msg.setData(data);

        System.out.println(JSONUtil.toPrettyString(msg));
    }

    @Test
    public void testGetParam() {
        String sn = "JCSW01000001";

        Param data = new Param();

        ParamMsg msg = new ParamMsg();
        msg.setSn(sn);
        msg.setDataType(JuchConst.CMD_GETPARA);
        msg.setProductId(JuchConst.PRODUCT_ID_SW);
        msg.setData(data);

        System.out.println(JSONUtil.toPrettyString(msg));
    }

    @Test
    public void testSetParam() {
        String sn = "JCSW01000001";

        Param data = new Param();
        data.setApnProt("0");
        data.setApnUsername("");
        data.setApnPassword("");
        data.setSn(sn);
        data.setCtrMode("0");
        data.setServerIP("mqtt.whfciot.com");
        data.setServerPort("1883");

        ParamMsg msg = new ParamMsg();
        msg.setSn(sn);
        msg.setDataType(JuchConst.CMD_SETPARA);
        msg.setProductId(JuchConst.PRODUCT_ID_SW);
        msg.setData(data);

        System.out.println(JSONUtil.toPrettyString(msg));
    }
}

package com.whfc.common;

import com.whfc.common.jwt.JwtPayLoad;
import com.whfc.common.jwt.JwtUtils;
import com.whfc.common.util.DateUtil;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.whfc.common.jwt.JwtUtils.parseJwt;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/21 14:41
 */
public class JwtUtilsTest {

    @Test
    public void test1() {
        String user = "xugc";
        String secret = "4355c41f986cd9cc193014db274fde0f0dfbe20d4d4a1349e02badd0";

        JwtPayLoad payLoad = new JwtPayLoad();
        payLoad.setAppType("cms");
        payLoad.setLoginMethod("user_pass_login");
        payLoad.setUser(user);
        payLoad.setVer(100L);
        payLoad.setExpireAt(DateUtil.addHours(new Date(), 2));

        System.out.println("payLoad:" + payLoad);

        String sign = JwtUtils.sign(payLoad, secret);
        System.out.println("sign:" + sign);

        //TimeUnit.MINUTES.sleep(2);

        try {
            JwtUtils.verify(sign, payLoad, secret);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        JwtPayLoad payLoad1 = JwtUtils.parse(sign);
        System.out.println("payLoad:" + payLoad1);
    }

    @Test
    public void test2() {
        String apiKey = "MAnRUAqL8ZeRGVhSIGYApusQYZACMKJm";
        String apiSecret = "RIHXwMYjBaO5LF4wGPhMDB7ePwAgMzAj";
        Map<String, Object> map = new HashMap<>();
        map.put("deptId", 2);
        String jwt = JwtUtils.createJwt(7 * 24 * 60 * 60 * 1000, apiKey, apiSecret, map);
        Map<String, String> claims = parseJwt(jwt);
        System.out.println(claims);
    }
}

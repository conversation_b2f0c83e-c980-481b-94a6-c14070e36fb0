package com.whfc.common;

import com.whfc.common.third.epiroc.ItiqApi;
import com.whfc.common.third.epiroc.entity.MachineData;
import com.whfc.common.third.epiroc.entity.MachineFiled;
import com.whfc.common.third.epiroc.entity.MachineInfo;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;

public class ItiqApiTest {

    private String ik = "30F73F301A9DB549814B17B17A8D7276";
    private ItiqApi api = new ItiqApi(ik);
    private String model = "BOOMER XL3 D";
    private String remoteId = "D4124310D4E5";

    @Test
    public void testGetDeviceList() {
        List<MachineInfo> list = api.getDeviceList();
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetDeviceFieldList() {
        List<MachineFiled> list = api.getDeviceFieldList(model);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetDeviceData() {
        Date start = DateUtil.parseDateTime("2022-08-04 00:00:00");
        Date end = DateUtil.parseDateTime("2022-08-04 23:59:59");
        List<MachineData> list = api.getDeviceData(remoteId, start, end);
        System.out.println("数据量:" + list.size());
        System.out.println(JSONUtil.toPrettyString(list));
    }
}

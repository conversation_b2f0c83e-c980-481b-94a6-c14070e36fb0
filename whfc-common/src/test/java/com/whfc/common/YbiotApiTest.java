package com.whfc.common;

import com.whfc.common.third.ybiot.YbiotApi;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/14 9:48
 */
public class YbiotApiTest {

    private String host = "http://v5.ybiot.net:8058/ybapi/index/getData_grcjl";
    private String user = "gr-cjl";
    private String key = "grcjl2403";
    YbiotApi ybiotApi = new YbiotApi(host, user, key);

    @Test
    public void test() {
        List<?> data = ybiotApi.getWaterMeterData();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void test2() {
        List<?> data = ybiotApi.getWaterMeterData("1210406801");
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

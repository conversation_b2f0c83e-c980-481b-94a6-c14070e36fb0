package com.whfc.common;

import com.whfc.common.third.tq.TqApi;
import com.whfc.common.third.tq.entity.AccessTokenData;
import com.whfc.common.third.tq.entity.ImeiData;
import com.whfc.common.third.tq.entity.LocationData;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-11-16 10:59
 */
public class TqApiTest {

    private String appKey = "8FB345B8693CCD00018BAA962EF8084C";
    private String appSecret = "3bcb4e3775b34b8485f4e4527a50c73f";
    private String username = "whfciot";
    private String password = "Abc888888";
    private TqApi api = new TqApi(appKey, appSecret, username, password);
    private String accessToken = "e17537cf345c1642979724abec9dee78";
    private String imei = "868120273020732";

    @Test
    public void testAccessToken() {
        AccessTokenData tokenData = api.accessToken();
        System.out.print(JSONUtil.toPrettyString(tokenData));
    }

    @Test
    public void testGetImeis() {
        List<ImeiData> list = api.getImeis(accessToken);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetLocations1() {
        List<LocationData> list = api.getLocations(accessToken);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetLocations2() {
        List<LocationData> list = api.getLocations(accessToken, Arrays.asList(imei));
        System.out.println(JSONUtil.toPrettyString(list));
    }
}

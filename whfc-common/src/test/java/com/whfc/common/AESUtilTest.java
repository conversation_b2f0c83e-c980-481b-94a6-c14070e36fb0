package com.whfc.common;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.yjbh.hjsw.StaffSyncData;
import com.whfc.common.util.AESUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;

public class AESUtilTest {

    private String testFile = "C:\\Users\\<USER>\\Desktop\\test.txt";

    @Test
    public void test() throws Exception {
        String encryptData = readData(new File(testFile));
        String key = "A4lznykDmsB3NyiZ";
        String decryptData = AESUtil.decrypt(encryptData, key);
        StaffSyncData syncData = JSONObject.parseObject(decryptData, StaffSyncData.class);
        System.out.println(JSONUtil.toPrettyString(syncData));
    }

    @Test
    public void test1() throws Exception {
        String encryptData = readData(new File(testFile));
        HttpUtil.doPost("http://127.0.0.1/open/api/faceGate/hjsw/sync?accessToken=yjbh", encryptData);
    }

    private String readData(File file) throws Exception {
        return FileUtils.readFileToString(file, "UTF-8");
    }
}

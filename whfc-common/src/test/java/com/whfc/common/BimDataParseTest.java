package com.whfc.common;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;

/**
 * @Description: bim数据解析
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/23 16:29
 */
public class BimDataParseTest {

    private String filePath = "C:\\Users\\<USER>\\Desktop\\bim.json";

    @Test
    public void test() throws Exception {
        String content = FileUtils.readFileToString(new File(filePath), "utf-8");
        JSONObject json = JSONObject.parseObject(content);
        //System.out.println(json.toJSONString());

        JSONArray floors = json.getJSONArray("data");
        for (int i = 0; i < floors.size(); i++) {
            JSONObject floor = floors.getJSONObject(i);
            String floorId = floor.getString("id");
            String floorType = floor.getString("type");
            String floorName = floor.getString("name");
            //System.out.println(String.format("%s,%s,%s", floorId, floorType, floorName));
            JSONArray categorys = floor.getJSONArray("items");
            for (int j = 0; j < categorys.size(); j++) {
                JSONObject category = categorys.getJSONObject(j);
                String categoryId = category.getString("id");
                String categoryType = category.getString("type");
                String categoryName = category.getString("name");
                //System.out.println(String.format("%s,%s,%s,%s,%s,%s", floorId, floorType, floorName, categoryId, categoryType, categoryName));
                JSONArray familys = category.getJSONArray("items");
                for (int k = 0; k < familys.size(); k++) {
                    JSONObject family = familys.getJSONObject(k);
                    String famId = family.getString("id");
                    String famType = family.getString("type");
                    String famName = family.getString("name");
                    //System.out.println(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s", floorId, floorType, floorName, categoryId, categoryType, categoryName, famId, famType, famName));
                    JSONArray familyTypes = family.getJSONArray("items");
                    for (int m = 0; m < familyTypes.size(); m++) {
                        JSONObject familyType = familyTypes.getJSONObject(m);
                        String familyTypeId = familyType.getString("id");
                        String familyTypeType = familyType.getString("type");
                        String familyTypeName = familyType.getString("name");
                        String elementId = familyType.getJSONArray("items").getJSONObject(0).getJSONArray("elementIds").getString(0);
                        System.out.println(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s", floorId, floorType, floorName, categoryId, categoryType, categoryName, famId, famType, famName, familyTypeId, familyTypeType, familyTypeName, elementId));
                    }
                }
            }
        }
    }
}

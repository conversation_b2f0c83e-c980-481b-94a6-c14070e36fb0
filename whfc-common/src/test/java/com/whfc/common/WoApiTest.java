package com.whfc.common;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.face.wo.WoFaceApi;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.Arrays;

/**
 * @ClasssName WoApiTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/21 15:09
 * @Version 1.0
 */
public class WoApiTest {

    /**
     * 应用appid
     */
    private String appId = "D847844DE9C94634B10E0AA30D7D95D3";

    /**
     * 应用key
     */
    private String appKey = "F52AFEAC27D74C9FAA590386FEDA0426";

    /**
     * 应用secret
     */
    private String appSecret = "A2081FE942B64CFC8D139540F306E3E2";

    /**
     * 请求令牌
     */
    private String token = "e76f7a66b6e0900e0984c7ceecd654e7fcac4b5a1e79ce21a60481ee259de004";

    WoFaceApi woFaceApi = new WoFaceApi(appId, appKey, appSecret);

    @Test
    public void auth() {
        System.out.println(woFaceApi.auth());
    }

    @Test
    public void deviceCreate() {
        System.out.println(JSONObject.toJSONString(woFaceApi.deviceCreate("84E0F42668581502", "公司闸机后门_出", "出", "1", token)));
    }

    @Test
    public void deviceDel() {
        System.out.println(JSONObject.toJSONString(woFaceApi.deviceDel("84E0F42668581502", token)));
    }

    @Test
    public void personCreate() {
        System.out.println(JSONObject.toJSONString(woFaceApi.personCreate("何伟", "17702771994", token)));
    }

    @Test
    public void personDelete() {
        System.out.println(JSONObject.toJSONString(woFaceApi.personDelete("C887C860830D4C80B2305DD08D6F5FD3", token)));
    }

    @Test
    public void faceImgCreate() {
        System.out.println(JSONObject.toJSONString(woFaceApi.faceImgCreate("83019632725D40D586E6EC05899C55B6", "https://file.whfciot.com/ms/test/emp/img/2021011910185217089.png", token)));
    }

    @Test
    public void setPeople() {
        System.out.println(JSONObject.toJSONString(woFaceApi.setPeople("84E0F42668581502", "83019632725D40D586E6EC05899C55B6", "1", token)));
    }

    @Test
    public void deleteSomePeople() {
        System.out.println(JSONObject.toJSONString(woFaceApi.deleteSomePeople("84E0F42668581502", "83019632725D40D586E6EC05899C55B6", "1", token)));
    }

    @Test
    public void testJoin() {
        System.out.println(StringUtils.join(Arrays.asList("x", "y"), ","));
        System.out.println(StringUtils.join(",", Arrays.asList("x", "y")));
        System.out.println(StringUtils.join(Arrays.asList("x", "y")));
    }
}

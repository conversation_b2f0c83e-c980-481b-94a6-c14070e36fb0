package com.whfc.common;

import com.whfc.common.third.yjbh.yunplc.YunPlcAddr;
import com.whfc.common.third.yjbh.yunplc.YunPlcFanData;
import com.whfc.common.third.yjbh.yunplc.YunplcApi;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

public class YunplcApiTest {
    private String user = "26400820344";
    private String pass = "QC202504007";

    @Test
    public void testLogin() {
        YunplcApi api = new YunplcApi(user, pass);
        YunPlcAddr addrInfo = api.login();
        System.out.println(JSONUtil.toPrettyString(addrInfo));

        YunPlcFanData fanData = api.data(addrInfo.getAddr(), addrInfo.getSid());
        System.out.println(JSONUtil.toPrettyString(fanData));
    }
}

package com.whfc.common;

import com.whfc.common.exception.BizException;
import com.whfc.common.third.bimface.BimfaceApi;
import com.whfc.common.third.bimface.entity.*;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-10 15:59
 */
public class BimFaceApiTest {

    private String appKey = "v3qK6m6OS6dnhmqML4MnEBUq7oQs6gKr";
    private String appSecret = "Kvxhcde39tBebCvn3eW7KAvJXRjjCBPu";
    private String fileId = "10000867645115";
    private String token = "cn-491ccf12-71fd-4c12-9783-3c383db216c0";

    @Test
    public void testGetAccessToken() {
        AccessToken token = BimfaceApi.getAccessToken(appKey, appSecret);
        System.out.println(token.getExpireTime());
        System.out.println(token.getToken());
    }

    @Test
    public void testGetViewToken() {
        String viewToken = BimfaceApi.getViewToken(token, fileId, "fileId");
        System.out.println(viewToken);
    }

    @Test
    public void testCreateShareUrl() {
        ShareUrl shareUrl = BimfaceApi.createShareUrl(token, fileId);
        System.out.println(shareUrl.getUrl());
    }

    @Test
    public void testUploadUrl() {
        String name = "测试.dwf";
        String url = "https://file.whfciot.com/ms/test/bim/model/2/2022-01-13/2022011309281085826.dwf";
        BimFile data = BimfaceApi.uploadUrl(token, name, url);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testUploadPolicy() {
        String name = "测试.dgn";
        UploadPolicy data = BimfaceApi.uploadPolicy(token, name);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetFileInfo() {
        BimFile data = BimfaceApi.fileInfo(token, fileId);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetFileUploadStatus() {
        BimFileStatus data = BimfaceApi.fileUploadStatus(token, fileId);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetFileSupport() {
        BimFileSupport data = BimfaceApi.fileSupport(token);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetFileDownloadUrl() {
        String data = BimfaceApi.fileDownloadUrl(token, fileId);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testTranslate() {
        TranslateSource source = new TranslateSource();
        source.setFileId("10000723837955");
        source.setCompressed(false);
        source.setRootName("测试.dgn");

        Map<String, Object> config = new HashMap<>();

        String callback = "https://test.whfciot.com/open/api/bimface/callback/anno";

        TranslateResult data = BimfaceApi.translateFile(token, source, config, callback);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test(expected = BizException.class)
    public void testGetTranslateStatus() {
        TranslateResult data = BimfaceApi.translateFileStatus(token, fileId);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testIntegrateStatus() {
        IntegrateResult data = BimfaceApi.integrateStatus(token, "2240345449997984");
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetTree() {
        String data = BimfaceApi.getTree(token, "10000867645115");
        System.out.print(data);
    }

    @Test
    public void testGetElement() {
        String fileId = "10000867645115";
        String categoryId = "-2000151";
        String family = "截水沟";
        String familyType = "截水沟";
        String floor = "12.6";
        String data = BimfaceApi.getElement(token, fileId, categoryId, family, familyType, floor);
        System.out.print(data);
    }
}

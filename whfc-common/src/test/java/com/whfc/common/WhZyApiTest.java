package com.whfc.common;

import com.whfc.common.third.wuhu.WhZyApi;
import com.whfc.common.third.wuhu.entity.*;
import com.whfc.common.util.Base64Util;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/29 12:20
 */
public class WhZyApiTest {

    private String host = "http://*************:8082/zhzj/data/api";
    private String account = "芜湖泛达网络科技有限公司";
    private String secret = "ea35ef68286d73527226cd14964c681d";
    private WhZyApi api = new WhZyApi(host, account, secret);

    private String faceImgUrl = "https://file.whfciot.com/common/idcard/face.jpg";

    @Test
    public void testQyry() {

        String grzp = Base64Util.getUrlImageToBase64(faceImgUrl);

        SmzQyry data = new SmzQyry();
        data.setSFZHM("340222198603044110");
        data.setTYSHXYDMZ("913456987777L");
        data.setXM("李四");
        data.setXB("1");
        data.setSJHM("**********");
        data.setCSRQ("1986-03-04");
        data.setXZZ("广东省深圳市xxx街道yyy号");
        data.setGW("5");
        data.setGRZP(grzp);

        List<SmzQyry> list = new ArrayList<>();
        list.add(data);

        api.smzQyry(list);
    }

    @Test
    public void testXmry() {
        SmzXmry data = new SmzXmry();
        data.setBDBM("BD2019002");
        data.setTYSHXYDMZ("913456987777L");
        data.setSFZHM("340222198603044110");
        data.setXM("张三");
        data.setGW("5");
        data.setJRXMSJ("2023-06-01");

        List<SmzXmry> list = new ArrayList<>();
        list.add(data);

        api.smzXmry(list);
    }

    @Test
    public void testXmrykq() {
        String image = Base64Util.getUrlImageToBase64(faceImgUrl);

        SmzXmrykq data = new SmzXmrykq();
        data.setBDBM("BD2021");
        data.setSBNO("3330000N");
        data.setSFZHM("340222198603044110");
        data.setXM("张三");
        data.setDKZP(image);
        data.setDKSJ("2023-06-30 09:00:01");
        data.setDKZT("0");
        data.setLNG("116.56");
        data.setLAT("39.80");

        List<SmzXmrykq> list = new ArrayList<>();
        list.add(data);

        api.smzXmrykq(list);
    }

    @Test
    public void testXmryht(){
        SmzXmryht data = new SmzXmryht();
        data.setBDBM("BD2019002");
        data.setTYSHXYDMZ("913456987777L");
        data.setSFZHM("340222198603044110");
        data.setXM("张三");
        data.setHTLX("0");
        data.setYDGZ("5000");
        data.setYGZ("5500");
        data.setGZFFXS("按月发放");
        data.setGZNR("安全员");
        data.setSXRQ("2022-05-10");
        data.setSXRQE("2024-05-10");

        List<SmzXmryht> list = new ArrayList<>();
        list.add(data);

        api.smzXmryht(list);
    }

    @Test
    public void testXmrypx(){
        SmzXmrypx data = new SmzXmrypx();
        data.setBDBM("BD2019002");
        data.setSFZHM("340222198603044110");
        data.setXM("张三");
        data.setPXBH("2023004");
        data.setPXRQ("2023-04-10");
        data.setPXSC("4.5");
        data.setPXDD("项目部");
        data.setPXJG("安全部门");
        data.setPXR("李四");
        data.setPXLX("1");
        data.setKCMC("2023年第四期安全教育培训");
        data.setPXJS("施工队安全教育培训");

        List<SmzXmrypx> list = new ArrayList<>();
        list.add(data);

        api.smzXmrypx(list);
    }

    @Test
    public void testXmrygz(){
        SmzXmrygz data = new SmzXmrygz();
        data.setBDBM("BD2019002");
        data.setSFZHM("340222198603044110");
        data.setXM("张三");
        data.setGW("5");
        data.setKSRQ("2023-04-10");
        data.setJSRQ("2023-04-30");
        data.setYHKH("6222024002322767789");
        data.setCQXS("180");
        data.setGZJE("3500");

        List<SmzXmrygz> list = new ArrayList<>();
        list.add(data);

        api.smzXmrygz(list);
    }

    @Test
    public void testYcsbxx(){
        Ycsbxx data = new Ycsbxx();
        data.setBDBM("BD2019002");
        data.setSBMC("测试1");
        data.setSBNBBS("YCSB202105");
        data.setWHRY("张三");
        data.setSJHM("**********");

        List<Ycsbxx> list = new ArrayList<>();
        list.add(data);

        api.ycsbxx(list);
    }

    @Test
    public void testYcsbsjcj(){
        Ycsbsjcj data = new Ycsbsjcj();
        data.setSBNBBS("YCSB202105");
        data.setSJCJSJ("2023-06-30 09:00:01");
        data.setPM25("33");
        data.setPM10("34");
        data.setZS("35");
        data.setWD("36");
        data.setSD("22");
        data.setFX("1");
        data.setFS("3");
        data.setDQY("100");
        data.setYC("10");

        List<Ycsbsjcj> list = new ArrayList<>();
        list.add(data);

        api.ycsbsjcj(list);
    }
}

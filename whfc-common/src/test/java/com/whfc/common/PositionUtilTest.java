package com.whfc.common;

import com.whfc.common.util.Gps;
import com.whfc.common.util.PositionUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/10/8 16:36
 */
public class PositionUtilTest {

    @Test
    public void test1() {
        Double lng = 114.48001;
        Double lat = 30.5088;

        //System.out.println(PositionUtil.isInChina(lng, lat));
        //System.out.println(PositionUtil.outOfChina(lng, lat));

        Gps gps1 = new Gps(lat, lng);
        Gps gps2 = PositionUtil.gps84_To_Gcj02(lat, lng);

        System.out.println(gps1);
        System.out.println(gps2);
    }
}

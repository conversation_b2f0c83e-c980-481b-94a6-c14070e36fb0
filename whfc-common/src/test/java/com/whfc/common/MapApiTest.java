package com.whfc.common;

import com.whfc.common.geometry.Point;
import com.whfc.common.third.map.MapApi;
import com.whfc.common.third.map.MapDistance;
import com.whfc.common.third.map.MapLoc;
import com.whfc.common.third.map.entity.Trajectory;
import com.whfc.common.third.map.entity.TrajectoryPoint;
import com.whfc.common.third.map.impl.AMapApi;
import com.whfc.common.third.map.impl.QQMapApi;
import com.whfc.common.util.Gps;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.PositionUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-13 15:20
 */
public class MapApiTest {

    String qqMapKey = "DAABZ-D3A3O-INHWO-SDCBM-T5PTE-RQFRG";
    String qqMapSk = "PXH4CtVyVv3B4oENB0o1MQpUroLCEYpj";
    MapApi qqMapApi = new QQMapApi(qqMapKey, qqMapSk);

    String key = "0b549aabcbb0528ba72b48ef0210b6b3";
    MapApi aMapApi = new AMapApi(key);

    @Test
    public void testQQMapApi() {
        Double lng = -58.39;
        Double lat = -34.64;


        //坐标转换
        Gps gps = qqMapApi.translateToGcj02(lng, lat);
        System.out.println(lng + "," + lat);
        System.out.println(gps.getLng() + "," + gps.getLat());

        //逆地址解析
        MapLoc mapLoc = qqMapApi.geocode(lng, lat);
        System.out.println(JSONUtil.toPrettyString(mapLoc));
    }

    @Test
    public void testAMapApi() {
        Double lng = -58.39;
        Double lat = -34.64;


        //坐标转换
        Gps gps = aMapApi.translateToGcj02(lng, lat);
        System.out.println(lng + "," + lat);
        System.out.println(gps.getLng() + "," + gps.getLat());

        //逆地址解析
        MapLoc mapLoc = aMapApi.geocode(lng, lat);
        System.out.println(JSONUtil.toPrettyString(mapLoc));
    }

    @Test
    public void testDistance() {

        long t0 = System.currentTimeMillis();

        double lng1 = 114.257433, lat1 = 30.466527;
        double lng2 = 114.254695, lat2 = 30.463933;
        Gps from = new Gps(lat1, lng1);
        Gps to = new Gps(lat2, lng2);
        MapDistance data = qqMapApi.distance(from, to);


        long t1 = System.currentTimeMillis();

        Point start = new Point(lng1, lat1);
        Point end = new Point(lng2, lat2);
        Double distance = PositionUtil.getDistance(start, end);


        long t2 = System.currentTimeMillis();

        System.out.println(JSONUtil.toPrettyString(data) + ",耗时:" + (t1 - t0));
        System.out.println("distance:" + distance + ",耗时:" + (t2 - t1));
    }

    @Test
    public void testTrasproad() {
        List<TrajectoryPoint> points = new ArrayList<>();
        points.add(new TrajectoryPoint(114.456088, 30.497851, 1697173563L, 0D, 0D));
        points.add(new TrajectoryPoint(114.456676, 30.497870, 1697173288L, 0D, 0D));
        points.add(new TrajectoryPoint(114.452219, 30.500240, 1697172557L, 0D, 0D));
        Trajectory data = aMapApi.grasproad(points);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

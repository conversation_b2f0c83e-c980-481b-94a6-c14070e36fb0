package com.whfc.common;

import com.whfc.common.third.sms.SmsApi;
import com.whfc.common.third.sms.SmsTplCode;
import com.whfc.common.third.sms.impl.AliyunSmsApi;
import com.whfc.common.third.sms.impl.TencentSmsApi;
import com.whfc.common.util.DateUtil;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-13 15:15
 */
public class SmsApiTest {

    private SmsApi aliyunSmsApi() {
        String accessKeyId = "************";
        String accessSecret = "************";
        String sign = "风潮物联";
        Map<String, String> template = new HashMap<>();
        template.put(SmsTplCode.TPL_VERIFY, "SMS_171745787");
        template.put(SmsTplCode.TPL_ALARM_ENV, "SMS_189027909");
        return new AliyunSmsApi(accessKeyId, accessSecret, sign, template, null);
    }

    private SmsApi tencentSmsApi() {
        String accessKeyId = "************";
        String accessSecret = "************";
        String sdkAppId = "1400963132";
        String sign = "数智建造广东横琴科技";
        Map<String, String> template = new HashMap<>();
        template.put(SmsTplCode.TPL_VERIFY, "2358745");
        return new TencentSmsApi(accessKeyId, accessSecret, sdkAppId, sign, template, null);
    }

    @Test
    public void testSmsVerify() {
        SmsApi smsApi = aliyunSmsApi();
        String phone = "18682262103";
        String code = "aabbcc";

        TreeMap<String, String> params = new TreeMap<>();
        params.put("code", code);

        smsApi.sendSms(phone, SmsTplCode.TPL_VERIFY, params);
    }

    @Test
    public void testSmsEnv() {
        SmsApi smsApi = aliyunSmsApi();
        String phone = "18682262103";
        Date time = new Date();

        TreeMap<String, String> params = new TreeMap<>();
        params.put("username", "张三");
        params.put("deviceName", "测试设备");
        params.put("rule", "PM2.5报警");
        params.put("time", DateUtil.formatDateTime(time));
        smsApi.sendSms(phone, SmsTplCode.TPL_ALARM_ENV, params);
    }

    @Test
    public void testTencentSms() {
        SmsApi smsApi = tencentSmsApi();
        String phone = "+8618682262103";
        TreeMap<String, String> params = new TreeMap<>();
        params.put("code", "159357");
        params.put("expire", "5");
        smsApi.sendSms(phone, SmsTplCode.TPL_VERIFY, params);
    }


}

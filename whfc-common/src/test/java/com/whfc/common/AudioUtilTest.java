package com.whfc.common;

import com.whfc.common.util.AudioUtil;
import org.junit.Test;

import java.io.File;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/14 14:43
 */
public class AudioUtilTest {

    private String wav = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.wav";
    private String wav2mp3 = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.wav.mp3";
    private String wav2amr = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.wav.amr";
    private String amr = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.amr";
    private String amr2mp3 = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.amr.mp3";
    private String mp3 = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.mp3";
    private String mp32amr = "C:\\Users\\<USER>\\Desktop\\test\\audio\\test.mp3.amr";

    @Test
    public void testGetDuration() {
        System.out.println(AudioUtil.getDuration(new File(wav)));
        System.out.println(AudioUtil.getDuration(new File(wav2mp3)));
    }

    @Test
    public void testWav2mp3() {
        AudioUtil.wav2mp3(new File(wav), new File(wav2mp3));
    }

    @Test
    public void testAmr2mp3() {
        AudioUtil.amr2mp3(new File(amr), new File(amr2mp3));
    }

    @Test
    public void testMp32Amr() {
        AudioUtil.mp32amr(new File(mp3), new File(mp32amr));
    }

    @Test
    public void testWav2Amr() {
        AudioUtil.wav2amr(new File(wav), new File(wav2amr));
    }
}

package com.whfc.common;

import com.whfc.common.third.renke.RenkeApi;
import com.whfc.common.third.renke.entity.Token;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/7/7 10:44
 */
public class RenkeApiTest {

    private String host = "http://dust.0531yun.cn";

    private String username = "c220919fdwl";

    private String password = "c220919fdwl";

    private String token = "eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2ODg3MjA0MzgsInVzZXJJZCI6IjI5YWE2ZTIyNTAyNzQ4OWZhZDIzYzU3Mzk3OWY0NDIyIiwiaWF0IjoxNjg4NzEzMjM4LCJqdGkiOiJ0b2tlbklkIn0.WRs1UndwBzROD-lXVZEdpj-FkR2pyQgrpIwyu_F8VsA";

    private RenkeApi api = new Renke<PERSON><PERSON>(host, username, password);

    @Test
    public void testToken() {

        Token token = api.getToken();

        System.out.println(JSONUtil.toPrettyString(token));

    }

    @Test
    public void testGetData() {

        List<String> snList = Arrays.asList("40210591", "40219057");
        List<?> list = api.getRealData(token, snList);
        System.out.println(JSONUtil.toPrettyString(list));
    }
}

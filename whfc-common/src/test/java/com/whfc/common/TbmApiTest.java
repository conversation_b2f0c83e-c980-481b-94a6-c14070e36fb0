package com.whfc.common;

import com.whfc.common.third.tbm.api.TbmApi;
import com.whfc.common.third.tbm.tjzg.TjzgTbmDataItem;
import org.junit.Test;

import java.util.List;

public class TbmApiTest {

    private static String host = "http://*************:9291";
    private TbmApi api = new TbmApi(host);

    @Test
    public void test() {
        List<TjzgTbmDataItem> items = api.getData();
        System.out.println("数量:" + items.size());
        for (TjzgTbmDataItem item : items) {
            System.out.println(item.getComment() + ":" + item.getValue());
        }
    }
}

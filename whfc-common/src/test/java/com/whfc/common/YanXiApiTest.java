package com.whfc.common;

import com.whfc.common.third.yanxi.YanXiApi;
import com.whfc.common.third.yanxi.entity.CurrentData;
import com.whfc.common.third.yanxi.entity.LddCurrentData;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/12 14:44
 */
public class YanXiApiTest {

    private static String tj = "YWRtaW4xOmFkbWluMTIzOlRKMDAwMDAwMDAwMDAwOjAwZmFjOWIyMTRlMzZhNDg3YmYxOTJiZWRmYWUzZWZi";
    private static String jqj = "YWRtaW4xOmFkbWluMTIzOkpRSjAwMDAwMDAwMDAwMDoxYTE4NTgxYzU5MDQ3NmY3NWUxOTdiZThhODFmZjIxMA";
    private static String mj = "YWRtaW4xOmFkbWluMTIzOk1KNDUwMTAwMTAwMTAwOmJkMDY3NGMzODM5OWQwM2ZiZDQ0MmZjYWIyMTkyOTli";
    private static String qj = "YWRtaW4xOmFkbWluMTIzOlFKNDUwMTAwMTAwMjAwOjE2YzNmZTY3ZTgzOGQ3MDhlMzk1OGNiYjRkOTE2MmU0";

    @Test
    public void testTj() {
        List<CurrentData> data = YanXiApi.getData(tj);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testJqj() {
        List<CurrentData> data = YanXiApi.getData(jqj);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testMj() {
        List<CurrentData> data = YanXiApi.getData(mj);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testQj() {
        List<CurrentData> data = YanXiApi.getData(qj);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testLdd() {
        String dataStr = "{\"DevID\":\"361101210189\",\"DevType\":\"LDD\",\"ctime\":\"2021-12-16 07:50:34.334\",\"WorkTime\":0.0,\"CumulativeWorkingTime\":202.0,\"OperatingCycleNumber\":0.0,\"AcquisitionModulesNumber\":7.0,\"Reserved1\":9.0,\"Neiweight1\":0.0,\"Neiweight2\":0.0,\"Neiweight3\":0.0,\"Neiweight4\":0.0,\"Distance5\":121.63,\"Distance6\":82.81,\"Distance7\":67.54,\"Distance8\":69.0,\"Distance9\":118.21,\"Distance10\":78.0,\"Distance11\":24.56,\"Angle1\":15.02,\"Angle2\":-0.01,\"Angle3\":-10.15,\"Angle4\":0.31,\"Angle5\":0.18,\"Angle6\":0.44,\"Wind1\":3.42,\"Wind2\":3.0,\"Speed1\":0.0,\"Speed2\":0.0,\"BRbit0\":0,\"BRbit1\":0,\"LI1bit8\":0,\"LI1bit9\":0,\"LI1bit10\":0,\"LI1bit11\":0,\"LI2bit0\":0,\"AL1bit0\":0,\"AL1bit1\":0,\"AL1bit5\":0,\"AL1bit6\":0,\"AL1bit7\":0,\"AL1bit8\":0,\"AL1bit9\":0,\"AL1bit10\":0,\"AL1bit11\":0,\"AL1bit12\":0,\"AL1bit13\":0,\"AL1bit14\":0,\"AL1bit15\":1,\"AL2bit0\":0,\"AL2bit1\":0,\"OP1bit0\":0,\"OP1bit1\":0,\"OP1bit6\":0,\"OP1bit7\":0,\"TS1bit0\":0,\"TS1bit1\":0,\"TS1bit2\":0,\"TS1bit3\":0,\"TS1bit4\":0,\"TS1bit5\":0,\"TS1bit6\":0}";
        LddCurrentData data = JSONUtil.parseObject(dataStr, LddCurrentData.class);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}

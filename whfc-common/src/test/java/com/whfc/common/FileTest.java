package com.whfc.common;

import com.whfc.common.util.ImageUtil;
import org.junit.Test;
import org.springframework.util.StreamUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;

public class FileTest {

    @Test
    public void test() throws Exception {
        String path = "C:\\yjbh.txt";
        String dir = "c:\\yjbh_avatar1";
        Map<String, String> map = new TreeMap<>();
        try (Scanner scanner = new Scanner(new File(path))) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                System.out.println(line); // 处理每一行
                String[] split = line.split(",");
                map.put(split[0], split[1]);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        System.out.println("map:" + map.size());
        for (String key : map.keySet()) {
            String val = map.get(key);
            InputStream is = ImageUtil.compressPicForScale(ImageUtil.getFileStream(val), 150);
            //HttpUtil.download(val, dir + File.separator + key + ".jpg");
            StreamUtils.copy(is, new FileOutputStream(dir + File.separator + key + ".jpg"));
        }
    }

    @Test
    public void test1() {
        int siaCnt = 2;
        String sisNo = "SC0XX/SIS/002";
        String seq = sisNo.substring(sisNo.indexOf("SIS/") + 4);
        String siaNo = String.format("SIA%s%s", seq, (char) ('A' + siaCnt - 1));
        System.out.println(seq);
        System.out.println(siaNo);

    }
}

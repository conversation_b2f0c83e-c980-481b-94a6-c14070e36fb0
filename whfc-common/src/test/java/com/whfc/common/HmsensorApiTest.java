package com.whfc.common;

import com.whfc.common.third.hmsensor.HmsensorApi;
import com.whfc.common.third.hmsensor.entity.Alarm;
import com.whfc.common.third.hmsensor.entity.Device;
import com.whfc.common.third.hmsensor.entity.Param;
import com.whfc.common.third.hmsensor.entity.RealData;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/4 15:59
 */
public class HmsensorApiTest {

    private String usr = "WHFC";
    private String pwd = "123456";
    private String cod = "302700000001";
    private HmsensorApi api = new HmsensorApi(usr, pwd);

    @Test
    public void testGetDevice() {
        List<Device> list = api.getDeviceList();
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetData() {
        List<RealData> list = api.getDeviceData(cod);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetAlarm() {
        Date start = DateUtil.parseDate("2023-03-01", "yyyy-MM-dd");
        Date end = DateUtil.parseDate("2023-04-01", "yyyy-MM-dd");
        List<Alarm> list = api.getAlarmData(cod, start, end);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testGetParam() {
        Param param = api.getParam(cod);
        System.out.println(JSONUtil.toPrettyString(param));
    }
}

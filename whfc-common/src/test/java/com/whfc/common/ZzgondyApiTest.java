package com.whfc.common;

import com.whfc.common.third.zzgondy.ZzgondyApi;
import com.whfc.common.third.zzgondy.entity.Device;
import com.whfc.common.third.zzgondy.entity.HistoryData;
import com.whfc.common.third.zzgondy.entity.RealData;
import com.whfc.common.third.zzgondy.entity.RelayInfo;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 10:36
 */
public class ZzgondyApiTest {

    private String username = "24053101";
    private String password = "123456";
    private ZzgondyApi api = new ZzgondyApi(username, password);
    private String token = "interface_1143_e0daaf11270c424985e871d6a186dfe420240722091224";
    private String deviceNUm = "GDZS24053101";

    @Test
    public void testGetToken() {
        String token = api.getToken();
        System.out.println(token);
    }

    @Test
    public void testGetDeviceList() {
        List<Device> data = api.getDeviceList(token);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetDeviceInfo() {
        Device data = api.getDeviceInfo(token, "GDZS24053101");
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetRealData() {
        RealData data = api.getRealData(token, "GDZS24053102");
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetHistoryData() {
        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date endTime = DateUtil.getDateEnd(now);
        Integer pageNum = 1;
        Integer pageSize = 20;
        List<HistoryData> data = api.getHistoryData(token, deviceNUm, startTime, endTime, pageNum, pageSize);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetRelayInfo() {
        List<RelayInfo> data = api.getRelayInfo(token, deviceNUm);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetRelayStatus() {
        List<RelayInfo> data = api.getRelayStatus(token, deviceNUm);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testReplayOpen() {
        api.replayControl(token, deviceNUm, "1", 1);
    }
}

package com.whfc.common;

import com.whfc.common.third.energy.TQResultDTO;
import com.whfc.common.third.energy.TQUtils;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.net.URLEncoder;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/6 15:07
 */
public class TQUtilsTest {

    String authCode = "179ffa35f9212095a51677491bab86a8";
    String url = "http://api1.tqdianbiao.com/Api/DataRequest";

    TQUtils api = new TQUtils(authCode, url);

    @Test
    public void testWater() {
        List<TQResultDTO> list = api.getWaterHistoryData();
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testElectric() {
        List<TQResultDTO> list = api.getElectricHistoryData();
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void test() throws Exception {
        System.out.println(URLEncoder.encode("2023-04-06 08:00:00", "UTF-8"));
        System.out.println(URLEncoder.encode("2023-04-06 18:00:00", "UTF-8"));
    }
}

package com.whfc.common;

import cn.hutool.core.date.DateUtil;
import com.whfc.common.result.Result;
import com.whfc.common.third.jdy.JdyApi;
import com.whfc.common.third.jdy.entity.App;
import com.whfc.common.third.jdy.entity.Form;
import com.whfc.common.third.jdy.entity.User;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.Collections;
import java.util.Date;
import java.util.List;

public class JdyApiTest {

    private final String apiKey = "aVupTmctzra4q9g1E0gW68YdGYUo73EW";

    private final JdyApi api = new JdyApi(apiKey);

    @Test
    public void testGetApps() {
        List<App> data = api.getApps();
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetForms() {
        String appId = "6859088f36ff89d1548b79e3";
        List<Form> data = api.getForms(appId);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testGetData() {
        String appId = "6859088f36ff89d1548b79e3";
        String entryId = "685908caf255d86b92de12c8";
        Date startDate = DateUtil.parseDate("2025-07-12");
        Date endDate = DateUtil.parseDate("2025-07-12");
        Integer size = api.countData(appId, entryId, startDate, endDate);
        System.out.println(size);
    }

    @Test
    public void testGetUser() {
        String username = "songzhu1";
        User user = api.getUser(username);
        System.out.println(JSONUtil.toPrettyString(user));
    }

    @Test
    public void testCreateUser() {
        String username = "songzhu13";
        String name = "TEST2";
        List<Integer> departments = Collections.singletonList(3);
        Result result = api.createUser(username, name, departments);
        System.out.println(result);
    }

    @Test
    public void testUpdateUser() {
        String username = "songzhu1";
        String name = "TEST1";
        List<Integer> departments = Collections.singletonList(3);
        Result result = api.createUser(username, name, departments);
        System.out.println(result);
    }

    @Test
    public void testDeleteUser() {
        String username = "songzhu1";
        String data = api.deleteUser(username);
        System.out.println(data);
    }
}

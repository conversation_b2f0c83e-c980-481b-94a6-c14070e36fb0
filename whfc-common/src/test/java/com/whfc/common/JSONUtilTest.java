package com.whfc.common;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.obd.ObdFrameItem;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-10-29 19:44
 */
public class JSONUtilTest {

    @Test
    public void testParseArray() {
        List<ObdFrameItem> obdFrameItemList = new ArrayList<>();
        obdFrameItemList.add(new ObdFrameItem("a", 1));
        obdFrameItemList.add(new ObdFrameItem("b", 2));

        String json = JSONUtil.toString(obdFrameItemList);
        System.out.println(json);

        List<ObdFrameItem> list = JSONUtil.parseArray(json, ObdFrameItem.class);
        for (ObdFrameItem item : list) {
            System.out.println("name : " + item.getValue() + ",value: " + item.getValue());
        }
    }

    @Test
    public void test1(){
        String str = null;
        ObdFrameItem frameItem = JSONUtil.parseObject(str, ObdFrameItem.class);
        System.out.println(frameItem);
    }

    @Test
    public void test(){
        JSONObject json = new JSONObject();
        json.put("SN","aaa");
        System.out.println(json.toJSONString());
    }
}

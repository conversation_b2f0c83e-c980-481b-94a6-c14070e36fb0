package com.whfc.common;

import com.whfc.common.obd.ObdDataUtil;
import com.whfc.common.obd.ObdFrame;
import com.whfc.common.util.JSONUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static com.whfc.common.obd.ObdDataUtil.*;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-10-19 11:12
 */
public class ObdDataUtilTest {

    @Test
    public void testFrameData101() {

        ObdFrame data101 = ObdDataUtil.parseObdFrame(ObdDataUtil.ID_101, "00220100200000");
        System.out.println(JSONUtil.toPrettyString(data101));

    }

    @Test
    public void testFrameData102() {

        ObdFrame data = ObdDataUtil.parseObdFrame(ObdDataUtil.ID_102, "00007f1cffe40001");
        System.out.println(JSONUtil.toPrettyString(data));

    }

    @Test
    public void testFrameData111() {

        int[] data = new int[]{
                0x39,
                0x01,
                0x1e,
                0x00,
                0x2a,
                0x00,
                0x1e,
                0x00
        };
        List data111 = ObdDataUtil.parseObdData111(data);
        System.out.println(JSONUtil.toPrettyString(data111));

    }

    @Test
    public void testFrameData112() {

        int[] data = new int[]{
                0x39,
                0x01,
                0x1e,
                0x00,
                0x2a,
                0x00,
                0x1e,
                0x00
        };
        List data112 = ObdDataUtil.parseObdData112(data);
        System.out.println(JSONUtil.toPrettyString(data112));

    }

    @Test
    public void testFrameData113() {

        int[] data = new int[]{
                0x00,
                0x00,
                0x84,
                0x00,
                0x00,
                0x00,
                0x88,
                0xfd
        };
        List data113 = ObdDataUtil.parseObdData113(data);
        System.out.println(JSONUtil.toPrettyString(data113));

    }

    @Test
    public void testFrameData114() {
        ObdFrame data114 = ObdDataUtil.parseObdFrame(ObdDataUtil.ID_114, "0a009cff00000000");
        System.out.println(JSONUtil.toPrettyString(data114));
    }

    @Test
    public void testFrameData115() {

        int[] data = new int[]{
                0x00,
                0x00,
                0x00,
                0x00,
                0x00,
                0x00,
                0x33,
                0x00
        };
        List data115 = ObdDataUtil.parseObdData115(data);
        System.out.println(JSONUtil.toPrettyString(data115));

    }

    @Test
    public void testFrameData116() {

        int[] data = new int[]{
                0x39,
                0x01,
                0x1e,
                0x00,
                0x2a,
                0x00,
                0x1e,
                0x00
        };
        List data116 = ObdDataUtil.parseObdData116(data);
        System.out.println(JSONUtil.toPrettyString(data116));

    }

    @Test
    public void testFrameData131() {
        //8c00060036000000
        //1a01240020000000
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_131, "1a01240020000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData132() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_132, "1a00000000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData134() {
        //fefff9ff00000000
        //feff000000000000
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_134, "feff000000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData135() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_135, "f82ae40300000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData136() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_136, "eb12640900000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData151() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_151, "eb12640900000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData152() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_152, "0000000029010000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData181() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_181, "000000009405220a");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData182() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_182, "0000170004020000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData183() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_183, "6400fb0200000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData184() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_184, "0200020000000100");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData185() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_185, "0100000000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData186() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_186, "0100010000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData187() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_187, "f4ff000000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData188() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_188, "f1fffffff1ffffff");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData189() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_189, "0900000009000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData18C() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_18C, "0000900200212000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData281() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_281, "3400400000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData282() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_282, "00b8020000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData283() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_283, "a9098f0126100000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData284() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_284, "0000230f4c0ce70f");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData285() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_285, "e70f9f0fcf0f0000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData286() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_286, "0000c30fcf0fdb0f");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData287() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_287, "99009d0400000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData288() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_288, "f001380200000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData289() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_289, "d101000000000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData28A() {
        ObdFrame frame = ObdDataUtil.parseObdFrame(ID_28A, "f101370200000000");
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    @Test
    public void testFrameData0CF00400() {

        int[] data = new int[]{
                0x06,
                0x7d,
                0x7d,
                0x00,
                0x00,
                0x03,
                0xf0,
                0x7d
        };
        List list = ObdDataUtil.parseObdData0CF00400(data);
        System.out.println(JSONUtil.toPrettyString(list));

    }

    @Test
    public void testFrameData0C000003() {

        int[] data = new int[]{
                0x01,
                0x8a,
                0x17,
                0x00,
                0xff,
                0xff,
                0xff,
                0xff
        };
        List list = ObdDataUtil.parseObdData0C000003(data);
        System.out.println(JSONUtil.toPrettyString(list));

    }


    @Test
    public void testDM_SIGNAL() {

        //
        String data = "433f00000000ffff";
        //List list = ObdDataUtil.parseObdData18FECA00(ObdDataUtil.translateFrameData(data));
        List list = ObdDataUtil.parseObdData(ObdDataUtil.ID_18FECA00, data);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testDM_BAM() {

        //
        String data1 = "20120003ffcafe00";
        //List list = ObdDataUtil.parseObdData18ECFF00(ObdDataUtil.translateFrameData(data1));
        List list = ObdDataUtil.parseObdData(ObdDataUtil.ID_18ECFF00, data1);
        System.out.println(JSONUtil.toPrettyString(list));


    }

    @Test
    public void testDM_PACKET() {

        List<String> frameList = Arrays.asList("01003f55f0e40156", "02f0e4013af0e501", "0369100b01ffffff", "044051004bfa00d9", "05a04b0000000000");
        ObdFrame frame = ObdDataUtil.parseObdFrame18EBFF00(frameList);
        System.out.println(JSONUtil.toPrettyString(frame));
    }

    /**
     * 耗油
     */
    @Test
    public void testFrameData18FEE900() {
        //000000005c010000
        ObdFrame frame1 = ObdDataUtil.parseObdFrame(ObdDataUtil.ID_18FEE900, "000000006b000000");
        ObdFrame frame2 = ObdDataUtil.parseObdFrame(ObdDataUtil.ID_18FEE900, "030000006e000000");
        System.out.println(JSONUtil.toPrettyString(frame1));
        System.out.println(JSONUtil.toPrettyString(frame2));
    }

    @Test
    public void testTranslate1() {
        String data = "f58c46ffffffffff";
        int[] arr = ObdDataUtil.translateFrameData(data);
        for (int i = 0; i < arr.length; i++) {
            System.out.println(arr[i] + " -- " + Integer.toHexString(arr[i]));
        }
        String data1 = ObdDataUtil.translateFrameData(arr);
        Assert.assertEquals(data, data1);
    }

    @Test
    public void testTranslate2() {
        String data = "058c46ffffffffff";
        int[] arr = ObdDataUtil.translateFrameData(data);
        for (int i = 0; i < arr.length; i++) {
            System.out.println(arr[i] + " -- " + Integer.toHexString(arr[i]));
        }
        String data1 = ObdDataUtil.translateFrameData(arr);
        Assert.assertEquals(data, data1);
    }

    @Test
    public void test() {
        int b = 200;
        int b1 = b & 0xff;
        System.out.println(Integer.toBinaryString(b));
        System.out.println(b1);
        System.out.println(Integer.toBinaryString(b1));
    }
}

package com.whfc.common;

import com.whfc.common.face.szyc.*;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.RandomUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-08 17:59
 */
public class SzycTest {

    @Test
    public void testQueryPerson() {
        QueryPerson queryPerson = new QueryPerson(RandomUtil.getRandomStr(16));
        System.out.println(JSONUtil.toPrettyString(queryPerson));

        String data = "{\"messageId\":\"ID:localhost-637046811507388956:23952:65:48\", \"operator\": \"QueryPerson-Ack\", \"info\": { \"facesluiceId\":\"5d0848e581c3e6f1938a035f\", \"customId\":\",03c81e0fce1846c696cdb7e049230f11,03c81e0fce1846c696cdb7e049230f11\", \"result\":\"ok\"} }";
        QueryPersonAck ack = JSONUtil.parseObject(data, QueryPersonAck.class);
        System.out.println(JSONUtil.toPrettyString(ack));
    }

    @Test
    public void testSearchPerson() {
        Person p = new Person();
        p.setCustomId("111");
        p.setPicture(1);
        SearchPerson searchPerson = new SearchPerson(RandomUtil.getRandomStr(16), p);
        System.out.println(JSONUtil.toPrettyString(searchPerson));

        String data = "{\n" +
                "\t\"messageId\": \"ID:localhost-637046811507388956:23952:65:48\",\n" +
                "\t\"operator\": \"SearchPerson-Ack\",\n" +
                "\t\"info\": {\n" +
                "\t\t\"facesluiceId\": \"5d0848e581c3e6f1938a035f\",\n" +
                "\t\t\"personId\": \"5\",\n" +
                "\t\t\"customId\": \"063c81e0fce184c696cdb7e049230f5e\",\n" +
                "\t\t\"name\": \"张三\",\n" +
                "\t\t\"gender\": \"0\",\n" +
                "\t\t\"idCard\": \"******************\",\n" +
                "\t\t\"address\": \" \",\n" +
                "\t\t\"creatTime\": \"2019-09-30T09:13:48\",\n" +
                "\t\t\"telnum1\": \" \",\n" +
                "\t\t\"personType\": \"0\",\n" +
                "\t\t\"cardNum2\": \"0\",\n" +
                "\t\t\"result\": \"ok\"\n" +
                "\t}\n" +
                "}";

        SearchPersonAck ack = JSONUtil.parseObject(data, SearchPersonAck.class);
        System.out.println(JSONUtil.toPrettyString(ack));
    }

    @Test
    public void testRecPush() {
        String data = "{\n" +
                "\t\"operator\": \"RecPush\",\n" +
                "\t\"info\": {\n" +
                "\t\t\"customId\": \"063c81e0fce184c696cdb7e049230f5e \",\n" +
                "\t\t\"personId\": \"41\",\n" +
                "\t\t\"direction\": \"entr\",\n" +
                "\t\t\"otype\": \"1\",\n" +
                "\t\t\"personName\": \"张三\",\n" +
                "\t\t\"facesluiceId\": \"1305433\",\n" +
                "\t\t\"facesluiceName\": \"Face1\",\n" +
                "\t\t\"cardNum2\": \"2\",\n" +
                "\t\t\"time\": \"2018-03-07 14:01:01\",\n" +
                "\t\t\"pic\": \"\",\n" +
                "\t\t\"temperature\": \"36.53 \",\n" +
                "\t\t\"temperatureAlarm\": \"0 \"\n" +
                "\t}\n" +
                "}";
        RecPush push = JSONUtil.parseObject(data, RecPush.class);
        System.out.println(JSONUtil.toPrettyString(push));
    }

    @Test
    public void testVerifyPush() {
        String data = "{\n" +
                "\t\"operator\": \"VerifyPush\",\n" +
                "\t\"info\": {\n" +
                "\t\t\"PersonID\": 313,\n" +
                "\t\t\"CreateTime\": \"2021-12-29T11:47:09\",\n" +
                "\t\t\"Similarity1\": 88.426308,\n" +
                "\t\t\"Similarity2\": 0.000000,\n" +
                "\t\t\"VerifyStatus\": 1,\n" +
                "\t\t\"VerfyType\": 1,\n" +
                "\t\t\"PersonType\": 0,\n" +
                "\t\t\"Name\": \"徐国诚\",\n" +
                "\t\t\"Gender\": 0,\n" +
                "\t\t\"Nation\": 0,\n" +
                "\t\t\"CardType\": 0,\n" +
                "\t\t\"IdCard\": \" \",\n" +
                "\t\t\"Birthday\": \"1970-00-00\",\n" +
                "\t\t\"Telnum\": \" \",\n" +
                "\t\t\"Native\": \" \",\n" +
                "\t\t\"Address\": \" \",\n" +
                "\t\t\"Notes\": \"dc668aaae02743a49610bef06073532b\",\n" +
                "\t\t\"MjCardFrom\": 0,\n" +
                "\t\t\"DeviceID\": 1770211,\n" +
                "\t\t\"PushType\": 1,\n" +
                "\t\t\"OpendoorWay\": 0,\n" +
                "\t\t\"MjCardNo\": 0,\n" +
                "\t\t\"RFIDCard\": \"0\",\n" +
                "\t\t\"Tempvalid\": 0,\n" +
                "\t\t\"CustomizeID\": 0,\n" +
                "\t\t\"PersonUUID\": \"dc668aaae02743a49610bef06073532b\",\n" +
                "\t\t\"ValidBegin\": \"0000-00-00T00:00:00\",\n" +
                "\t\t\"ValidEnd\": \"0000-00-00T00:00:00\",\n" +
                "\t\t\"Sendintime\": 1,\n" +
                "\t\t\"Direction\": 1\n" +
                "\t},\n" +
                "\t\"RegisteredPic\": \"\"\n" +
                "}";
        VerifyPush push = JSONUtil.parseObject(data, VerifyPush.class);
        System.out.println(JSONUtil.toPrettyString(push));
    }

    @Test
    public void testPushAck() {
        PushAckInfo info = new PushAckInfo("1", "311");
        PushAck ack = new PushAck(info);
        System.out.println(JSONUtil.toPrettyString(ack));
    }
}

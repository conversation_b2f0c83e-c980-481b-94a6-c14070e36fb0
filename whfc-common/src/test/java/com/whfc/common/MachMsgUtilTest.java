package com.whfc.common;

import com.whfc.common.enums.DeviceType;
import com.whfc.common.enums.WorkState;
import com.whfc.common.iot.mach.entity.*;
import com.whfc.common.iot.mach.util.MachMsgUtil;
import com.whfc.common.obd.ObdDataUtil;
import com.whfc.common.obd.ObdFrame;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/29 16:29
 */
public class MachMsgUtilTest {

    public static void main(String[] args) {
        Date now = new Date();

        MachMsg0x05 msg = new MachMsg0x05();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.obd.getValue());
        msg.setDeviceId(3028);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x05);

        msg.setTime(now);
        msg.setStatus(1);

        msg.setFrameType(0x00);
        msg.setFrameRtr(0x01);
        msg.setFrameDlc(8);

        //
        msg.setFrameId(0x18ECFF00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("20120003ffcafe00"));

        msg.setFrameId(0x18EBFF00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("01003f55f0e40156"));
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("02f0e4013af0e501"));
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0369100b01ffffff"));

        msg.setFrameId(0x18FECA00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("033f00000000ffff"));

        //1轮
        msg.setFrameId(0x113L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0101010101000100"));

//        //2-3轮
//        msg.setFrameId(0x114L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("000018fd00000000"));
//
//        //4轮
//        msg.setFrameId(0x115L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0000000000000300"));
//
//        //总油耗
//        msg.setFrameId(0x18fee900L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0100000033000000"));
//
//        msg.setFrameId(0x0152L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0100000033000000"));

//        //发动机转速
//        msg.setFrameId(0x0CF00400L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("267d8a6a1703f38a"));
//
//        //总里程1
//        msg.setFrameId(0x120L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("ab18000000000000"));
//
//        //总里程2
//        msg.setFrameId(0x121L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("3c0f000000000000"));

//        msg.setBatteryState(0x01);
        //      msg.setBatteryPower(90);

        msg.setLngFlag("E");
        msg.setLatFlag("N");
        msg.setLngWgs84(112.38223816666667D);
        msg.setLatWgs84(29.993149D);
        msg.setMasterVer("1.0.0");
        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x01() {

        Date now = new Date();
        List<MachWorkState> workStates = new ArrayList<>();
        workStates.add(new MachWorkState(DateUtil.addMinutes(now, -2), WorkState.RUN.getValue()));
        workStates.add(new MachWorkState(DateUtil.addMinutes(now, -1), WorkState.RUN.getValue()));
        workStates.add(new MachWorkState(DateUtil.addMinutes(now, -0), WorkState.RUN.getValue()));

        MachMsg0x01 msg = new MachMsg0x01();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(1);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x01);

        msg.setWorkStates(workStates);
        msg.setBatteryState(0x01);
        msg.setBatteryPower(10);
        msg.setDeviceTemp(-23.6D);
        msg.setLngFlag("E");
        msg.setLatFlag("N");
        msg.setLngWgs84(112.21623816666667D);
        msg.setLatWgs84(29.943149D);
        msg.setOilPos(450);
        msg.setPressure(4.47);
        msg.setOilTemp(34.5D);
        msg.setOilState(0x01);
        msg.setOilPower(0x1F);
        msg.setMasterVer("1.0.0");
        msg.setSlaveVer("1.0.0");
        msg.setSpeed(68D);
        msg.setElevation(3500D);
        msg.setRotationX(67D);
        msg.setRotationY(62D);
        msg.setRotationZ(69D);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

        String payload = MachMsgUtil.encode(msg);
        System.out.println(payload);

    }

    @Test
    public void testData0x02() {

        MachMsg0x02 msg = new MachMsg0x02();

        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        //msg.setLength(data0x02.getDataLength() + 11)
        msg.setSeq(0x01);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x02);

        msg.setMasterOrSlave(1);
        msg.setVer("1.0.12");
        msg.setSubPackageTotal(0x88);
        msg.setSubPackageSeq(0x00);
        msg.setResult(1);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x03() {

        MachMsg0x03 msg = new MachMsg0x03();

        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        //msg.setLength(data0x02.getDataLength() + 11)
        msg.setSeq(0x01);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x03);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

        String payload = MachMsgUtil.encode(msg);
        System.out.println(payload);
    }

    @Test
    public void testData0x04() {

        Date now = new Date();
        MachMsg0x04 msg = new MachMsg0x04();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.equip.getValue());
        msg.setDeviceId(2299);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x04);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x04);

        msg.setBatteryPower(1);
        msg.setLngFlag("E");
        msg.setLatFlag("N");
        msg.setLngWgs84(112.21623816666667D);
        msg.setLatWgs84(29.943149D);
        msg.setMasterVer("1.0.0");
        msg.setWorkState(new MachWorkState(DateUtil.addMinutes(now, -0), WorkState.STOP.getValue()));
        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

    }

    @Test
    public void testData0x05() {

        Date now = new Date();

        MachMsg0x05 msg = new MachMsg0x05();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.obd.getValue());
        msg.setDeviceId(3024);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);
        msg.setCmd(MsgConst.UPSTREAM_CMD_0x05);

        msg.setTime(now);
        msg.setStatus(1);

        msg.setFrameType(0x00);
        msg.setFrameRtr(0x01);
        msg.setFrameDlc(8);

        //
        msg.setFrameId(0x18ECFF00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("20120003ffcafe00"));

        msg.setFrameId(0x18EBFF00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("01003f55f0e40156"));
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("02f0e4013af0e501"));
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0369100b01ffffff"));

        msg.setFrameId(0x18FECA00);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("033f00000000ffff"));

        //1轮
        msg.setFrameId(0x113L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("000090010000e5ff"));

        //2-3轮
        msg.setFrameId(0x114L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("000018fd00000000"));

        //4轮
        msg.setFrameId(0x115L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0000000000000300"));

        //总油耗
        msg.setFrameId(0x18fee900L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0100000033000000"));

        msg.setFrameId(0x0152L);
        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("0100000033000000"));

//        //发动机转速
//        msg.setFrameId(0x0CF00400L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("267d8a6a1703f38a"));
//
//        //总里程1
//        msg.setFrameId(0x120L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("ab18000000000000"));
//
//        //总里程2
//        msg.setFrameId(0x121L);
//        msg.setFrameDataBytes(ObdDataUtil.translateFrameData("3c0f000000000000"));

//        msg.setBatteryState(0x01);
        //      msg.setBatteryPower(90);

        msg.setLngFlag("E");
        msg.setLatFlag("N");
        msg.setLngWgs84(112.38223816666667D);
        msg.setLatWgs84(29.993149D);
        msg.setMasterVer("1.0.0");
        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x06() {

        Date now = new Date();

        MachMsg0x06 msg = new MachMsg0x06();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.obd.getValue());
        msg.setDeviceId(3304);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);

        msg.setTime(now);
        msg.setStatus(1);

        ObdFrame frame1 = new ObdFrame();
        frame1.setFrameId(Long.valueOf(ObdDataUtil.ID_101));
        frame1.setFrameData("0003000020040000");
        ObdFrame frame2 = new ObdFrame();
        frame2.setFrameId(Long.valueOf(ObdDataUtil.ID_114));
        frame2.setFrameData("0a009cff00000000");

        ObdFrame frame3 = new ObdFrame();
        frame3.setFrameId(Long.valueOf(ObdDataUtil.ID_152));
        frame3.setFrameData("0a009cff00000000");

        List<ObdFrame> frameList = Arrays.asList(frame1, frame2, frame3);

        msg.setFrameNum(frameList.size());
        msg.setFrameList(frameList);

        msg.setBatteryState(0x01);
        msg.setBatteryPower(101);

        msg.setLngFlag("E");
        msg.setLatFlag("N");
        msg.setLngWgs84(112.21623816666667D);
        msg.setLatWgs84(29.943149D);
        msg.setMasterVer("1.0.0");

        ByteBuf buf = Unpooled.buffer(msg.getTotalLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getTotalLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x07() {
        MachMsg0x07 msg = new MachMsg0x07();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(70);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x08() {

        Date now = new Date();

        MachWarnrotation rotation = new MachWarnrotation();
        rotation.setRotationX(11);
        rotation.setRotationZ(12);
        rotation.setWarnTime(now);

        MachMsg0x08 msg = new MachMsg0x08();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);
        msg.setRotation(rotation);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x09() {

        Date now = new Date();
        MachMsg0x09 msg = new MachMsg0x09();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.equip.getValue());
        msg.setDeviceId(2299);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x04);

        List<MachGps> gpsList = Arrays.asList(
                new MachGps("E", "N", 112.21623816D, 29.943149D),
                new MachGps("E", "N", 112.21623817D, 29.943148D)
        );

        msg.setWorkState(new MachWorkState(DateUtil.addMinutes(now, -0), WorkState.STOP.getValue()));
        msg.setBatteryPower(1);
        msg.setGpsNum(gpsList.size());
        msg.setGpsList(gpsList);
        msg.setMasterVer("1.0.0");

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x0A() {

        Date now = new Date();
        MachMsg0x0A msg = new MachMsg0x0A();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.equip.getValue());
        msg.setDeviceId(2299);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x04);

        msg.setTime(now);
        msg.setPressure(20.1D);
        msg.setOilPos(300);
        msg.setTemp(-10.0D);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);
    }

    @Test
    public void testData0x0B() {

        MachMsg0x0B msg = new MachMsg0x0B();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        msg.setSeq(0x01);

        msg.setResult(0x00);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

    }

    @Test
    public void testData0x0C() {

        MachMsg0x0C msg = new MachMsg0x0C();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        msg.setSeq(0x01);

        msg.setTime(new Date());
        msg.setAccX(11.2F);
        msg.setAccY(12.3F);
        msg.setAccZ(13.4F);
        msg.setGyroX(7.5F);
        msg.setGyroY(6.4F);
        msg.setGyroZ(5.3F);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

    }

    @Test
    public void testData0x87() {

        MachParamRotary rotary = new MachParamRotary();
        rotary.setLeft(3);
        rotary.setRight(5);
        rotary.setFront(3);
        rotary.setBack(6);

        MachParamLoraAppEui appEui = new MachParamLoraAppEui();
        appEui.setAppEui("FFFFFE0000000001");

        MachParamLoraAppKey appKey = new MachParamLoraAppKey();
        appKey.setAppKey("2B7E151628AED2A6ABF7158809CF4F3C");

        MachParamThreshold threshold = new MachParamThreshold();
        threshold.setThresHoldList(Arrays.asList(
                new MachThresHold(0x01, 1.1F),
                new MachThresHold(0x02, 1.2F))
        );
        threshold.setCount(threshold.getThresHoldList().size());

        MachMsg0x87 msg = new MachMsg0x87();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(70);
        //msg.setLength(data0x01.getDataLength() + 11);
        msg.setSeq(0x01);
        msg.setParamList(Arrays.asList(appEui, appKey, threshold));
        msg.setParamNum(msg.getParamList().size());

        System.out.println(msg);

        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        System.out.println(ByteBufUtil.prettyHexDump(buf));

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        MachMsgUtil.encode(buf1, msg1);
        System.out.println(msg1);
    }

    @Test
    public void testData0x8B() {

        MachMsg0x8B msg = new MachMsg0x8B();
        msg.setStartFlag(MsgConst.START_FLAG);
        msg.setDeviceType(DeviceType.mach.getValue());
        msg.setDeviceId(0x01);
        msg.setSeq(0x01);

        msg.setSubCmd(0x00);
        msg.setSpeed(0.9);
        ByteBuf buf = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf, msg);

        MachMsg msg1 = MachMsgUtil.decode(buf);
        ByteBuf buf1 = Unpooled.buffer(msg.getLength());
        MachMsgUtil.encode(buf1, msg1);

        System.out.println(ByteBufUtil.prettyHexDump(buf));
        System.out.println(ByteBufUtil.prettyHexDump(buf1));

        System.out.println(msg);
        System.out.println(msg1);

    }

    @Test
    public void testDecode() {
        String payload = "VaoBACN7zgBFAQUZAhkRERIFGQIZERESBRkCGREREgNkB+kOBgAAAAoAAAAAAAAAAAAAAAAAAAAJAQIAAAAAAAAAAABH";
        MachMsg msg = MachMsgUtil.decode(payload);
        System.out.println(JSONUtil.toPrettyString(msg));
    }
}

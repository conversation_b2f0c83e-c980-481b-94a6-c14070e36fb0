package com.whfc.common;

import com.whfc.common.pdf.OfficeUtil;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/2 10:53
 */
public class OfficeUtilTest {

    String officeHome = "C:\\Program Files\\LibreOffice";
    String office1 = "C:\\Users\\<USER>\\Desktop\\test\\test1.xls";
    String office2 = "C:\\Users\\<USER>\\Desktop\\test\\test2.doc";
    String office3 = "C:\\Users\\<USER>\\Desktop\\test\\test3.doc";
    String pdf1 = "C:\\Users\\<USER>\\Desktop\\test\\test1.pdf";
    String pdf2 = "C:\\Users\\<USER>\\Desktop\\test\\test2.pdf";
    String pdf3 = "C:\\Users\\<USER>\\Desktop\\test\\test3.pdf";
    OfficeUtil officeUtil = new OfficeUtil(officeHome, new int[]{1100, 1101, 1102});


    @Test
    public void test1() {
        officeUtil.office2Pdf(office1, pdf1);
    }

    @Test
    public void test2() {
        officeUtil.office2Pdf(office2, pdf2);
    }

    @Test
    public void test3() {
        officeUtil.office2Pdf(office3, pdf3);
    }

}

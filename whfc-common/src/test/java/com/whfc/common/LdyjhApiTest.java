package com.whfc.common;

import com.whfc.common.third.ldyjh.LdyjhApi;
import com.whfc.common.third.ldyjh.entity.AttendInfo;
import com.whfc.common.third.ldyjh.entity.AttendItem;
import com.whfc.common.third.ldyjh.entity.LdyjhConst;
import com.whfc.common.third.ldyjh.entity.Result;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/7 14:55
 */
public class LdyjhApiTest {

    private String host = "http://www.ldyjh.com";

    private String appKey = "20230061225";

    private String sign = "qrMeqgtT2sgh6rEXsxtrhxceshi";

    private String projectCode = "xawpznkjyxgscs001";

    private String idcardNo = "610114198603052015";

    private LdyjhApi api = new LdyjhApi(host, appKey, sign, projectCode);


    @Test
    public void testInsertAttendInfo() {

        AttendItem item = new AttendItem();
        item.setIdcardnumber(idcardNo);
        item.setDate(DateUtil.formatDateTime(new Date()));
        item.setDirection(LdyjhConst.IN);

        List<AttendItem> attendList = new ArrayList<>();
        attendList.add(item);

        AttendInfo attendInfo = new AttendInfo();
        attendInfo.setAttendanceList(attendList);

        Result result = api.insertAttend(attendInfo);
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testInsertAttendItem() {

        Result result = api.insertAttend(idcardNo, LdyjhConst.IN, DateUtil.formatDateTime(new Date()));
        System.out.println(JSONUtil.toPrettyString(result));
    }

    @Test
    public void testInsertWorker() {

    }

    @Test
    public void testInsertEnter() {

    }
}

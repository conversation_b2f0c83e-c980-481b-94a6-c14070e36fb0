package com.whfc.common;

import com.whfc.common.mail.Email;
import com.whfc.common.mail.EmailParser;
import com.whfc.common.mail.EmailType;
import com.whfc.common.mail.EmailTypeParser;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import org.junit.Test;

import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import java.io.FileInputStream;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.Properties;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/18 16:20
 */
public class EmailParseTest {

    String send = "C:\\Users\\<USER>\\Desktop\\发送邮件.eml";
    String audit = "C:\\Users\\<USER>\\Desktop\\审批邮件.eml";
    String f1 = "C:\\Users\\<USER>\\Desktop\\email\\1.eml";
    String f2 = "C:\\Users\\<USER>\\Desktop\\email\\2.eml";
    String f3 = "C:\\Users\\<USER>\\Desktop\\email\\3.eml";
    String f4 = "C:\\Users\\<USER>\\Desktop\\email\\4.eml";
    String f5 = "C:\\Users\\<USER>\\Desktop\\email\\5.eml";
    String f6 = "C:\\Users\\<USER>\\Desktop\\email\\6.eml";
    String f7 = "C:\\Users\\<USER>\\Desktop\\email\\7.eml";
    String f8 = "C:\\Users\\<USER>\\Desktop\\email\\8.eml";

    @Test
    public void test1() throws Exception {
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props);
        FileInputStream fis = new FileInputStream(f8);
        MimeMessage message = new MimeMessage(session, fis);
        Email mail = EmailParser.parse(message);

        System.out.println("subject:" + mail.getSubject());
        System.out.println("from:" + mail.getFrom());
        System.out.println("to:" + mail.getTo());
        System.out.println("cc:" + mail.getCc());
        System.out.println("bcc:" + mail.getBcc());
        System.out.println("sentDate:" + mail.getSentDate());
        System.out.println("messageId:" + mail.getMessageId());
        System.out.println("attachments:" + mail.getAttachments());

        EmailType mailType = EmailTypeParser.parse(mail.getSubject(), mail.getTextContent());
        System.out.println("letterReference:" + mailType.getLetterReference());
        System.out.println("date:" + mailType.getDate());
        System.out.println("subject:" + mailType.getSubject());
    }

    @Test
    public void test5() {
        String str = "8 Jan 2024";
        Date date = DateUtil.parseDate(str, "d MMM yyyy", java.util.Locale.ENGLISH);
        System.out.println(DateUtil.formatDate(date));
        System.out.println(DateUtil.formatDate(date,"d-MMM-yy", java.util.Locale.ENGLISH));
        System.out.println(DateUtil.formatDate(date,"MMM yyyy", java.util.Locale.ENGLISH));

    }

    @Test
    public void test6() {
        //String from = "=?GBK?B?08q8/tb6ytY=?= <<EMAIL>>";
        String from = "=?GBK?B?****************************?= <<EMAIL>>";
        System.out.println(from);
        String[] str = from.split(" ");
        String name = str[0];
        String email = str[1];
        String[] nameArr = name.split("\\?");
        String charset = nameArr[1];
        String base64 = nameArr[3];
        String encodeName = new String(Base64Util.decode(base64), Charset.forName(charset));
        System.out.println(encodeName + " " + email);
    }

}

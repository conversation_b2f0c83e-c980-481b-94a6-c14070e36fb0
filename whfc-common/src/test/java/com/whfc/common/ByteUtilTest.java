package com.whfc.common;

import com.whfc.common.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import org.junit.Test;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-10-19 10:56
 */
public class ByteUtilTest {

    @Test
    public void testGetBit() {
        int value = 0xFF;
        System.out.print(ByteUtil.getBit(value, 7));
        System.out.print(ByteUtil.getBit(value, 6));
        System.out.print(ByteUtil.getBit(value, 5));
        System.out.print(ByteUtil.getBit(value, 4));
        System.out.print(ByteUtil.getBit(value, 3));
        System.out.print(ByteUtil.getBit(value, 2));
        System.out.print(ByteUtil.getBit(value, 1));
        System.out.print(ByteUtil.getBit(value, 0));
    }

    @Test
    public void testGetBitStr() {
        int value = 0x08;
        System.out.println("十进制:" + value);
        System.out.println(ByteUtil.getBitStr(value, 0, 3));
        System.out.println(ByteUtil.getBit(value, 0, 3));
    }

    @Test
    public void testLow2ByteToInt() {
        int[] bytes = new int[]{
                0x02,
                0xFF
        };
        int x = ByteUtil.low2ByteToIntBE(bytes);
        System.out.println(x);
        System.out.println(Integer.toHexString(x));
        System.out.println(ByteUtil.getSignedShort(0x02,0xFF));
    }

    @Test
    public void test() {
        for (int i = 0; i <= 255; i++) {
            String hexStr = com.whfc.common.iot.util.ByteUtil.toHexString(i);
            int data = com.whfc.common.iot.util.ByteUtil.fromHexString(hexStr);
            System.out.println(hexStr + " -- " + data);
        }
    }

    @Test
    public void test1() {

        int[] bytes = new int[]{
                //0x3a, 0x15, 0xf9, 0x06, 0xba, 0x15
                0x00, 0x2e, 0xa4, 0x3d, 0xa9, 0xe4
                //0xff,0xff,0xff,0xff,0xff,0xff
        };

        long l = com.whfc.common.iot.util.ByteUtil.low6ByteToLong(bytes);
        System.out.println(l);
        System.out.println(Long.toHexString(l));
    }

    @Test
    public void test2() {
        int[] bytes = new int[]{
                //0x3a, 0x15, 0xf9, 0x06, 0xba, 0x15
                //0x00, 0x2e, 0xa4, 0x3d, 0xa9, 0xe4
                //0xff,0xff,0xff,0xff,0xff,0xff
                0x3A, 0x15, 0xF9, 0x07, 0x36, 0x82
        };
        ByteBuf buf = Unpooled.buffer(6);
        for (int b : bytes) {
            buf.writeByte(b);
        }
        System.out.println(ByteBufUtil.prettyHexDump(buf));
        byte[] dst = new byte[6];
        buf.readBytes(dst);
        long l = com.whfc.common.iot.util.ByteUtil.low6ByteToLong(dst);
        System.out.println(l);
        System.out.println(Long.toHexString(l));
    }

    @Test
    public void test3() {
        System.out.println(Long.toHexString(200324000228L));
        System.out.println(Long.toHexString(63866046691861L));
        System.out.println(Long.toHexString(16));
        System.out.println(Long.toHexString(65535));
    }
}

package com.whfc.common;

import com.whfc.common.util.Base64Util;
import org.junit.Assert;
import org.junit.Test;

import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/3/16 16:42
 */
public class Base64UtilTest {

    @Test
    public void test() throws Exception {
        String str = "aHR0cHM6Ly9maWxlLndoZmNpb3QuY29tL21zL3Byby9kb2MvZmlsZS8xNjYvMjAyMi0wOS0xOS8yMDIyMDkxOTEwMTUwMDc2MDcucG5nP0V4cGlyZXM9MTY3ODk1NzYyNyZPU1NBY2Nlc3NLZXlJZD1MVEFJOXZGY3dZT3BJVlh1JlNpZ25hdHVyZT16cGRPcjQ0bTdTdjZYczB1dyUyQjVOcnk5OG8xWSUzRA%3D%3D";
        String src = "https://file.whfciot.com/ms/pro/doc/file/166/2022-09-19/202209191015007607.png?Expires=1678957627&OSSAccessKeyId=LTAI9vFcwYOpIVXu&Signature=zpdOr44m7Sv6Xs0uw%2B5Nry98o1Y%3D";

        String str1 = Base64Util.decodeToString(URLDecoder.decode(str, "utf8"));
        System.out.println(str1);
        Assert.assertEquals(src, str1);

        String dst = URLEncoder.encode(Base64Util.encodeToString(src), "utf8");
        System.out.println(dst);
        Assert.assertEquals(dst, str);

        System.out.println(Base64Util.encodeToString("lpJf+CcXFNQjW6Pf2VwMnw=="));
    }

    @Test
    public void test1() {
        String picture = "-A_B.C.D";

        String picture1 = picture.replaceAll("-", "+")
                .replaceAll("_", "/")
                .replaceAll("\\.", "=");

        System.out.println(picture);
        System.out.println(picture1);
    }
}

package com.whfc.common.geometry;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.io.WKTReader;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 空间几何对象工具
 */
public class GeometryUtil {

    private final static Logger logger = LoggerFactory.getLogger(GeometryUtil.class);

    /**
     * 点正则
     * 例：POINT(114.071 22.5522)
     */
    public static final Pattern POINT_MATCHER = Pattern.compile("(\\d+\\.?\\d+)");
    /**
     * 多边形正则
     * 例：POLYGON((114.285 30.5958, 114.265 30.5946, 114.266 30.5841, 114.282 30.585, 114.285 30.5958))
     */
    public static final Pattern POLYGON_MATCHER = Pattern.compile("(\\d+\\.?\\d+)");

    /**
     * 点
     */
    public static final String POINT = "point";
    /**
     * 多边形
     */
    public static final String POLYGON = "polygon";
    /**
     * 多个多边形
     */
    public static final String MULTIPOLYGON = "multipolygon";
    /**
     * 多条线
     */
    public static final String MULTILINESTRING = "multilinestring";


    /**
     * point解码
     *
     * @param wkt wkt字符串
     * @return 坐标
     */
    public static Point decodePoint(String wkt) {
        if (!StringUtils.isEmpty(wkt)) {
            Matcher matcher = POINT_MATCHER.matcher(wkt);
            List<String> groups = new ArrayList<>(2);
            while (matcher.find()) {
                String group = matcher.group(0);
                groups.add(group);
            }
            Double lng = Double.parseDouble(groups.get(0));
            Double lat = Double.parseDouble(groups.get(1));
            return new Point(lng, lat);
        }
        return new Point();
    }

    /**
     * point编码
     *
     * @param point 坐标
     * @return wkt字符串
     */
    public static String encodePoint(Point point) {
        String pointStr = String.format("POINT(%s %s)", point.getLng(), point.getLat());
        logger.info("point:{}", pointStr);
        return pointStr;
    }

    /**
     * polygon解码
     *
     * @param wkt wkt字符串
     * @return 坐标
     */
    public static Polygon decodePolygon(String wkt) {
        if (!StringUtils.isEmpty(wkt)) {
            Matcher matcher = POLYGON_MATCHER.matcher(wkt);
            List<String> groups = new ArrayList<>(16);
            while (matcher.find()) {
                String group = matcher.group(0);
                groups.add(group);
            }
            int pointNum = groups.size() / 2;
            List<Point> pointList = new ArrayList<>(pointNum);
            for (int i = 0; i < pointNum; i++) {
                Point point = new Point();
                point.setLng(Double.parseDouble(groups.get(2 * i)));
                point.setLat(Double.parseDouble(groups.get(2 * i + 1)));
                pointList.add(point);
            }
            return new Polygon(pointList);
        }
        return new Polygon();
    }

    /**
     * 加密多边形坐标
     *
     * @param list 多边形坐标
     * @return 多边形kwt字符串
     */
    public static String encodeMultiPolygon(List<List<Point>> list) {
        StringBuilder str = new StringBuilder();
        str.append(MULTIPOLYGON).append("(");
        for (int i = 0; i < list.size(); i++) {
            str.append("((");
            List<Point> pointList = list.get(i);
            for (int j = 0; j < pointList.size(); j++) {
                Point point = pointList.get(j);
                str.append(point.getLng()).append(" ").append(point.getLat());
                if (j < pointList.size() - 1) {
                    str.append(",");
                }
            }
            str.append("))");
            if (i < list.size() - 1) {
                str.append(",");
            }
        }
        str.append(")");
        return str.toString();
    }

    /**
     * polygon解码
     *
     * @param wkt wkt字符串
     * @return 坐标
     */
    public static List<List<Point>> decodeMultiPolygon(String wkt) {
        if (!StringUtils.isEmpty(wkt) && wkt.startsWith(MULTIPOLYGON)) {
            String points = wkt.substring(wkt.indexOf("(") + 3, wkt.lastIndexOf(")") - 2);
            points = points.replace(")),((", ";");
            String[] arrays = points.split(";");
            List<List<Point>> pointList = new ArrayList<>();
            for (String str : arrays) {
                List<Point> pointArray = dealString(str);
                pointList.add(pointArray);
            }
            return pointList;
        }
        return Collections.emptyList();
    }

    /**
     * 解析 MultiPolygon
     *
     * @param points 坐标字符串
     * @return 坐标
     */
    private static List<Point> dealString(String points) {
        String[] arrays = points.split(",");
        List<Point> list = new ArrayList<>();
        for (String array : arrays) {
            String str = array;
            str = str.trim();
            String[] ps = str.split(" ");
            if (ps.length == 2) {
                Point point = new Point(Double.valueOf(ps[0]), Double.valueOf(ps[1]));
                list.add(point);
            }
        }
        return list;
    }

    /**
     * polygon编码
     *
     * @param polygon 多边形坐标
     * @return 多边形KWT字符串
     */
    public static String encodePolygon(Polygon polygon) {
        StringBuffer str = new StringBuffer();
        str.append(POLYGON).append("((");
        List<Point> pointList = polygon.pointList;
        for (int i = 0; i < pointList.size(); i++) {
            Point point = pointList.get(i);
            str.append(point.getLng()).append(" ").append(point.getLat());
            if (i < pointList.size() - 1) {
                str.append(",");
            }
        }
        str.append("))");
        logger.info("polygon:{}", str);
        return str.toString();
    }


    /**
     * 计算坐标点到线的距离（米）
     *
     * @param lineStringWkt 线坐标字符串
     * @param lat           纬度
     * @param lng           经度
     * @return 距离（米）
     */
    public static Double getDistanceFromPoint2Line(String lineStringWkt, Double lat, Double lng) {
        double distance = 0.0;
        try {
            GeometryFactory geometryFactory = new GeometryFactory();
            WKTReader reader = new WKTReader();
            Coordinate coord = new Coordinate(lng, lat);
            com.vividsolutions.jts.geom.Point point = geometryFactory.createPoint(coord);
            Geometry read = reader.read(lineStringWkt);
            // 单位米
            distance = read.distance(point) * 1000 * 100;
        } catch (Exception e) {
            logger.warn("获取点到线的距离异常.", e);
        }
        return distance;
    }

    /**
     * line解码
     *
     * @param lineWkt 线坐标字符串
     * @return 线坐标
     */
    public static List<Point> decodeLine(String lineWkt) {
        if (!StringUtils.isEmpty(lineWkt)) {
            Matcher matcher = POINT_MATCHER.matcher(lineWkt);
            List<String> groups = new ArrayList<>(16);
            while (matcher.find()) {
                String group = matcher.group(0);
                System.out.println(group);
                groups.add(group);
            }
            int pointNum = groups.size() / 2;
            List<Point> pointList = new ArrayList<>(pointNum);
            for (int i = 0; i < pointNum; i++) {
                if (i % 2 == 1 && i != pointNum - 1) {
                    continue;
                }
                Point pointDTO = new Point();
                pointDTO.setLng(Double.parseDouble(groups.get(2 * i)));
                pointDTO.setLat(Double.parseDouble(groups.get(2 * i + 1)));
                pointList.add(pointDTO);
            }
            return pointList;
        }
        return Collections.emptyList();
    }

    /**
     * line编码
     * MULTILINESTRING((114.285 30.5958, 114.265 30.5946),(111.285 31.5958, 112.265 32.5946))
     *
     * @param pointList 线坐标字符串
     * @return 线坐标字符串
     */
    public static String encodeLine(List<Point> pointList) {
        int minPointSize = 2;
        if (pointList.size() < minPointSize) {
            return "";
        }
        StringBuilder str = new StringBuilder();
        str.append(MULTILINESTRING).append("(");
        try {
            for (int i = 0; i < pointList.size() - 1; i++) {
                Point point1 = pointList.get(i);
                Point point2 = pointList.get(i + 1);
                str.append("(").
                        append(point1.getLng()).
                        append(" ").
                        append(point1.getLat()).
                        append(",").
                        append(point2.getLng()).
                        append(" ").
                        append(point2.getLat()).
                        append(")");
                if (i < pointList.size() - 2) {
                    str.append(",");
                }
            }
        } catch (Exception e) {
            logger.warn("line编码出现异常", e);
            return "";
        }
        str.append(")");
        return str.toString();
    }


    /**
     * 判断坐标是否在圆内
     *
     * @param pointWkt 坐标WKT字符串
     * @param radius   半径
     * @param lat      纬度
     * @param lng      经度
     * @return 是否范围内
     */
    public static boolean isInCircle(String pointWkt, Double radius, Double lat, Double lng) {
        GeometryFactory geometryFactory = new GeometryFactory();
        WKTReader reader = new WKTReader();
        com.vividsolutions.jts.geom.Point point;
        try {
            point = (com.vividsolutions.jts.geom.Point) reader.read(pointWkt);
        } catch (Exception e) {
            logger.warn("坐标解析失败", e);
            return false;
        }
        Coordinate coord = new Coordinate(lng, lat);
        com.vividsolutions.jts.geom.Point point2 = geometryFactory.createPoint(coord);
        // 单位米
        double distance = point.distance(point2) * 1000 * 100;
        return radius >= distance;
    }

    /**
     * 判断坐标点是否在多边形内
     *
     * @param polygonWkt 多边形字符串
     * @param lat        纬度
     * @param lng        经度
     * @return 是否有效范围
     */
    public static boolean isInPolygon(String polygonWkt, Double lat, Double lng) {
        GeometryFactory geometryFactory = new GeometryFactory();
        WKTReader reader = new WKTReader();
        Geometry read = null;
        try {
            read = reader.read(polygonWkt);
        } catch (Exception e) {
            logger.warn("读取多边形WKT失败", e);
        }
        Coordinate coord = new Coordinate(lng, lat);
        com.vividsolutions.jts.geom.Point point = geometryFactory.createPoint(coord);

        if (polygonWkt.startsWith(MULTIPOLYGON)) {
            com.vividsolutions.jts.geom.MultiPolygon polygon = (com.vividsolutions.jts.geom.MultiPolygon) read;
            return polygon != null && polygon.contains(point);
        } else {
            com.vividsolutions.jts.geom.Polygon polygon = (com.vividsolutions.jts.geom.Polygon) read;
            return polygon != null && polygon.contains(point);
        }
    }

    /**
     * 坐标点转polygon
     *
     * @param gpsStr
     * @return
     */
    public static String toPolygonWkt(String gpsStr) {
        String[] gpsArr = gpsStr.split(" ");
        StringBuilder ret = new StringBuilder("POLYGON((");
        for (int i = 0; i < gpsArr.length; i++) {
            String gps = gpsArr[i];
            String[] lngLat = gps.split(",");
            Double lng = Double.valueOf(lngLat[0]);
            Double lat = Double.valueOf(lngLat[1]);
            String output = String.format("%s %s", lng, lat);
            ret.append(output);
            if (i < gpsArr.length - 1) {
                ret.append(",");
            }
        }
        ret.append("))");
        return ret.toString();
    }

    public static void main(String[] args) {
        String pointWkt1 = "POINT(114.071 22.5522)";
        Point point = GeometryUtil.decodePoint(pointWkt1);
        String pointWkt2 = GeometryUtil.encodePoint(point);
        System.out.println(JSONUtil.toPrettyString(point));
        System.out.println(pointWkt1);
        System.out.println(pointWkt2);
        Assert.assertEquals(pointWkt1, pointWkt2);

        System.out.println("***************");

        String multiPointWkt1 = "MULTIPOLYGON(((113.544201 22.187051, 113.545124 22.186594, 113.545006 22.186479, 113.545596 22.186157, 113.545719 22.186281, 113.546722 22.185769, 113.54676 22.185794, 113.547441 22.185416, 113.547264 22.185272, 113.547184 22.185059, 113.547237 22.185049, 113.547226 22.184964, 113.547119 22.184964, 113.547125 22.184875, 113.547232 22.184786, 113.547339 22.184408, 113.547452 22.18407, 113.547623 22.183886, 113.547886 22.183723, 113.548085 22.183628, 113.548235 22.183638, 113.548374 22.183822, 113.54845 22.183911, 113.548487 22.183981, 113.548482 22.184125, 113.548482 22.184259, 113.548487 22.184284, 113.54853 22.184274, 113.548648 22.184105, 113.548798 22.183886, 113.548868 22.183827, 113.548997 22.183877, 113.549136 22.183956, 113.549201 22.184264, 113.549201 22.184661, 113.549372 22.18493, 113.549683 22.185208, 113.549887 22.185337, 113.551743 22.180936, 113.5517 22.180737, 113.551185 22.180509, 113.550885 22.18034, 113.550799 22.180042, 113.550885 22.179783, 113.55067 22.179644, 113.551014 22.178015, 113.549533 22.177637, 113.549598 22.177935, 113.549104 22.179505, 113.548975 22.179843, 113.548503 22.179942, 113.547709 22.179863, 113.547151 22.179843, 113.546551 22.180081, 113.546422 22.180062, 113.545735 22.18036, 113.545413 22.180578, 113.54477 22.181035, 113.544448 22.181433, 113.544383 22.181472, 113.544448 22.18183, 113.544083 22.182148, 113.54374 22.182982, 113.543546 22.183479, 113.543418 22.183678, 113.543074 22.183877, 113.54316 22.185049, 113.543396 22.185804, 113.544019 22.186877, 113.544201 22.187051)),((113.535731 22.179108, 113.535548 22.179028, 113.535505 22.179013, 113.535495 22.178944, 113.535489 22.178899, 113.535774 22.178502, 113.535977 22.178323, 113.536192 22.178134, 113.53645 22.178035, 113.536562 22.17799, 113.53697 22.177876, 113.537002 22.177925, 113.538611 22.177508, 113.538853 22.177394, 113.538998 22.177329, 113.539341 22.17726, 113.539411 22.177235, 113.539438 22.177086, 113.539668 22.177111, 113.539668 22.177185, 113.540242 22.177195, 113.540468 22.17717, 113.540698 22.17722, 113.540757 22.17723, 113.540822 22.177622, 113.541186 22.177523, 113.541261 22.177637, 113.540886 22.177781, 113.540907 22.177841, 113.541122 22.177756, 113.54124 22.177707, 113.541267 22.177677, 113.541331 22.177841, 113.540966 22.178, 113.541009 22.178054, 113.541326 22.177896, 113.541401 22.178025, 113.541084 22.178199, 113.541122 22.178233, 113.541406 22.178069, 113.541481 22.178164, 113.541245 22.178367, 113.54146 22.178591, 113.541846 22.178874, 113.542275 22.179127, 113.542463 22.179242, 113.542468 22.179525, 113.542409 22.179619, 113.542281 22.179912, 113.542291 22.180121, 113.542393 22.18028, 113.542404 22.180315, 113.54205 22.180464, 113.542061 22.180946, 113.541894 22.180985, 113.54175 22.181085, 113.541637 22.181114, 113.54161 22.181219, 113.541573 22.181333, 113.541583 22.181492, 113.541658 22.181586, 113.541766 22.181711, 113.541782 22.181845, 113.541809 22.181939, 113.541766 22.182028, 113.541771 22.182073, 113.541819 22.182197, 113.541889 22.182297, 113.542012 22.182366, 113.542157 22.183007, 113.54212 22.183052, 113.540736 22.182779, 113.540559 22.182694, 113.540419 22.18257, 113.540264 22.182351, 113.540199 22.182133, 113.540087 22.18172, 113.540006 22.181437, 113.539942 22.181363, 113.53932 22.18108, 113.538987 22.180951, 113.538735 22.180792, 113.538622 22.180518, 113.538622 22.18033, 113.538708 22.180215, 113.538719 22.180007, 113.538724 22.179843, 113.538703 22.179594, 113.538628 22.17943, 113.538547 22.179157, 113.538467 22.178899, 113.538375 22.178795, 113.538273 22.178636, 113.537995 22.178497, 113.537694 22.178442, 113.537485 22.178417, 113.537201 22.178462, 113.537007 22.178556, 113.536906 22.178571, 113.536798 22.178511, 113.536707 22.178497, 113.536605 22.178576, 113.536584 22.178531, 113.536568 22.178631, 113.536568 22.17873, 113.536509 22.17879, 113.536079 22.178924, 113.535736 22.179137, 113.535731 22.179108)),((113.560236 22.195503, 113.561245 22.194763, 113.561357 22.194733, 113.561502 22.194723, 113.561754 22.194778, 113.561947 22.195021, 113.561985 22.195086, 113.562065 22.195369, 113.562226 22.195573, 113.562457 22.195747, 113.562656 22.195811, 113.562951 22.195851, 113.56647 22.196829, 113.566727 22.196968, 113.566818 22.197152, 113.566856 22.197351, 113.566673 22.197892, 113.565649 22.201141, 113.565536 22.201324, 113.565263 22.201444, 113.565091 22.201429, 113.563981 22.201096, 113.563777 22.200967, 113.563594 22.200684, 113.563455 22.200455, 113.563256 22.200291, 113.562908 22.200147, 113.562693 22.200107, 113.562495 22.200162, 113.562409 22.200291, 113.562361 22.200351, 113.562221 22.200286, 113.56117 22.199879, 113.560773 22.19978, 113.560478 22.199675, 113.560059 22.199502, 113.559716 22.199233, 113.559609 22.199089, 113.55955 22.198866, 113.560188 22.196213, 113.560242 22.195796, 113.560236 22.195483, 113.560236 22.195503)),((113.592724 22.14466, 113.594397 22.144282, 113.595459 22.145847, 113.589667 22.161624, 113.589764 22.161992, 113.590032 22.162349, 113.590601 22.162617, 113.591459 22.162816, 113.592071 22.162687, 113.592446 22.162329, 113.590268 22.210059, 113.586899 22.204337, 113.586063 22.202351, 113.585953 22.201308, 113.585986 22.198978, 113.585964 22.195621, 113.585707 22.195194, 113.584923 22.194776, 113.583872 22.194101, 113.5809 22.194081, 113.577306 22.194021, 113.576694 22.19418, 113.576405 22.194607, 113.575107 22.194568, 113.575622 22.189243, 113.57574 22.187067, 113.575536 22.18675, 113.57531 22.186561, 113.573379 22.186531, 113.572006 22.186928, 113.570375 22.187783, 113.569924 22.187862, 113.569796 22.189273, 113.569667 22.191319, 113.569774 22.193942, 113.569817 22.196406, 113.569318 22.198208, 113.567612 22.201611, 113.567302 22.204339, 113.567269 22.206107, 113.567162 22.20702, 113.567151 22.207179, 113.566003 22.20706, 113.566194 22.205488, 113.566151 22.205235, 113.566151 22.204187, 113.566167 22.203715, 113.566135 22.203526, 113.566135 22.203079, 113.566296 22.202265, 113.566542 22.201599, 113.566371 22.20152, 113.566274 22.20154, 113.565899 22.201582, 113.567451 22.196879, 113.56737 22.196625, 113.567091 22.196432, 113.566287 22.196223, 113.565841 22.196094, 113.565385 22.19524, 113.56575 22.195105, 113.565648 22.195061, 113.564479 22.193044, 113.562033 22.194276, 113.562129 22.1944, 113.561872 22.19435, 113.561705 22.194112, 113.561818 22.194033, 113.561228 22.193292, 113.562151 22.192682, 113.562126 22.192576, 113.561708 22.19207, 113.561718 22.191513, 113.561611 22.191518, 113.561622 22.19124, 113.561724 22.190912, 113.562131 22.190197, 113.561777 22.190073, 113.561944 22.189725, 113.562019 22.18975, 113.562856 22.187813, 113.562963 22.187495, 113.563585 22.187222, 113.563285 22.186472, 113.563248 22.186426, 113.562958 22.18655, 113.562518 22.186391, 113.562535 22.186267, 113.562706 22.186068, 113.562856 22.186008, 113.563071 22.186103, 113.563103 22.185979, 113.56283 22.185755, 113.562846 22.185422, 113.563114 22.18501, 113.563361 22.184637, 113.563565 22.184444, 113.563715 22.184315, 113.563666 22.184146, 113.563221 22.184245, 113.56298 22.183584, 113.562878 22.183093, 113.561612 22.182338, 113.561097 22.182536, 113.560925 22.182993, 113.561118 22.183391, 113.560523 22.184608, 113.557449 22.18343, 113.557342 22.183704, 113.556956 22.183555, 113.557165 22.183078, 113.557347 22.183023, 113.557412 22.182988, 113.55761 22.182685, 113.557084 22.182551, 113.556918 22.182655, 113.556886 22.182899, 113.556902 22.183078, 113.556988 22.183088, 113.556972 22.183217, 113.556569 22.183103, 113.556349 22.182541, 113.555845 22.180659, 113.555534 22.180172, 113.555389 22.180157, 113.555277 22.180211, 113.555223 22.180122, 113.555309 22.179725, 113.553412 22.178475, 113.551009 22.1778, 113.547361 22.177243, 113.54307 22.176528, 113.540066 22.176329, 113.538778 22.17625, 113.53865 22.175932, 113.538692 22.174978, 113.544593 22.163353, 113.54528 22.162777, 113.545731 22.161744, 113.546117 22.16085, 113.546975 22.160591, 113.548241 22.161346, 113.549271 22.161485, 113.55088 22.161545, 113.552533 22.161764, 113.553434 22.161962, 113.553884 22.162459, 113.554571 22.162221, 113.555837 22.162797, 113.55809 22.163135, 113.559571 22.162777, 113.560408 22.162698, 113.560665 22.162797, 113.561266 22.163095, 113.561888 22.163572, 113.562124 22.163532, 113.562446 22.163194, 113.564012 22.163115, 113.564957 22.162976, 113.565659 22.162418, 113.567258 22.162244, 113.568116 22.162304, 113.570047 22.162075, 113.570777 22.161946, 113.571002 22.162035, 113.571496 22.164132, 113.56964 22.164629, 113.569318 22.165006, 113.569275 22.165195, 113.569586 22.167778, 113.57406 22.166377, 113.576914 22.16444, 113.577665 22.166974, 113.57951 22.166467, 113.580562 22.168076, 113.580969 22.167858, 113.580841 22.167073, 113.578673 22.164092, 113.578416 22.162582, 113.579081 22.162323, 113.579446 22.163476, 113.579982 22.163277, 113.57966 22.162284, 113.58112 22.161966, 113.582128 22.165304, 113.583501 22.164708, 113.582793 22.161727, 113.583973 22.16133, 113.583587 22.159899, 113.584767 22.160177, 113.584403 22.161449, 113.584875 22.161866, 113.58569 22.164211, 113.585797 22.164291, 113.586033 22.16437, 113.586634 22.1628, 113.5879 22.163158, 113.588759 22.160595, 113.583823 22.159025, 113.584016 22.15821, 113.583652 22.15594, 113.582836 22.153357, 113.581077 22.151687, 113.581377 22.151727, 113.592724 22.14466, 113.592724 22.14466)),((113.589972 22.231286, 113.589961 22.210371, 113.58449 22.211782, 113.577344 22.211683, 113.575005 22.208723, 113.575177 22.206617, 113.576658 22.205663, 113.576529 22.194756, 113.575177 22.194875, 113.575241 22.19865, 113.573675 22.204352, 113.572903 22.205584, 113.571336 22.206657, 113.568568 22.207769, 113.567989 22.207789, 113.567538 22.208385, 113.565779 22.208882, 113.564792 22.208882, 113.558075 22.21204, 113.55917 22.212497, 113.559384 22.213629, 113.559255 22.214106, 113.557775 22.21496, 113.557539 22.215556, 113.565714 22.228488, 113.568246 22.230732, 113.570285 22.231606, 113.571422 22.231626, 113.572495 22.231288, 113.574147 22.231328, 113.579683 22.233513, 113.582644 22.233513, 113.582644 22.233513, 113.584769 22.232877, 113.589972 22.231286)))";
        System.out.println(decodeMultiPolygon(multiPointWkt1));

        String polygonWkt1 = "POLYGON((114.285 30.5958,114.265 30.5946,114.266 30.5841,114.282 30.585,114.285 30.5958))";
        Polygon polygon = decodePolygon(polygonWkt1);
        String polygonWkt2 = encodePolygon(polygon);
        System.out.println(JSONUtil.toPrettyString(polygon));
        System.out.println(polygonWkt1);
        System.out.println(polygonWkt2);
        Assert.assertEquals(polygonWkt1, polygonWkt2);
        System.out.println(isInCircle("POINT(114.27781 30.592934)", 0.113, 30.61407, 114.3044));


        List<List<Point>> list = new ArrayList<>();
        List<Point> pointList = new ArrayList<>();
        pointList.add(new Point(114.425111, 30.456111));
        pointList.add(new Point(114.425222, 30.652222));
        list.add(pointList);
        List<Point> pointList1 = new ArrayList<>();
        pointList1.add(new Point(114.425333, 30.456333));
        pointList1.add(new Point(114.425444, 30.652444));
        pointList1.add(new Point(114.425555, 30.456555));
        list.add(pointList1);
        List<Point> pointList2 = new ArrayList<>();
        pointList2.add(new Point(114.425666, 30.652666));
        list.add(pointList2);
        String kwt = encodeMultiPolygon(list);
        System.out.println(kwt);
        List<List<Point>> lists = decodeMultiPolygon(kwt);
        System.out.println(lists);
    }

}

package com.whfc.common.constant;

/**
 * @Description: 消息队列定义
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/9/9 16:57
 */
public class QueueConst {

    /**
     * 智能监控-识别结果
     */
    public static final String FIM_RESULT = "fim_result";

    /**
     * 智能监控-天秤-AI识别队列
     */
    public static final String FIM_RESULT_TOWER_QUEUE = "fim_result_tower_queue";

    /**
     * 智能监控-识别结果交换机
     */
    public static final String FIM_RESULT_EXCHANGE = "fim_result_exchange";

    /**
     * 人员考勤闸机-考勤数据
     */
    public static final String EMP_FACEGATE_ATTEND_DATA = "emp_facegate_attend_data";

    /**
     * 人员考勤闸机-人员信息数据
     */
    public static final String EMP_FACEGATE_EMP_INFO = "emp_facegate_emp_info";

    /**
     * 人员考勤闸机-班组数据
     */
    public static final String EMP_FACEGATE_GROUP_DATA = "emp_facegate_group_data";

    /**
     * 人员考勤闸机-设备数据
     */
    public static final String EMP_FACEGATE_DEVICE_DATA = "emp_facegate_device_data";

    /**
     * 人员-安全帽数据
     */
    public static final String EMP_HELMET_DATA = "emp_helmet_data";

    /**
     * 人员-安全帽广播
     */
    public static final String EMP_HELMET_BOARDCAST = "emp_helmet_boardcast";

    /**
     * 人员-安全帽广播-响应
     */
    public static final String EMP_HELMET_BOARDCAST_STATE = "emp_helmet_boardcast_state";

    /**
     * 人员-智能手表数据
     */
    public static final String EMP_WATCH_DATA = "emp_watch_data";

    /**
     * 人员-区域定位-蓝牙
     */
    public static final String EMP_INDOOR_BLE = "emp_indoor_ble";

    /**
     * 人员-区域定位-UWB
     */
    public static final String EMP_INDOOR_UWB = "emp_indoor_uwb";

    /**
     * 人员-UWB测距标签数据
     */
    public static final String EMP_INDOOR_UWB_TAG_DATA = "emp_indoor_uwb_tag_data";
    /**
     * 人员-UWB测距报警
     */
    public static final String EMP_INDOOR_UWB_TAG_WARN = "emp_indoor_uwb_tag_warn";

    /**
     * 人员-培训箱数据
     */
    public static final String EMP_TRAIN_BOX_DATA = "emp_train_box_data";

    /**
     * 机械-云管家数据
     */
    public static final String MACH_DATA = "mach_data";

    /**
     * 机械-定位数据
     */
    public static final String MACH_LOC_DATA = "mach_loc_data";

    /**
     * 机械-定位数据(UWB融合定位)
     */
    public static final String MACH_UWB_DATA = "mach_uwb_data";

    /**
     * 机械-考勤数据
     */
    public static final String EQUIP_DATA = "equip_data";

    /**
     * 机械-OBD硬件
     */
    public static final String OBD_DATA = "obd_data";

    /**
     * 硬件消息0x01(云管家工时数据)
     */
    public static final String MACH_MSG_0X01 = "mach_msg_0x01";

    /**
     * 硬件消息0x04(考勤设备数据)
     */
    public static final String MACH_MSG_0X04 = "mach_msg_0x04";

    /**
     * 硬件消息0x05(OBD硬件数据)
     */
    public static final String MACH_MSG_0X05 = "mach_msg_0x05";

    /**
     * 硬件消息0x06(OBD硬件数据-批量)
     */
    public static final String MACH_MSG_0X06 = "mach_msg_0x06";

    /**
     * 硬件消息0x08(硬件报警数据)
     */
    public static final String MACH_MSG_0X08 = "mach_msg_0x08";

    /**
     * 硬件消息0x09(考勤设备数据-多个gps定位)
     */
    public static final String MACH_MSG_0X09 = "mach_msg_0x09";

    /**
     * 硬件消息0x0A(油箱盖标定)
     */
    public static final String MACH_MSG_0X0A = "mach_msg_0x0A";

    /**
     * 硬件下行指令-硬件开关机
     */
    public static final String MACH_MSG_0X8B = "mach_msg_0x8B";

    /**
     * 设备MQTT下行消息
     */
    public static final String MACH_MQTT_DOWN = "mach_mqtt_down";

    /**
     * 设备MQTT上行消息
     */
    public static final String MACH_MQTT_UP = "mach_mqtt_up";

    /**
     * 旋挖项目-车载摄像头数据
     */
    public static final String ROTATY_IVC_DATA = "rotaty_ivc_data";

    /**
     * 环境数据
     */
    public static final String ENV_DUST_DATA = "env_dust_data";


    /**
     * 特种设备-塔机实时数据交换机
     */
    public static final String FSE_CRANE_REAL_DATA_EXC = "fanout.fse_crane_real_data";

    /**
     * 特种设备-塔吊实时数据
     */
    public static final String FSE_CRANE_REAL_DATA = "fse_crane_real_data";

    /**
     * 特种设备-塔吊吊装记录数据
     */
    public static final String FSE_CRANE_RECORD_DATA = "fse_crane_record_data";

    /**
     * 特种设备-塔吊参数数据
     */
    public static final String FSE_CRANE_PARAM_DATA = "fse_crane_param_data";

    /**
     * 特种设备-塔吊报警数据
     */
    public static final String FSE_CRANE_WARN_DATA = "fse_crane_warn_data";

    /**
     * 特种设备-塔机机同步数据
     */
    public static final String FSE_CRANE_SYNC_DATA = "fse_crane_sync_data";


    /**
     * 特种设备-升降机实时数据交换机
     */
    public static final String FSE_LIFT_REAL_DATA_EXC = "fanout.fse_lift_real_data";

    /**
     * 特种设备-升降机参数数据
     */
    public static final String FSE_LIFT_PARAM_DATA = "fse_lift_param_data";

    /**
     * 特种设备-升降机实时机数据
     */
    public static final String FSE_LIFT_REAL_DATA = "fse_lift_real_data";

    /**
     * 特种设备-升降机运行记录数据
     */
    public static final String FSE_LIFT_RECORD_DATA = "fse_lift_record_data";

    /**
     * 特种设备-升降机报警数据
     */
    public static final String FSE_LIFT_WARN_DATA = "fse_lift_warn_data";

    /**
     * 特种设备-升降机同步数据
     */
    public static final String FSE_LIFT_SYNC_DATA = "fse_lift_sync_data";


    /**
     * 特种设备-龙门吊同步实时数据
     */
    public static final String FSE_GANTRY_CURRENT_DATA = "fse_gantry_current_data";

    /**
     * 特种设备-架桥机同步实时数据
     */
    public static final String FSE_BRIDGE_CURRENT_DATA = "fse_bridge_current_data";

    /**
     * 特种设备-履带吊同步实时数据
     */
    public static final String FSE_CRAWLER_CURRENT_DATA = "fse_crawler_current_data";


    /**
     * 推送消息-交换机
     */
    public static final String PUSH_MSG_EXCHANGE = "fanout.push_msg";

    /**
     * 消息推送-后台
     */
    public static final String PUSH_MS = "push_ms";

    /**
     * 消息推送-小程序
     */
    public static final String PUSH_WXMP = "push_wxmp";

    /**
     * 消息推送-短信
     */
    public static final String PUSH_SMS = "push_sms";

    /**
     * 消息推送-邮件
     */
    public static final String PUSH_MAIL = "push_mail";

    /**
     * 消息推送-消息
     */
    public static final String PUSH_MSG = "push_msg";

    /**
     * 消息推送-环境-密闭空间-报警
     */
    public static final String PUSH_FCS_WARN = "push_fcs_warn";


    /**
     * 湖南实名制考勤上报数据交换机
     */
    public static final String ATTEND_SYNC_HUNAN_EXC = "fanout.attend_sync_hunan";

    /**
     * 河南实名制政府平台
     */
    public static final String ATTEND_SYNC_HUNAN_EMP = "attend_sync_hunan_emp";

    /**
     * 河南实名制智慧工地平台
     */
    public static final String ATTEND_SYNC_HUNAN_MS = "attend_sync_hunan_ms";


    /**
     * 报警检测数据
     */
    public static final String ALARM_CHECK_DATA = "alarm_check_data";

    /**
     * 设备保养websocket数据
     */
    public static final String OBD_MAINTAIN_DATA = "obd_maintain_data";

    /**
     * 智能安全帽实时数据
     */
    public static final String FSH_CURRENT_DATA = "fsh_current_data";

    /**
     * 视频监控设备定位
     */
    public static final String FVS_GPS_DATA = "fvs_gps_data";

    /**
     * 监测-沉降位移数据
     */
    public static final String MON_SETTLEMENT_DATA = "mon_settlement_data";

    /**
     * 隧道人员定位
     */
    public static final String TUNNEL_EMP_POSITION_DATA = "tunnel_emp_position_data";

    /**
     * 隧道安全步距
     */
    public static final String TUNNEL_SAFETY_INTERVAL_DATA = "tunnel_safety_interval_data";

    /**
     * 智能锁数据
     */
    public static final String LOCK_DATA = "lock_data";

    /**
     * 混凝土测温数据
     */
    public static final String CONCRETE_DATA = "concrete_data";

    public static final String KYY_UP_MSG = "kyy_up_msg";

    public static final String KYY_DOWN_MSG = "kyy_down_msg";

    public static final String SCA_DOWN_MSG = "sca_down_msg";

    public static final String UWB_TTL_EXCHANGE = "uwb_ttl_exchange";

    public static final String UWB_TTL_QUEUE = "uwb_ttl_queue";

    public static final String UWB_DLX_EXCHANGE = "uwb_dlx_exchange";

    public static final String UWB_DLX_QUEUE = "uwb_dlx_queue";

    /**
     * 盲区报警数据
     */
    public static final String BLIND_WARN_DATA = "blind_warn_data";
}

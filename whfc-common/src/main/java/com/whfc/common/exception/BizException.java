package com.whfc.common.exception;

import com.whfc.common.result.ResultEnum;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/22 14:43
 */
public class BizException extends RuntimeException {

    private String code;

    private String msg;

    public BizException() {
    }

    public BizException(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 是否超时错误
     *
     * @return
     */
    public boolean isTimeoutError() {
        return ResultEnum.NETWORK_TIMEOUT.getCode().equals(code);
    }

    /**
     * 是否网络错误
     *
     * @return
     */
    public boolean isNetoworkError() {
        return ResultEnum.NETWORK_ERROR.getCode().equals(code);
    }
}

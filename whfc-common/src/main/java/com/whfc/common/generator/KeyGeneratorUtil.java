package com.whfc.common.generator;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/2 15:02
 */
public class KeyGeneratorUtil {

    private static KeyGenerator keyGenerator;

    static {
        keyGenerator = KeyGeneratorFactory.newInstance(DefaultKeyGenerator.class.getName());
    }

    public static KeyGenerator instance() {
        return keyGenerator;
    }

    /**
     * 生成String型ID
     *
     * @return
     */
    public static String genStrId() {
        return String.valueOf(keyGenerator.generateKey().longValue());
    }

    /**
     * 生成long型ID
     *
     * @return
     */
    public static long genLongId() {
        return keyGenerator.generateKey().longValue();
    }

    public static void main(String[] args) {
        System.out.println(KeyGeneratorUtil.genLongId());
        System.out.println(KeyGeneratorUtil.genStrId());
    }
}

package com.whfc.common.idcard;

import java.util.Arrays;

/**
 * @Description 错误信息
 * <AUTHOR>
 * @Date 2021-08-05 15:28
 * @Version 1.0
 */
public enum WarnInfoEnum {

    ERROR_9100("-9100", " 身份证有效日期不合法告警"),
    ERROR_9101("-9101", "身份证边框不完整告警"),
    ERROR_9102("-9102", "身份证复印件告警"),
    ERROR_9103("-9103", "身份证翻拍告警"),
    ERROR_9104("-9104", "身份证框内遮挡告警"),
    ERROR_9105("-9105", "身份证框内遮挡告警"),
    ERROR_9106("-9106", "身份证PS告警"),
    ERROR_9107("-9107", "身份证反光告警");

    private String value;

    private String desc;

    WarnInfoEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }


    public String getDesc() {
        return desc;
    }


    public static String parseValue(String value) {
        WarnInfoEnum[] values = WarnInfoEnum.values();
        WarnInfoEnum infoEnum = Arrays.stream(values).filter(warnInfoEnum -> warnInfoEnum.getValue().equals(value)).findFirst().get();
        if (infoEnum != null) {
            return infoEnum.desc;
        }
        return null;
    }
}

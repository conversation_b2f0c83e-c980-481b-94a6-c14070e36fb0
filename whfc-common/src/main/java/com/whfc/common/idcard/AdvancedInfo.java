package com.whfc.common.idcard;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 身份证识别返回类
 * <AUTHOR>
 * @Date 2021-08-05 15:00
 * @Version 1.0
 */
@Data
public class AdvancedInfo implements Serializable {


    @J<PERSON>NField(name = "WarnInfos")
    private String[] warnInfos;

    @JSONField(name = "Portrait", serialize = false)
    private String Portrait;


    public String getWarnInfos() {
        if (this.warnInfos == null || this.warnInfos.length <= 0) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (String warnInfo : this.warnInfos) {
            String errorMassage = WarnInfoEnum.parseValue(warnInfo);
            sb.append(errorMassage);
            sb.append("、");
        }
        return sb.toString();
    }
}

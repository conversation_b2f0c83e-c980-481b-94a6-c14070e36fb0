package com.whfc.common.idcard;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.whfc.common.util.DateUtil;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 身份证识别返回类
 * <AUTHOR>
 * @Date 2021-08-05 11:39
 * @Version 1.0
 */
@Data
public class IdentifyDTO implements Serializable {

    /**
     * 姓名
     */
    @JSONField(name = "Name")
    private String name;

    /**
     * 性别
     */
    @JSONField(name = "Sex")
    private String sex;

    /**
     * 民族
     */
    @JSONField(name = "Nation")
    private String nation;

    /**
     * 生日
     */
    @JSONField(name = "Birth", format = DateUtil.DATE_1_FORMAT)
    private Date birth;

    /**
     * 地址
     */
    @JSONField(name = "Address")
    private String address;

    /**
     * 证件号码
     */
    @JSONField(name = "IdNum")
    private String idNum;

    /**
     * 发证机关
     */
    @JSONField(name = "Authority")
    private String authority;

    /**
     * 证件有效期
     */
    @JSONField(name = "ValidDate")
    private String validDate;

    /**
     * 扩展信息
     */
    @JSONField(name = "AdvancedInfo")
    private String advancedInfo;


    public AdvancedInfo getAdvancedInfo() {
        if (StringUtils.isEmpty(this.advancedInfo)) {
            return null;
        }
        try {
            return JSONObject.parseObject(this.advancedInfo, AdvancedInfo.class);
        } catch (Exception e) {
            return null;
        }

    }
}

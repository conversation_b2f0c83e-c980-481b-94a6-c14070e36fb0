package com.whfc.common.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "响应体")
@Data
public class Result<T> implements Serializable {

    @Schema(description = "返回码", example = "200")
    private String code;

    @Schema(description = "返回信息", example = "成功")
    private String msg;

    @Schema(description = "返回数据")
    private T data;

    public Result(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

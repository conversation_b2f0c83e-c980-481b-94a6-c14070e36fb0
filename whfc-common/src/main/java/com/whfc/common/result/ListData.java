package com.whfc.common.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 服务层返回的结果列表
 * @date 2019-07-23
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ListData<T> extends ResultData<T> {

    /**
     * 数据列表
     */
    @Schema(description = "数据列表")
    private List<T> list;

}

package com.whfc.common.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019-07-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ResultData<T> implements Serializable {

    /**
     * 单个详细数据对象
     */
    @Schema(description = "详细数据对象")
    private T detail;

    /**
     * 自定义键值对
     */
    @Schema(description = "自定义键值对")
    private Map<String, Object> attrs;


}

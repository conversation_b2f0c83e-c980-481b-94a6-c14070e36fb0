package com.whfc.common.result;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019年7月18日
 */
public class ResultUtil {

    public static <T> Result<T> success() {
        return new Result<T>(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getMessageCode());
    }

    public static <T> Result<T> success(String code, T object) {
        Result<T> result = new Result<T>(code, ResultEnum.SUCCESS.getMessageCode());
        result.setData(object);
        return result;
    }

    public static <T> Result<T> success(T object) {
        Result<T> result = new Result<T>(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getMessageCode());
        result.setData(object);
        return result;
    }

    public static <T> Result<T> failure() {
        return new Result<T>(ResultEnum.FAILURE.getCode(), ResultEnum.FAILURE.getMessageCode());
    }


    public static <T> Result<T> failure(T object) {
        Result<T> result = new Result<T>(ResultEnum.FAILURE.getCode(), ResultEnum.FAILURE.getMessageCode());
        result.setData(object);
        return result;
    }

    public static <T> Result<T> paramError() {
        return new Result<T>(ResultEnum.PARAM_ERROR.getCode(), ResultEnum.PARAM_ERROR.getMessageCode());
    }


    public static <T> Result<T> paramError(T object) {
        Result<T> result = new Result<T>(ResultEnum.PARAM_ERROR.getCode(), ResultEnum.PARAM_ERROR.getMessageCode());
        result.setData(object);
        return result;
    }


    public static <T> Result<T> authError() {
        return new Result<T>(ResultEnum.AUTHZ_ERROR.getCode(), ResultEnum.AUTHZ_ERROR.getMessageCode());
    }


    public static <T> Result<T> authError(T object) {
        Result<T> result = new Result<T>(ResultEnum.AUTHZ_ERROR.getCode(), ResultEnum.AUTHZ_ERROR.getMessageCode());
        result.setData(object);
        return result;
    }

    public static <T> Result<T> tokenExpired() {
        return new Result<T>(ResultEnum.TOKEN_EXPIRED.getCode(), ResultEnum.TOKEN_EXPIRED.getMessageCode());
    }


    public static <T> Result<T> unknownError() {
        return new Result<T>(ResultEnum.UNKNOWN_ERROR.getCode(), ResultEnum.UNKNOWN_ERROR.getMessageCode());
    }


    public static <T> Result<T> unknownError(T object) {
        Result<T> result = new Result<T>(ResultEnum.UNKNOWN_ERROR.getCode(), ResultEnum.UNKNOWN_ERROR.getMessageCode());
        result.setData(object);
        return result;
    }
}

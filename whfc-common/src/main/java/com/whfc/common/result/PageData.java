package com.whfc.common.result;



import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 服务层返回的分页结果
 * @date 2019-07-23
 */
@Schema(description = "分页实体")
public class PageData<T> extends ListData<T> {

    /**
     * 页码
     */
    @Schema(description = "页码",example = "1")
    private Integer pageNum;

    /**
     * 分页大小
     */
    @Schema(description =  "分页大小",example = "10")
    private Integer pageSize;

    /**
     * 总页数
     */
    @Schema(description =  "总页数",example = "1")
    private Integer pages;

    /**
     * 数据总数量
     */
    @Schema(description =  "数据总数量",example = "10")
    private Long total;

    public PageData() {

    }

    public PageData(List<T> list, Integer pageNum, Integer pageSize, Integer pages, Long total) {
        super(list);
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = pages;
        this.total = total;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }
}

package com.whfc.common.result;

public enum ResultEnum {

    UNKNOWN_ERROR("100", "未知错误", "DEFAULT_UNKNOWN_ERROR"),

    SUCCESS("200", "成功", "DEFAULT_SUCCESS"),

    PARAM_ERROR("400", "参数验证失败", "DEFAULT_PARAM_ERROR"),

    AUTHC_ERROR("401", "未认证", "DEFAULT_AUTHC_ERROR"),

    TOKEN_EXPIRED("402", "登录已过期", "DEFAULT_TOKEN_EXPIRED"),

    AUTHZ_ERROR("403", "未授权", "DEFAULT_AUTHZ_ERROR"),

    NOT_FOUND("404", "未找到相关资源", "DEFAULT_NOT_FOUND"),

    NOT_BIND_PHONE("405", "未绑定手机号", "DEFAULT_NOT_BIND_PHONE"),

    API_ERROR("406", "API接口错误", "DEFAULT_API_ERROR"),

    CRYPT_ERROR("407", "加解密错误", "DEFAULT_CRYPT_ERROR"),

    FAILURE("500", "服务器错误", "DEFAULT_FAILURE"),

    NETWORK_ERROR("501", "网络错误", "DEFAULT_NETWORK_ERROR"),

    NETWORK_TIMEOUT("502", "网络超时", "NETWORK_TIMEOUT");


    private final String code;
    private final String message;
    private final String messageCode;

    ResultEnum(String code, String message, String messageCode) {
        this.code = code;
        this.message = message;
        this.messageCode = messageCode;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getMessageCode() {
        return messageCode;
    }
}

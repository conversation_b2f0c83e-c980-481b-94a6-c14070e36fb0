package com.whfc.common.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 默认参数
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-12 10:35
 */
@Data
public class DefaultProperties implements Serializable {

    /**
     * 业务域名 , 例如: https://cms.whfciot.com
     */
    private String host;

    /**
     * 文件服务器域名,例如: https://file.whfciot.com
     */
    private String fileHost;

    /**
     * 文件预览域名,例如: https://preview.whfciot.com
     */
    private String previewHost;

    /**
     * 内网地址,例如: http://************
     */
    private String innerHost;

    /**
     * AI助手起否开放
     */
    private String aiFLag;

    /**
     * AI助手名称
     */
    private String aiName;

    /**
     * 多语言是否开放
     */
    private String i18nFlag;

    /**
     * 多语言支持的语言
     */
    private List<String> i18nLanguages;

    /**
     * 默认部门ID
     */
    private Integer deptId;

    /**
     * 默认角色ID
     */
    private Integer roleId;
}

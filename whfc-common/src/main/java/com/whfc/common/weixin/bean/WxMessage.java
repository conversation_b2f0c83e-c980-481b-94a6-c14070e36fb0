package com.whfc.common.weixin.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamConverter;
import com.whfc.common.util.JSONUtil;
import lombok.Data;
import me.chanjar.weixin.common.util.xml.XStreamCDataConverter;

import java.io.Serializable;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@XStreamAlias("xml")
@Data
public class WxMessage implements Serializable {
    private static final long serialVersionUID = -3586245291677274914L;

    @JSONField(name = "Encrypt")
    @XStreamAlias("Encrypt")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String encrypt;

    @JSONField(name = "ToUserName")
    @XStreamAlias("ToUserName")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String toUser;

    @JSONField(name = "FromUserName")
    @XStreamAlias("FromUserName")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String fromUser;

    @JSONField(name = "CreateTime")
    @XStreamAlias("CreateTime")
    private Integer createTime;

    @JSONField(name = "MsgType")
    @XStreamAlias("MsgType")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String msgType;

    @JSONField(name = "MsgDataFormat")
    @XStreamAlias("MsgDataFormat")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String msgDataFormat;

    @JSONField(name = "Content")
    @XStreamAlias("Content")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String content;

    @JSONField(name = "MsgId")
    @XStreamAlias("MsgId")
    private Long msgId;

    @JSONField(name = "PicUrl")
    @XStreamAlias("PicUrl")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String picUrl;

    @JSONField(name = "MediaId")
    @XStreamAlias("MediaId")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String mediaId;

    @JSONField(name = "Event")
    @XStreamAlias("Event")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String event;

    @JSONField(name = "Title")
    @XStreamAlias("Title")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String title;

    @JSONField(name = "AppId")
    @XStreamAlias("AppId")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String appId;

    @JSONField(name = "PagePath")
    @XStreamAlias("PagePath")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String pagePath;

    @JSONField(name = "ThumbUrl")
    @XStreamAlias("ThumbUrl")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String thumbUrl;

    @JSONField(name = "ThumbMediaId")
    @XStreamAlias("ThumbMediaId")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String thumbMediaId;

    @JSONField(name = "SessionFrom")
    @XStreamAlias("SessionFrom")
    @XStreamConverter(value = XStreamCDataConverter.class)
    private String sessionFrom;

    public static WxMessage fromJson(String json) {
        return JSON.parseObject(json, WxMessage.class);
    }

    public String toJson() {
        return JSONUtil.toString(this);
    }

    @Override
    public String toString() {
        return this.toJson();
    }

    public static void main(String[] args) {
        WxMessage message = new WxMessage();
        message.setEncrypt("aes");
        message.setSessionFrom("abcdefg");
        String json = JSONUtil.toString(message);
        System.out.println(json);
        WxMessage message1 = JSON.parseObject(json, WxMessage.class);
        System.out.println(message1);
    }
}

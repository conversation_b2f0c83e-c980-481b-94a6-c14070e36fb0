package com.whfc.common.weixin.mp;

import com.whfc.common.util.HttpUtil;

/**
 * <AUTHOR>
 * @desc 客服消息api
 * @date 2019-08-06
 */
public class CustomMessageApi {

    private static final String CUSTOM_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s";

    /**
     * 发送客服消息接口
     *
     * @param accessToken
     * @param msgBody
     * @return
     */
    public static String send(String accessToken, String msgBody) {
        String url = String.format(CUSTOM_MESSAGE_URL, accessToken);
        return HttpUtil.doPost(url, msgBody);
    }
}

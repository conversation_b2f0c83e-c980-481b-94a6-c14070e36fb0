package com.whfc.common.weixin.mp;

import com.whfc.common.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @desc
 * @date 2019-08-06
 */
public class TokenApi {

    private static Logger logger = LoggerFactory.getLogger(TokenApi.class);

    private static final String GET_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

    /**
     * 获取accessToken
     *
     * @param appId
     * @param appSecret
     * @return
     */
    public static String getAccessToken(String appId, String appSecret) {
        String url = String.format("%s?grant_type=client_credential&appid=%s&secret=%s", GET_TOKEN_URL, appId, appSecret);
        logger.info("getAccessToken,url:{}", url);
        String response = HttpUtil.doGet(url);
        logger.info("getAccessToken,response:{}", response);
        return response;
    }
}

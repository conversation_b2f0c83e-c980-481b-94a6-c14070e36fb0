package com.whfc.common.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-08-28
 */
public class StringValueBetweenValidator implements ConstraintValidator<StringValueBetween, String> {

    private List<String> between;

    @Override
    public void initialize(StringValueBetween constraintAnnotation) {
        this.between = Arrays.asList(constraintAnnotation.between());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (this.between != null && this.between.contains(value)) {
            return true;
        }
        return false;
    }
}

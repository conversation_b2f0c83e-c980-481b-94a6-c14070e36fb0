package com.whfc.common.validator;

import com.whfc.common.spring.AppContextUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @date 2018-08-28
 */
public class FieldScopeValidator implements ConstraintValidator<FieldScope, Object> {

    private FieldScopeType scopeType;

    @Override
    public void initialize(FieldScope constraintAnnotation) {
        this.scopeType = constraintAnnotation.value();
    }


    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (AppContextUtil.context() == null) {
            return false;
        }
        ValidatorExec exec = AppContextUtil.context().getBean(ValidatorExec.class);
        return exec.valid(scopeType, value);
    }
}

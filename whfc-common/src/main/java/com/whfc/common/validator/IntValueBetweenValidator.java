package com.whfc.common.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-08-28
 */
public class IntValueBetweenValidator implements ConstraintValidator<IntValueBetween, Integer> {

    private int[] between;

    @Override
    public void initialize(IntValueBetween constraintAnnotation) {
        this.between = constraintAnnotation.between();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (this.between != null && value != null) {
            for (int ele : between) {
                if (ele == value.intValue()) {
                    return true;
                }
            }
        }
        return false;
    }
}

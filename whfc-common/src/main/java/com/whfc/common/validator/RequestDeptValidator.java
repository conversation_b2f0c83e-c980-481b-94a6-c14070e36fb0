package com.whfc.common.validator;

import com.whfc.common.util.RequestAttr;
import org.springframework.web.context.request.RequestContextHolder;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import static org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST;

/**
 * <AUTHOR>
 * @date 2018-08-28
 */
public class RequestDeptValidator implements ConstraintValidator<RequestDept, Integer> {

    @Override
    public void initialize(RequestDept constraintAnnotation) {
    }


    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        //获取组织机构ID
        Integer deptId = (Integer) RequestContextHolder.currentRequestAttributes().getAttribute(RequestAttr.DEPT_ID, SCOPE_REQUEST);
        return value != null && value.equals(deptId);
    }
}

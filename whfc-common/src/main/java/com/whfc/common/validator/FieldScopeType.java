package com.whfc.common.validator;

/**
 * 字段授权验证类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-06-21 15:47
 */
public enum FieldScopeType {

    MACH_ID(10, "设备ID"),

    MACH_GUID(11, "设备GUID"),

    MACH_WARN_ID(12, "设备报警ID"),

    EMP_ID(20, "人员ID"),

    EQUIP_ID(30, "考勤设备ID"),

    FSE_CRANE_ID(41,"塔机ID"),

    FSE_LIFT_ID(42,"升降机ID"),

    FSE_GANTRY_ID(43,"龙门吊ID"),

    FSE_BRIDGE_ID(44,"架桥机ID"),

    FSE_CRAWLER_ID(45,"履带吊ID"),

    ENV_ID(50,"环境设备ID"),

    FVS_ID(60,"视频设备ID");

    private final Integer value;

    private final String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }


    FieldScopeType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}

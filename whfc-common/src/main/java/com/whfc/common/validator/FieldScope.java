package com.whfc.common.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 字段鉴权验证注解
 *
 * <AUTHOR>
 * @date 2018-08-28
 */
@Documented
@Constraint(validatedBy = {FieldScopeValidator.class})
@Target({FIELD, PARAMETER})
@Retention(RUNTIME)
public @interface FieldScope {

    FieldScopeType value();

    String message() default "超出数据范围";

    Class<?>[] groups() default {};

    /**
     * @return the payload associated to the constraint
     */
    Class<? extends Payload>[] payload() default {};

}

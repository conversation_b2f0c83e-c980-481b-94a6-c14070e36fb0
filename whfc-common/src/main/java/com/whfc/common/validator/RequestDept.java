package com.whfc.common.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 请求参数-项目校验注解
 */
@Documented
@Constraint(validatedBy = {RequestDeptValidator.class})
@Target({FIELD, PARAMETER})
@Retention(RUNTIME)
public @interface RequestDept {

    String message() default "";

    Class<?>[] groups() default {};

    /**
     * @return the payload associated to the constraint
     */
    Class<? extends Payload>[] payload() default {};
}

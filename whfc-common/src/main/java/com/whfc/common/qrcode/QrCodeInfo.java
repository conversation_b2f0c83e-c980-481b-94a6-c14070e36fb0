package com.whfc.common.qrcode;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@Schema(description = "二维码信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QrCodeInfo implements Serializable {

    /**
     * 二维码类型
     * {@link com.whfc.common.qrcode.QrCodeType}
     */
    @Schema(description = "二维码类型")
    private String type;

    /**
     * 二维码内容
     */
    @Schema(description = "二维码内容")
    private String guid;

    /**
     * 二维码过期时间
     */
    @Schema(description = "二维码过期时间")
    private Long expireTime;


}

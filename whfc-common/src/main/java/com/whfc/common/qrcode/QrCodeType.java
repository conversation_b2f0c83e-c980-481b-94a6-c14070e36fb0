package com.whfc.common.qrcode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
public enum QrCodeType {

    TRACK_MACH("TRACK_MACH", "设备数字追踪"),

    ;
    private final String type;

    private final String desc;

    QrCodeType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}

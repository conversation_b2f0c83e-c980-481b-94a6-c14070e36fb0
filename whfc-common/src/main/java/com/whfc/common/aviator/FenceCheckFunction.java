package com.whfc.common.aviator;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.whfc.common.enums.FenceType;
import com.whfc.common.geometry.GeometryUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 出区域自定义函数
 * @date 2020-12-10
 */
public class FenceCheckFunction extends AbstractFunction {
    private static Logger logger = LoggerFactory.getLogger(MachRotationWarnCheckFunction.class);

    @Override
    public AviatorObject call(Map<String, Object> env,
                              AviatorObject preLat,
                              AviatorObject preLng,
                              AviatorObject curLat,
                              AviatorObject curLng,
                              AviatorObject type,
                              AviatorObject radius,
                              AviatorObject geometryWKT) {
        boolean flag = false;
        try {
            Double preLatV = FunctionUtils.getNumberValue(preLat, env).doubleValue();
            Double curLatV = FunctionUtils.getNumberValue(curLat, env).doubleValue();
            Double preLngV = FunctionUtils.getNumberValue(preLng, env).doubleValue();
            Double curLngV = FunctionUtils.getNumberValue(curLng, env).doubleValue();
            Integer typeV = FunctionUtils.getNumberValue(type, env).intValue();
            Double radiusV = FunctionUtils.getNumberValue(radius, env).doubleValue();
            String wkt = FunctionUtils.getStringValue(geometryWKT, env);

            if (FenceType.CIRCLE.value().equals(typeV)) {
                flag = !GeometryUtil.isInCircle(wkt, radiusV, curLatV, curLngV) && GeometryUtil.isInCircle(wkt, radiusV, preLatV, preLngV);
            }
            if (FenceType.POLYGON.value().equals(typeV)) {
                flag = !GeometryUtil.isInPolygon(wkt, curLatV, curLngV) && GeometryUtil.isInPolygon(wkt, preLatV, preLngV);
            }

        } catch (Exception e) {
            logger.error("aviator参数错误", e);
        }
        return new AviatorString(String.valueOf(flag));

    }

    @Override
    public String getName() {
        return "fenceCheck";
    }

}

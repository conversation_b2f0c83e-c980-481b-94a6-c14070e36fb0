package com.whfc.common.aviator;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 设备倾斜检测自定义函数
 * @date 2020-12-10
 */
public class MachRotationWarnCheckFunction extends AbstractFunction {
    private static Logger logger = LoggerFactory.getLogger(MachRotationWarnCheckFunction.class);

    private static final double BORDER_VALUE = 180;
    private static final double MAX_VALUE = 360;

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject rotationX, AviatorObject rotationZ, AviatorObject machRotationRule) {
        StringBuffer sf = new StringBuffer();
        AviatorString result = new AviatorString("");
        try {
            Number rotationXValue = FunctionUtils.getNumberValue(rotationX, env);
            Number rotationZValue = FunctionUtils.getNumberValue(rotationZ, env);
            if (null == rotationXValue || null == rotationZValue) {
                return result;
            }
            String ruleValue = FunctionUtils.getStringValue(machRotationRule, env);
            if (StringUtils.isBlank(ruleValue)) {
                return result;
            }
            JSONObject jsonObject = JSONObject.parseObject(ruleValue);
            if (null == jsonObject) {
                return result;
            }
            Double preRotationX = jsonObject.getDouble("preRotationX");
            Double postRotationX = jsonObject.getDouble("postRotationX");
            Double leftRotationZ = jsonObject.getDouble("leftRotationZ");
            Double rightRotationZ = jsonObject.getDouble("rightRotationZ");

            double v = rotationXValue.doubleValue();
            double v1 = rotationZValue.doubleValue();

            if (v < BORDER_VALUE) {
                // 前倾角
                if (null != preRotationX && preRotationX != 0 && v > preRotationX) {
                    sf.append("前倾斜" + v);
                }
            }
            if (v >= BORDER_VALUE) {
                // 后倾角
                if (null != postRotationX && postRotationX != 0 && (MAX_VALUE - v) > postRotationX) {
                    sf.append("后倾斜" + (MAX_VALUE - v));
                }
            }
            if (v1 < BORDER_VALUE) {
                // 左倾角
                if (null != leftRotationZ && leftRotationZ != 0 && v1 > leftRotationZ) {
                    sf.append("左倾斜" + v1);
                }
            }
            if (v1 >= BORDER_VALUE) {
                // 右倾角
                if (null != rightRotationZ && rightRotationZ != 0 && (MAX_VALUE - v1) > rightRotationZ) {
                    sf.append("右倾斜" + (MAX_VALUE - v1));
                }
            }
        } catch (Exception e) {
            logger.error("aviator参数错误", e);
        }

        return new AviatorString(sf.toString());

    }

    @Override
    public String getName() {
        return "machRotationWarnCheck";
    }

}

package com.whfc.common.aviator;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 设备倾斜检测自定义函数
 * @date 2020-12-10
 */
public class MachRotationCheckFunction extends AbstractFunction {
    private static Logger logger = LoggerFactory.getLogger(MachRotationWarnCheckFunction.class);

    private static final double BORDER_VALUE = 180;
    private static final double MAX_VALUE = 360;

    @Override
    public AviatorObject call(Map<String, Object> env,
                              AviatorObject rotationX,
                              AviatorObject rotationZ,
                              AviatorObject preRotationX,
                              AviatorObject postRotationX,
                              AviatorObject leftRotationZ,
                              AviatorObject rightRotationZ) {
        StringBuffer sf = new StringBuffer();
        AviatorString result = new AviatorString("");
        try {
            Number rotationXValue = FunctionUtils.getNumberValue(rotationX, env);
            Number rotationZValue = FunctionUtils.getNumberValue(rotationZ, env);
            Number preRotationXValue = FunctionUtils.getNumberValue(preRotationX, env);
            Number postRotationXValue = FunctionUtils.getNumberValue(postRotationX, env);
            Number leftRotationZValue = FunctionUtils.getNumberValue(leftRotationZ, env);
            Number rightRotationZValue = FunctionUtils.getNumberValue(rightRotationZ, env);
            if (null == rotationXValue || null == rotationZValue ||
                    null == preRotationXValue || null == postRotationXValue ||
                    null == leftRotationZValue || null == rightRotationZValue) {
                return result;
            }
            double v = rotationXValue.doubleValue();
            double v1 = rotationZValue.doubleValue();

            if (v < BORDER_VALUE) {
                // 前倾角
                if (preRotationXValue.doubleValue() != 0 && v > preRotationXValue.doubleValue()) {
                    sf.append("前倾斜" + v);
                }
            }
            if (v >= BORDER_VALUE) {
                // 后倾角
                if (postRotationXValue.doubleValue() != 0 && (MAX_VALUE - v) > postRotationXValue.doubleValue()) {
                    sf.append("后倾斜" + (MAX_VALUE - v));
                }
            }
            if (v1 < BORDER_VALUE) {
                // 左倾角
                if (leftRotationZValue.doubleValue() != 0 && v1 > leftRotationZValue.doubleValue()) {
                    sf.append("左倾斜" + v1);
                }
            }
            if (v1 >= BORDER_VALUE) {
                // 右倾角
                if (rightRotationZValue.doubleValue() != 0 && (MAX_VALUE - v1) > rightRotationZValue.doubleValue()) {
                    sf.append("右倾斜" + (MAX_VALUE - v1));
                }
            }
        } catch (Exception e) {
            logger.error("aviator参数错误", e);
        }
        return new AviatorString(sf.toString());

    }

    @Override
    public String getName() {
        return "machRotationCheck";
    }

}

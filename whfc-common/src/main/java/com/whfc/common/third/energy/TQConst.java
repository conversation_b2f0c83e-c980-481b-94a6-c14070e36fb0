package com.whfc.common.third.energy;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-02 9:58
 */
public class TQConst {

    /**
     * 水表数据类型FIDS
     */
    public static final Map<String, String> WATER_FIDS = new HashMap<>();

    /**
     * 电表数据类型FIDS
     */
    public static final Map<String, String> ELECTRIC_FIDS = new HashMap<>();

    static {
        WATER_FIDS.put("42", "水表数据");

        ELECTRIC_FIDS.put("3", "正向有功总电能");
        ELECTRIC_FIDS.put("4", "反向有功总电能");
        ELECTRIC_FIDS.put("5", "正向无功总电能");
        ELECTRIC_FIDS.put("6", "反向无功总电能");
        ELECTRIC_FIDS.put("15","ABC三相电压");
        ELECTRIC_FIDS.put("16","ABC三相电流");
        ELECTRIC_FIDS.put("17","ABC三相有功功率");
        ELECTRIC_FIDS.put("18","ABC三相无功功率");
        ELECTRIC_FIDS.put("22","剩余金额");
        ELECTRIC_FIDS.put("27","A相电流");
        ELECTRIC_FIDS.put("28","B相电流");
        ELECTRIC_FIDS.put("29","C相电流");
        ELECTRIC_FIDS.put("30","A相电压");
        ELECTRIC_FIDS.put("31","B相电压");
        ELECTRIC_FIDS.put("32","C相电压");
        ELECTRIC_FIDS.put("33","瞬时有功功率");
        ELECTRIC_FIDS.put("34","瞬时无功功率");
    }
}

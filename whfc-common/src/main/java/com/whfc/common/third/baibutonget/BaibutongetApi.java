package com.whfc.common.third.baibutonget;

import com.whfc.common.result.PageData;
import com.whfc.common.third.baibutonget.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.PageUtil;

import java.util.*;

/**
 * @Description: 百步通视频安全帽API
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/8 11:36
 */
public class BaibutongetApi {

    private static final String getPKey = "/api/index.php?ctl=tool&act=get_pkey";

    private static final String getToken = "/api/index.php?ctl=tool&act=get_token";

    private static final String getGroup = "/api/index.php?ctl=bruce&act=get_my_group_members";

    private static final String getPath = "/api/index.php?ctl=location&act=get_user_path_web";

    private static final String getImage = "/api/index.php?ctl=report&act=get_user_image";

    private String host;

    private String user;

    private String pwd;

    private String adminId;

    public BaibutongetApi(String host, String user, String pwd, String adminId) {
        this.host = host;
        this.user = user;
        this.pwd = pwd;
        this.adminId = adminId;
    }

    /**
     * 获取pkey
     *
     * @return
     */
    public String getPkey() {
        String url = host + getPKey;
        Map<String, String> param = new HashMap<>();
        param.put("user_name", user);
        param.put("pwd", pwd);
        String response = HttpUtil.doPost(url, param);
        GetPkeyResult result = JSONUtil.parseObject(response, GetPkeyResult.class);
        if (result.getStatus()) {
            return result.getData();
        }
        return null;
    }

    /**
     * 获取token
     *
     * @param pkey
     * @return
     */
    public Token getToken(String pkey) {
        String url = host + getToken;
        Map<String, String> param = new HashMap<>();
        param.put("user_name", user);
        param.put("pkey", pkey);
        String response = HttpUtil.doPost(url, param);
        GetTokenResult result = JSONUtil.parseObject(response, GetTokenResult.class);
        if (result.getStatus()) {
            Token token = new Token();
            token.setToken(result.getToken());
            token.setSessionId(result.getSession_id());
            return token;
        }
        return null;
    }

    /**
     * 查询管理员下所有分组和设备
     *
     * @param token
     * @return
     */
    public List<Group> getGroupMembers(String token) {
        String url = host + getGroup;
        Map<String, String> param = new HashMap<>();
        param.put("admin_id", adminId);
        param.put("token", token);
        String response = HttpUtil.doPost(url, param);
        GetGroupMembersResult result = JSONUtil.parseObject(response, GetGroupMembersResult.class);
        if (result.getStatus()) {
            return result.getData();
        }
        return Collections.emptyList();
    }

    /**
     * 获取轨迹
     *
     * @param token
     * @param userId
     * @param start
     * @param end
     * @return
     */
    public List<Path> getPath(String token, String userId, Date start, Date end) {
        String url = host + getPath;
        Map<String, String> param = new HashMap<>();
        param.put("admin_id", adminId);
        param.put("token", token);
        param.put("user_id", userId);
        param.put("start", String.valueOf(start.getTime() / 1000));
        param.put("end", String.valueOf(end.getTime() / 1000));
        String response = HttpUtil.doPost(url, param);
        GetPathResult result = JSONUtil.parseObject(response, GetPathResult.class);
        if (result.getStatus()) {
            return result.getData();
        }
        return Collections.emptyList();
    }

    /**
     * 获取图片
     *
     * @param token
     * @param userId
     * @param date
     * @param pageNo
     * @return
     */
    public PageData<Image> getImage(String token, String userId, Date date, Integer pageNo) {
        String url = host + getImage;
        Map<String, String> param = new HashMap<>();
        param.put("admin_id", adminId);
        param.put("token", token);
        param.put("user_id", userId);
        param.put("date", DateUtil.formatDate(date));
        param.put("p", String.valueOf(pageNo));
        String response = HttpUtil.doPost(url, param);
        GetImageResult result = JSONUtil.parseObject(response, GetImageResult.class);
        if (result.getStatus()) {
            List<Image> dataList = result.getData();
            int total = result.getTotal();
            int pageNum = 10;
            int pages = total % pageNum == 0 ? total / pageNum : total / pageNum + 1;
            return PageUtil.pageData(pageNo, pageNum, total, pages, dataList);
        }
        return PageUtil.emptyPage();
    }
}

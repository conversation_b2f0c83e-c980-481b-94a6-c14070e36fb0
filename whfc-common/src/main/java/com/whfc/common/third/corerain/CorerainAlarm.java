package com.whfc.common.third.corerain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CorerainAlarm implements Serializable {

    /**
     * 报警时间
     */
    private String timestamp;

    /**
     * 报警图片数据 base64
     */
    private String alarm_pic_data;

    /**
     * 报警图片名称
     */
    private String alarm_pic_name;

    /**
     * 报警图片url
     */
    private String alarm_pic_url;

    /**
     * 算法id
     */
    private String algorithm_id;

    /**
     * 算法名称
     */
    private String algorithm_name;

    /**
     * 算法英文名称
     */
    private String algorithm_name_en;

    /**
     * 原图图片 base64
     */
    private String src_pic_data;

    /**
     * 原图图片名称
     */
    private String src_pic_name;

    /**
     * 原图图片 url
     */
    private String src_pic_url;

    /**
     * 摄像头分组
     */
    private List<String> camera_group;

    /**
     * 摄像头id
     */
    private String camera_id;

    /**
     * 摄像头名称
     */
    private String camera_name;

    /**
     * 摄像头uuid
     */
    private String camera_name_uuid;

    /**
     * 摄像头类型
     */
    private List<String> camera_types;

    /**
     * 国标编码
     */
    private String channel_id;

    /**
     * 视频流地址
     */
    private String stream_url;

    /**
     * 视频片段名称
     */
    private String video_name;

    /**
     * 视频片段地址
     */
    private String video_url;

    /**
     * 人员信息
     */
    private List<CorerainAlarmMember> members;
}

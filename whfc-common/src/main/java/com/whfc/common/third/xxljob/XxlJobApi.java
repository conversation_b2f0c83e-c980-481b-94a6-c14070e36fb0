package com.whfc.common.third.xxljob;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.xxljob.entity.XxlGroup;
import com.whfc.common.third.xxljob.entity.XxlJobInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * XXL-Job API工具类
 * 用于通过API接口操作XXL-Job，包括登录、创建任务、删除任务等功能
 *
 * <AUTHOR>
 * @date 2023-06-10
 */
public class XxlJobApi {

    private static final Logger logger = LoggerFactory.getLogger(XxlJobApi.class);

    /**
     * XXL-Job Admin地址
     */
    private String adminAddress;

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 登录后的Cookie
     */
    private String cookie;

    /**
     * 构造函数
     *
     * @param adminAddress XXL-Job Admin地址，例如：<a href="http://localhost:8008/xxl-job-admin"/>
     * @param username     登录用户名
     * @param password     登录密码
     */
    public XxlJobApi(String adminAddress, String username, String password) {
        // 确保adminAddress以/结尾
        if (!adminAddress.endsWith("/")) {
            adminAddress = adminAddress + "/";
        }

        this.adminAddress = adminAddress;
        this.username = username;
        this.password = password;
    }

    /**
     * 登录XXL-Job Admin
     *
     * @return 是否登录成功
     */
    public boolean login() {
        try {
            // 发送登录请求
            String url = adminAddress + "login";
            Map<String, Object> params = new HashMap<>();
            params.put("userName", username);
            params.put("password", password);
            params.put("ifRemember", "on");

            // 执行请求
            HttpResponse response = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .execute();
            if (response.isOk()) {
                String setCookie = response.header("Set-Cookie");
                if (StringUtils.isNotBlank(setCookie)) {
                    this.cookie = setCookie;
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("登录XXL-Job异常", e);
            return false;
        }
    }


    /**
     * 添加执行器
     *
     * @param appName 执行器AppName
     * @param title   执行器名称
     * @return 执行器ID，失败返回0
     */
    public boolean addExecutor(String appName, String title) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appname", appName);
            params.put("title", title);
            params.put("addressType", 0);

            // 发送请求
            String url = adminAddress + "jobgroup/save";
            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();


            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            return code == 200;
        } catch (Exception e) {
            logger.error("添加执行器异常", e);
            return false;
        }
    }

    /**
     * 添加任务
     *
     * @param jobGroup        执行器ID
     * @param jobDesc         任务描述
     * @param author          负责人
     * @param scheduleConf    调度配置，CRON表达式或者固定速率
     * @param executorHandler 执行器任务handler
     * @param executorParam   执行器任务参数
     * @return 任务ID，失败返回0
     */
    public int addJob(int jobGroup, String jobDesc, String author, String scheduleConf,
                      String executorHandler, String executorParam) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("jobGroup", jobGroup);
            params.put("jobDesc", jobDesc);
            params.put("author", author);
            params.put("alarmEmail", "");
            params.put("scheduleType", "CRON");
            params.put("scheduleConf", scheduleConf);
            params.put("cronGen_display", scheduleConf);
            params.put("schedule_conf_CRON", "");
            params.put("schedule_conf_FIX_RATE", "");
            params.put("schedule_conf_FIX_DELAY", "");
            params.put("glueType", "BEAN");
            params.put("executorHandler", executorHandler);
            params.put("executorParam", executorParam);
            params.put("executorRouteStrategy", "FIRST");
            params.put("childJobId", "");
            params.put("misfireStrategy", "DO_NOTHING");
            params.put("executorBlockStrategy", "SERIAL_EXECUTION");
            params.put("executorTimeout", 0);
            params.put("executorFailRetryCount", 0);
            params.put("glueRemark", "GLUE代码初始化");
            params.put("glueSource", "");


            // 发送请求
            String url = adminAddress + "jobinfo/add";

            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();

            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            if (code == 200) {
                return jsonObject.getIntValue("content");
            }
            logger.error("添加任务失败，返回结果：{}", result);
            return 0;
        } catch (Exception e) {
            logger.error("添加任务异常", e);
            return 0;
        }
    }


    /**
     * 删除任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    public boolean deleteJob(int id) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("id", String.valueOf(id));

            // 发送请求
            String url = adminAddress + "jobinfo/remove";
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            headers.put("Cookie", cookie);
            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();

            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            return code == 200;
        } catch (Exception e) {
            logger.error("删除任务异常", e);
            return false;
        }
    }

    /**
     * 启动任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    public boolean startJob(int id) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("id", id);

            // 发送请求
            String url = adminAddress + "jobinfo/start";
            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();

            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            return code == 200;
        } catch (Exception e) {
            logger.error("启动任务异常", e);
            return false;
        }
    }

    /**
     * 停止任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    public boolean stopJob(int id) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("id", id);

            // 发送请求
            String url = adminAddress + "jobinfo/stop";

            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();
            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            return code == 200;
        } catch (Exception e) {
            logger.error("停止任务异常", e);
            return false;
        }
    }

    /**
     * 触发任务
     *
     * @param id            任务ID
     * @param executorParam 执行参数，可覆盖任务默认参数
     * @param addressList   执行器地址列表，多地址逗号分隔
     * @return 是否成功
     */
    public boolean triggerJob(int id, String executorParam, String addressList) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("id", id);
            if (StringUtils.isNotBlank(executorParam)) {
                params.put("executorParam", executorParam);
            }
            if (StringUtils.isNotBlank(addressList)) {
                params.put("addressList", addressList);
            }

            // 发送请求
            String url = adminAddress + "jobinfo/trigger";

            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();

            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("code");
            return code == 200;
        } catch (Exception e) {
            logger.error("触发任务异常", e);
            return false;
        }
    }

    /**
     * 查询执行器列表
     *
     * @return 执行器列表的JSON字符串，失败返回null
     */
    public List<XxlGroup> listExecutors(String appName) {
        try {
            // 发送请求
            String url = adminAddress + "jobgroup/pageList";
            Map<String, Object> params = new HashMap<>();
            params.put("appname", appName);
            params.put("title", "");
            params.put("start", "0");
            params.put("length", "100");

            String result = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute().body();
            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int code = jsonObject.getIntValue("recordsTotal");
            if (code < 0) {
                return Collections.emptyList();
            }
            return JSON.parseArray(jsonObject.getString("data"), XxlGroup.class);
        } catch (Exception e) {
            logger.error("查询执行器列表异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询任务列表
     *
     * @param jobGroup        执行器ID，0表示全部
     * @param triggerStatus   调度状态：-1=全部，0=停止，1=运行
     * @param jobDesc         任务描述，模糊匹配
     * @param executorHandler 执行器任务handler，模糊匹配
     * @param author          负责人，模糊匹配
     * @return 任务列表
     */
    public List<XxlJobInfo> listJobs(int jobGroup, int triggerStatus, String jobDesc, String executorHandler, String author) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("jobGroup", jobGroup);
            params.put("triggerStatus", triggerStatus);
            params.put("jobDesc", jobDesc == null ? "" : jobDesc);
            params.put("executorHandler", executorHandler == null ? "" : executorHandler);
            params.put("author", author == null ? "" : author);
            params.put("start", "0");
            params.put("length", "100");

            // 发送请求
            String url = adminAddress + "jobinfo/pageList";

            String result;
            try (HttpResponse response = HttpUtil.createPost(url)
                    .form(params)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .header("Cookie", cookie)
                    .execute()) {
                result = response.body();
            }

            // 解析结果
            JSONObject jsonObject = JSON.parseObject(result);
            int recordsTotal = jsonObject.getIntValue("recordsTotal");
            if (recordsTotal > 0) {
                return JSON.parseArray(jsonObject.getString("data"), XxlJobInfo.class);
            }
            return Collections.emptyList();
        } catch (Exception e) {
            logger.error("查询任务列表异常", e);
            return Collections.emptyList();
        }
    }


    public static void main(String[] args) {
        XxlJobApi xxlJobApi = new XxlJobApi("http://127.0.0.1:8008/job", "admin", "123456");
        xxlJobApi.login();
        // xxlJobApi.addExecutor("test", "test");
        List<XxlGroup> xxlGroups = xxlJobApi.listExecutors("fvs-xxl-job");
        System.out.println(xxlGroups);
        Integer groupId = xxlGroups.get(0).getId();
        // Date time = DateUtil.parse("2025-04-11 15:30:00");
        // String scheduleConf = DateUtil.format(time, "0 mm HH * * ?");
        // String timeStr = DateUtil.format(time, "HH:mm");
        // int jobId = xxlJobApi.addJob(groupId, "【系统生成】定时监控抓拍-" + timeStr, "System",
        //         scheduleConf, "startScheduledSnapshot", "");
        // xxlJobApi.startJob(jobId);
        List<XxlJobInfo> xxlJobInfos = xxlJobApi.listJobs(groupId, 0, null, null, null);
        System.out.println(xxlJobInfos);
        // xxlJobApi.triggerJob(jobId, null, null);
        // xxlJobApi.stopJob(jobId);
        // xxlJobApi.deleteJob(jobId);
    }
}
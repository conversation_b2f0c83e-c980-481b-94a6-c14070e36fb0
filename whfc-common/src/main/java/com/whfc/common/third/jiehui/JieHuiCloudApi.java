package com.whfc.common.third.jiehui;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.enums.WindDirection;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.MathUtil;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 杰辉科技api
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/5 9:35
 */
public class JieHuiCloudApi {

    private static final List<String> directions = Arrays.asList("东", "南", "西", "北");

    private static final String ENV_URL = "http://api.jiehuicloud.com/cgi-bin/get.json?appid=%s&appsecret=%s&terminal=%s";

    private String appid;

    private String appsecret;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppsecret() {
        return appsecret;
    }

    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    public JieHuiCloudApi(String appid, String appsecret) {
        this.appid = appid;
        this.appsecret = appsecret;
    }

    /**
     * 获取扬尘数据
     *
     * @param sn
     * @return
     */
    public String getEnvData(String sn) {
        String url = String.format(ENV_URL, appid, appsecret, sn);
        String response = HttpUtil.doGet(url);
        JSONObject json = JSON.parseObject(response);
        if (json != null && json.containsKey("uploadtime")) {

            String winddirection = json.getString("winddirection");
            if (directions.contains(winddirection)) {
                winddirection = "正" + winddirection;
            }
            WindDirection direction = WindDirection.parseByName(winddirection);

            JSONObject result = new JSONObject();
            result.put("deviceCode", sn);
            result.put("time", DateUtil.parseDate(json.getString("uploadtime"), "yy-MM-dd HH:mm:ss"));
            result.put("noise", json.getDouble("noise"));
            result.put("pm25", MathUtil.doule2int(json.getDouble("pm25")));
            result.put("pm10", MathUtil.doule2int(json.getDouble("pm10")));
            result.put("envTemp", json.get("temperature"));
            result.put("envRh", json.get("humidity"));
            result.put("windSpeed", json.get("windspeed"));
            result.put("windDirection", direction != null ? direction.getValue() : null);
            return result.toString();
        }
        return null;
    }
}

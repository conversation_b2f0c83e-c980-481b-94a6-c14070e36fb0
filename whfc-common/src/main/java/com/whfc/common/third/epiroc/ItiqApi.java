package com.whfc.common.third.epiroc;

import com.whfc.common.third.epiroc.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 安百拓-设备数据api
 */
public class ItiqApi {
    private static final Logger logger = LoggerFactory.getLogger(ItiqApi.class);

    private static final Integer success = 200;

    private static final String machine_info = "http://www.chinacertiq.com:12808/antbi/api/machine_info";

    private static final String field_translate = "http://www.chinacertiq.com:12808/antbi/api/field_translate";

    private static final String data_all = "http://www.chinacertiq.com:12808/antbi/api/data/all";

    private String ik;

    public ItiqApi(String ik) {
        this.ik = ik;
    }

    public List<MachineInfo> getDeviceList() {
        String url = machine_info + "?ik=" + ik;
        String response = HttpUtil.doGet(url);
        MachineInfoResult result = JSONUtil.parseObject(response, MachineInfoResult.class);
        if (result != null && success.equals(result.getErrorCode())) {
            return result.getData();
        }
        logger.info("获取设备列表失败,{}", response);
        return Collections.emptyList();
    }

    public List<MachineFiled> getDeviceFieldList(String model) {
        try {
            String url = field_translate + "?ik=" + ik + "&model=" + URLEncoder.encode(model, "UTF-8");
            String response = HttpUtil.doGet(url);
            MachineFiledResult result = JSONUtil.parseObject(response, MachineFiledResult.class);
            if (result != null && success.equals(result.getErrorCode())) {
                return result.getData();
            }
            logger.info("获取设备字段列表失败,{}", response);
        } catch (Exception ex) {
            logger.error("获取设备字段列表异常", ex);
        }
        return Collections.emptyList();
    }

    public List<MachineData> getDeviceData(String remoteId, Date start, Date end) {
        String beginTime = null;
        String endTime = null;
        try {
            beginTime = URLEncoder.encode(DateUtil.formatDateTime(start), "UTF-8");
            endTime = URLEncoder.encode(DateUtil.formatDateTime(end), "UTF-8");
            String url = data_all + "?ik=" + ik + "&remoteId=" + remoteId + "&beginTime=" + beginTime + "&endTime=" + endTime;
            String response = HttpUtil.doGet(url);
            MachineDataResult result = JSONUtil.parseObject(response, MachineDataResult.class);
            if (result != null && success.equals(result.getErrorCode())) {
                return result.getData();
            }
            logger.info("获取设备数据失败,{}", response);
        } catch (Exception ex) {
            logger.error("获取历史数据失败，{}", ex.getMessage());
        }

        return Collections.emptyList();
    }
}

package com.whfc.common.third.wonhere.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 传感器类型
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-30 17:14
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DevType {

    TYPE9(9, "无线荷重"),

    TYPE10(10, "竖向沉降"),

    TYPE11(11, "水平位移"),

    TYPE13(13, "拉线位移计"),

    TYPE14(14, "激光测距仪"),

    TYPE17(17, "双轴倾角计"),

    TYPE20(20, "投入式水位计"),

    TYPE21(21, "浮子式水位计"),

    TYPE22(22, "渗压计"),

    TYPE24(24, "锚索测力计"),

    TYPE25(25, "钢筋计"),

    TYPE26(26, "压力盒"),

    TYPE27(27, "孔隙水压力计"),

    TYPE28(28, "应变计"),

    TYPE29(29, "轴力计"),

    TYPE30(30, "雨量计"),

    TYPE31(31, "阻式温度计"),

    TYPE95(95, "无线倾角");

    private Integer value;

    private String desc;

    DevType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static DevType parseByValue(Integer value) {
        DevType[] types = DevType.values();
        for (DevType type : types) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}

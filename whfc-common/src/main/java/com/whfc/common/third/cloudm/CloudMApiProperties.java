package com.whfc.common.third.cloudm;

import java.io.Serializable;

/**
 * @Description: 云机械接口
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 17:27
 */
public class CloudMApiProperties implements Serializable {

    /**
     * 服务器地址
     */
    private String host;

    /**
     * 公钥
     */
    private String accessKey;

    /**
     * 私钥
     */
    private String secretKey;

    public CloudMApiProperties() {
    }

    public CloudMApiProperties(String host, String accessKey, String secretKey) {
        this.host = host;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
}

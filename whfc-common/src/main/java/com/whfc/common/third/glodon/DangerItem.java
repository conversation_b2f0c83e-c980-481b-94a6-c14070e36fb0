
package com.whfc.common.third.glodon;

import lombok.Data;

import java.io.Serializable;

@Data
public class DangerItem implements Serializable {

    private Integer changeLimit;

    private String code;

    private String content;

    private String createTime;

    private String dangerTypeId;

    private Boolean deleteStatus;

    private Long fine;

    private String fullId;

    private String fullName;

    private String id;

    private String identify;

    private String lastStopTime;

    private String lastUpdateTime;

    private Long level;

    private Long order;

    private Long points;

    private Long pushPeriod;

    private Boolean recordStatus;

    private Long related;

    private String remark;
}

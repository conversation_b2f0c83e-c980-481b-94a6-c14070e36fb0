package com.whfc.common.third.hkpoly;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.hkpoly.dto.HkPolyResult;
import com.whfc.common.util.RsaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 香港理工API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17
 */
@Slf4j
public class HkPolyApi {

    public static final String SSE_WARN_URL = "/sse/notice";

    private static final String SIGNATURE_URL = "/auth/login/account/signature";

    private static final String LOGIN_TOKEN_URL = "/auth/login/account";

    private static final String SSE_TOKEN_URL = "/api/me/sse/auth";

    private static final String FILE_AUTH_URL = "/api/me/file/auth/default";

    private static final String FILE_RESOURCE_URL = "/file/resource";


    private final String host;

    private final String username;

    private final String password;

    private final String voucher;

    public HkPolyApi(HkPolyProperties hkPolyProperties) {
        this.host = hkPolyProperties.getHost();
        this.username = hkPolyProperties.getUsername();
        this.password = hkPolyProperties.getPassword();
        this.voucher = hkPolyProperties.getVoucher();
    }

    /**
     * 获取签名
     *
     * @return 签名
     */
    private String getSignature() {
        log.info("HkPolyApi 获取签名.");
        String signature = null;
        try {
            String url = host + SIGNATURE_URL;
            String body = HttpUtil.createGet(url).header("X-Voucher", voucher).execute().body();
            log.info("HkPolyApi 获取签名响应,{}", body);
            HkPolyResult result = JSON.parseObject(body, HkPolyResult.class);
            if (result == null || StringUtils.isBlank(result.getData())) {
                return signature;
            }
            JSONObject jsonObject = JSON.parseObject(result.getData());
            if (jsonObject == null || !jsonObject.containsKey("content")) {
                return signature;
            }
            signature = jsonObject.getString("content");
        } catch (Exception e) {
            log.error("HkPolyApi 获取签名失败.", e);
        }
        return signature;
    }

    /**
     * 获取token
     *
     * @return token
     */
    public String getToken() {
        log.info("HkPolyApi 获取token.");
        String token = null;
        try {
            String url = host + LOGIN_TOKEN_URL;
            String signature = getSignature();
            if (StringUtils.isBlank(signature)) {
                return token;
            }
            String password = RsaUtil.encryptByPublicKey(this.password, signature);

            JSONObject data = new JSONObject();
            data.put("account", username);
            data.put("password", password);

            String body = HttpUtil.createPost(url)
                    .header("X-Voucher", voucher)
                    .body(data.toJSONString())
                    .execute().body();
            log.info("HkPolyApi 获取token响应,{}", body);
            HkPolyResult result = JSON.parseObject(body, HkPolyResult.class);
            if (result == null || StringUtils.isBlank(result.getData())) {
                return token;
            }
            JSONObject jsonObject = JSON.parseObject(result.getData());
            if (jsonObject == null || !jsonObject.containsKey("accessToken")) {
                return token;
            }
            token = jsonObject.getString("accessToken");
        } catch (Exception e) {
            log.error("HkPolyApi 获取token失败.", e);
        }
        return token;
    }

    /**
     * 获取sse token
     *
     * @return sse token
     */
    public String getSseToken(String token) {
        log.info("HkPolyApi 获取sse token.");
        String sseToken = null;
        try {
            String mark = RandomUtil.randomString(12);
            String url = host + SSE_TOKEN_URL + "?mark=" + mark;
            // 获取请求头
            Map<String, String> headers = getHeaders(token);

            String body = HttpUtil.createGet(url)
                    .addHeaders(headers)
                    .execute().body();
            log.info("HkPolyApi 获取sse token响应,{}", body);
            HkPolyResult result = JSON.parseObject(body, HkPolyResult.class);
            if (result == null || result.getData() == null) {
                return sseToken;
            }
            sseToken = result.getData();
        } catch (Exception e) {
            log.error("HkPolyApi 获取sse token失败.", e);
        }
        return sseToken;
    }


    public String getFileAuth(String token) {
        log.info("HkPolyApi 获取文件授权.");
        try {
            String url = host + FILE_AUTH_URL;
            String body = HttpUtil.createGet(url)
                    .addHeaders(getHeaders(token))
                    .execute().body();
            log.info("HkPolyApi 获取文件授权响应,{}", body);
            HkPolyResult result = JSON.parseObject(body, HkPolyResult.class);
            if (result == null || result.getData() == null) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("HkPolyApi 获取文件授权失败.", e);
        }
        return null;
    }


    public String getFileUrl(String fileAuth, String filePath) {
        log.info("HkPolyApi 获取文件下载地址.");
        return host + FILE_RESOURCE_URL + "?v=" + fileAuth +
                "&node=alarm&path=" + filePath;
    }

    /**
     * 拼装请求头
     *
     * @param token token
     * @return 请求头
     */
    private Map<String, String> getHeaders(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Project", "20000");
        headers.put("X-Language", "zh-hk");
        headers.put("Authorization", "Bearer " + token);
        return headers;
    }

    public static void main(String[] args) {
        HkPolyProperties hkPolyProperties = new HkPolyProperties();
        hkPolyProperties.setHost("https://ss2-api.4s-hk.com");
        hkPolyProperties.setUsername("test-user");
        hkPolyProperties.setPassword("tu123456");
        hkPolyProperties.setVoucher("50baa830c40947ffb65bdef614bbbca63gjgil");

        HkPolyApi hkPolyApi = new HkPolyApi(hkPolyProperties);
        String token = hkPolyApi.getToken();
        System.out.println(token);

        String sseToken = hkPolyApi.getSseToken(token);
        System.out.println(sseToken);
    }

}

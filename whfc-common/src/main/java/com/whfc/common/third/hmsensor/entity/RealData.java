package com.whfc.common.third.hmsensor.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/4 16:02
 */
public class RealData implements Serializable {

    /**
     * 序号
     */
    private String id;

    /**
     * 编码
     */
    private String cod;

    /**
     * 压力
     */
    private Double d1;

    /**
     * 单位
     */
    private String d2;

    /**
     * 电压
     */
    private Double d3;

    /**
     * 信号
     */
    private Integer d4;

    /**
     * 时间
     */
    private Date dte;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCod() {
        return cod;
    }

    public void setCod(String cod) {
        this.cod = cod;
    }

    public Double getD1() {
        return d1;
    }

    public void setD1(Double d1) {
        this.d1 = d1;
    }

    public String getD2() {
        return d2;
    }

    public void setD2(String d2) {
        this.d2 = d2;
    }

    public Double getD3() {
        return d3;
    }

    public void setD3(Double d3) {
        this.d3 = d3;
    }

    public Integer getD4() {
        return d4;
    }

    public void setD4(Integer d4) {
        this.d4 = d4;
    }

    public Date getDte() {
        return dte;
    }

    public void setDte(Date dte) {
        this.dte = dte;
    }
}

package com.whfc.common.third.xiaoma.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/30 17:09
 */
@Data
public class MaintenanceInfo implements Serializable {

    /**
     * 机器地址
     */
    private String machineAddress;

    /**
     * 客户
     */
    private String saleCustName;

    /**
     * 服务人员
     */
    private String userName;

    /**
     * 服务费
     */
    private Double timeMoney;

    /**
     * 配件费
     */
    private Double partsMoney;

    /**
     * 服务配件销售ID
     */
    private String servPartSales;

    /**
     * 服务订单时间
     */
    private Date servPartsDate;

    /**
     * 配件信息
     */
    private List<PartInfo> partsInfo;
}

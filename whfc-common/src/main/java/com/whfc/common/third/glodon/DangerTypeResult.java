package com.whfc.common.third.glodon;

import com.whfc.common.result.Result;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/12 14:32
 */
public class DangerTypeResult extends Result {

    private List<DangerType> data;

    @Override
    public List<DangerType> getData() {
        return data;
    }

    public void setData(List<DangerType> data) {
        this.data = data;
    }
}

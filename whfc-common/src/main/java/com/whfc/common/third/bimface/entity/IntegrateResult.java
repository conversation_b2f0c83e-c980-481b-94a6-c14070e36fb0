package com.whfc.common.third.bimface.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 模型集成结果
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-17 9:49
 */
public class IntegrateResult implements Serializable {

    /**
     * 集成ID
     */
    private String integrateId;

    /**
     * 资源ID
     */
    private String sourceId;

    /**
     * 数据包ID
     */
    private String databagId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 缩略图
     */
    private List<String> thumbnail;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 集成文件名
     */
    private String name;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 任务状态
     */
    private String appKey;

    public String getIntegrateId() {
        return integrateId;
    }

    public void setIntegrateId(String integrateId) {
        this.integrateId = integrateId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getDatabagId() {
        return databagId;
    }

    public void setDatabagId(String databagId) {
        this.databagId = databagId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(List<String> thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

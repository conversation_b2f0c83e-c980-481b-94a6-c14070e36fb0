package com.whfc.common.third.cloudm.entity;

import java.io.Serializable;

/**
 * @Description: 请求结果
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 15:38
 */
public class Result<T> implements Serializable {

    /**
     * 请求是否成功
     */
    private boolean success;

    /**
     * 请求码，800表示成功
     */
    private int code;

    /**
     * 提示消息
     */
    private String message;

    /**
     * 开发调用阶段的错误提示消息
     */
    private String devMsg;

    /**
     * 请求结果
     */
    private T result;

    /**
     * 分页信息
     */
    private Page page;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDevMsg() {
        return devMsg;
    }

    public void setDevMsg(String devMsg) {
        this.devMsg = devMsg;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}

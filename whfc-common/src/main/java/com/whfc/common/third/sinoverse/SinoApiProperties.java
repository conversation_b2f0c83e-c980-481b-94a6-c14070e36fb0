package com.whfc.common.third.sinoverse;

import java.io.Serializable;

/**
 * @Description:
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2024/8/13 9:40
 */
public class SinoApiProperties implements Serializable {

    private String host;

    private String username;

    private String password;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}

package com.whfc.common.third.indoor.uwb;

import java.io.Serializable;
import java.util.Date;

/**
 * 实时位置
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/17 17:16
 */
public class UwbRealPosition implements Serializable {

    /**
     * 令牌
     */
    private String token;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 类型: 人员-RYLDKJDZ ,车辆-CLLDKJDZ
     */
    private String type;

    /**
     * 时间
     */
    private Date time;

    /**
     * 设备编号
     */
    private String tagId;

    /**
     * 设备电量
     */
    private Integer tagPower;

    /**
     * Sos状态 1触发 0未触发
     */
    private Integer tagSos;

    /**
     * 人员运动状态 1运动 0静止
     */
    private Integer tagState;

    /**
     * 人员x坐标
     */
    private Double personX;

    /**
     * 人员y坐标
     */
    private Double personY;

    /**
     * 人员z坐标
     */
    private Double personZ;

    /**
     * 基站X坐标
     */
    private Double anchorX;

    /**
     * 基站y坐标
     */
    private Double anchorY;

    /**
     * 基站z坐标
     */
    private Double anchorZ;

    /**
     * 人员到基站的距离
     */
    private Double anchorDis;

    /**
     * 设备距离洞口的距离
     */
    private Double gateDis;

    /**
     * 车辆经度
     */
    private Double carLon;

    /**
     * 车辆纬度
     */
    private Double carLat;

    /**
     * 车辆海拔
     */
    private Double carHei;

    /**
     * 车辆x坐标
     */
    private Double carX;

    /**
     * 车辆y坐标
     */
    private Double carY;

    /**
     * 车辆x坐标
     */
    private Double carZ;

    private Double lng;

    private Double lat;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public Integer getTagPower() {
        return tagPower;
    }

    public void setTagPower(Integer tagPower) {
        this.tagPower = tagPower;
    }

    public Integer getTagSos() {
        return tagSos;
    }

    public void setTagSos(Integer tagSos) {
        this.tagSos = tagSos;
    }

    public Integer getTagState() {
        return tagState;
    }

    public void setTagState(Integer tagState) {
        this.tagState = tagState;
    }

    public Double getPersonX() {
        return personX;
    }

    public void setPersonX(Double personX) {
        this.personX = personX;
    }

    public Double getPersonY() {
        return personY;
    }

    public void setPersonY(Double personY) {
        this.personY = personY;
    }

    public Double getPersonZ() {
        return personZ;
    }

    public void setPersonZ(Double personZ) {
        this.personZ = personZ;
    }

    public Double getAnchorX() {
        return anchorX;
    }

    public void setAnchorX(Double anchorX) {
        this.anchorX = anchorX;
    }

    public Double getAnchorY() {
        return anchorY;
    }

    public void setAnchorY(Double anchorY) {
        this.anchorY = anchorY;
    }

    public Double getAnchorZ() {
        return anchorZ;
    }

    public void setAnchorZ(Double anchorZ) {
        this.anchorZ = anchorZ;
    }

    public Double getAnchorDis() {
        return anchorDis;
    }

    public void setAnchorDis(Double anchorDis) {
        this.anchorDis = anchorDis;
    }

    public Double getGateDis() {
        return gateDis;
    }

    public void setGateDis(Double gateDis) {
        this.gateDis = gateDis;
    }

    public Double getCarLon() {
        return carLon;
    }

    public void setCarLon(Double carLon) {
        this.carLon = carLon;
    }

    public Double getCarLat() {
        return carLat;
    }

    public void setCarLat(Double carLat) {
        this.carLat = carLat;
    }

    public Double getCarHei() {
        return carHei;
    }

    public void setCarHei(Double carHei) {
        this.carHei = carHei;
    }

    public Double getCarX() {
        return carX;
    }

    public void setCarX(Double carX) {
        this.carX = carX;
    }

    public Double getCarY() {
        return carY;
    }

    public void setCarY(Double carY) {
        this.carY = carY;
    }

    public Double getCarZ() {
        return carZ;
    }

    public void setCarZ(Double carZ) {
        this.carZ = carZ;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }
}

package com.whfc.common.third.bresee;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 人脸识别结果
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/7 12:04
 */
@Data
public class FaceResult implements Serializable {

    private String DeviceName;

    private String DeviceAddr0;

    private String DeviceAddr1;

    private String SerialNumber;

    private String IPCSerialNum;

    private String IPCAddr;

    private String ChannelId;

    private String ChannelName;

    /**
     * 告警类型
     * 11: 人脸抓拍报警
     * 12:人脸比对成功报警
     * 13:人脸比对失败报警
     */
    private String AlarmType;

    private String AlarmId;

    private Long Timestamp;

    private String BigImageURL;

    private Img BigImage;

    private String SmallImageURL;

    private Img SmallImage;

    private FaceAttr FaceAttr;

    private CompareResult CompareResult;
}

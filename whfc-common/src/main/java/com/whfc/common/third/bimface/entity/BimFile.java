package com.whfc.common.third.bimface.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 上传文件响应数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-10 16:13
 */
public class BimFile extends Result implements Serializable {

    /**
     * 模型文件ID
     */
    private String fileId;

    /**
     * 状态 : uploading, success, failure
     */
    private String status;

    /**
     * 长度
     */
    private Long length;

    /**
     * 名称
     */
    private String name;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * etag
     */
    private String etag;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getLength() {
        return length;
    }

    public void setLength(Long length) {
        this.length = length;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getEtag() {
        return etag;
    }

    public void setEtag(String etag) {
        this.etag = etag;
    }
}

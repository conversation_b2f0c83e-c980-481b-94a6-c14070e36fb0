package com.whfc.common.third.zzgondy.entity;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 10:55
 */
public class DeviceItem implements Serializable {

    private String digitalID;
    private String digitalName;
    private String unit;
    private String backColor;
    private String backImage;

    public String getDigitalID() {
        return digitalID;
    }

    public void setDigitalID(String digitalID) {
        this.digitalID = digitalID;
    }

    public String getDigitalName() {
        return digitalName;
    }

    public void setDigitalName(String digitalName) {
        this.digitalName = digitalName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBackColor() {
        return backColor;
    }

    public void setBackColor(String backColor) {
        this.backColor = backColor;
    }

    public String getBackImage() {
        return backImage;
    }

    public void setBackImage(String backImage) {
        this.backImage = backImage;
    }
}

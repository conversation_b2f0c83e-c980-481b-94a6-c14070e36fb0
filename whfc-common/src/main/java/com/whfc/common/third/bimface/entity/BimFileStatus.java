package com.whfc.common.third.bimface.entity;

import java.io.Serializable;

/**
 * @Description: 上传文件状态
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-10 16:13
 */
public class BimFileStatus extends Result implements Serializable {

    /**
     * 模型文件ID
     */
    private String fileId;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态 : uploading, success, failure
     */
    private String status;

    /**
     * 失败原因
     */
    private String failedReason;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }
}

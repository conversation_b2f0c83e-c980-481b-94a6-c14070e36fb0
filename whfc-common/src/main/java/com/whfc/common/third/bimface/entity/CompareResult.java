package com.whfc.common.third.bimface.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 模型对比结果
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-17 9:49
 */
public class CompareResult implements Serializable {

    /**
     * 对比ID
     */
    private String compareId;

    /**
     * 资源ID
     */
    private String sourceId;

    /**
     * worker类型
     */
    private String workerType;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 缩略图
     */
    private List<String> thumbnail;

    /**
     * 耗时
     */
    private Integer cost;

    /**
     * 离线数据包状态
     */
    private String offlineDatabagStatus;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 对比名称
     */
    private String name;

    /**
     * 项目ID
     */
    private String projectId;

    public String getCompareId() {
        return compareId;
    }

    public void setCompareId(String compareId) {
        this.compareId = compareId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getWorkerType() {
        return workerType;
    }

    public void setWorkerType(String workerType) {
        this.workerType = workerType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(List<String> thumbnail) {
        this.thumbnail = thumbnail;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public String getOfflineDatabagStatus() {
        return offlineDatabagStatus;
    }

    public void setOfflineDatabagStatus(String offlineDatabagStatus) {
        this.offlineDatabagStatus = offlineDatabagStatus;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}

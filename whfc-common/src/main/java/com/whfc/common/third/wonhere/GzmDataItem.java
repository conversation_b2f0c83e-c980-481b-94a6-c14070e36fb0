package com.whfc.common.third.wonhere;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 高支模监测数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-30 16:55
 */
@Data
public class GzmDataItem implements Serializable {

    /**
     * 采集仪的设备编号
     */
    private String devid;

    /**
     * 设备电压
     */
    private Double volt;

    /**
     * 报警状态，0：正常  1：预警  2：报警  3：控制
     */
    private Integer alarm;

    /**
     * 采集时间 “yyyy-MM-dd HH:mm:ss”
     */
    private Date datetime;

    /**
     * 设备类型，95：无线倾角、9：无线荷重 10：无线拉线（竖向沉降）、11：无线拉线（水平位移）
     */
    private Integer type;

    /**
     * 数据值，元素个数与传感器相关
     */
    private List<Double> val;

    /**
     * 数值1
     */
    private Double f1;

    /**
     * 数值2
     */
    private Double f2;

    /**
     * 数值3
     */
    private Double f3;

    /**
     * 数值4
     */
    private Double f4;
}

package com.whfc.common.third.cloudm.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 17:11
 */
public class OilAddPoint implements Serializable {

    private String deviceId;

    private String oilId;

    /**
     * 加油时间
     */
    private Date addOilTime;

    /**
     * 加油量
     */
    private Double oilCharge;

    private Double lng;

    private Double lat;

    private String address;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getOilId() {
        return oilId;
    }

    public void setOilId(String oilId) {
        this.oilId = oilId;
    }

    public Date getAddOilTime() {
        return addOilTime;
    }

    public void setAddOilTime(Date addOilTime) {
        this.addOilTime = addOilTime;
    }

    public Double getOilCharge() {
        return oilCharge;
    }

    public void setOilCharge(Double oilCharge) {
        this.oilCharge = oilCharge;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}

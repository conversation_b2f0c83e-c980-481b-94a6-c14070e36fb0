package com.whfc.common.third.indoor.msg;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/12 17:02
 */
public class LocationMsg extends Msg {

    private String tagId;

    private String tagName;

    private Double x;

    private Double y;

    private Double lng;

    private Double lat;

    /**
     * 地图ID
     */
    private String mapId;

    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 基站ID
     */
    private String mainStationId;

    /**
     * 距离
     */
    private Integer distance;

    /**
     * 电量百分比
     */
    private Integer vbatPercent;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Double getX() {
        return x;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public Double getY() {
        return y;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getMapId() {
        return mapId;
    }

    public void setMapId(String mapId) {
        this.mapId = mapId;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getMainStationId() {
        return mainStationId;
    }

    public void setMainStationId(String mainStationId) {
        this.mainStationId = mainStationId;
    }

    public Integer getDistance() {
        return distance;
    }

    public void setDistance(Integer distance) {
        this.distance = distance;
    }

    public Integer getVbatPercent() {
        return vbatPercent;
    }

    public void setVbatPercent(Integer vbatPercent) {
        this.vbatPercent = vbatPercent;
    }
}

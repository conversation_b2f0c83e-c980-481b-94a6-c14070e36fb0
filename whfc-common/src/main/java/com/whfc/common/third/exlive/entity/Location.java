package com.whfc.common.third.exlive.entity;

import java.io.Serializable;
import java.util.Date;

public class Location implements Serializable {

    private String vehicle_id;

    private String vehicle_name;

    private String remarks;

    private String remarks2;

    private String client_name;

    private Date recvtime;

    private Date gpstime;

    private Double lng;

    private Double lat;

    private Double glng;

    private Double glat;

    private String location_mode_str;

    /**
     * 有效/无效
     */
    private String av_str;

    /**
     * 当日里程
     */
    private Double today_distance;

    /**
     * 总里程
     */
    private Double distance;

    /**
     * 地址
     */
    private String adree;

    /**
     * 是否在线 true在线 false离线
     */
    private Boolean online;

    public String getVehicle_id() {
        return vehicle_id;
    }

    public void setVehicle_id(String vehicle_id) {
        this.vehicle_id = vehicle_id;
    }

    public String getVehicle_name() {
        return vehicle_name;
    }

    public void setVehicle_name(String vehicle_name) {
        this.vehicle_name = vehicle_name;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }

    public String getClient_name() {
        return client_name;
    }

    public void setClient_name(String client_name) {
        this.client_name = client_name;
    }

    public Date getRecvtime() {
        return recvtime;
    }

    public void setRecvtime(Date recvtime) {
        this.recvtime = recvtime;
    }

    public Date getGpstime() {
        return gpstime;
    }

    public void setGpstime(Date gpstime) {
        this.gpstime = gpstime;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getGlng() {
        return glng;
    }

    public void setGlng(Double glng) {
        this.glng = glng;
    }

    public Double getGlat() {
        return glat;
    }

    public void setGlat(Double glat) {
        this.glat = glat;
    }

    public String getLocation_mode_str() {
        return location_mode_str;
    }

    public void setLocation_mode_str(String location_mode_str) {
        this.location_mode_str = location_mode_str;
    }

    public String getAv_str() {
        return av_str;
    }

    public void setAv_str(String av_str) {
        this.av_str = av_str;
    }

    public Double getToday_distance() {
        return today_distance;
    }

    public void setToday_distance(Double today_distance) {
        this.today_distance = today_distance;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public String getAdree() {
        return adree;
    }

    public void setAdree(String adree) {
        this.adree = adree;
    }

    public Boolean getOnline() {
        return online;
    }

    public void setOnline(Boolean online) {
        this.online = online;
    }
}

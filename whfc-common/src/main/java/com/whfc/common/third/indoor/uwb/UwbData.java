package com.whfc.common.third.indoor.uwb;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * uwb数据
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/26 16:39
 */
@Data
public class UwbData implements Serializable {

    private String type;

    private Date time;

    private String tagId;

    /************人员**************/

    private Integer tagPower;

    private Integer tagSos;

    private Integer tagState;

    private Double personX;

    private Double personY;

    private Double personZ;

    private Double anchorX;

    private Double anchorY;

    private Double anchorZ;

    private Double anchorDis;

    private Double gateDis;


    /***********车辆*************/

    private Double carLon;

    private Double carLat;

    private Double carHei;

    private Double carX;

    private Double carY;

    private Double carZ;
}

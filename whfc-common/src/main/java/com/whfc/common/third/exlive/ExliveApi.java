package com.whfc.common.third.exlive;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.exlive.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 车辆定位平台接口
 */
public class ExliveApi {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String SUCCESS = "200";

    private static final String LOGIN_URI = "/video/webapi/user/login";

    private static final String DEVICE_LIST_URI = "/video/webapi/vehicle/list";

    private static final String LOCATION_URI = "/video/webapi/location/get-location-use-carids";

    private static final String TRAJECTORY_URI = "/video/webapi/location/get-trajectory-use-id";

    private String host;

    private String id;

    private String secret;

    public ExliveApi(String host, String id, String secret) {
        this.host = host;
        this.id = id;
        this.secret = secret;
    }

    public Token login() {
        String url = host + LOGIN_URI;
        JSONObject param = new JSONObject();
        param.put("id", id);
        param.put("secret", secret);
        String body = param.toJSONString();
        String response = HttpUtil.doPost(url, body);
        TokenResult result = JSONUtil.parseObject(response, TokenResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.info("获取token失败,{}", response);
        return null;
    }

    /**
     * 查询车辆列表
     *
     * @param token
     * @param page
     * @param size
     * @return
     */
    public VehicleQueryData queryVehicleList(String token, Integer page, Integer size) {
        String url = host + DEVICE_LIST_URI;

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);

        JSONObject param = new JSONObject();
        param.put("page", page);
        param.put("size", size);

        String response = HttpUtil.doPost(url, param.toString(), headers);
        VehicleQueryResult result = JSONUtil.parseObject(response, VehicleQueryResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.info("获取设备列表失败,{}", response);
        return null;
    }

    /**
     * 查询车辆实时定位
     *
     * @param token
     * @param carIds
     * @return
     */
    public LocationData queryLocationData(String token, Collection<String> carIds) {
        String url = host + LOCATION_URI;

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);

        JSONObject param = new JSONObject();
        param.put("car_ids", StringUtils.join(carIds, ","));

        String response = HttpUtil.doPost(url, param.toString(), headers);
        LocationResult result = JSONUtil.parseObject(response, LocationResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getData();
        } else {
            logger.info("获取设备实时定位失败,{}", response);
        }
        return null;
    }

    /**
     * 查询车辆轨迹
     *
     * @param token
     * @param car_id
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Location> queryTrajectoryData(String token, String car_id, Date startTime, Date endTime) {
        String url = host + TRAJECTORY_URI;

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);

        JSONObject param = new JSONObject();
        param.put("car_id", car_id);
        param.put("start_time", DateUtil.formatDateTime(startTime));
        param.put("end_time", DateUtil.formatDateTime(endTime));

        String response = HttpUtil.doPost(url, param.toString(), headers);
        TrajectoryResult result = JSONUtil.parseObject(response, TrajectoryResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getData();
        } else {
            logger.info("获取设备实时定位失败,{}", response);
        }

        return Collections.emptyList();
    }
}

package com.whfc.common.third.exlive.entity;

import java.io.Serializable;

public class Vehicle implements Serializable {

    private String vehicle_id;

    private String vehicle_name;

    private String devicecoding;

    private String plate_color;

    private String terminal_model;

    private String terminal_wired;

    private String vehicle_type;

    private String fromno;

    private String installdate;

    private String passageway;

    private String client_id;

    private String client_name;

    private String remark;

    public String getVehicle_id() {
        return vehicle_id;
    }

    public void setVehicle_id(String vehicle_id) {
        this.vehicle_id = vehicle_id;
    }

    public String getVehicle_name() {
        return vehicle_name;
    }

    public void setVehicle_name(String vehicle_name) {
        this.vehicle_name = vehicle_name;
    }

    public String getDevicecoding() {
        return devicecoding;
    }

    public void setDevicecoding(String devicecoding) {
        this.devicecoding = devicecoding;
    }

    public String getPlate_color() {
        return plate_color;
    }

    public void setPlate_color(String plate_color) {
        this.plate_color = plate_color;
    }

    public String getTerminal_model() {
        return terminal_model;
    }

    public void setTerminal_model(String terminal_model) {
        this.terminal_model = terminal_model;
    }

    public String getTerminal_wired() {
        return terminal_wired;
    }

    public void setTerminal_wired(String terminal_wired) {
        this.terminal_wired = terminal_wired;
    }

    public String getVehicle_type() {
        return vehicle_type;
    }

    public void setVehicle_type(String vehicle_type) {
        this.vehicle_type = vehicle_type;
    }

    public String getFromno() {
        return fromno;
    }

    public void setFromno(String fromno) {
        this.fromno = fromno;
    }

    public String getInstalldate() {
        return installdate;
    }

    public void setInstalldate(String installdate) {
        this.installdate = installdate;
    }

    public String getPassageway() {
        return passageway;
    }

    public void setPassageway(String passageway) {
        this.passageway = passageway;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getClient_name() {
        return client_name;
    }

    public void setClient_name(String client_name) {
        this.client_name = client_name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

package com.whfc.common.third.ansafe;

import com.whfc.common.third.ansafe.entity.*;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 安拾平台
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-16 10:01
 */
public class AnSafeApi {

    private static final Logger logger = LoggerFactory.getLogger(AnSafeApi.class);

    /**
     * 获取龙门吊设备信息
     */
    private static final String GANTRY_SN_URL = "http://as.an-safe.com/api/gantry/receive";

    /**
     * 获取龙门吊运行实时数据
     */
    private static final String GANTRY_WORKING_URL = "http://as.an-safe.com/api/gantry/working?sn=%s";

    /**
     * 获取塔机设备
     */
    private static final String CRANE_SN_URL = "http://as.an-safe.com/api/crane/receive_list";

    /**
     * 获取塔机实时数据
     */
    private static final String CRANE_WORKING_URL = "http://as.an-safe.com/api/crane/working_now?sn=%s";

    /**
     * 获取塔机运行记录数据
     */
    private static final String CRANE_WORK_LOOP_URL = "http://as.an-safe.com/api/crane/loop/working_loop?sn=%s";

    /**
     * 成功状态码
     */
    private static final Integer SUCCESS = 200;

    private String accessKey;

    public AnSafeApi(String accessKey) {
        this.accessKey = accessKey;
    }

    /**
     * 获取龙门吊设备信息
     *
     * @return 龙门吊设备信息
     */
    public List<GantryDevice> getGantryDevice() {
        Map<String, String> headers = new HashMap<>(1);
        headers.put("accessKey", accessKey);
        String response = HttpUtil.doGet(GANTRY_SN_URL, headers, null);
        logger.debug("获取安拾平台门机设备SN,{}", response);
        GantryDeviceResult result = JSONUtil.parseObject(response, GantryDeviceResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getDate();
        }
        return Collections.emptyList();
    }

    /**
     * 获取龙门吊设备数据
     *
     * @param sn 龙门吊设备编号
     * @return 龙门吊设备数据
     */
    public GantryWork getGantryWork(String sn) {
        String url = String.format(GANTRY_WORKING_URL, sn);
        Map<String, String> headers = new HashMap<>(1);
        headers.put("accessKey", accessKey);
        String response = HttpUtil.doGet(url, headers, null);
        logger.debug("获取安拾平台门机设备数据,{}", response);
        GantryWorkResult result = JSONUtil.parseObject(response, GantryWorkResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getDate();
        }
        return null;
    }

    /**
     * 获取塔机设备信息
     *
     * @return 塔机设备信息
     */
    public List<CraneDevice> getCraneDevice() {
        Map<String, String> headers = new HashMap<>(1);
        headers.put("accessKey", accessKey);
        String response = HttpUtil.doGet(CRANE_SN_URL, headers, null);
        logger.debug("获取安拾平台塔机设备SN,{}", response);
        CraneDeviceResult result = JSONUtil.parseObject(response, CraneDeviceResult.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getDate();
        }
        return Collections.emptyList();
    }

    /**
     * 获取塔机设备数据
     *
     * @param sn 塔机设备编号
     * @return 塔机设备数据
     */
    public CraneWork getCraneWork(String sn) {
        String url = String.format(CRANE_WORKING_URL, sn);
        Map<String, String> headers = new HashMap<>(1);
        headers.put("accessKey", accessKey);
        String response = HttpUtil.doGet(url, headers, null);
        logger.debug("获取安拾平台塔机设备数据信息,{}", response);
        CraneWorkResult craneWorkResult = JSONUtil.parseObject(response, CraneWorkResult.class);

        if (craneWorkResult != null && SUCCESS.equals(craneWorkResult.getCode())) {
            return craneWorkResult.getDate();
        }
        return null;
    }

    /**
     * 获取塔机运行记录数据
     *
     * @param sn 塔机设备编号
     * @return 塔机运行记录
     */
    public CraneWorkLoop getCraneWorkLoop(String sn) {
        String url = String.format(CRANE_WORK_LOOP_URL, sn);
        Map<String, String> headers = new HashMap<>(1);
        headers.put("accessKey", accessKey);
        String response = HttpUtil.doGet(url, headers, null);
        logger.debug("获取安拾平台塔机设备实时运行记录数据信息,{}", response);
        CraneWorkLoopResult craneWorkLoopResult = JSONUtil.parseObject(response, CraneWorkLoopResult.class);
        if (craneWorkLoopResult != null && SUCCESS.equals(craneWorkLoopResult.getCode())) {
            return craneWorkLoopResult.getDate();
        }
        return null;
    }
}

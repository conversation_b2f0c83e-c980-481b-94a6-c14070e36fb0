package com.whfc.common.third.exlive.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/9/15 10:06
 */
public class TrajectoryResult extends Result implements Serializable {

    private List<Location> data;

    public List<Location> getData() {
        return data;
    }

    public void setData(List<Location> data) {
        this.data = data;
    }
}


package com.whfc.common.third.glodon;

import lombok.Data;

import java.util.List;

@Data
public class DangerType {

    private Boolean catalogNode;

    private Boolean deleteStatus;

    private String fullId;

    private String id;

    private Boolean leaf;

    private Long level;

    private String name;

    private String fullName;

    private Long order;

    private String remark;

    private String safeCatalogId;

    private String pid;

    private List<DangerType> childNodes;
}

package com.whfc.common.third.energy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.*;

/**
 * @Description 恒焊水电表工具类
 * <AUTHOR>
 * @Date 2021-09-16 9:04
 * @Version 1.0
 * http://doc-api.tqdianbiao.com/#/api1/2/2_1
 */
public class TQUtils {

    private static Logger logger = LoggerFactory.getLogger(TQUtils.class);

    private static final String SUCCESS = "1";

    private static Map<String, String> params = new HashMap<>();

    static {
        params.put("179ffa35f9212095a51677491bab86a8", "http://api1.tqdianbiao.com/Api/DataRequest");
        params.put("cabc9f9f04fa686b766b6903e3068891", "https://iot.tqdianbiao.com/Api/DataRequest");
    }

    private String authCode;

    private String url;

    public TQUtils(String authCode, String url) {
        this.authCode = authCode;
        this.url = url;
    }

    /**
     * 获取授权码
     *
     * @return
     */
    public static Map<String, String> getParams() {
        return params;
    }

    /**
     * 获取电表历史数据
     *
     * @return
     */
    public List<TQResultDTO> getElectricHistoryData() {
        String fids = StringUtils.join(TQConst.ELECTRIC_FIDS.keySet(), ",");
        return getHistoryData(fids);
    }

    /**
     * 获取水表历史数据
     *
     * @return
     */
    public List<TQResultDTO> getWaterHistoryData() {
        String fids = StringUtils.join(TQConst.WATER_FIDS.keySet(), ",");
        return getHistoryData(fids);
    }

    /**
     * 查询历史数据
     *
     * @param fids
     * @return
     */
    public List<TQResultDTO> getHistoryData(String fids) {
        Date endTime = new Date();
        Date startTime = DateUtil.addDays(endTime, -2);
        try {
            Map<String, String> params = new HashMap<>(5);
            params.put("auth", authCode);
            params.put("type", "json");
            params.put("functionids", fids);
            params.put("start_time", URLEncoder.encode(DateUtil.formatDateTime(startTime), "UTF-8"));
            params.put("end_time", URLEncoder.encode(DateUtil.formatDateTime(endTime), "UTF-8"));

            String rep = HttpUtil.doGet(url, null, params);
            JSONObject repJson = JSONObject.parseObject(rep);
            logger.info("查询水电表历史数据返回数据，rep:{}", rep);
            if (SUCCESS.equals(repJson.getString("status"))) {
                JSONArray dataArr = repJson.getJSONArray("data");
                if (dataArr.size() > 0) {
                    return dataArr.toJavaList(TQResultDTO.class);
                }
            }

        } catch (Exception e) {
            logger.error("查询水电表历史数据失败", e);
        }
        return Collections.emptyList();
    }
}

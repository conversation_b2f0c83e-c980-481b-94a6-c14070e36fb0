package com.whfc.common.third.gzglgcjt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.gzglgcjt.entity.AssetRecord;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 广州公路工程-上报数据API
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/10 16:03
 */
public class GzglgcjtApi {

    private Logger logger = LoggerFactory.getLogger(GzglgcjtApi.class);

    private static final String UPDATE_ASERT_RECORD = "/api/cube/restful/interface/saveOrUpdateModeData/AssetRecord";

    private String host;

    private String systemid;

    private String password;

    public GzglgcjtApi(String host, String systemid, String password) {
        this.host = host;
        this.systemid = systemid;
        this.password = password;
    }

    /**
     * 固定资产
     *
     * @param assetRecord
     */
    public String updateAssetRecord(AssetRecord assetRecord) {

        //当前日期
        String currentDate = getCurrentDate();
        //当前时间
        String currentTime = getCurrentTime();
        //获取时间戳
        String currentTimeTamp = getTimestamp();
        //md5
        String md5Source = systemid + password + currentTimeTamp;
        String md5OfStr = getMD5Str(md5Source).toLowerCase();

        //header
        Map header = new HashMap<>();
        header.put("systemid", systemid);
        header.put("currentDateTime", currentTimeTamp);
        header.put("Md5", md5OfStr);

        //封装operationinfo参数
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("operator", "15273");
        operationinfo.put("operationDate", currentDate);
        operationinfo.put("operationTime", currentTime);

        //封装mainTable参数
        //JSONObject mainTable = new JSONObject();
        //mainTable.put("id", "1");

        //封装data
        JSONObject data = new JSONObject();
        data.put("operationinfo", operationinfo);
        data.put("mainTable", assetRecord);

        JSONArray dataArr = new JSONArray();
        dataArr.add(data);

        //封装datajson
        Map datajson = new HashMap<>();
        datajson.put("data", dataArr);
        datajson.put("header", header);

        Map params = new HashMap<>();
        params.put("datajson", JSONUtil.toString(datajson));

        String url = host + UPDATE_ASERT_RECORD;
        String response = HttpUtil.doPost(url, params);

        logger.info("资产登记参数:{},{}", JSONUtil.toString(params), response);

        return response;
    }

    public String getMD5Str(String plainText) {
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            logger.error("", e);
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        // 不能把变量放到循环条件，值改变之后会导致条件变化。如果生成30位 只能生成31位md5
        int tempIndex = 32 - md5code.length();
        for (int i = 0; i < tempIndex; i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    public static String getCurrentTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        String currenttime = (timestamp.toString()).substring(11, 13) + ":" + (timestamp.toString()).substring(14, 16) + ":"
                + (timestamp.toString()).substring(17, 19);
        return currenttime;
    }

    public static String getCurrentDate() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        String currentdate = (timestamp.toString()).substring(0, 4) + "-" + (timestamp.toString()).substring(5, 7) + "-"
                + (timestamp.toString()).substring(8, 10);
        return currentdate;
    }

    /**
     * 获取当前日期时间。 YYYY-MM-DD HH:MM:SS
     *
     * @return 当前日期时间
     */
    public static String getCurDateTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        return (timestamp.toString()).substring(0, 19);
    }

    /**
     * 获取时间戳   格式如：19990101235959
     *
     * @return
     */
    public static String getTimestamp() {
        return getCurDateTime().replace("-", "").replace(":", "").replace(" ", "");
    }
}

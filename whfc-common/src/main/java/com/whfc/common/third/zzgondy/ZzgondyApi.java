package com.whfc.common.third.zzgondy;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.zzgondy.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.*;

/**
 * 郑州港迪-气象站API
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 10:26
 */
public class ZzgondyApi {

    private static final Logger logger = LoggerFactory.getLogger(ZzgondyApi.class);

    private static final Integer SUCCESS = 200;

    private static final String HOST = "http://*************:60005";

    private static final String TOKEN_URL = "/gd/device/token";
    private static final String DEVICE_LIST_URL = "/gd/device/list";
    private static final String DEVICE_INFO_URL = "/gd/device/deviceInfo";
    private static final String REAL_DATA_URL = "/gd/device/realData";
    private static final String HISTORY_DATA_URL = "/gd/device/historyData";
    private static final String RELAY_INFO_URL = "/gd/device/relayInfo";
    private static final String RELAY_URL = "/gd/device/relay";
    private static final String RELAY_CONTROL_URL = "/gd/device/control";

    private String username;

    private String password;

    public ZzgondyApi(String username, String password) {
        this.username = username;
        this.password = password;
    }

    public String getToken() {
        String url = HOST + TOKEN_URL;
        JSONObject param = new JSONObject();
        param.put("loginName", username);
        param.put("password", password);
        String body = param.toJSONString();
        String response = HttpUtil.doPost(url, body);
        TokenResult result = JSONUtil.parseObject(response, TokenResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getData().getToken();
        }
        logger.error("获取token失败，{}", response);
        return null;
    }

    public List<Device> getDeviceList(String token) {
        String url = HOST + DEVICE_LIST_URL + "?pageIndex=1&pageSize=10";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        DeviceListResult result = JSONUtil.parseObject(response, DeviceListResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getRows();
        }
        return Collections.emptyList();
    }

    public Device getDeviceInfo(String token, String deviceNum) {
        String url = HOST + DEVICE_INFO_URL + "?deviceNum=" + deviceNum;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        DeviceInfoResult result = JSONUtil.parseObject(response, DeviceInfoResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.error("获取设备信息失败，{}", response);
        return null;
    }

    public RealData getRealData(String token, String deviceNum) {
        String url = HOST + REAL_DATA_URL + "?deviceNum=" + deviceNum;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        RealDataResult result = JSONUtil.parseObject(response, RealDataResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.error("获取设备实时数据失败，{}", response);
        return null;
    }

    public List<HistoryData> getHistoryData(String token, String deviceNum, Date startTime, Date endTime, Integer pageNum, Integer pageSize) {

        String begin = null;
        String end = null;
        try {
            begin = URLEncoder.encode(DateUtil.formatDateTime(startTime), "UTF-8");
            end = URLEncoder.encode(DateUtil.formatDateTime(endTime), "UTF-8");
        } catch (Exception ex) {
            logger.error("获取历史数据失败，{}", ex.getMessage());
        }

        String url = new StringBuilder()
                .append(HOST).append(HISTORY_DATA_URL)
                .append("?deviceNum=").append(deviceNum)
                .append("&beginTime=").append(begin)
                .append("&endTime=").append(end)
                .append("&pageIndex=").append(pageNum)
                .append("&pageSize=").append(pageSize)
                .toString();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        HistoryDataResult result = JSONUtil.parseObject(response, HistoryDataResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getRows();
        }
        return Collections.emptyList();
    }

    public List<RelayInfo> getRelayInfo(String token, String deviceNum) {
        String url = HOST + RELAY_INFO_URL + "?deviceNum=" + deviceNum;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        RelayInfoResult result = JSONUtil.parseObject(response, RelayInfoResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.error("获取继电器信息失败，{}", response);
        return Collections.emptyList();
    }

    public List<RelayInfo> getRelayStatus(String token, String deviceNum) {
        String url = HOST + RELAY_URL + "?deviceNum=" + deviceNum;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        RelayStatusResult result = JSONUtil.parseObject(response, RelayStatusResult.class);
        if (SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        logger.error("获取继电器状态失败，{}", response);
        return Collections.emptyList();
    }

    public void replayControl(String token, String deviceNum, String relayId, Integer status) {
        String url = HOST + RELAY_CONTROL_URL + "?deviceNum=" + deviceNum + "&relayId=" + relayId + "&status=" + status;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String response = HttpUtil.doGet(url, headers);
        RelayStatusResult result = JSONUtil.parseObject(response, RelayStatusResult.class);
        if (!SUCCESS.equals(result.getCode())) {
            logger.error("继电器控制失败，{}", response);
        }
    }
}

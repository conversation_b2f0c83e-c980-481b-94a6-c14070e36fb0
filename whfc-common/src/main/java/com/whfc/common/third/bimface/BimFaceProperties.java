package com.whfc.common.third.bimface;

/**
 * @Description: bimface配置参数
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-11 17:57
 */
public class BimFaceProperties {

    /**
     * appKey
     */
    private String appKey;

    /**
     * appSecret
     */
    private String appSecret;

    /**
     * BIM模型转换回调地址
     */
    private String callback;

    /**
     * 对象存储上传回调
     */
    private String ossCallback;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }

    public String getOssCallback() {
        return ossCallback;
    }

    public void setOssCallback(String ossCallback) {
        this.ossCallback = ossCallback;
    }
}

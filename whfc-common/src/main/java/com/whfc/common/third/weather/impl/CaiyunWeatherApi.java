package com.whfc.common.third.weather.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.enums.WindLevel;
import com.whfc.common.third.weather.WeatherApi;
import com.whfc.common.third.weather.WeatherDaily;
import com.whfc.common.third.weather.WeatherHourly;
import com.whfc.common.third.weather.WeatherInfo;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.UnicodeUtil;
import com.whfc.common.util.WindUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @description 彩云天气预报api
 * @date 2020-06-16
 */
public class CaiyunWeatherApi implements WeatherApi {

    private static final Logger logger = LoggerFactory.getLogger(CaiyunWeatherApi.class);

    private static final String URL = "https://api.caiyunapp.com/v2.5/{0}/{1}/weather.json?dailysteps=7&hourlysteps=24";

    private String token;

    public CaiyunWeatherApi(String token) {
        this.token = token;
    }

    @Override
    public WeatherInfo getWeather(Double lng, Double lat) {
        Assert.notNull(lng, "经度不能为空");
        Assert.notNull(lat, "纬度不能为空");
        String location = String.format("%s,%s", lng, lat);
        String url = MessageFormat.format(URL, token, location);
        logger.info("url:" + url);
        String response = HttpUtil.doGet(url);
        logger.debug("response:{}", response);

        WeatherInfo weatherInfo = null;

        JSONObject retJSON = JSON.parseObject(response);
        if (retJSON != null && "ok".equals(retJSON.getString("status"))) {
            JSONObject result = retJSON.getJSONObject("result");
            if (result != null) {
                weatherInfo = new WeatherInfo();
                // 实时天气
                if (result.containsKey("realtime")) {
                    JSONObject realtime = result.getJSONObject("realtime");
                    String skycon = realtime.getString("skycon");
                    Double temperature = realtime.getDouble("temperature");
                    Double windSpeed = realtime.getJSONObject("wind").getDouble("speed");
                    weatherInfo.setTemperature(temperature);
                    weatherInfo.setWeather(skycon);
                    weatherInfo.setWindSpeed(windSpeed);
                }

                // 未来两小时
                if (result.containsKey("minutely")) {
                    JSONObject minutely = result.getJSONObject("minutely");
                    JSONArray precipitationArray = minutely.getJSONArray("precipitation_2h");
                    String description = UnicodeUtil.decodeUnicode(minutely.getString("description"));
                    weatherInfo.setPrecipitationList(precipitationArray.toJavaList(Double.class));
                    weatherInfo.setDescription(description);
                }

                // 小时级别的天气预报
                if (result.containsKey("hourly")) {
                    JSONObject hourly = result.getJSONObject("hourly");
                    JSONArray skyconArray = hourly.getJSONArray("skycon");
                    JSONArray temperatureArray = hourly.getJSONArray("temperature");
                    JSONArray windArray = hourly.getJSONArray("wind");
                    List<WeatherHourly> hourlyWeatherList = skyconArray.toJavaList(WeatherHourly.class);
                    int j = 0;
                    for (WeatherHourly weatherHourly : hourlyWeatherList) {
                        Double hourlyTemperature = temperatureArray.getJSONObject(j).getDouble("value");
                        Double hourlyWindSpeed = windArray.getJSONObject(j).getDouble("speed");
                        weatherHourly.setTemperature(hourlyTemperature);
                        weatherHourly.setWindSpeed(hourlyWindSpeed);
                        j++;
                    }
                    weatherInfo.setHourlyWeatherList(hourlyWeatherList);
                }

                // 未来7天
                if (result.containsKey("daily")) {
                    JSONObject daily = result.getJSONObject("daily");
                    JSONArray temperatureList = daily.getJSONArray("temperature");
                    JSONArray skyconList = daily.getJSONArray("skycon");
                    JSONArray precipitationList = daily.getJSONArray("precipitation");
                    JSONArray windList = daily.getJSONArray("wind");
                    List<WeatherDaily> weatherList = temperatureList.toJavaList(WeatherDaily.class);
                    int i = 0;
                    for (WeatherDaily dailyWeatherDTO : weatherList) {
                        // 获取天气名称
                        dailyWeatherDTO.setWeather(skyconList.getJSONObject(i).getString("value"));
                        // 获取降水量
                        dailyWeatherDTO.setPrecipitation(precipitationList.getJSONObject(i).getDouble("avg"));
                        // 获取风向
                        JSONObject jsonObject = windList.getJSONObject(i).getJSONObject("avg");
                        Double direction = jsonObject.getDouble("direction");
                        dailyWeatherDTO.setWindDirection(WindUtil.angle2WindDirection(direction));
                        // 获取风力
                        Double speed = jsonObject.getDouble("speed");
                        dailyWeatherDTO.setWindPower(WindLevel.parseWindLevelBySpeed(speed).getLevel());
                        i++;
                    }
                    weatherInfo.setDailyWeatherList(weatherList);
                }
            }
        }

        return weatherInfo;
    }
}

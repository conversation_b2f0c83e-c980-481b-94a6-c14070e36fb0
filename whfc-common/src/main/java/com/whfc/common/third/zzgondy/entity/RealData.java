package com.whfc.common.third.zzgondy.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 11:01
 */
public class RealData implements Serializable {

    private String createTime;

    private String preStoreTime;

    private String deviceId;

    private String deviceNum;

    private List<Double> digital;

    private List<String> relay;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPreStoreTime() {
        return preStoreTime;
    }

    public void setPreStoreTime(String preStoreTime) {
        this.preStoreTime = preStoreTime;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceNum() {
        return deviceNum;
    }

    public void setDeviceNum(String deviceNum) {
        this.deviceNum = deviceNum;
    }

    public List<Double> getDigital() {
        return digital;
    }

    public void setDigital(List<Double> digital) {
        this.digital = digital;
    }

    public List<String> getRelay() {
        return relay;
    }

    public void setRelay(List<String> relay) {
        this.relay = relay;
    }
}

package com.whfc.common.third.zhiquli;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/20 11:05
 */
public enum ZhiquliAlarmType {

    NO_HELMET("NO_HELMET", "未佩戴安全帽检测"),

    NO_WORK_CLOTHES("NO_WORK_CLOTHES", "未穿工服检测"),

    NO_REFLECTVEST("NO_REFLECTVEST", "未穿戴反光衣检测"),

    AREA("AREA", "区域入侵"),

    SMOG("SMOG", "烟雾检测"),

    FIRE("FIRE", "明火检测"),

    CAR_TYPE("CAR_TYPE","车型识别");

    private String value;

    private String name;

    ZhiquliAlarmType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

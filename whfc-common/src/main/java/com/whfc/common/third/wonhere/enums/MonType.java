package com.whfc.common.third.wonhere.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 监测类型
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-30 17:14
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MonType {

    TYPE2(2, "测距(位移)"),

    TYPE3(3, "深层水平位移"),

    TYPE4(4, "沉降监测"),

    TYPE6(6, "倾斜(X&Y,角度)"),

    TYPE8(8, "倾斜(水平位移)"),

    TYPE11(11, "水位监测"),

    TYPE13(13, "钢筋应力"),

    TYPE14(14, "压力监测"),

    TYPE16(16, "应变监测"),

    TYPE17(17, "钢支撑轴力"),

    TYPE18(18, "锚杆拉力"),

    TYPE20(20, "混凝土温度"),

    TYPE27(27, "雨量");

    private Integer value;

    private String desc;

    MonType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MonType parseByValue(Integer value) {
        MonType[] types = MonType.values();
        for (MonType type : types) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}

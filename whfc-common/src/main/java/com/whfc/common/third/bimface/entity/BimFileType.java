package com.whfc.common.third.bimface.entity;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: BIM模型文件类型
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-13 11:59
 */
public class BimFileType {

    /**
     * 文件转换ID
     */
    public static final String fileId = "fileId";

    /**
     * 模型集成ID
     */
    public static final String integrateId = "integrateId";

    /**
     * 模型对比ID
     */
    public static final String compareId = "compareId";

    /**
     * 场景ID
     */
    public static final String sceneId = "sceneId";

    /**
     * 子模型ID
     */
    public static final String submodelId = "sceneId";

    /**
     * 碰撞检测ID
     */
    public static final String clashDetectiveId = "clashDetectiveId";

    /**
     * 模型文件类型
     */
    private static final String[] fileTypes = new String[]{
            "rvt",
            "rfa",
            "dwg",
            "dxf",
            "skp",
            "ifc",
            "dgn",
            "obj",
            "stl",
            "3ds",
            "dae",
            "ply",
            "igms",
            "zip",
            "fbx",
            "dwf",
            "nwd",
            "bmv"
    };

    /**
     * 获取所有bim文件类型
     *
     * @return
     */
    public static String[] getBimFileTypes() {
        return fileTypes;
    }

    /**
     * 是否bim文件类型
     *
     * @param fileName
     * @return
     */
    public static boolean isBimFileType(String fileName) {
        if (StringUtils.isNotBlank(fileName)) {
            for (String fileType : fileTypes) {
                if (fileName.toLowerCase().endsWith(fileType)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println(isBimFileType(".png"));
        System.out.println(isBimFileType(".dwg"));
        System.out.println(isBimFileType("dwg"));
    }

}

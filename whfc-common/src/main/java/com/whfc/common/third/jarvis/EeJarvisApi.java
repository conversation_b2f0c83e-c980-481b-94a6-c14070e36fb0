package com.whfc.common.third.jarvis;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 鹰眼API
 *
 * @Description:
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2024/6/11 9:34
 */
public class EeJarvisApi {

    private final Logger logger = LoggerFactory.getLogger(EeJarvisApi.class);

    //    private static final String LOGIN_URL = "https://dev-ocean.jarvisbim.com.cn/core/auth/password-login";
    private static final String LOGIN_URL = "http://*************:8000/api/core/v1.0/auth/pwd/sign-in";

    private static final Integer SUCCESS = 200;

    private String host;

    private String username;

    private String password;

    public EeJarvisApi(String host, String username, String password) {
        this.host = host;
        this.username = username;
        this.password = password;
    }

    public AccessToken getAccessToken() {
        JSONObject param = new JSONObject();
        param.put("username", username);
        param.put("password", password);
        String body = param.toJSONString();
        String response = HttpUtil.doPost(host, body);
        JSONObject ret = JSONObject.parseObject(response);
        Integer code = ret.getInteger("code");
        String msg = ret.getString("msg");
        if (SUCCESS.equals(code)) {
            return ret.getObject("data", AccessToken.class);
        }
        logger.error("getAccessToken error, code:{}, msg:{}", code, msg);
        return null;
    }
}

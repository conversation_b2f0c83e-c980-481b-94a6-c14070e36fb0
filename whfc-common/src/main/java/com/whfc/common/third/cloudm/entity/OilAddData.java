package com.whfc.common.third.cloudm.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 加油数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 17:10
 */
public class OilAddData implements Serializable {

    /**
     * 总的加油量
     */
    private Double addOilTotal;

    /**
     * 加油次数
     */
    private Integer addOilCount;

    /**
     * 加油详情
     */
    private List<OilAddPoint> addOilPointDTOList;

    public Double getAddOilTotal() {
        return addOilTotal;
    }

    public void setAddOilTotal(Double addOilTotal) {
        this.addOilTotal = addOilTotal;
    }

    public Integer getAddOilCount() {
        return addOilCount;
    }

    public void setAddOilCount(Integer addOilCount) {
        this.addOilCount = addOilCount;
    }

    public List<OilAddPoint> getAddOilPointDTOList() {
        return addOilPointDTOList;
    }

    public void setAddOilPointDTOList(List<OilAddPoint> addOilPointDTOList) {
        this.addOilPointDTOList = addOilPointDTOList;
    }
}

package com.whfc.common.third.bresee;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/20 10:57
 */
@Data
public class BehaviorResult implements Serializable {

    private String DeviceName;

    private String DeviceAddr0;

    private String DeviceAddr1;

    private String SerialNumber;

    private String IPCSerialNum;

    private String IPCAddr;

    private String ChannelId;

    private String ChannelName;

    private String AlarmType;

    private String AlarmId;

    private Integer ReportRate;

    private Long Timestamp;

    private String BigImageURL;

    private Img BigImage;

    private CompareResult CompareResult;
}

package com.whfc.common.third.weather;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-28
 */
public class WeatherHourly implements Serializable {

    @J<PERSON>NField(name = "datetime")
    private String time;

    @J<PERSON>NField(name = "value")
    private String weather;

    private String weatherPic;

    private Double windSpeed;

    private Double temperature;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public String getWeatherPic() {
        return weatherPic;
    }

    public void setWeatherPic(String weatherPic) {
        this.weatherPic = weatherPic;
    }

    public Double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
}

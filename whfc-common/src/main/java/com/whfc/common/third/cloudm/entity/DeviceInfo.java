package com.whfc.common.third.cloudm.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 设备信息
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 16:06
 */
public class DeviceInfo implements Serializable {

    /**
     * 设备id
     */
    private String id;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 型号
     */
    private String model;

    /**
     * 机架号
     */
    private String rackId;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 设备sn号
     */
    private String snId;

    /**
     * 工作总工时
     */
    private Double workTime;

    /**
     * 出厂时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date factoryTime;

    /**
     * 安装时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 油箱容积列表
     */
    private List<DeviceOil> deviceOilList;

    /**
     * 工作状态 -1:离线 0:在线 1:怠速 2:负荷
     */
    private Integer workStatus;

    /**
     * 单日工作时间
     */
    private Double dailyWorkTime;

    /**
     * 油位百分比
     */
    private Integer oilLevel;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 定位采集时间
     */
    private Date collectTime;

    /**
     * 行驶速度，单位：km/h
     */
    private Double runSpeed;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getRackId() {
        return rackId;
    }

    public void setRackId(String rackId) {
        this.rackId = rackId;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getSnId() {
        return snId;
    }

    public void setSnId(String snId) {
        this.snId = snId;
    }

    public Double getWorkTime() {
        return workTime;
    }

    public void setWorkTime(Double workTime) {
        this.workTime = workTime;
    }

    public Date getFactoryTime() {
        return factoryTime;
    }

    public void setFactoryTime(Date factoryTime) {
        this.factoryTime = factoryTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<DeviceOil> getDeviceOilList() {
        return deviceOilList;
    }

    public void setDeviceOilList(List<DeviceOil> deviceOilList) {
        this.deviceOilList = deviceOilList;
    }

    public Integer getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(Integer workStatus) {
        this.workStatus = workStatus;
    }

    public Double getDailyWorkTime() {
        return dailyWorkTime;
    }

    public void setDailyWorkTime(Double dailyWorkTime) {
        this.dailyWorkTime = dailyWorkTime;
    }

    public Integer getOilLevel() {
        return oilLevel;
    }

    public void setOilLevel(Integer oilLevel) {
        this.oilLevel = oilLevel;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Double getRunSpeed() {
        return runSpeed;
    }

    public void setRunSpeed(Double runSpeed) {
        this.runSpeed = runSpeed;
    }
}

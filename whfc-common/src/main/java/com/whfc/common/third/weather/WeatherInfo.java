package com.whfc.common.third.weather;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 天气信息
 */
public class WeatherInfo implements Serializable {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 实时温度
     */
    private Double temperature;

    /**
     * 实时天气
     */
    private String weather;

    /**
     * 实时风速
     */
    private Double windSpeed;

    /**
     * 未来两个小时每分钟的降水强度
     */
    private List<Double> precipitationList;

    /**
     * 未来两小时的天气描述
     */
    private String description;

    /**
     * 未来七天的天气预报
     */
    private List<WeatherDaily> dailyWeatherList;

    /**
     * 小时级别天气预报
     */
    private List<WeatherHourly> hourlyWeatherList;

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public Double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public List<Double> getPrecipitationList() {
        return precipitationList;
    }

    public void setPrecipitationList(List<Double> precipitationList) {
        this.precipitationList = precipitationList;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<WeatherDaily> getDailyWeatherList() {
        return dailyWeatherList;
    }

    public void setDailyWeatherList(List<WeatherDaily> dailyWeatherList) {
        this.dailyWeatherList = dailyWeatherList;
    }

    public List<WeatherHourly> getHourlyWeatherList() {
        return hourlyWeatherList;
    }

    public void setHourlyWeatherList(List<WeatherHourly> hourlyWeatherList) {
        this.hourlyWeatherList = hourlyWeatherList;
    }
}

package com.whfc.common.third.cloudm;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.whfc.common.result.PageData;
import com.whfc.common.third.cloudm.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.PageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * @Description: 云机械接口工具
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 15:36
 */
public class CloudMApi {

    private static final Logger log = LoggerFactory.getLogger(CloudMApi.class);

    private static final int SUCESS = 800;

    private static final String TOKEN_URI = "/cmsdk/open/device/checkUser";

    private static final String DEVICE_LIST_URI = "/cmsdk/open/device/deviceList";

    private static final String WORK_TIME_LIST_URI = "/cmsdk/open/device/workTimeList";

    private static final String WORK_TIME_DETAIL_URI = "/cmsdk/open/device/workTimeDetails";

    private static final String WORK_TIME_DETAIL_STATUS_URI = "/cmsdk/open/device/workTimeDetailWithStatus";

    private static final String OIL_LEVEL_URI = "/cmsdk/open/device/oilLevel";

    private static final String OIL_LEVEL_MERGE_URI = "/cmsdk/open/device/oilLevelMerged";

    private static final String LOCATION_URI = "/cmsdk/open/device/locus";

    private static final String DEVICE_DATA__URI = "/cmsdk/open/device/device";

    private static final String DEVICE_ADD_OIL_URI = "/cmsdk/open/device/statisticsAddOil";

    /**
     * 服务器地址
     */
    private String host;

    /**
     * 公钥
     */
    private String accessKey;

    /**
     * 私钥
     */
    private String secretKey;

    public CloudMApi(String host, String accessKey, String secretKey) {
        this.host = host;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
    }

    public CloudMApi() {
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    /**
     * 获取token
     *
     * @return
     */
    public String getToken() {
        String url = host + TOKEN_URI;
        Map<String, String> params = new HashMap<>();
        params.put("accessKey", accessKey);
        params.put("secretKey", secretKey);
        String response = HttpUtil.doGet(url, null, params);
        log.info("云机械获取token,response:{}", response);
        Result<String> result = JSONObject.parseObject(response, new TypeReference<Result<String>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        }
        return null;
    }

    /**
     * 获取设备列表
     *
     * @param token
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageData<DeviceInfo> deviceList(String token, int pageNum, int pageSize) {
        String url = host + DEVICE_LIST_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("page", String.valueOf(pageNum));
        params.put("size", String.valueOf(pageSize));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<DeviceInfo>> result = JSONObject.parseObject(response, new TypeReference<Result<List<DeviceInfo>>>() {
        });
        if (result.isSuccess()) {
            Page page = result.getPage();
            PageData<DeviceInfo> pageData = new PageData<>();
            pageData.setPageNum(pageNum);
            pageData.setPageSize(pageSize);
            pageData.setList(result.getResult());
            pageData.setTotal((long) page.getTotalElements());
            pageData.setPages(page.getTotalPages());
            return pageData;
        } else {
            log.info("云机械获取设备列表,response:{}", response);
        }
        return PageUtil.emptyPage();
    }

    /**
     * 获取设备详情
     *
     * @param token
     * @param deviceId
     * @return
     */
    public DeviceInfo deviceData(String token, String deviceId) {
        String url = host + DEVICE_DATA__URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        String response = HttpUtil.doGet(url, null, params);
        Result<DeviceInfo> result = JSONObject.parseObject(response, new TypeReference<Result<DeviceInfo>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备详情,response:{}", response);
        }
        return null;
    }

    /**
     * 获取设备工时
     *
     * @param token
     * @param deviceId
     * @param startDay
     * @param endDay
     * @return
     */
    public List<WorkTime> workTimeList(String token, String deviceId, Date startDay, Date endDay) {
        String url = host + WORK_TIME_LIST_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("startDay", DateUtil.formatDate(startDay));
        params.put("endDay", DateUtil.formatDate(endDay));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<WorkTime>> result = JSONObject.parseObject(response, new TypeReference<Result<List<WorkTime>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备工时,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取某天的工作详情
     *
     * @param token
     * @param deviceId
     * @param date
     * @return
     */
    public List<WorkTime> workTimeDetail(String token, String deviceId, Date date) {
        String url = host + WORK_TIME_DETAIL_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("date", DateUtil.formatDate(date));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<WorkTime>> result = JSONObject.parseObject(response, new TypeReference<Result<List<WorkTime>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备工作详情,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取某天的工作详情(状态)
     *
     * @param token
     * @param deviceId
     * @param date
     * @return
     */
    public List<WorkTime> workTimeDetailStatus(String token, String deviceId, Date date) {
        String url = host + WORK_TIME_DETAIL_STATUS_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("date", DateUtil.formatDate(date));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<WorkTime>> result = JSONObject.parseObject(response, new TypeReference<Result<List<WorkTime>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备工作详情,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备油位百分比
     *
     * @param token
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<OilInfo> oilLevel(String token, String deviceId, Date startTime, Date endTime) {
        String url = host + OIL_LEVEL_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("startTime", encode(DateUtil.formatDateTime(startTime)));
        params.put("endTime", encode(DateUtil.formatDateTime(endTime)));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<OilInfo>> result = JSONObject.parseObject(response, new TypeReference<Result<List<OilInfo>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备油位百分比,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备融合油位
     *
     * @param token
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<OilInfo> oilLevelMerged(String token, String deviceId, Date startTime, Date endTime) {
        String url = host + OIL_LEVEL_MERGE_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("startTime", encode(DateUtil.formatDateTime(startTime)));
        params.put("endTime", encode(DateUtil.formatDateTime(endTime)));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<OilInfo>> result = JSONObject.parseObject(response, new TypeReference<Result<List<OilInfo>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备融合油位,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备定位信息
     *
     * @param token
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    public List<LocInfo> location(String token, String deviceId, Date startTime, Date endTime) {
        String url = host + LOCATION_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("startTime", DateUtil.formatDate(startTime));
        params.put("endTime", DateUtil.formatDate(endTime));
        String response = HttpUtil.doGet(url, null, params);
        Result<List<LocInfo>> result = JSONObject.parseObject(response, new TypeReference<Result<List<LocInfo>>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备定位信息,response:{}", response);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备加油数据
     *
     * @param token
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    public OilAddData oilAddData(String token, String deviceId, Date startTime, Date endTime) {
        String url = host + DEVICE_ADD_OIL_URI;
        Map<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("deviceId", deviceId);
        params.put("startTime", encode(DateUtil.formatDateTime(startTime)));
        params.put("endTime", encode(DateUtil.formatDateTime(endTime)));
        String response = HttpUtil.doGet(url, null, params);
        Result<OilAddData> result = JSONObject.parseObject(response, new TypeReference<Result<OilAddData>>() {
        });
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            log.info("云机械获取设备加油信息,response:{}", response);
        }
        return null;
    }

    private String encode(String str) {
        return str.replace(" ", "%20");
    }
}

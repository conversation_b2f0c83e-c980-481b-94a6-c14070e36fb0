package com.whfc.common.third.wonhere;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 基坑监测数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-10-30 16:55
 */
@Data
public class JkDataItem implements Serializable {

    /**
     * 唯一标识，组合方式：
     * 采集仪编号#板卡序号#通道序号#传感器地址
     */
    private String upload_code;

    /**
     * 测点名称
     */
    private String point_name;

    /**
     * 监测类型
     */
    private Integer mon_type;

    /**
     * 传感器类型
     */
    private Integer dev_type;

    /**
     * 深度(m)
     */
    private Double depth;

    /**
     * 采样值1
     */
    private Double data1;

    /**
     * 采样值2
     */
    private Double data2;

    /**
     * 采样值3
     */
    private Double data3;

    /**
     * 采样值4
     */
    private Double data4;

    /**
     * 采样值1 单次变化量
     */
    private Double data1_this;

    private Double data2_this;

    private Double data3_this;

    private Double data4_this;

    /**
     * 采样值1 累计变化量
     */
    private Double data1_total;

    private Double data2_total;

    private Double data3_total;

    private Double data4_total;

    /**
     * 采样值1 变化速率
     */
    private Double data1_rate;

    private Double data2_rate;

    private Double data3_rate;

    private Double data4_rate;

    /**
     * 报警状态：0-正常，1-预警，2-报警，3-控制
     */
    private Integer alarm_state;

    /**
     * 预警字段
     */
    private List<String> warning;

    /**
     * 报警字段
     */
    private List<String> error;

    /**
     * 控制字段
     */
    private List<String> control;
}

package com.whfc.common.third.weather;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-16
 */
public class WeatherDaily implements Serializable {

    private String date;

    /**
     * 最高气温
     */
    private Double max;

    /**
     * 最低气温
     */
    private Double min;

    /**
     * 平均气温
     */
    private Double avg;

    /**
     * 天气
     */
    private String weather;

    /**
     * 降水量
     */
    private Double precipitation;

    /**
     * 风力
     */
    private Integer windPower;

    /**
     * 风向
     */
    private Integer windDirection;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Double getMax() {
        return max;
    }

    public void setMax(Double max) {
        this.max = max;
    }

    public Double getMin() {
        return min;
    }

    public void setMin(Double min) {
        this.min = min;
    }

    public Double getAvg() {
        return avg;
    }

    public void setAvg(Double avg) {
        this.avg = avg;
    }

    public Double getPrecipitation() {
        return precipitation;
    }

    public void setPrecipitation(Double precipitation) {
        this.precipitation = precipitation;
    }

    public Integer getWindPower() {
        return windPower;
    }

    public void setWindPower(Integer windPower) {
        this.windPower = windPower;
    }

    public Integer getWindDirection() {
        return windDirection;
    }

    public void setWindDirection(Integer windDirection) {
        this.windDirection = windDirection;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }
}

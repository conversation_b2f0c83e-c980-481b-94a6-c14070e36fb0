package com.whfc.common.third.corerain;

public enum CorerainAlarmType {

    CR_PERSON_INVASION("人员闯入", "Person-Invasion"),

    HELMET_DETECTION("未佩戴安全帽", "Helmet Detection"),

    VEST_DETECTION("反光衣", "Reflective Safety Vest Detection"),

    SMOG_DETECTION("烟雾检测", "SmokeFire-L");

    /**
     * 算法名称
     */
    private final String name;

    /**
     * 算法英文名称
     */
    private final String nameEn;

    CorerainAlarmType(String name, String nameEn) {
        this.name = name;
        this.nameEn = nameEn;
    }

    public String getName() {
        return name;
    }

    public String getNameEn() {
        return nameEn;
    }
}

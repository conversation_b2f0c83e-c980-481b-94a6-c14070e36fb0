package com.whfc.common.third.energy;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 恒焊历史数据查询
 * <AUTHOR>
 * @Date 2021-09-16 9:58
 * @Version 1.0
 */
@Data
public class TQResultDTO implements Serializable {

    /**
     * 表地址
     */
    private String address;

    /**
     * 采集时间
     */
    private Date addTime;

    /**
     *功能类型
     *
     * 3. 正向有功总电能
     * 4. 反向有功总电能
     * 5. 正向无功总电能
     * 6. 反向无功总电能
     * 15. ABC三相电压
     * 16. ABC三相电流
     * 17. ABC三相有功功率
     * 18. ABC三相无功功率
     * 22. 剩余金额
     * 27. A相电流
     * 28. B相电流
     * 29. C相电流
     * 30. A相电压
     * 31. B相电压
     * 32. C相电压
     * 33. 瞬时有功功率
     * 34. 瞬时无功功率
     * 42. 水表数据
     */
    private String fid;


    /**
     *
     */
    private List<Object> data;
}

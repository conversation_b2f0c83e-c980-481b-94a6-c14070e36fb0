package com.whfc.common.third.epiroc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MachineData implements Serializable {

    /**
     * 设备编号"
     */
    private String remoteId;

    /**
     * 数据时间
     */
    private Date remoteTime;

    /**
     * 上传日期
     */
    private Date uploadDate;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 用户编号
     */
    private String userName;

    /**
     * FMI
     */
    private String fmi;

    /**
     * 空压机当日工作小时数
     */
    private String airCompressorHourDay;

    /**
     * 空压机累积工作小时
     */
    private String airCompressorHours;

    /**
     * 空压机温度高故障
     */
    private String airCompressorTempError;

    /**
     * 空压机温度报警次数
     */
    private String airTempErrors;

    /**
     * 当天二氧化碳排放量
     */
    private String co2Day;

    /**
     * 凿岩机油位预警次数
     */
    private String copOilLevelAlarms;

    /**
     * COP油位低故障
     */
    private String copOilLevelError;

    /**
     * 当日累积进尺米数
     */
    private String dayDepth;

    /**
     * 1#总累积进尺米
     */
    private String dayDepth1;

    /**
     * 2#总累积进尺米
     */
    private String dayDepth2;

    /**
     * 3#总累积进尺米
     */
    private String dayDepth3;

    /**
     * 每电机小时钻米
     */
    private String depthPerElectricalHour;

    /**
     * 1#每电机小时钻米
     */
    private String depthPerElectricalHour1;

    /**
     * 2#每电机小时钻米
     */
    private String depthPerElectricalHour2;

    /**
     * 2#每电机小时钻米
     */
    private String depthPerElectricalHour3;

    /**
     * 当日钻孔数
     */
    private String drillDay;

    /**
     * 1#当日钻孔数
     */
    private String drillDay1;

    /**
     * 2#当日钻孔数
     */
    private String drillDay2;

    /**
     * 3#当日钻孔数
     */
    private String drillDay3;

    /**
     * 1#实时钻进速度
     */
    private String drillSpeed1;

    /**
     * 2#实时钻进速度
     */
    private String drillSpeed2;

    /**
     * 3#实时钻进速度
     */
    private String drillSpeed3;

    /**
     * 1#总累积钻孔数
     */
    private String drills1;

    /**
     * 2#总累积钻孔数
     */
    private String drills2;

    /**
     * 3#总累积钻孔数
     */
    private String drills3;

    /**
     * 1#当日电机小时数
     */
    private String electricalHourDay1;

    /**
     * 2#当日电机小时数
     */
    private String electricalHourDay2;

    /**
     * 3#当日电机小时数
     */
    private String electricalHourDay3;

    /**
     * 1#电机小时数
     */
    private String electricalHours1;

    /**
     * 2#电机小时数
     */
    private String electricalHours2;

    /**
     * 3#电机小时数
     */
    private String electricalHours3;

    /**
     * 发动机进气压力,kpa
     */
    private String engineAirPressure;

    /**
     * 发动机总线故障
     */
    private String engineCanError;

    /**
     * 发动机冷却液温度,℃
     */
    private String engineCoolingTemp;

    /**
     * 发动机报警数
     */
    private String engineErrors;

    /**
     * 发动机总累积耗油量,L
     */
    private String engineFuels;

    /**
     * 发动机瞬时油耗,L/h
     */
    private String engineFuelCurrent;

    /**
     * 发动机当日耗油量,L
     */
    private String engineFuelDay;

    /**
     * 发动机燃油压力,kpa
     */
    private String engineFuelPressure;

    /**
     * 发动机当日工作小时数
     */
    private String engineHourDay;

    /**
     * 发动机总累积工作小时数
     */
    private String engineHours;

    /**
     * 发动机负荷,%
     */
    private String engineLoad;

    /**
     * 发动机机油压力,kpa
     */
    private String engineOilPressure;

    /**
     * 发动机油压报警
     */
    private String engineOilPressureError;

    /**
     * 发动机油温,℃
     */
    private String engineOilTemp;

    /**
     * 发动机转速,r/min
     */
    private String engineSpeed;

    /**
     * 车辆编号
     */
    private String fleetNum;

    /**
     * 变速箱油压报警次数
     */
    private String gearOilPressureAlarms;

    /**
     * 变速箱油压报警
     */
    private String gearOilPressureError;

    /**
     * 变速箱油温报警次数
     */
    private String gearOilTempAlarms;

    /**
     * 变速箱油温报警
     */
    private String gearOilTempError;

    /**
     * 液压油油位报警次数
     */
    private String hydLevelAlarms;

    /**
     * 液压油油位低故障
     */
    private String hydLevelError;

    /**
     * 液压油温度报警次数
     */
    private String hydTempAlarms;

    /**
     * 液压油温度高故障
     */
    private String hydTempError;

    /**
     * 瞬时功率,kw
     */
    private String instantaneousPower;

    /**
     * 当日耗电,kwh
     */
    private String kwhDay;

    /**
     * 故障灯状态
     */
    private String lampStatus;

    /**
     * 发动机油压报警次数
     */
    private String oilPressureAlarms;

    /**
     * 1#当日冲击小时
     */
    private String percussionHourDay1;

    /**
     * 2#当日冲击小时
     */
    private String percussionHourDay2;

    /**
     * 3#当日冲击小时
     */
    private String percussionHourDay3;

    /**
     * 1#累积冲击小时
     */
    private String percussionHours1;

    /**
     * 2#累积冲击小时
     */
    private String percussionHours2;

    /**
     * 3#累积冲击小时
     */
    private String percussionHours3;

    /**
     * 生产利用率,%
     */
    private String productionRatio;

    /**
     * 1#生产利用率
     */
    private String productionRatio1;

    /**
     * 2#生产利用率
     */
    private String productionRatio2;

    /**
     * 3#生产利用率
     */
    private String productionRatio3;

    /**
     * 1#凿岩机位置
     */
    private String rockDrillPosition1;

    /**
     * 2#凿岩机位置
     */
    private String rockDrillPosition2;

    /**
     * 3#凿岩机位置
     */
    private String rockDrillPosition3;

    /**
     * SPN
     */
    private String spn;

    /**
     * SPN_FMI数
     */
    private String spnFmiCnt;

    /**
     * 系统电流,A
     */
    private String systemCurrent;

    /**
     * 系统电压,V
     */
    private String systemVoltage;

    /**
     * 电瓶电压 ,单位:v
     */
    private String engineVoltage;

    /**
     * 累积二氧化碳排放量,kg
     */
    private String totalCo2;

    /**
     * 总累积进尺米
     */
    private String totalDepth;

    /**
     * 1#总累积进尺米
     */
    private String totalDepth1;

    /**
     * 2#总累积进尺米
     */
    private String totalDepth2;

    /**
     * 3#总累积进尺米
     */
    private String totalDepth3;

    /**
     * 累计耗电
     */
    private String totalKwh;

    /**
     * 设备利用率
     */
    private String utilizationRatio;

    /**
     * 1#设备利用率
     */
    private String utilizationRatio1;

    /**
     * 2#设备利用率
     */
    private String utilizationRatio2;

    /**
     * 3#设备利用率
     */
    private String utilizationRatio3;
}

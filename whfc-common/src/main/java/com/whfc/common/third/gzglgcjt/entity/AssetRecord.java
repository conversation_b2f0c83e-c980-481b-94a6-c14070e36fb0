package com.whfc.common.third.gzglgcjt.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/10 16:06
 */
@Data
public class AssetRecord implements Serializable {

    /**
     * 固定资产
     */
    private String gdzc;

    /**
     * 原资产编码
     */
    private String bm;

    /**
     * 分类
     */
    private String fl;

    /**
     * flbm
     */
    private String flbm;

    /**
     * 登记日期
     */
    private String djrq;

    /**
     * 工作小时
     */
    private String gzxs;

    /**
     * 工作地点
     */
    private String gzdd;

    /**
     * 工作内容
     */
    private String gznr;

    /**
     * 工作里程
     */
    private String gzlc;
}

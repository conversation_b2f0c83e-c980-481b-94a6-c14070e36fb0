package com.whfc.common.third.jarvis;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/11 9:35
 */
@Data
public class AccessToken implements Serializable {

    private String access_token;
    private String refresh_token;
    private String userId;
    private String scope;
    private String token_type;
    private Long expires_in;
    private String jti;
}

package com.whfc.common.third.xiaoma;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.xiaoma.entity.DeviceData;
import com.whfc.common.third.xiaoma.entity.Result;
import com.whfc.common.util.HttpUtil;

/**
 * @Description: 小马快修API
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/5/30 17:01
 */
public class XiaoMaApi {

    private static final String INFO_URI = "/api/identify-parts/info/";

    private static final Integer SUCCESS = 0;

    private String host;

    public XiaoMaApi(String host) {
        this.host = host;
    }

    /**
     * 获取设备维保信息
     *
     * @param serialNo 设备机号
     * @return
     */
    public DeviceData maintainInfo(String serialNo) {
        String url = new StringBuffer().append(host).append(INFO_URI).append(serialNo).toString();
        String response = HttpUtil.doGet(url);
        Result result = JSONObject.parseObject(response, Result.class);
        if (result != null && SUCCESS.equals(result.getCode())) {
            return result.getData();
        }
        return null;
    }
}

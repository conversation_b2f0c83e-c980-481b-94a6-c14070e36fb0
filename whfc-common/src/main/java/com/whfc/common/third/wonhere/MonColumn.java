package com.whfc.common.third.wonhere;

import java.io.Serializable;

/**
 * @Description: 监测结果字段
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-11-18 16:54
 */
public class MonColumn implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String desc;

    /**
     * 单位
     */
    private String unit;

    public MonColumn() {
    }

    public MonColumn(String name, String desc, String unit) {
        this.name = name;
        this.desc = desc;
        this.unit = unit;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}

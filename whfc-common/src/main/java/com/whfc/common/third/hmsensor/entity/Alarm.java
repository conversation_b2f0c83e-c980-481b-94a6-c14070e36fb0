package com.whfc.common.third.hmsensor.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/4 16:07
 */
public class Alarm implements Serializable {

    private String cod;

    private String cmt;

    private Date dte;

    public String getCod() {
        return cod;
    }

    public void setCod(String cod) {
        this.cod = cod;
    }

    public String getCmt() {
        return cmt;
    }

    public void setCmt(String cmt) {
        this.cmt = cmt;
    }

    public Date getDte() {
        return dte;
    }

    public void setDte(Date dte) {
        this.dte = dte;
    }
}

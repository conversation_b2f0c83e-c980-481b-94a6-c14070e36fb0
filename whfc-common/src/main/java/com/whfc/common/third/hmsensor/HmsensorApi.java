package com.whfc.common.third.hmsensor;

import com.whfc.common.third.hmsensor.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;

import java.util.*;

/**
 * @Description: 压力传感器api
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/4 15:48
 */
public class HmsensorApi {

    //    {
    //        "error_code": 0,
    //        "error_msg": "成功!",
    //        "count": "1",
    //        "data": [{
    //            "id": "682",
    //            "cod": "302700000001",
    //            "nme": "FC-1"
    //         }]
    //    }

    private static final String success = "0";
    private static final String device_url = "http://cgq.hmsensor.com/cgi/list.php";
    private static final String data_url = "http://cgq.hmsensor.com/cgi/data.php";
    private static final String alarm_url = "http://cgq.hmsensor.com/cgi/alarm.php";
    private static final String param_get_url = "http://cgq.hmsensor.com/cgi/param_get.php";
    private static final String param_set_url = "http://cgq.hmsensor.com/cgi/param_set.php";

    private String usr;

    private String pwd;

    public HmsensorApi(String usr, String pwd) {
        this.usr = usr;
        this.pwd = pwd;
    }

    /**
     * 获取设备列表
     *
     * @return
     */
    public List<Device> getDeviceList() {

        Map<String, String> params = new HashMap<>();
        params.put("usr", usr);
        params.put("pwd", pwd);
        String response = HttpUtil.doPost(device_url, params);
        DeviceResult result = JSONUtil.parseObject(response, DeviceResult.class);
        if (result != null && success.equals(result.getError_code())) {
            return result.getData();
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备实时数据
     *
     * @param cod
     * @return
     */
    public List<RealData> getDeviceData(String cod) {
        Map<String, String> params = new HashMap<>();
        params.put("usr", usr);
        params.put("pwd", pwd);
        params.put("cod", cod);
        String response = HttpUtil.doPost(data_url, params);
        RealDataResult result = JSONUtil.parseObject(response, RealDataResult.class);
        if (result != null && success.equals(result.getError_code())) {
            return result.getData();
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取设备报警数据
     *
     * @param cod
     * @param start
     * @param end
     * @return
     */
    public List<Alarm> getAlarmData(String cod, Date start, Date end) {
        Map<String, String> params = new HashMap<>();
        params.put("usr", usr);
        params.put("pwd", pwd);
        params.put("cod", cod);
        params.put("dts", DateUtil.formatDate(start));
        params.put("dte", DateUtil.formatDate(end));
        String response = HttpUtil.doPost(alarm_url, params);
        AlarmResult result = JSONUtil.parseObject(response, AlarmResult.class);
        if (result != null && success.equals(result.getError_code())) {
            return result.getData();
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 获取参数
     *
     * @param cod
     * @return
     */
    public Param getParam(String cod) {
        Map<String, String> params = new HashMap<>();
        params.put("usr", usr);
        params.put("pwd", pwd);
        params.put("cod", cod);
        String response = HttpUtil.doPost(param_get_url, params);
        ParamResult result = JSONUtil.parseObject(response, ParamResult.class);
        if (result != null && success.equals(result.getError_code())) {
            return result.getData();
        }
        return null;
    }

    /**
     * 设置参数
     *
     * @param param
     */
    public void setParam(Param param) {
        Map<String, String> params = new HashMap<>();
        params.put("usr", usr);
        params.put("pwd", pwd);
        params.put("cod", param.getCod());
        params.put("p1", String.valueOf(param.getP1()));
        params.put("p2", String.valueOf(param.getP2()));
        params.put("p3", String.valueOf(param.getP3()));
        params.put("p4", String.valueOf(param.getP4()));
        params.put("p5", String.valueOf(param.getP5()));
        params.put("alm", String.valueOf(param.getAlm()));
        String response = HttpUtil.doPost(param_get_url, params);
        ParamResult result = JSONUtil.parseObject(response, ParamResult.class);
    }

}

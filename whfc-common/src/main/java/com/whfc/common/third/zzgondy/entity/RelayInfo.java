package com.whfc.common.third.zzgondy.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 11:32
 */
public class RelayInfo implements Serializable {

    private String deviceId;

    private String relayId;

    private String relayName;

    private String backColor;

    private String backImage;

    private String status;

    private List<RelayStatus> children;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getRelayId() {
        return relayId;
    }

    public void setRelayId(String relayId) {
        this.relayId = relayId;
    }

    public String getRelayName() {
        return relayName;
    }

    public void setRelayName(String relayName) {
        this.relayName = relayName;
    }

    public String getBackColor() {
        return backColor;
    }

    public void setBackColor(String backColor) {
        this.backColor = backColor;
    }

    public String getBackImage() {
        return backImage;
    }

    public void setBackImage(String backImage) {
        this.backImage = backImage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<RelayStatus> getChildren() {
        return children;
    }

    public void setChildren(List<RelayStatus> children) {
        this.children = children;
    }
}

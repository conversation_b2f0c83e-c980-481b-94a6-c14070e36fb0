package com.whfc.common.third.indoor.ble;

import java.io.Serializable;
import java.util.List;

/**
 * 蓝牙标签扫描
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/4 10:33
 */
public class BleScan implements Serializable {

    /**
     * 基站imei
     */
    private String imei;

    /**
     * 扫描标签列表
     */
    private List<BleTag> tagList;

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public List<BleTag> getTagList() {
        return tagList;
    }

    public void setTagList(List<BleTag> tagList) {
        this.tagList = tagList;
    }
}

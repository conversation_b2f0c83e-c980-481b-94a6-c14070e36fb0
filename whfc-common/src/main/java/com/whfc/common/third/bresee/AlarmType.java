package com.whfc.common.third.bresee;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/20 11:05
 */
public enum AlarmType {

    NO_HELMET("4", "未戴安全帽"),

    NO_WORK_CLOTHES("5", "未穿工服"),

    NO_REFLECTVEST("65", "未穿反光衣"),

    AREA("20", "周界入侵"),

    SMOKE("7", "抽烟监测");
    private String value;

    private String name;

    AlarmType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

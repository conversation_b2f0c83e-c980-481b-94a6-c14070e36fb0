
package com.whfc.common.third.yanxi.entity;

import javax.annotation.Generated;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

@Generated("net.hexar.json2pojo")
@SuppressWarnings("unused")
public class LddCurrentData extends CurrentData implements Serializable {

    @SerializedName("AL1bit0")
    private Integer aL1bit0;
    @SerializedName("AL1bit1")
    private Integer aL1bit1;
    @SerializedName("AL1bit10")
    private Integer aL1bit10;
    @SerializedName("AL1bit11")
    private Integer aL1bit11;
    @SerializedName("AL1bit12")
    private Integer aL1bit12;
    @SerializedName("AL1bit13")
    private Integer aL1bit13;
    @SerializedName("AL1bit14")
    private Integer aL1bit14;
    @SerializedName("AL1bit15")
    private Integer aL1bit15;
    @SerializedName("AL1bit5")
    private Integer aL1bit5;
    @SerializedName("AL1bit6")
    private Integer aL1bit6;
    @SerializedName("AL1bit7")
    private Integer aL1bit7;
    @SerializedName("AL1bit8")
    private Integer aL1bit8;
    @SerializedName("AL1bit9")
    private Integer aL1bit9;
    @SerializedName("AL2bit0")
    private Integer aL2bit0;
    @SerializedName("AL2bit1")
    private Integer aL2bit1;
    @SerializedName("AcquisitionModulesNumber")
    private Double acquisitionModulesNumber;
    @SerializedName("Angle1")
    private Double angle1;
    @SerializedName("Angle2")
    private Double angle2;
    @SerializedName("Angle3")
    private Double angle3;
    @SerializedName("Angle4")
    private Double angle4;
    @SerializedName("Angle5")
    private Double angle5;
    @SerializedName("Angle6")
    private Double angle6;
    @SerializedName("BRbit0")
    private Integer bRbit0;
    @SerializedName("BRbit1")
    private Integer bRbit1;
    @Expose
    private String ctime;
    @SerializedName("CumulativeWorkingTime")
    private Double cumulativeWorkingTime;
    @SerializedName("DevID")
    private String devID;
    @SerializedName("DevType")
    private String devType;
    @SerializedName("Distance10")
    private Double distance10;
    @SerializedName("Distance11")
    private Double distance11;
    @SerializedName("Distance5")
    private Double distance5;
    @SerializedName("Distance6")
    private Double distance6;
    @SerializedName("Distance7")
    private Double distance7;
    @SerializedName("Distance8")
    private Double distance8;
    @SerializedName("Distance9")
    private Double distance9;
    @SerializedName("LI1bit10")
    private Integer lI1bit10;
    @SerializedName("LI1bit11")
    private Integer lI1bit11;
    @SerializedName("LI1bit8")
    private Integer lI1bit8;
    @SerializedName("LI1bit9")
    private Integer lI1bit9;
    @SerializedName("LI2bit0")
    private Integer lI2bit0;
    @SerializedName("Neiweight1")
    private Double neiweight1;
    @SerializedName("Neiweight2")
    private Double neiweight2;
    @SerializedName("Neiweight3")
    private Double neiweight3;
    @SerializedName("Neiweight4")
    private Double neiweight4;
    @SerializedName("OP1bit0")
    private Integer oP1bit0;
    @SerializedName("OP1bit1")
    private Integer oP1bit1;
    @SerializedName("OP1bit6")
    private Integer oP1bit6;
    @SerializedName("OP1bit7")
    private Integer oP1bit7;
    @SerializedName("OperatingCycleNumber")
    private Double operatingCycleNumber;
    @SerializedName("Reserved1")
    private Double reserved1;
    @SerializedName("Speed1")
    private Double speed1;
    @SerializedName("Speed2")
    private Double speed2;
    @SerializedName("TS1bit0")
    private Integer tS1bit0;
    @SerializedName("TS1bit1")
    private Integer tS1bit1;
    @SerializedName("TS1bit2")
    private Integer tS1bit2;
    @SerializedName("TS1bit3")
    private Integer tS1bit3;
    @SerializedName("TS1bit4")
    private Integer tS1bit4;
    @SerializedName("TS1bit5")
    private Integer tS1bit5;
    @SerializedName("TS1bit6")
    private Integer tS1bit6;
    @SerializedName("Wind1")
    private Double wind1;
    @SerializedName("Wind2")
    private Double wind2;
    @SerializedName("WorkTime")
    private Double workTime;

    public Integer getAL1bit0() {
        return aL1bit0;
    }

    public void setAL1bit0(Integer aL1bit0) {
        this.aL1bit0 = aL1bit0;
    }

    public Integer getAL1bit1() {
        return aL1bit1;
    }

    public void setAL1bit1(Integer aL1bit1) {
        this.aL1bit1 = aL1bit1;
    }

    public Integer getAL1bit10() {
        return aL1bit10;
    }

    public void setAL1bit10(Integer aL1bit10) {
        this.aL1bit10 = aL1bit10;
    }

    public Integer getAL1bit11() {
        return aL1bit11;
    }

    public void setAL1bit11(Integer aL1bit11) {
        this.aL1bit11 = aL1bit11;
    }

    public Integer getAL1bit12() {
        return aL1bit12;
    }

    public void setAL1bit12(Integer aL1bit12) {
        this.aL1bit12 = aL1bit12;
    }

    public Integer getAL1bit13() {
        return aL1bit13;
    }

    public void setAL1bit13(Integer aL1bit13) {
        this.aL1bit13 = aL1bit13;
    }

    public Integer getAL1bit14() {
        return aL1bit14;
    }

    public void setAL1bit14(Integer aL1bit14) {
        this.aL1bit14 = aL1bit14;
    }

    public Integer getAL1bit15() {
        return aL1bit15;
    }

    public void setAL1bit15(Integer aL1bit15) {
        this.aL1bit15 = aL1bit15;
    }

    public Integer getAL1bit5() {
        return aL1bit5;
    }

    public void setAL1bit5(Integer aL1bit5) {
        this.aL1bit5 = aL1bit5;
    }

    public Integer getAL1bit6() {
        return aL1bit6;
    }

    public void setAL1bit6(Integer aL1bit6) {
        this.aL1bit6 = aL1bit6;
    }

    public Integer getAL1bit7() {
        return aL1bit7;
    }

    public void setAL1bit7(Integer aL1bit7) {
        this.aL1bit7 = aL1bit7;
    }

    public Integer getAL1bit8() {
        return aL1bit8;
    }

    public void setAL1bit8(Integer aL1bit8) {
        this.aL1bit8 = aL1bit8;
    }

    public Integer getAL1bit9() {
        return aL1bit9;
    }

    public void setAL1bit9(Integer aL1bit9) {
        this.aL1bit9 = aL1bit9;
    }

    public Integer getAL2bit0() {
        return aL2bit0;
    }

    public void setAL2bit0(Integer aL2bit0) {
        this.aL2bit0 = aL2bit0;
    }

    public Integer getAL2bit1() {
        return aL2bit1;
    }

    public void setAL2bit1(Integer aL2bit1) {
        this.aL2bit1 = aL2bit1;
    }

    public Double getAcquisitionModulesNumber() {
        return acquisitionModulesNumber;
    }

    public void setAcquisitionModulesNumber(Double acquisitionModulesNumber) {
        this.acquisitionModulesNumber = acquisitionModulesNumber;
    }

    public Double getAngle1() {
        return angle1;
    }

    public void setAngle1(Double angle1) {
        this.angle1 = angle1;
    }

    public Double getAngle2() {
        return angle2;
    }

    public void setAngle2(Double angle2) {
        this.angle2 = angle2;
    }

    public Double getAngle3() {
        return angle3;
    }

    public void setAngle3(Double angle3) {
        this.angle3 = angle3;
    }

    public Double getAngle4() {
        return angle4;
    }

    public void setAngle4(Double angle4) {
        this.angle4 = angle4;
    }

    public Double getAngle5() {
        return angle5;
    }

    public void setAngle5(Double angle5) {
        this.angle5 = angle5;
    }

    public Double getAngle6() {
        return angle6;
    }

    public void setAngle6(Double angle6) {
        this.angle6 = angle6;
    }

    public Integer getBRbit0() {
        return bRbit0;
    }

    public void setBRbit0(Integer bRbit0) {
        this.bRbit0 = bRbit0;
    }

    public Integer getBRbit1() {
        return bRbit1;
    }

    public void setBRbit1(Integer bRbit1) {
        this.bRbit1 = bRbit1;
    }

    public String getCtime() {
        return ctime;
    }

    public void setCtime(String ctime) {
        this.ctime = ctime;
    }

    public Double getCumulativeWorkingTime() {
        return cumulativeWorkingTime;
    }

    public void setCumulativeWorkingTime(Double cumulativeWorkingTime) {
        this.cumulativeWorkingTime = cumulativeWorkingTime;
    }

    public String getDevID() {
        return devID;
    }

    public void setDevID(String devID) {
        this.devID = devID;
    }

    public String getDevType() {
        return devType;
    }

    public void setDevType(String devType) {
        this.devType = devType;
    }

    public Double getDistance10() {
        return distance10;
    }

    public void setDistance10(Double distance10) {
        this.distance10 = distance10;
    }

    public Double getDistance11() {
        return distance11;
    }

    public void setDistance11(Double distance11) {
        this.distance11 = distance11;
    }

    public Double getDistance5() {
        return distance5;
    }

    public void setDistance5(Double distance5) {
        this.distance5 = distance5;
    }

    public Double getDistance6() {
        return distance6;
    }

    public void setDistance6(Double distance6) {
        this.distance6 = distance6;
    }

    public Double getDistance7() {
        return distance7;
    }

    public void setDistance7(Double distance7) {
        this.distance7 = distance7;
    }

    public Double getDistance8() {
        return distance8;
    }

    public void setDistance8(Double distance8) {
        this.distance8 = distance8;
    }

    public Double getDistance9() {
        return distance9;
    }

    public void setDistance9(Double distance9) {
        this.distance9 = distance9;
    }

    public Integer getLI1bit10() {
        return lI1bit10;
    }

    public void setLI1bit10(Integer lI1bit10) {
        this.lI1bit10 = lI1bit10;
    }

    public Integer getLI1bit11() {
        return lI1bit11;
    }

    public void setLI1bit11(Integer lI1bit11) {
        this.lI1bit11 = lI1bit11;
    }

    public Integer getLI1bit8() {
        return lI1bit8;
    }

    public void setLI1bit8(Integer lI1bit8) {
        this.lI1bit8 = lI1bit8;
    }

    public Integer getLI1bit9() {
        return lI1bit9;
    }

    public void setLI1bit9(Integer lI1bit9) {
        this.lI1bit9 = lI1bit9;
    }

    public Integer getLI2bit0() {
        return lI2bit0;
    }

    public void setLI2bit0(Integer lI2bit0) {
        this.lI2bit0 = lI2bit0;
    }

    public Double getNeiweight1() {
        return neiweight1;
    }

    public void setNeiweight1(Double neiweight1) {
        this.neiweight1 = neiweight1;
    }

    public Double getNeiweight2() {
        return neiweight2;
    }

    public void setNeiweight2(Double neiweight2) {
        this.neiweight2 = neiweight2;
    }

    public Double getNeiweight3() {
        return neiweight3;
    }

    public void setNeiweight3(Double neiweight3) {
        this.neiweight3 = neiweight3;
    }

    public Double getNeiweight4() {
        return neiweight4;
    }

    public void setNeiweight4(Double neiweight4) {
        this.neiweight4 = neiweight4;
    }

    public Integer getOP1bit0() {
        return oP1bit0;
    }

    public void setOP1bit0(Integer oP1bit0) {
        this.oP1bit0 = oP1bit0;
    }

    public Integer getOP1bit1() {
        return oP1bit1;
    }

    public void setOP1bit1(Integer oP1bit1) {
        this.oP1bit1 = oP1bit1;
    }

    public Integer getOP1bit6() {
        return oP1bit6;
    }

    public void setOP1bit6(Integer oP1bit6) {
        this.oP1bit6 = oP1bit6;
    }

    public Integer getOP1bit7() {
        return oP1bit7;
    }

    public void setOP1bit7(Integer oP1bit7) {
        this.oP1bit7 = oP1bit7;
    }

    public Double getOperatingCycleNumber() {
        return operatingCycleNumber;
    }

    public void setOperatingCycleNumber(Double operatingCycleNumber) {
        this.operatingCycleNumber = operatingCycleNumber;
    }

    public Double getReserved1() {
        return reserved1;
    }

    public void setReserved1(Double reserved1) {
        this.reserved1 = reserved1;
    }

    public Double getSpeed1() {
        return speed1;
    }

    public void setSpeed1(Double speed1) {
        this.speed1 = speed1;
    }

    public Double getSpeed2() {
        return speed2;
    }

    public void setSpeed2(Double speed2) {
        this.speed2 = speed2;
    }

    public Integer getTS1bit0() {
        return tS1bit0;
    }

    public void setTS1bit0(Integer tS1bit0) {
        this.tS1bit0 = tS1bit0;
    }

    public Integer getTS1bit1() {
        return tS1bit1;
    }

    public void setTS1bit1(Integer tS1bit1) {
        this.tS1bit1 = tS1bit1;
    }

    public Integer getTS1bit2() {
        return tS1bit2;
    }

    public void setTS1bit2(Integer tS1bit2) {
        this.tS1bit2 = tS1bit2;
    }

    public Integer getTS1bit3() {
        return tS1bit3;
    }

    public void setTS1bit3(Integer tS1bit3) {
        this.tS1bit3 = tS1bit3;
    }

    public Integer getTS1bit4() {
        return tS1bit4;
    }

    public void setTS1bit4(Integer tS1bit4) {
        this.tS1bit4 = tS1bit4;
    }

    public Integer getTS1bit5() {
        return tS1bit5;
    }

    public void setTS1bit5(Integer tS1bit5) {
        this.tS1bit5 = tS1bit5;
    }

    public Integer getTS1bit6() {
        return tS1bit6;
    }

    public void setTS1bit6(Integer tS1bit6) {
        this.tS1bit6 = tS1bit6;
    }

    public Double getWind1() {
        return wind1;
    }

    public void setWind1(Double wind1) {
        this.wind1 = wind1;
    }

    public Double getWind2() {
        return wind2;
    }

    public void setWind2(Double wind2) {
        this.wind2 = wind2;
    }

    public Double getWorkTime() {
        return workTime;
    }

    public void setWorkTime(Double workTime) {
        this.workTime = workTime;
    }

}

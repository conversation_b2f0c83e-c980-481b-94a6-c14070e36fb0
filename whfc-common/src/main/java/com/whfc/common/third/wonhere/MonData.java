package com.whfc.common.third.wonhere;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-11-18 16:53
 */
public class MonData implements Serializable {

    /**
     * 类型
     */
    private Integer type;

    /**
     *
     */
    private String name;

    /**
     * 数据字段
     */
    private List<MonColumn> columns;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MonColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<MonColumn> columns) {
        this.columns = columns;
    }
}

package com.whfc.common.third.hkpoly;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/2/20 11:05
 */
public enum HkPolyAlarmType {

    CAMERA_HELMET("CAMERA_HELMET", "未戴安全帽"),

    CAMERA_DANGER_ZONE("DANGER_ZONE", "危险区域"),

    WATCH_SOS("WATCH_2", "SOS"),
    WATCH_HEALTH("WATCH_100", "健康报警"),
    WATCH_STOP("WATCH_32", "长时间静止"),
    WATCH_FALL("WATCH_16384", "跌倒"),

    PLANTPROXIMITY_BSD("BSD", "盲区检测报警"),


    ;

    private final String value;

    private final String name;

    HkPolyAlarmType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

package com.whfc.common.third.wonhere;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 基坑监测数据
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2021-10-30 16:55
 */
@Data
public class JkData implements Serializable {

    /**
     * 采集仪的设备编号
     */
    private String dev_id;

    /**
     * 设备电压
     */
    private Double volt;

    /**
     * 采集时间 “yyyy-MM-dd HH:mm:ss”
     */
    private Date sample_time;

    /**
     * 测点数据
     */
    private List<JkDataItem> datas;
}

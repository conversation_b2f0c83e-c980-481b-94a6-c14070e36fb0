package com.whfc.common.third.xxljob.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/12
 */
@Data
public class XxlJobInfo implements Serializable {

    private Integer id;
    private Integer jobGroup;
    private String jobCron;
    private String jobDesc;
    private String addTime;
    private String updateTime;
    private String author;
    private String alarmEmail;
    private String executorRouteStrategy;
    private String executorHandler;
    private String executorParam;
    private String executorBlockStrategy;
    private Integer executorTimeout;
    private Integer executorFailRetryCount;
    private String glueType;
    private String glueSource;
    private String glueRemark;
    private String glueUpdatetime;
    private String childJobId;
    private Integer triggerStatus;
    private Long triggerLastTime;
    private Long triggerNextTime;

}

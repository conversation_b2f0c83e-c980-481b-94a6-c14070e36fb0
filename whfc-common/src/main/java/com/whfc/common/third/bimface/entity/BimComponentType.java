package com.whfc.common.third.bimface.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 构件类型
 * @date 2024/12/26 16:14
 */
public enum BimComponentType {

    FLOOR("floor"),

    CATEGORY("category"),

    FAMILY("family"),

    FAMILY_TYPE("familyType"),

    ELEMENT("element");

    private final String type;

    BimComponentType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

}

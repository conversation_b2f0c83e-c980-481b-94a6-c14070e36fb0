package com.whfc.common.third.baibutonget.entity;

import java.io.Serializable;

public class Path implements Serializable {

    /**
     * 纬度坐标点(坐标系：高德 GCJ-02)
     */
    private Double x_point;

    /**
     * 经度坐标点
     */
    private Double y_point;

    /**
     * 创建时间
     */
    private Long time;

    /**
     * 角度
     */
    private Double c_angle;

    /**
     * 速度
     */
    private Double c_speed;

    /**
     * 纠偏
     */
    private Double c_trust;

    private Integer gps;

    public Double getX_point() {
        return x_point;
    }

    public void setX_point(Double x_point) {
        this.x_point = x_point;
    }

    public Double getY_point() {
        return y_point;
    }

    public void setY_point(Double y_point) {
        this.y_point = y_point;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Double getC_angle() {
        return c_angle;
    }

    public void setC_angle(Double c_angle) {
        this.c_angle = c_angle;
    }

    public Double getC_speed() {
        return c_speed;
    }

    public void setC_speed(Double c_speed) {
        this.c_speed = c_speed;
    }

    public Double getC_trust() {
        return c_trust;
    }

    public void setC_trust(Double c_trust) {
        this.c_trust = c_trust;
    }

    public Integer getGps() {
        return gps;
    }

    public void setGps(Integer gps) {
        this.gps = gps;
    }
}

package com.whfc.common.third.baibutonget.entity;

import com.whfc.common.util.JSONUtil;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/8 11:42
 */
public class GetTokenResult extends Result implements Serializable {

    private String token;

    private String session_id;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }

    public static void main(String[] args) {
        String s = "{\"status\":true,\"token\":\"b318be27cdfc2a6c723234e87f8c353f\",\"session_id\":\"b318be27cdfc2a6c723234e87f8c353f\",\"msg_code\":\"GetSucc\"}";
        GetTokenResult r = JSONUtil.parseObject(s, GetTokenResult.class);
        System.out.println(JSONUtil.toPrettyString(r));
    }
}

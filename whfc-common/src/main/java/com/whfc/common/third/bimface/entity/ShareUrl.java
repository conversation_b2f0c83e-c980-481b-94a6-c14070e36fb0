package com.whfc.common.third.bimface.entity;

import java.io.Serializable;

/**
 * @Description: 分享链接
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-09-09 17:38
 */
public class ShareUrl implements Serializable {

    private String appKey;

    private String expireTime;

    private String password;

    private String sourceId;

    private String sourceName;

    private String sourceType;

    private String url;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}

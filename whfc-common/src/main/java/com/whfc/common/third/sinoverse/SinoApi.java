package com.whfc.common.third.sinoverse;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.third.sinoverse.entity.*;
import com.whfc.common.util.HttpUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.MD5Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/8/13 9:39
 */
public class SinoApi {

    private static final Logger logger = LoggerFactory.getLogger(SinoApi.class);

    private static final String LOGIN_URI = "http://app.wlwapp.cn/api/v2/login";
    private static final String DEVICE_URI = "http://app.wlwapp.cn/api/v2/device";
    private static final String REAL_DATA_URI = "http://app.wlwapp.cn/api/v2/data";
    private static final String HISTORY_DATA_URI = "http://app.wlwapp.cn/api/v2/dataRecord.jsp";

    private static final Integer SUCCESS = 1;

    private String username;

    private String password;

    public SinoApi(String username, String password) {
        this.username = username;
        this.password = password;
    }

    public Token getToken(){
        String url = LOGIN_URI;
        JSONObject body = new JSONObject();
        body.put("username",username);
        body.put("password", MD5Util.md5Encode(password));
        String data = body.toString();
        String response = HttpUtil.doPost(url, data);
        logger.info("登录,response:{}",response);
        TokenResult result = JSONObject.parseObject(response, TokenResult.class);
        if(result!=null && SUCCESS.equals(result.getStatus())){
            return result.getData();
        }
        return null;
    }

    public List<Device> getDeviceList(String token){
        String url = DEVICE_URI;

        Map<String,String> header = new HashMap<>();
        header.put("token",token);

        JSONObject body = new JSONObject();
        String data = body.toString();

        String response = HttpUtil.doPost(url, data, header);
        logger.info("获取设备列表,response:{}",response);
        DeviceResult result = JSONObject.parseObject(response, DeviceResult.class);
        if(result!=null && SUCCESS.equals(result.getStatus())){
            return result.getData();
        }
        return Collections.emptyList();
    }

    public List<RealData> getDeviceData(String token,String did){
        String url = REAL_DATA_URI;

        Map<String,String> header = new HashMap<>();
        header.put("token",token);

        JSONObject body = new JSONObject();
        body.put("did",did);
        String data = body.toString();

        String response = HttpUtil.doPost(url, data, header);
        logger.info("获取设备实时数据,response:{}",response);
        RealDataResult result = JSONObject.parseObject(response, RealDataResult.class);
        if(result!=null && SUCCESS.equals(result.getStatus())){
            for(RealData realData : result.getData()){
                List<DataItem> dataList = JSONUtil.parseArray(realData.getJsonstr(), DataItem.class);
                realData.setDataList(dataList);
            }
            return result.getData();
        }
        return Collections.emptyList();
    }
}

package com.whfc.common.third.indoor.msg;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/12 17:02
 */
public class FenceAlarmMsg extends Msg {

    private String tagId;

    private String tagName;

    private Double x;

    private Double y;

    private Double lng;

    private Double lat;

    /**
     * 地图ID
     */
    private String mapId;

    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 电子围栏 id
     */
    private String fenceId;

    /**
     * 电子围栏名称
     */
    private String fenceName;

    /**
     * 围栏类型id
     */
    private String fenceTypeId;

    /**
     * 围栏类型名称
     */
    private String fenceTypeName;

    /**
     * 报警类型，1：进入围栏；2：出围栏
     */
    private Integer inFence;

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Double getX() {
        return x;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public Double getY() {
        return y;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getMapId() {
        return mapId;
    }

    public void setMapId(String mapId) {
        this.mapId = mapId;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getFenceId() {
        return fenceId;
    }

    public void setFenceId(String fenceId) {
        this.fenceId = fenceId;
    }

    public String getFenceName() {
        return fenceName;
    }

    public void setFenceName(String fenceName) {
        this.fenceName = fenceName;
    }

    public String getFenceTypeId() {
        return fenceTypeId;
    }

    public void setFenceTypeId(String fenceTypeId) {
        this.fenceTypeId = fenceTypeId;
    }

    public String getFenceTypeName() {
        return fenceTypeName;
    }

    public void setFenceTypeName(String fenceTypeName) {
        this.fenceTypeName = fenceTypeName;
    }

    public Integer getInFence() {
        return inFence;
    }

    public void setInFence(Integer inFence) {
        this.inFence = inFence;
    }
}

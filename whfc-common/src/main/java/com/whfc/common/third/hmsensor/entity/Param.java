package com.whfc.common.third.hmsensor.entity;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/4/4 16:18
 */
public class Param implements Serializable {

    private String cod;

    /**
     * 压力上限
     */
    private Double p1;

    /**
     * 压力下限
     */
    private Double p2;

    /**
     * 唤醒周期
     */
    private Integer p3;

    /**
     * 电池报警
     */
    private String p4;

    /**
     * 报警延时
     */
    private String p5;

    /**
     * 报警模式(0:阈值,1:区间)
     */
    private Integer alm;

    public String getCod() {
        return cod;
    }

    public void setCod(String cod) {
        this.cod = cod;
    }

    public Double getP1() {
        return p1;
    }

    public void setP1(Double p1) {
        this.p1 = p1;
    }

    public Double getP2() {
        return p2;
    }

    public void setP2(Double p2) {
        this.p2 = p2;
    }

    public Integer getP3() {
        return p3;
    }

    public void setP3(Integer p3) {
        this.p3 = p3;
    }

    public String getP4() {
        return p4;
    }

    public void setP4(String p4) {
        this.p4 = p4;
    }

    public String getP5() {
        return p5;
    }

    public void setP5(String p5) {
        this.p5 = p5;
    }

    public Integer getAlm() {
        return alm;
    }

    public void setAlm(Integer alm) {
        this.alm = alm;
    }
}

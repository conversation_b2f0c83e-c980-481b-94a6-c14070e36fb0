package com.whfc.common.third.zzgondy.entity;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/20 11:08
 */
public class HistoryData implements Serializable {

    private String updateDt;

    private Double digital1;

    private Double digital2;

    private Double digital3;

    private Double digital4;

    private Double digital5;

    private Double digital6;

    private Double digital7;

    private Double digital8;

    private Double digital9;

    public String getUpdateDt() {
        return updateDt;
    }

    public void setUpdateDt(String updateDt) {
        this.updateDt = updateDt;
    }

    public Double getDigital1() {
        return digital1;
    }

    public void setDigital1(Double digital1) {
        this.digital1 = digital1;
    }

    public Double getDigital2() {
        return digital2;
    }

    public void setDigital2(Double digital2) {
        this.digital2 = digital2;
    }

    public Double getDigital3() {
        return digital3;
    }

    public void setDigital3(Double digital3) {
        this.digital3 = digital3;
    }

    public Double getDigital4() {
        return digital4;
    }

    public void setDigital4(Double digital4) {
        this.digital4 = digital4;
    }

    public Double getDigital5() {
        return digital5;
    }

    public void setDigital5(Double digital5) {
        this.digital5 = digital5;
    }

    public Double getDigital6() {
        return digital6;
    }

    public void setDigital6(Double digital6) {
        this.digital6 = digital6;
    }

    public Double getDigital7() {
        return digital7;
    }

    public void setDigital7(Double digital7) {
        this.digital7 = digital7;
    }

    public Double getDigital8() {
        return digital8;
    }

    public void setDigital8(Double digital8) {
        this.digital8 = digital8;
    }

    public Double getDigital9() {
        return digital9;
    }

    public void setDigital9(Double digital9) {
        this.digital9 = digital9;
    }
}

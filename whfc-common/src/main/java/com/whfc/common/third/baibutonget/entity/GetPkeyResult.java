package com.whfc.common.third.baibutonget.entity;

import com.whfc.common.util.JSONUtil;

import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/5/8 11:42
 */
public class GetPkeyResult extends Result implements Serializable {

    private String data;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public static void main(String[] args) {
        String s = "{\"status\":true,\"msg\":\"success\",\"data\":\"412839d24f9d4cac3d03cdbf3dda6b50\",\"msg_code\":\"GetSucc\"}";
        GetPkeyResult r = JSONUtil.parseObject(s, GetPkeyResult.class);
        System.out.println(JSONUtil.toPrettyString(r));
    }
}

package com.whfc.common.third.indoor.ble;

import java.io.Serializable;

/**
 * 蓝牙标签
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/4 10:32
 */
public class BleTag implements Serializable {

    /**
     * mac地址
     */
    private String mac;

    /**
     * 信号强度
     */
    private Integer rssi;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public Integer getRssi() {
        return rssi;
    }

    public void setRssi(Integer rssi) {
        this.rssi = rssi;
    }

    public BleTag(String mac, Integer rssi) {
        this.mac = mac;
        this.rssi = rssi;
    }
}

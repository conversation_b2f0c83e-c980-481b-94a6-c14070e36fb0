package com.whfc.common.third.cloudm.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 工时信息
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/3/7 16:40
 */
public class WorkTime implements Serializable {

    /**
     * 日期
     */
    private Date date;

    /**
     * 工时，单位：小时
     */
    private Double workTime;

    /**
     * 怠速时间，单位：小时
     */
    private Double idleTime;

    /**
     * 负荷工作时间，单位：小时
     */
    private Double loadWorkTime;

    /**
     * 开始时间， 格式 HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间， 格式 HH:mm:ss
     */
    private String endTime;

    /**
     * 状态，-1:不在线 0:在线，1:怠速，2:工作中
     */
    private Integer status;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Double getWorkTime() {
        return workTime;
    }

    public void setWorkTime(Double workTime) {
        this.workTime = workTime;
    }

    public Double getIdleTime() {
        return idleTime;
    }

    public void setIdleTime(Double idleTime) {
        this.idleTime = idleTime;
    }

    public Double getLoadWorkTime() {
        return loadWorkTime;
    }

    public void setLoadWorkTime(Double loadWorkTime) {
        this.loadWorkTime = loadWorkTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

package com.whfc.common.third.wonhere;

import com.whfc.common.third.wonhere.enums.DevType;
import com.whfc.common.third.wonhere.enums.MonType;
import com.whfc.common.util.JSONUtil;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-11-18 16:58
 */
public class WonhereConst {


    /**
     * 测距（位移）
     */
    private static List<MonColumn> mon_type_2 = Arrays.asList(
            new MonColumn("data1", "记录值", "mm"),
            new MonColumn("data1this", "本次变化量", "mm"),
            new MonColumn("data1total", "累计变化量", "mm"),
            new MonColumn("data1rate", "变化速率", "mm/d")
    );

    /**
     * 层水平位移
     */
    private static List<MonColumn> mon_type_3 = Arrays.asList(
            new MonColumn("data1", "X轴单点记录值", "mm"),
            new MonColumn("data1this", "X轴单点单次变化量", "mm"),
            new MonColumn("data1total", "X轴单点累计变化量", "mm"),
            new MonColumn("data1rate", "X轴单点变化速率", "mm/d"),
            new MonColumn("data2", "Y轴单点记录值", "mm"),
            new MonColumn("data2this", "Y轴单点单次变化量", "mm"),
            new MonColumn("data2total", "Y轴单点累计变化量", "mm"),
            new MonColumn("data2rate", "Y轴单点变化速率", "mm/d"),
            new MonColumn("data3", "X轴累积记录值", "mm"),
            new MonColumn("data3this", "X轴累积记录值本次位移增量", "mm"),
            new MonColumn("data3total", "X轴累计位移增量", "mm"),
            new MonColumn("data3rate", "X轴累积记录值变化速率", "mm/d"),
            new MonColumn("data4", "Y轴累积记录值", "mm"),
            new MonColumn("data4this", "Y轴累积记录值本次位移增量", "mm"),
            new MonColumn("data4total", "Y轴累计位移增量", "mm"),
            new MonColumn("data4rate", "Y轴累积记录值变化速率", "mm/d"),
            new MonColumn("depth", "深度", "m")
    );

    /**
     * 沉降（竖向位移）
     */
    private static List<MonColumn> mon_type_4 = Arrays.asList(
            new MonColumn("data1", "记录值", "mm"),
            new MonColumn("data1this", "单次变化量", "mm"),
            new MonColumn("data1total", "累计变化量", "mm"),
            new MonColumn("data1rate", "变化速率", "mm/d")
    );

    /**
     * 倾斜(X&Y,角度)
     */
    private static List<MonColumn> mon_type_6 = Arrays.asList(
            new MonColumn("data1", "X倾斜值", "°"),
            new MonColumn("data1this", "X倾斜值单次变化量", "°"),
            new MonColumn("data1total", "X倾斜值累计变化量", "°"),
            new MonColumn("data1rate", "X倾斜值变化速率", "°/d"),
            new MonColumn("data2", "Y倾斜值", "°"),
            new MonColumn("data2this", "Y倾斜值单次变化量", "°"),
            new MonColumn("data2total", "Y倾斜值累计变化量", "°"),
            new MonColumn("data2rate", "Y倾斜值变化速率", "°/d")
    );

    /**
     * 倾斜(水平位移)
     */
    private static List<MonColumn> mon_type_8 = Arrays.asList(
            new MonColumn("data1", "X轴位移", "mm"),
            new MonColumn("data1this", "X轴位移单次变化量", "mm"),
            new MonColumn("data1total", "X轴位移累计变化量", "mm"),
            new MonColumn("data1rate", "X轴位移变化速率", "mm/d"),
            new MonColumn("data2", "Y轴位移", "mm"),
            new MonColumn("data2this", "Y轴位移单次变化量", "mm"),
            new MonColumn("data2total", "Y轴位移累计变化量", "mm"),
            new MonColumn("data2rate", "Y轴位移变化速率", "mm/d")
    );

    /**
     * 水位监测
     */
    private static List<MonColumn> mon_type_11 = Arrays.asList(
            new MonColumn("data1", "水位高程", "m")
    );

    /**
     * 钢筋应力
     */
    private static List<MonColumn> mon_type_13 = Arrays.asList(
            new MonColumn("data1", "记录值", "kN"),
            new MonColumn("data1this", "单次变化量", "kN"),
            new MonColumn("data1total", "累计变化量", "kN"),
            new MonColumn("data1rate", "变化速率", "kN/d")
    );

    /**
     * 压力监测
     */
    private static List<MonColumn> mon_type_14 = Arrays.asList(
            new MonColumn("data1", "记录值", "kpa"),
            new MonColumn("data1this", "单次变化量", "kpa"),
            new MonColumn("data1total", "累计变化量", "kpa"),
            new MonColumn("data1rate", "变化速率", "kPa/d")
    );

    /**
     * 应变监测
     */
    private static List<MonColumn> mon_type_16 = Arrays.asList(
            new MonColumn("data1", "记录值", "με"),
            new MonColumn("data1this", "单次变化量", "με"),
            new MonColumn("data1total", "累计变化量", "με"),
            new MonColumn("data1rate", "变化速率", "με/d")
    );

    /**
     * 钢支撑轴力
     */
    private static List<MonColumn> mon_type_17 = Arrays.asList(
            new MonColumn("data1", "记录值", "kN"),
            new MonColumn("data1this", "单次变化量", "kN"),
            new MonColumn("data1total", "累计变化量", "kN"),
            new MonColumn("data1rate", "变化速率", "kN/d")
    );

    /**
     * 锚杆拉力
     */
    private static List<MonColumn> mon_type_18 = Arrays.asList(
            new MonColumn("data1", "记录值", "kN"),
            new MonColumn("data1this", "单次变化量", "kN"),
            new MonColumn("data1total", "累计变化量", "kN"),
            new MonColumn("data1rate", "变化速率", "kN/d")
    );

    /**
     * 混凝土温度
     */
    private static List<MonColumn> mon_type_20 = Arrays.asList(
            new MonColumn("data1", "记录值", "℃"),
            new MonColumn("data1this", "单次变化量", "℃"),
            new MonColumn("data1total", "累计变化量", "℃"),
            new MonColumn("data1rate", "变化速率", "℃/d")
    );

    /**
     * 雨量
     */
    private static List<MonColumn> mon_type_27 = Arrays.asList(
            new MonColumn("data1", "降雨量", "mm")
    );

    /****************************************************/

    /**
     * 无线荷重
     */
    private static List<MonColumn> dev_type_9 = Arrays.asList(
            new MonColumn("f1", "压力", "KN")
    );

    /**
     * 无线拉线（竖向沉降）
     */
    private static List<MonColumn> dev_type_10 = Arrays.asList(
            new MonColumn("f1", "沉降", "mm")
    );

    /**
     * 无线拉线（水平位移）
     */
    private static List<MonColumn> dev_type_11 = Arrays.asList(
            new MonColumn("f1", "沉降", "mm")
    );

//    /**
//     * 静力水准仪
//     */
//    private static List<MonColumn> dev_type_10 = Arrays.asList(
//            new MonColumn("f1", "温度", "℃"),
//            new MonColumn("f2", "压强", "kpa")
//    );
//
//    /**
//     * 固定式测斜仪
//     */
//    private static List<MonColumn> dev_type_11 = Arrays.asList(
//            new MonColumn("f1", "Y轴温度", "℃"),
//            new MonColumn("f2", "Y轴倾斜角度", "°"),
//            new MonColumn("f3", "X轴温度", "℃"),
//            new MonColumn("f4", "X轴倾斜角度", "°")
//    );

    /**
     * 拉线位移计
     */
    private static List<MonColumn> dev_type_13 = Arrays.asList(
            new MonColumn("f1", "位移", "mm")
    );

    /**
     * 激光测距仪
     */
    private static List<MonColumn> dev_type_14 = Arrays.asList(
            new MonColumn("f2", "距离", "m")
    );

    /**
     * 双轴倾角计
     */
    private static List<MonColumn> dev_type_17 = Arrays.asList(
            new MonColumn("f1", "Y轴温度", "℃"),
            new MonColumn("f2", "Y轴倾斜角度", "°"),
            new MonColumn("f3", "X轴温度", "℃"),
            new MonColumn("f4", "X轴倾斜角度", "°")
    );

    /**
     * 投入式水位计
     */
    private static List<MonColumn> dev_type_20 = Arrays.asList(
            new MonColumn("f1", "水位", "dm")
    );

    /**
     * 浮子式水位计
     */
    private static List<MonColumn> dev_type_21 = Arrays.asList(
            new MonColumn("f1", "温度", "℃"),
            new MonColumn("f2", "水位", "dm")
    );

    /**
     * 渗压计
     */
    private static List<MonColumn> dev_type_22 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz")
    );

    /**
     * 锚索测力计
     */
    private static List<MonColumn> dev_type_24 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz")
    );

    /**
     * 钢筋计
     */
    private static List<MonColumn> dev_type_25 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz"),
            new MonColumn("f2", "温度", "℃")
    );

    /**
     * 压力盒
     */
    private static List<MonColumn> dev_type_26 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz")
    );

    /**
     * 孔隙水压力计
     */
    private static List<MonColumn> dev_type_27 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz"),
            new MonColumn("f2", "温度", "℃")
    );

    /**
     * 应变计
     */
    private static List<MonColumn> dev_type_28 = Arrays.asList(
            new MonColumn("f1", "频率", "Hz"),
            new MonColumn("f2", "电阻值", "Ω")
    );

    /**
     * 轴力计
     */
    private static List<MonColumn> dev_type_29 = Arrays.asList(
            new MonColumn("f1", "轴力", "KN")
    );

    /**
     * 雨量
     */
    private static List<MonColumn> dev_type_30 = Arrays.asList(
            new MonColumn("f2", "水位", "mm")
    );

    /**
     * 电阻
     */
    private static List<MonColumn> dev_type_31 = Arrays.asList(
            new MonColumn("f1", "电阻值", "Ω")
    );

    /**
     * 无线倾角
     */
    private static List<MonColumn> dev_type_95 = Arrays.asList(
            new MonColumn("f1", "X轴角度", "°"),
            new MonColumn("f2", "Y轴角度", "°"),
            new MonColumn("f3", "Z轴角度", "°")
    );


    /**
     * 获取基坑数据字段
     *
     * @param monType
     * @return
     */
    public static MonData getJKData(MonType monType) {
        List<MonColumn> columns = null;
        switch (monType) {
            case TYPE2:
                columns = mon_type_2;
                break;
            case TYPE3:
                columns = mon_type_3;
                break;
            case TYPE4:
                columns = mon_type_4;
                break;
            case TYPE6:
                columns = mon_type_6;
                break;
            case TYPE8:
                columns = mon_type_8;
                break;
            case TYPE11:
                columns = mon_type_11;
                break;
            case TYPE13:
                columns = mon_type_13;
                break;
            case TYPE14:
                columns = mon_type_14;
                break;
            case TYPE16:
                columns = mon_type_16;
                break;
            case TYPE17:
                columns = mon_type_17;
                break;
            case TYPE18:
                columns = mon_type_18;
                break;
            case TYPE20:
                columns = mon_type_20;
                break;
            case TYPE27:
                columns = mon_type_27;
                break;
            default:
                break;
        }
        MonData monData = new MonData();
        monData.setType(monType.getValue());
        monData.setName(monType.getDesc());
        monData.setColumns(columns);
        return monData;
    }

    /**
     * 获取高支模数据字段
     *
     * @param devType
     * @return
     */
    public static MonData getGzmData(DevType devType) {
        List<MonColumn> columns = null;
        switch (devType) {
            case TYPE9:
                columns = dev_type_9;
                break;
            case TYPE10:
                columns = dev_type_10;
                break;
            case TYPE11:
                columns = dev_type_11;
                break;
            case TYPE13:
                columns = dev_type_13;
                break;
            case TYPE14:
                columns = dev_type_14;
                break;
            case TYPE17:
                columns = dev_type_17;
                break;
            case TYPE20:
                columns = dev_type_20;
                break;
            case TYPE21:
                columns = dev_type_21;
                break;
            case TYPE22:
                columns = dev_type_22;
                break;
            case TYPE24:
                columns = dev_type_24;
                break;
            case TYPE25:
                columns = dev_type_25;
                break;
            case TYPE26:
                columns = dev_type_26;
                break;
            case TYPE27:
                columns = dev_type_27;
                break;
            case TYPE28:
                columns = dev_type_28;
                break;
            case TYPE29:
                columns = dev_type_29;
                break;
            case TYPE30:
                columns = dev_type_30;
                break;
            case TYPE31:
                columns = dev_type_31;
                break;
            case TYPE95:
                columns = dev_type_95;
                break;
            default:
                break;
        }
        MonData monData = new MonData();
        monData.setType(devType.getValue());
        monData.setName(devType.getDesc());
        monData.setColumns(columns);
        return monData;
    }

    public static void main(String[] args) {
        MonData data = WonhereConst.getJKData(MonType.TYPE27);
        System.out.println(JSONUtil.toPrettyString(data));

        MonData data1 = WonhereConst.getGzmData(DevType.TYPE31);
        System.out.println(JSONUtil.toPrettyString(data1));
    }
}

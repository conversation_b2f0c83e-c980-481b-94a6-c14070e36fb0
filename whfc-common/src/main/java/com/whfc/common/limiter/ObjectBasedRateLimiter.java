package com.whfc.common.limiter;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.RateLimiter;
import com.whfc.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

public class ObjectBasedRateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(ObjectBasedRateLimiter.class);

    private final Cache<String, RateLimiter> limiters;

    private double permitsPerSecond;

    public ObjectBasedRateLimiter(double permitsPerSecond, long expireAfterAccess, TimeUnit timeUnit) {
        // 每秒允许的请求数
        this.permitsPerSecond = permitsPerSecond;
        limiters = CacheBuilder.newBuilder().initialCapacity(256).maximumSize(2000).expireAfterAccess(expireAfterAccess, timeUnit).build();
    }

    public boolean tryAcquire(String objectId) throws ExecutionException {
        // 获取或创建RateLimiter实例
        RateLimiter limiter = limiters.get(objectId, () -> {
            logger.info("tryAcquire:{}", objectId);
            return RateLimiter.create(permitsPerSecond);
        });
        return limiter.tryAcquire();
    }

    public void acquire(String objectId) throws ExecutionException {
        // 获取或创建RateLimiter实例
        RateLimiter limiter = limiters.get(objectId, () -> {
            logger.info("acquire:{}", objectId);
            return RateLimiter.create(permitsPerSecond);
        });
        limiter.acquire();
    }

    public static void main(String[] args) {
        ObjectBasedRateLimiter limiter = new ObjectBasedRateLimiter(0.1, 1, TimeUnit.MINUTES);
        for (int i = 0; i < 10; i++) {
            try {
                limiter.acquire("test");
                logger.info("请求成功");
                TimeUnit.SECONDS.sleep(30);
            } catch (Exception e) {
                logger.info("请求失败");
            }
        }
        for (int i = 0; i < 10; i++) {
            try {
                limiter.acquire("test");
                logger.info("请求成功");
                TimeUnit.SECONDS.sleep(60);
            } catch (Exception e) {
                logger.info("请求失败");
            }
        }
    }
}

package com.whfc.entity.param.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
@Data
@Schema(description = "报警对象")
public class AppWarnObjParam implements Serializable {

    /**
     * 报警规则ID
     */
    @Schema(description = "报警规则ID")
    private Integer ruleId;

    /**
     * 报警对象ID
     */
    @Schema(description = "报警对象ID（与 guidList 只传一个）")
    private List<Integer> idList;

    /**
     * 报警对象GUID
     */
    @Schema(description = "报警对象GUID（与 idList 只传一个）")
    private List<String> guidList;

}

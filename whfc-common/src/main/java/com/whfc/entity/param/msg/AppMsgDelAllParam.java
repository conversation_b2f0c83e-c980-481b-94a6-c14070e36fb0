package com.whfc.entity.param.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-06
 */
@Schema(description = "删除消息参数")
@Data
public class AppMsgDelAllParam implements Serializable {


    @NotNull
    @Schema(description = "项目ID")
    private Integer deptId;

    @Schema(description = "模块类型")
    private Integer moduleType;

    @Schema(description = "读取状态")
    private Integer state;
}

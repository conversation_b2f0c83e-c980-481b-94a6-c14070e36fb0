package com.whfc.entity.param.fim;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: qzexing
 * @Version: 1.0
 * @Date: 2020/1/10 11:07
 */
@Data
public class FimAlgResultParam implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 设备ID
     */
    @NotNull
    private Integer deviceId;

    /**
     * 设备名称
     */
    @NotNull
    private String deviceName;

    /**
     * 设备厂商
     */
    private String deviceVendor;

    /**
     * 摄像头编号
     */
    private String cameraNum;

    /**
     * 摄像头名称
     */
    private String cameraName;

    /**
     * 报警类型
     */
    @NotNull
    private Integer algCheckType;

    /**
     * 报警类型名称
     */
    @NotNull
    private String algCheckTypeName;

    /**
     * 报警时间
     */
    @NotNull
    private Date time;

    /**
     * 报警图片
     */
    private List<String> algResultImgList;

    /**
     * 报警视频地址
     */
    private String videoUrl;

    /**
     * AI服务器ID
     */
    private Integer aiServerId;

    /**
     * AI服务器名称
     */
    private String aiServerName;

}

package com.whfc.entity.param.warn;

import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/23
 */
@Data
@Schema(description = "报警接收用户")
public class AppWarnUserParam implements Serializable {

    /**
     * 报警规则ID
     */
    @Deprecated
    @Schema(description = "报警规则ID(废弃 统一使用 ruleIdList)")
    private Integer ruleId;

    @Schema(description = "报警规则ID列表")
    private List<Integer> ruleIdList;

    /**
     * 接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件
     */
    @Schema(description = "接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件")
    private List<Integer> msgChannelList;

    /**
     * 接收消息的人
     */
    @Schema(description = "接收消息的人")
    private List<AppMsgToUserDTO> userList;
}
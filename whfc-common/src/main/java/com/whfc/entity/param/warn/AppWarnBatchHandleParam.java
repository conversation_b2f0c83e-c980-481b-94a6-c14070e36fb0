package com.whfc.entity.param.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/9/26 15:16
 */
@Data
@Schema(description = "报警批量处理参数")
public class AppWarnBatchHandleParam implements Serializable {

    /**
     * 报警记录ID列表
     */
    @NotNull
    @Schema(description = "报警记录ID列表")
    private List<Integer> warnIdList;

    /**
     * 报警处理结果
     */
    @NotEmpty
    @Length(max = 100)
    @Schema(description = "报警处理结果")
    private String handleResult;

    /**
     * 报警处理说明
     */
    @Length(max = 100)
    @Schema(description = "报警处理说明")
    private String handleRemark;

    /**
     * 处理人id
     */
    @Schema(description = "处理人id", hidden = true)
    private Integer handleUserId;

    /**
     * 处理人姓名
     */
    @Schema(description = "处理人姓名", hidden = true)
    private String handleUserName;

    /**
     * 处理人电话
     */
    @Schema(description = "处理人电话", hidden = true)
    private String handleUserPhone;
}

package com.whfc.entity.param.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/9/26 15:16
 */
@Data
@Schema(description = "报警批量处理参数")
public class AppWarnBatchDeleteParam implements Serializable {

    /**
     * 报警记录ID列表
     */
    @NotNull
    @Schema(description = "报警记录ID列表")
    private List<Integer> warnIdList;
}

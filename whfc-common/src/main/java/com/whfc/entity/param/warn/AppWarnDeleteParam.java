package com.whfc.entity.param.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/9/26 15:16
 */
@Data
@Schema(description = "报警删除参数")
public class AppWarnDeleteParam implements Serializable {

    /**
     * 报警记录ID
     */
    @NotNull
    @Schema(description = "报警记录ID")
    private Integer warnId;

}

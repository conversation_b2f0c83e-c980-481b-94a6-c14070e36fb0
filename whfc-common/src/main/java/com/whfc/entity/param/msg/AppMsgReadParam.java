package com.whfc.entity.param.msg;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 设置消息已读
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/12/3 19:19
 */
@Data
public class AppMsgReadParam implements Serializable {

    /**
     * 消息ID列表
     */
    @NotEmpty
    private List<Integer> idList;
}

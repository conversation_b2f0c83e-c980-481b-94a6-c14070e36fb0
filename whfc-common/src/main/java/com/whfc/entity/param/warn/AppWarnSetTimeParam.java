package com.whfc.entity.param.warn;

import com.whfc.entity.dto.warn.AppWarnTimeDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019-12-05
 */
@Data
public class AppWarnSetTimeParam implements Serializable {
    @NotNull
    private Integer ruleId;

    @NotEmpty
    private List<AppWarnTimeDTO> timeList;
}

package com.whfc.entity.param;

import com.whfc.common.validator.IntValueBetween;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 检测硬件是否绑定参数
 * @date 2019-11-19
 */
@Data
public class WxBindCheckParam {

    /**
     * 设备类型
     */
    @NotNull
    @IntValueBetween(between = {1, 2, 3, 8})
    private Integer type;

    /**
     * 硬件SN
     */
    @NotEmpty
    private String sn;

}

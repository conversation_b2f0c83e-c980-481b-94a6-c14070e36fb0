package com.whfc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-01-16
 */
@Schema(description = "oss路径")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssPathDTO implements Serializable {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "类型 1-图片 2-视频")
    private Integer type;

    public OssPathDTO(String path) {
        this.path = path;
    }

    public OssPathDTO(String name, String path) {
        this.name = name;
        this.path = path;
    }
}

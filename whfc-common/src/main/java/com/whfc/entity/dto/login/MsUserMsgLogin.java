package com.whfc.entity.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 后台账号密码登录参数
 */
@Schema(description = "短信验证码登录参数")
@Data
public class MsUserMsgLogin {


    @Schema(description = "手机号")
    @NotEmpty
    private String phone;

    @Schema(description = "验证码")
    @NotEmpty
    private String code;

    @Schema(description = "图形验证码信息")
    private String captchaVerification;

    @Schema(description = "域名")
    private String host;
}

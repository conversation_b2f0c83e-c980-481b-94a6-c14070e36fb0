package com.whfc.entity.dto.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @DESCRIPTION 消息联系人
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/3/27
 */
@Schema(description = "消息联系人")
@Data
public class AppMsgToUserDTO implements Serializable {

    /**
     * 规则ID
     */
    @Schema(description = "规则ID")
    private Integer ruleId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String nickName;

    /**
     * 区号
     */
    @Schema(description = "区号")
    private String areaCode;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 组织机构
     */
    @Schema(description = "组织机构")
    private String deptName;

    /**
     * 合作单位ID
     */
    @Schema(description = "合作单位ID")
    private Integer corpId;

    /**
     * 合作单位名称
     */
    @Schema(description = "合作单位名称")
    private String corpName;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Integer departmentId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String departmentName;

    /**
     * 职位
     */
    @Schema(description = "职位")
    private String position;

    /**
     * 邮件html(每个接收人不一样)
     */
    @Schema(description = "邮件html(每个接收人不一样)")
    private String html;

    public AppMsgToUserDTO() {
    }

    public AppMsgToUserDTO(Integer userId) {
        this.userId = userId;
    }

    public AppMsgToUserDTO(Integer userId, String username) {
        this.userId = userId;
        this.username = username;
    }
}

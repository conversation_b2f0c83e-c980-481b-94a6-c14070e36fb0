package com.whfc.entity.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/10
 */
@Data
public class CaptchaDTO implements Serializable {

    @Schema(description = "拼图验证码图片base64")
    private String jigsawImageBase64;

    @Schema(description = "拼图验证码原图base64")
    private String originalImageBase64;

    @Schema(description = "拼图验证码")
    private boolean result;

    @Schema(description = "拼图验证码密钥")
    private String secretKey;

    @Schema(description = "拼图验证码token")
    private String token;

    @Schema(description = "验证码类型")
    private String captchaType;

    @Schema(description = "拼图验证码坐标")
    private String pointJson;


}

package com.whfc.entity.dto.emp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 项目人员出勤记录
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/9/10 16:33
 */
@Data
public class AttendStatDTO implements Serializable {

    /**
     * 日期
     */
    private Date date;

    /**
     * 出勤人数
     */
    private Integer attendNum;

    /**
     * 在场人数
     */
    private Integer localeInNum;

    /**
     * 入场人次
     */
    private Integer inCount;

    /**
     * 出场人次
     */
    private Integer outCount;
}

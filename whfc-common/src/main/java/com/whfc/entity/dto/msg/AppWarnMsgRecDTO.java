package com.whfc.entity.dto.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @DESCRIPTION
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/3/27
 */
@Schema(description = "预警消息接收DTO")
@Data
public class AppWarnMsgRecDTO implements Serializable {

    /**
     * 接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件
     */
    @Schema(description = "接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件")
    private List<Integer> msgChannelList;

    /**
     * 接收消息的人
     */
    @Schema(description = "接收消息的人")
    private List<AppMsgToUserDTO> userList;
}

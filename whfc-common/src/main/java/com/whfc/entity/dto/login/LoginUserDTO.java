package com.whfc.entity.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 后台登录用户信息
 */
@Data
@Schema(description = "后台登录用户信息")
public class LoginUserDTO implements Serializable {

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    private String nickname;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    private String userName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * token
     */
    @Schema(description = "token")
    private String accessToken;

    /**
     * 登录方式
     */
    @Schema(description = "登录方式")
    private String loginMethod;

    /**
     * guid
     */
    @Schema(description = "guid")
    private String guid;

    /**
     * 签名
     */
    @Schema(description = "签名")
    private String sign;
    
    /**
     * 密码是否已过期
     */
    @Schema(description = "密码是否已过期")
    private Boolean passwordExpired;
    
    /**
     * 密码是否即将过期
     */
    @Schema(description = "密码是否即将过期")
    private Boolean passwordAboutToExpire;
    
    /**
     * 密码过期剩余天数
     */
    @Schema(description = "密码过期剩余天数")
    private Integer passwordExpireDaysLeft;
}

package com.whfc.entity.dto.login;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@ToString
public class WxMpLogin {

    private String openId;
    /**
     * 授权码
     */
    private String code;
    /**
     *加密数据
     */
    private String encryptedData;
    /**
     * 加密算法的初始向量
     */
    private String iv;
    /**
     *登录方式（1-授权码登录，2-微信快捷登录，3-手机号验证码登录，4-账号密码登录）
     */
    private Integer appType;
    /**
     * 登录方式（1-授权码登录，2-微信快捷登录，3-手机号验证码登录，4-账号密码登录）
     */
    private Integer loginType;
    /**
     * 手机号）
     */
    private String phone;
    /**
     * 账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;

}

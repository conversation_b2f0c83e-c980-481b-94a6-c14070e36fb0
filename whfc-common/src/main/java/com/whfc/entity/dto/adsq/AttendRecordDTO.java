package com.whfc.entity.dto.adsq;

import lombok.Data;

import java.io.Serializable;

/**
 * 考勤记录DTO
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-14 10:39
 */
@Data
public class AttendRecordDTO implements Serializable {

    /**
     * 流水号
     */
    private String id;

    /**
     * 验证方式
     */
    private String verify;

    /**
     * 打卡时间  yyyy-MM-dd HH:mm:ss
     */
    private String checktime;

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 设备名称
     */
    private String alias;

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 人员姓名
     */
    private String ename;

    /**
     * 部门编号
     */
    private String deptnumber;

    /**
     * 部门名称
     */
    private String deptname;

    /**
     * 是否佩戴口罩 . （0:否，1:是）
     */
    private Integer mask_flag;

    /**
     * 考勤状态
     */
    private String stateno;

    /**
     * 考勤状态说明
     */
    private String state;

    /**
     * 照片信息数据为BASE64格式
     */
    private String photograph;


}

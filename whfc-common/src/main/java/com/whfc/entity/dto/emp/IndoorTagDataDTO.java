package com.whfc.entity.dto.emp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "室内定位标签报警信息")
@Data
public class IndoorTagDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目ID")
    private Integer deptId;

    @Schema(description = "基站ID")
    private Integer stationId;

    @Schema(description = "基站名称")
    private String stationName;

    @Schema(description = "标签ID")
    private Integer tagId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "绑定类型 1-人员 2-设备")
    private Integer bindType;

    @Schema(description = "人员ID")
    private Integer empId;

    @Schema(description = "人员名称")
    private String empName;

    @Schema(description = "设备唯一标识")
    private String deviceCode;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "距离1")
    private Double distance1;

    @Schema(description = "报警时间")
    private Date time;

}

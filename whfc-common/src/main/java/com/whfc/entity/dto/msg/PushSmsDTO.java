package com.whfc.entity.dto.msg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.TreeMap;


/**
 * 短信发送配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushSmsDTO implements Serializable {

    /**
     * 短信模版code
     */
    private String tplCode;

    /**
     * 短信模版参数 : 参数顺序与模版中参数顺序一致
     */
    private TreeMap<String, String> tplParam;
}

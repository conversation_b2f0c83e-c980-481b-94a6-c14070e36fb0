package com.whfc.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 首页数据统计
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/24
 */
@Data
@Schema(description = "首页数据统计")
public class IndexStatDTO implements Serializable {


    @Schema(description = "人员总数")
    private Integer empTotal = 0;

    @Schema(description = "今日出勤人数")
    private Integer empAttendTotal;

    @Schema(description = "今日在场人数")
    private Integer empLocaleTotal;

    @Schema(description = "报警总数")
    private Integer warnTotal = 0;

    @Schema(description = "许可证总数")
    private Integer permitTotal = 0;

    @Schema(description = "证书过期总数")
    private Integer certExpireTotal = 0;

}

package com.whfc.entity.dto.board;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-15
 */
@Data
public class BoardProjectDataDTO implements Serializable {
    /**
     * 项目的设备总数
     */
    private Integer machNum;

    /**
     * 项目的在线设备数
     */
    private Integer machOnlineNum;

    /**
     * 项目的总特种设备数
     */
    private Integer fseNum;

    /**
     * 项目的在线特种设备数
     */
    private Integer fseOnlineNum;

    /**
     * 项目的总环境设备数
     */
    private Integer dustNum;

    /**
     * 项目的在线环境设备数
     */
    private Integer dustOnlineNum;

    /**
     * 项目的总视频设备数
     */
    private Integer fvsNum;
    /**
     * 项目的在线视频设备数
     */
    private Integer fvsOnlineNum;

    /**
     * 项目在岗人数
     */
    private Integer empNum;

    /**
     * 项目的出勤人数
     */
    private Integer empAttendNum;

    /**
     * 设备报警数
     */
    private Integer machWarnNum;

    /**
     * 特种设备报警数
     */
    private Integer FseWarnNum;

    /**
     * 劳务报警数
     */
    private Integer empWarnNum;

    /**
     * 环境报警数
     */
    private Integer envWarnNum;

    /**
     * 智能监控报警数
     */
    private Integer fimWarnNum;

    /**
     * 安全步距报警
     */
    private Integer safetyWarnNum;

    /**
     * 有毒气体报警
     */
    private Integer toxicWarnNum;

    /**
     * 设备总数
     */
    private Integer total;

    /**
     * 在线设备数
     */
    private Integer online;

    /**
     * 正计时天数
     */
    private Integer safeDays;

    /**
     * 倒计时天数
     */
    private Integer countdown;
}

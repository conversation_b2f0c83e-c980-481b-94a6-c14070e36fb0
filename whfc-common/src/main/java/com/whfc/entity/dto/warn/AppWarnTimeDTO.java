package com.whfc.entity.dto.warn;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-30
 */
@Schema(description = "报警时间段")
@Data
public class AppWarnTimeDTO implements Serializable {

    @Schema(description = "报警规则ID")
    private Integer ruleId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private Date endTime;
}

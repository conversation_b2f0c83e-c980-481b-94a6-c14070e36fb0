package com.whfc.entity.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/10
 */
@Data
@Schema(description = "验证码获取参数")
public class CaptchaCheckParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "拼图验证码坐标")
    private String pointJson;

    @Schema(description = "token")
    private String token;

}

package com.whfc.entity.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 后台账号密码登录参数
 */
@Schema(description = "账号密码登录参数")
@Data
public class MsUserLogin {


    @Schema(description = "账号")
    @NotEmpty
    private String username;


    @Schema(description = "密码")
    @NotEmpty
    private String password;


    @Schema(description = "图形验证码信息")
    private String captchaVerification;


    @Schema(description = "域名")
    private String host;

    @Schema(description = "登录平台 WEB-PC后端 APP-app端")
    private String loginPlatform;
}

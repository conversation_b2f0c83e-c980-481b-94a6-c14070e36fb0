package com.whfc.entity.dto.access;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能接入统计
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/15 15:34
 */
@Data
public class AccessStatDTO implements Serializable {

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 项目数量
     */
    private Integer projectNum;

    /**
     * 接入数量
     */
    private Integer accessNum;

    /**
     * 接入率
     */
    private Double accessRate;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 在线
     */
    private Integer online;

    /**
     * 离线
     */
    private Integer offline;
}

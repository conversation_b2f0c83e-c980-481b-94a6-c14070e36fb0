package com.whfc.entity.dto.adsq;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 奥氹四桥 返回结果集
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-16 09:58
 */
public class AdsqResultDTO implements Serializable {

    /**
     * 响应状态
     */
    @JsonProperty(value = "Success")
    private Boolean success;
    /**
     * 响应消息
     */
    @JsonProperty("Msg")
    private String msg;
    /**
     * 响应内容
     */
    @JsonProperty("Data")
    private Object data;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "AdsqResultDTO{" +
                "success=" + success +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}

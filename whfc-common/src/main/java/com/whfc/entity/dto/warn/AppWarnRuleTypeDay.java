package com.whfc.entity.dto.warn;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/9/26 10:06
 */
@Data
public class AppWarnRuleTypeDay implements Serializable {

    /**
     * 报警日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 报警统计列表
     */
    private List<AppWarnRuleType> warnStat;

    public AppWarnRuleTypeDay(Date date, List<AppWarnRuleType> warnStat) {
        this.date = date;
        this.warnStat = warnStat;
    }
}

package com.whfc.entity.dto.adsq;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 奥氹四桥 考勤统计项
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-16 11:45
 */
public class AdsqStatisticsItemDTO implements Serializable {

    /**
     * 日期  格式：MM-dd
     */
    @JsonProperty("Time")
    private String time;
    /**
     * 出勤数
     */
    @JsonProperty("Value")
    private Integer value;

    /**
     * 日期
     */
    @JsonIgnore
    private Date date;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    @Override
    public String toString() {
        return "AdsqStatisticsItemDTO{" +
                "time='" + time + '\'' +
                ", value=" + value +
                ", date=" + date +
                '}';
    }
}

package com.whfc.entity.dto.adsq;

import lombok.Data;

import java.io.Serializable;

/**
 * 人员信息
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-14 10:51
 */
@Data
public class EmpInfoDTO implements Serializable {

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 名字
     */
    private String name;

    /**
     * 部门编号
     */
    private String deptnumber;

    /**
     * 部门名字
     */
    private String deptname;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 人员卡号
     */
    private String Card;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 人员是否在离职。0为在职，1为离职
     */
    private String status;


}

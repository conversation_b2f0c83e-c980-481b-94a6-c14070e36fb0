package com.whfc.entity.dto.warn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/9/26 10:06
 */
@Schema(description = "报警类型")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppWarnRuleType implements Serializable {


    @Schema(description = "序号")
    private Integer index;

    @Schema(description = "报警日期")
    private Date date;

    @Schema(description = "报警类型")
    private Integer type;

    @Schema(description = "报警对象ID")
    private Integer warnObjId;

    @Schema(description = "部门id")
    private Integer deptId;

    @Schema(description = "报警日期字符串")
    private String dateStr;

    @Schema(description = "报警类型 1-硬件报警 2-系统报警")
    private Integer warnType;

    @Schema(description = "报警类型")
    private Integer ruleType;

    @Schema(description = "报警编码")
    private String ruleCode;

    @Schema(description = "报警类型名称")
    private String ruleTypeName;

    @Schema(description = "报警数量")
    private Integer warnNum;

    @Schema(description = "已处理数量")
    private Integer handledNum;

    @Schema(description = "未处理数量")
    private Integer unHandledNum;
}

package com.whfc.entity.dto.adsq;

import lombok.Data;

import java.io.Serializable;

/**
 * 门禁记录
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-14 10:44
 */
@Data
public class InoutRecordDTO implements Serializable {

    /**
     * 流水号
     */
    private Integer id;

    /**
     * 验证方式
     */
    private Integer verify;

    /**
     * 时间
     */
    private String ttime;


    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 设备名称
     */
    private String alias;

    /**
     * 事件点
     */
    private String event_point;

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 人员姓名
     */
    private String ename;

    /**
     * 部门编号
     */
    private String deptnumber;

    /**
     * 部门名称
     */

    private String deptname;

    /**
     * 体温
     */
    private Double temperature;

    /**
     * 是否佩戴口罩. （0:否，1:是）
     */
    private Integer mask_flag;

    /**
     * 事件描述
     */
    private Integer event_no;

    /**
     * 进出 （0:入，1:出，2:无）
     */
    private Integer inorout;

    /**
     * 卡号
     */
    private String card_no;

    /**
     * 照片信息数据为BASE64格式
     */
    private String photograph;


}

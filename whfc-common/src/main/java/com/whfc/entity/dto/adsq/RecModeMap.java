package com.whfc.entity.dto.adsq;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 澳门闸机识别模式转换
 * @date 2020-09-16
 */
@Data
public class RecModeMap implements Serializable {
    private static final Map<String, Integer> map;

    static {
        map = new HashMap<>();
        // 刷脸
        map.put("15", 1);
        // 指纹
        map.put("1", 5);

    }

    public static Integer getRecMode(String name) {
        Integer result = map.get(name);
        return result;
    }


}

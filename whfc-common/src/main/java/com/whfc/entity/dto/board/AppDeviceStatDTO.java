package com.whfc.entity.dto.board;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/14
 */
@Schema(description = "设备在线统计")
@Data
public class AppDeviceStatDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id")
    private Integer deptId;

    @Schema(description = "设备类型名称")
    private String deviceTypeName;

    @Schema(description = "设备类型")
    private Integer deviceType;

    @Schema(description = "设备类型编码")
    private String deviceTypeCode;

    @Schema(description = "在线数量")
    private Integer onlineNum;

    @Schema(description = "总数量")
    private Integer total;

    @Schema(description = "序号")
    private Integer index;

    @Schema(description = "报警数量")
    private Integer warnNum;

}

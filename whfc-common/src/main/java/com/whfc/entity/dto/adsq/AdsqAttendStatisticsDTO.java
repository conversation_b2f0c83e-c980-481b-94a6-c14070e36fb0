package com.whfc.entity.dto.adsq;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 奥氹四桥 考勤统计
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-16 10:50
 */
public class AdsqAttendStatisticsDTO implements Serializable {

    /**
     * 总出勤人数
     */
    @JsonProperty("TotalAttendance")
    private Integer totalAttendance;
    /**
     * 总在场人数
     */
    @JsonProperty("TotalOnsite")
    private Integer totalOnsite;
    /**
     * 总入场人次
     */
    @JsonProperty("TotalIn")
    private Integer totalIn;
    /**
     * 总出场人次
     */
    @JsonProperty("TotalOut")
    private Integer totalOut;
    /**
     * 部门考勤情况
     */
    @JsonProperty("CompanyRecords")
    private List<AdsqGroupAttendDTO> companyRecords;

    public Integer getTotalAttendance() {
        return totalAttendance;
    }

    public void setTotalAttendance(Integer totalAttendance) {
        this.totalAttendance = totalAttendance;
    }

    public Integer getTotalOnsite() {
        return totalOnsite;
    }

    public void setTotalOnsite(Integer totalOnsite) {
        this.totalOnsite = totalOnsite;
    }

    public Integer getTotalIn() {
        return totalIn;
    }

    public void setTotalIn(Integer totalIn) {
        this.totalIn = totalIn;
    }

    public Integer getTotalOut() {
        return totalOut;
    }

    public void setTotalOut(Integer totalOut) {
        this.totalOut = totalOut;
    }

    public List<AdsqGroupAttendDTO> getCompanyRecords() {
        return companyRecords;
    }

    public void setCompanyRecords(List<AdsqGroupAttendDTO> companyRecords) {
        this.companyRecords = companyRecords;
    }


    @Override
    public String toString() {
        return "AdsqAttendStatisticsDTO{" +
                "totalAttendance=" + totalAttendance +
                ", totalOnsite=" + totalOnsite +
                ", totalIn=" + totalIn +
                ", totalOut=" + totalOut +
                ", companyRecords=" + companyRecords +
                '}';
    }
}

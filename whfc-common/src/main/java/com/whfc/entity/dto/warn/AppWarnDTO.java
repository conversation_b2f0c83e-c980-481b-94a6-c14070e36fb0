package com.whfc.entity.dto.warn;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 报警记录-父对象
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/11/30 9:46
 */
@Data
public class AppWarnDTO {

    /**
     * 消息对应的报警记录ID
     */
    private Integer warnId;

    /**
     * 消息对应报警记录状态
     */
    private Integer warnState;

    /**
     * 触发时间
     */
    private Date triggerTime;

    /**
     * 触发值
     */
    private String triggerValue;

    /**
     * 触发对象ID
     */
    private String triggerObjectId;

    /**
     * 业务模块(1-设备 2-人员 3-物资)
     * @see com.whfc.common.enums.AppWarnModuleType
     */
    private Integer moduleType;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 规则ID
     */
    private Integer ruleId;

    /**
     * 规则类型
     */
    private Integer ruleType;

    /**
     * 报警类型名称
     */
    private String ruleTypeName;

    /**
     * 规则最大值
     */
    private Integer ruleMaxValue;

    /**
     * 规则最小值
     */
    private Integer ruleMinValue;


    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理用户ID
     */
    private Integer handleUserId;

    /**
     * 处理用户姓名
     */
    private String handleUserName;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 备注
     */
    private String handleRemark;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;
}

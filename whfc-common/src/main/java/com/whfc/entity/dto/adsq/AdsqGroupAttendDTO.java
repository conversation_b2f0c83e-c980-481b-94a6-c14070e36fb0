package com.whfc.entity.dto.adsq;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 奥氹四桥 部门考勤
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-16 11:35
 */
public class AdsqGroupAttendDTO implements Serializable {

    /**
     * 部门ID
     */
    @JsonProperty("ID")
    private Integer id;
    /**
     * 部门名称
     */
    @JsonProperty("Name")
    private String name;
    /**
     * 出勤人数
     */
    @JsonProperty("Attendance")
    private Integer attendance;
    /**
     * 在场人数
     */
    @JsonProperty("Onsite")
    private Integer onsite;
    /**
     * 入场人次
     */
    @JsonProperty("In")
    private Integer in;
    /**
     * 出场人次
     */
    @JsonProperty("Out")
    private Integer out;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAttendance() {
        return attendance;
    }

    public void setAttendance(Integer attendance) {
        this.attendance = attendance;
    }

    public Integer getOnsite() {
        return onsite;
    }

    public void setOnsite(Integer onsite) {
        this.onsite = onsite;
    }

    public Integer getIn() {
        return in;
    }

    public void setIn(Integer in) {
        this.in = in;
    }

    public Integer getOut() {
        return out;
    }

    public void setOut(Integer out) {
        this.out = out;
    }

    @Override
    public String toString() {
        return "AdsqGroupAttendDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", attendance=" + attendance +
                ", onsite=" + onsite +
                ", in=" + in +
                ", out=" + out +
                '}';
    }
}

package com.whfc.entity.dto.adsq;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 奥氹四桥 部门信息
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-16 11:41
 */
public class AdsqGroupDTO implements Serializable {

    /**
     * 部门ID
     */
    @JsonProperty("ID")
    private Integer id;
    /**
     * 项目ID
     */
    @JsonProperty("ProjectID")
    private Integer projectId;
    /**
     * 部门名称
     */
    @JsonProperty("Name")
    private String name;
    /**
     * 部门类型ID
     */
    @JsonProperty("CompanyTypeID")
    private Integer companyTypeId;
    /**
     * 描述
     */
    @JsonProperty("Description")
    private String description;
    /**
     * 配置
     */
    @JsonProperty("Properties")
    private String properties;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCompanyTypeId() {
        return companyTypeId;
    }

    public void setCompanyTypeId(Integer companyTypeId) {
        this.companyTypeId = companyTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    @Override
    public String toString() {
        return "AdsqGroupDTO{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", name='" + name + '\'' +
                ", companyTypeId=" + companyTypeId +
                ", description='" + description + '\'' +
                ", properties='" + properties + '\'' +
                '}';
    }
}

package com.whfc.entity.dto.msg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 推送消息对象
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/12/2 11:32
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PushMsgDTO implements Serializable {

    /**
     * 消息ID
     */
    private Integer id;

    /**
     * 消息唯一标识
     */
    private String guid;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 复杂消息内容
     */
    private String html;

    /**
     * 是否单独的html
     */
    private boolean single;

    /**
     * 消息时间
     */
    private Date time;

    /**
     * 业务类型
     */
    private Integer moduleType;

    /**
     * 消息类型
     */
    private Integer msgType;

    /**
     * 消息对象类型
     */
    private String msgObjectType;

    /**
     * 业务对象ID
     */
    private String msgObjectId;

    /**
     * 消息发送渠道
     */
    private Integer msgChannel;

    /**
     * 接收人
     */
    private Integer toUserId;

    /**
     * 接收人姓名
     */
    private String toUserName;

    /**
     * 接收人手机号
     */
    private String toUserPhone;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 项目名称
     */
    private String deptName;

    /************************/

    /**
     * 消息接收人
     */
    private List<AppMsgToUserDTO> toUserList;

    /**
     * 消息渠道
     */
    private List<Integer> msgChannelList;

    private String triggerObjectId;

    private Integer ruleType;

    /******************/

    /**
     * 短信发送参数
     */
    private PushSmsDTO sms;
}

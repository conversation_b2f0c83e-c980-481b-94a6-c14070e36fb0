package com.whfc.entity.dto.blind;

import com.whfc.entity.dto.OssPathDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 车辆盲区报警数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/10/9 15:38
 */
@Data
public class BlindWarnCheckDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 设备ID
     */
    private Integer blindId;

    /**
     * sn
     */
    private String sn;

    /**
     * 平台
     */
    private String platform;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 硬件时间
     */
    private Date time;

    /*****************疲劳驾驶报警*****************/

    /**
     * 疲劳状态值
     */
    private Integer fatigueState = 0;

    /******************盲区报警*****************/

    /**
     * 盲区状态值
     */
    private Integer blindState = 0;

    /**
     * 报警文件
     */
    private List<OssPathDTO> alarmFiles;


}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.env.dao.FcsSpaceMapper">
  <resultMap id="BaseResultMap" type="com.whfc.env.entity.FcsSpace">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="lock_id" jdbcType="VARCHAR" property="lockId" />
    <result column="lock_name" jdbcType="VARCHAR" property="lockName" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, guid, name, address, lock_id, lock_name, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcs_space
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fcs_space
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.env.entity.FcsSpace">
    insert into fcs_space
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="lockId != null">
        lock_id,
      </if>
      <if test="lockName != null">
        lock_name,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="lockId != null">
        #{lockId,jdbcType=VARCHAR},
      </if>
      <if test="lockName != null">
        #{lockName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.env.entity.FcsSpace">
    update fcs_space
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="lockId != null">
        lock_id = #{lockId,jdbcType=VARCHAR},
      </if>
      <if test="lockName != null">
        lock_name = #{lockName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectSpaceList" resultType="com.whfc.env.dto.FcsSpaceDTO">
    select id as spaceId,
           guid,
            name,
            address,
            lock_id,
            lock_name
    from fcs_space
    where dept_id = #{deptId,jdbcType=INTEGER}
      and del_flag = 0
      <if test="keyword != null and keyword != ''">
        and name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
      </if>
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from fcs_space
    where guid = #{guid,jdbcType=VARCHAR}
  </select>

  <select id="selectByDeptIdAndName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from fcs_space
    where dept_id = #{deptId,jdbcType=INTEGER}
      and name = #{name,jdbcType=VARCHAR}
      and del_flag = 0
  </select>

  <select id="selectByDeptIdAndLockId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from fcs_space
    where dept_id = #{deptId,jdbcType=INTEGER}
      and lock_id = #{lockId,jdbcType=VARCHAR}
      and del_flag = 0
  </select>

  <select id="countByDeptId" resultType="java.lang.Integer">
    select count(1)
    from fcs_space
    where dept_id = #{deptId,jdbcType=INTEGER}
      and del_flag = 0
    <if test="keyword != null and keyword != ''">
      and name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
    </if>
    <if test="spaceId != null">
      and id = #{spaceId,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByLockId" resultType="java.lang.Integer">
    select count(1)
    from fcs_space
    where lock_id = #{lockId,jdbcType=VARCHAR}
      and del_flag = 0
  </select>

  <update id="logicDeleteById">
    update fcs_space
    set del_flag = 1
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectDeptIdList" resultType="java.lang.Integer">
    select distinct dept_id
    from fcs_space
    where del_flag = 0
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.env.dao.EnvMappingMapper">
  <resultMap id="BaseResultMap" type="com.whfc.env.entity.EnvMapping">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="src_id" jdbcType="INTEGER" property="srcId" />
    <result column="dst_id" jdbcType="INTEGER" property="dstId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, src_id, dst_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from env_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from env_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.env.entity.EnvMapping">
    insert into env_mapping (id, type, src_id, 
      dst_id)
    values (#{id,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{srcId,jdbcType=INTEGER}, 
      #{dstId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.env.entity.EnvMapping">
    insert into env_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="srcId != null">
        src_id,
      </if>
      <if test="dstId != null">
        dst_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="srcId != null">
        #{srcId,jdbcType=INTEGER},
      </if>
      <if test="dstId != null">
        #{dstId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.env.entity.EnvMapping">
    update env_mapping
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="srcId != null">
        src_id = #{srcId,jdbcType=INTEGER},
      </if>
      <if test="dstId != null">
        dst_id = #{dstId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.env.entity.EnvMapping">
    update env_mapping
    set type = #{type,jdbcType=INTEGER},
      src_id = #{srcId,jdbcType=INTEGER},
      dst_id = #{dstId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectAll" resultMap="BaseResultMap">
    select src_id,dst_id
      from env_mapping
  </select>

  <select id="selectSrcIdByDstId" resultType="java.lang.Integer">
    select src_id
    from env_mapping
    where dst_id = #{dstId}
    order by id desc
    limit 1
  </select>

  <select id="selectDstIdBySrcId" resultType="java.lang.Integer">
    select distinct dst_id
    from env_mapping
    where src_id = #{srcId}
  </select>
</mapper>
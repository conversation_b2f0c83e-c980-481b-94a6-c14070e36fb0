<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.env.dao.EnvDayStatMapper">
  <resultMap id="BaseResultMap" type="com.whfc.env.entity.EnvDayStat">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="dust_id" jdbcType="INTEGER" property="dustId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="data_num" jdbcType="INTEGER" property="dataNum" />
    <result column="exceed_num" jdbcType="INTEGER" property="exceedNum" />
    <result column="pm25_exceed_num" jdbcType="INTEGER" property="pm25ExceedNum" />
    <result column="pm10_exceed_num" jdbcType="INTEGER" property="pm10ExceedNum" />
    <result column="noise_exceed_num" jdbcType="INTEGER" property="noiseExceedNum" />
    <result column="warn_num" jdbcType="INTEGER" property="warnNum" />
    <result column="rain" jdbcType="DOUBLE" property="rain" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    dust_id,
    `date`,
    data_num,
    exceed_num,
    pm25_exceed_num,
    pm10_exceed_num,
    noise_exceed_num,
    warn_num,
    rain,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from env_day_stat
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from env_day_stat
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.env.entity.EnvDayStat">
    insert into env_day_stat
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="dustId != null">
        dust_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="dataNum != null">
        data_num,
      </if>
      <if test="exceedNum != null">
        exceed_num,
      </if>
      <if test="pm25ExceedNum != null">
        pm25_exceed_num,
      </if>
      <if test="pm10ExceedNum != null">
        pm10_exceed_num,
      </if>
      <if test="noiseExceedNum != null">
        noise_exceed_num,
      </if>
      <if test="warnNum != null">
        warn_num,
      </if>
      <if test="rain != null">
        rain,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="dustId != null">
        #{dustId,jdbcType=INTEGER},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="dataNum != null">
        #{dataNum,jdbcType=INTEGER},
      </if>
      <if test="exceedNum != null">
        #{exceedNum,jdbcType=INTEGER},
      </if>
      <if test="pm25ExceedNum != null">
        #{pm25ExceedNum,jdbcType=INTEGER},
      </if>
      <if test="pm10ExceedNum != null">
        #{pm10ExceedNum,jdbcType=INTEGER},
      </if>
      <if test="noiseExceedNum != null">
        #{noiseExceedNum,jdbcType=INTEGER},
      </if>
      <if test="warnNum != null">
        #{warnNum,jdbcType=INTEGER},
      </if>
      <if test="rain != null">
        #{rain,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.env.entity.EnvDayStat">
    update env_day_stat
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="dustId != null">
        dust_id = #{dustId,jdbcType=INTEGER},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="dataNum != null">
        data_num = #{dataNum,jdbcType=INTEGER},
      </if>
      <if test="exceedNum != null">
        exceed_num = #{exceedNum,jdbcType=INTEGER},
      </if>
      <if test="pm25ExceedNum != null">
        pm25_exceed_num = #{pm25ExceedNum,jdbcType=INTEGER},
      </if>
      <if test="pm10ExceedNum != null">
        pm10_exceed_num = #{pm10ExceedNum,jdbcType=INTEGER},
      </if>
      <if test="noiseExceedNum != null">
        noise_exceed_num = #{noiseExceedNum,jdbcType=INTEGER},
      </if>
      <if test="warnNum != null">
        warn_num = #{warnNum,jdbcType=INTEGER},
      </if>
      <if test="rain != null">
        rain = #{rain,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDustIdAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from env_day_stat
    where dust_id = #{dustId,jdbcType=INTEGER}
      and `date` = date(#{date,jdbcType=DATE})
  </select>

  <select id="selectTotalRainByDustIdAndDateRange" resultType="java.lang.Double">
    select sum(rain) as rain
    from env_day_stat
    where dust_id = #{dustId,jdbcType=INTEGER}
    <if test="startDate != null and endDate != null">
      and `date` between date(#{startDate}) and date(#{endDate})
    </if>
  </select>

  <select id="enterpriseExceedNumStat" resultType="com.whfc.env.dto.EnvStatItem">
    select dept_id,
           count(distinct dust_id) as deviceNum,
           ifnull(sum(exceed_num),0) as exceedNum,
           ifnull(sum(pm25_exceed_num),0) as pm25ExceedNum,
           ifnull(sum(pm10_exceed_num),0) as pm10ExceedNum,
           ifnull(sum(noise_exceed_num),0) as noiseExceedNum,
           ifnull(sum(warn_num),0) as warnNum
     from env_day_stat
    where dept_id in
      <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
        #{deptId}
      </foreach>
     <if test="startDate != null and endDate != null">
       and `date` between date(#{startDate}) and date(#{endDate})
     </if>
     group by dept_id
  </select>

  <select id="enterpriseExceedDaysStat" resultType="com.whfc.env.dto.EnvStatItem">
    select dept_id,
           sum(case when exceed_num > 0 then 1 else 0 end) as exceedDays
      from env_day_stat
     where dept_id in
       <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
         #{deptId}
       </foreach>
      <if test="startDate != null and endDate != null">
        and `date` between date(#{startDate}) and date(#{endDate})
      </if>
      group by dept_id
  </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.env.dao.EnvOpLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.env.entity.EnvOpLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dust_id" jdbcType="INTEGER" property="dustId" />
    <result column="op_time" jdbcType="TIMESTAMP" property="opTime" />
    <result column="op_code" jdbcType="VARCHAR" property="opCode" />
    <result column="op_user" jdbcType="VARCHAR" property="opUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dust_id, op_time, op_code, op_user, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from env_op_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from env_op_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.env.entity.EnvOpLog">
    insert into env_op_log (id, dust_id, op_time, 
      op_code, op_user, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{dustId,jdbcType=INTEGER}, #{opTime,jdbcType=TIMESTAMP}, 
      #{opCode,jdbcType=VARCHAR}, #{opUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.env.entity.EnvOpLog">
    insert into env_op_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dustId != null">
        dust_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opCode != null">
        op_code,
      </if>
      <if test="opUser != null">
        op_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="dustId != null">
        #{dustId,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opCode != null">
        #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="opUser != null">
        #{opUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.env.entity.EnvOpLog">
    update env_op_log
    <set>
      <if test="dustId != null">
        dust_id = #{dustId,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opCode != null">
        op_code = #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="opUser != null">
        op_user = #{opUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.env.entity.EnvOpLog">
    update env_op_log
    set dust_id = #{dustId,jdbcType=INTEGER},
      op_time = #{opTime,jdbcType=TIMESTAMP},
      op_code = #{opCode,jdbcType=VARCHAR},
      op_user = #{opUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectOpLogByDustId" resultType="com.whfc.env.dto.EnvOpLogDTO">
    select op_time,
           op_code,
           op_user
      from env_op_log
     where dust_id = #{dustId}
      order by id desc
  </select>

  <select id="selectLastOpLog" resultType="com.whfc.env.dto.EnvOpLogDTO">
    select op_time,
           op_code,
           op_user
    from env_op_log
    where dust_id = #{dustId}
    order by id desc
     limit 1
  </select>
</mapper>